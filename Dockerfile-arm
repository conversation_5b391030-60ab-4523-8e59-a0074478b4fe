FROM registry.linker.cc/linker/openjdk-arm64v8:8u432-jdk-alpine-skyagent-fix
WORKDIR /usr/local/
ARG CI_PROJECT_NAME
ARG PASSWORD
ADD ${CI_PROJECT_NAME}.jar app.jar
EXPOSE 8080
RUN mkdir -p /usr/local/logs && chmod 777 /usr/local/logs
RUN echo "java -javaagent:/usr/local/app.jar=\"-pwd ${PASSWORD}\" \
-javaagent:/usr/local/skywalking-agent/skywalking-agent.jar \
-Dskywalking.agent.is_cache_enhanced_class=true \
-Dskywalking.agent.class_cache_mode=FILE \
-Dskywalking.agent.service_name=${CI_PROJECT_NAME} \
-Dskywalking.agent.keep_tracing=true \
-Dskywalking.collector.backend_service=\${SKYWALKING_SERVICE} \
-jar \
\${JAVA_OPTS} \
-XX:MaxRAMPercentage=80.0 \
-XX:+PrintGC \
-XX:+PrintGCDetails \
-Xloggc:/usr/local/logs/gc.log \
-XX:ErrorFile=/usr/local/logs/hs_err_pid.log \
-XX:+HeapDumpOnOutOfMemoryError \
-XX:HeapDumpPath=/usr/local/logs/jvmlog \
app.jar \
-Dspring.profiles.active=\${SPRING_PROFILES_ACTIVE}" > /run.sh && chmod 777 /run.sh
ENTRYPOINT ["/bin/sh","/run.sh"]