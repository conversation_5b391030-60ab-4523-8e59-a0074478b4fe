<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>knowledge-center</artifactId>
        <groupId>com.linker.fusion</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>knowledge-center-infrastructure</artifactId>
    <version>1.0-SNAPSHOT</version>
    <dependencies>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>bson</artifactId>
            <version>4.6.1</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator-annotation-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>web-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>log-record-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>redis-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>skywalking-starter</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.linker</groupId>-->
        <!--            <artifactId>rocketmq-starter</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>mybatis-plus-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.lianjiatech</groupId>
            <artifactId>retrofit-spring-boot-starter</artifactId>
            <version>2.3.11</version>
        </dependency>
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>core-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>cos-adapter-starter</artifactId>
            <!--            <version>1.0.3-SNAPSHOT</version>-->
        </dependency>
        <dependency>
            <groupId>com.linker.omagent</groupId>
            <artifactId>omagent-spring-boot-starter</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.linker.conductor</groupId>-->
        <!--            <artifactId>conductor-client</artifactId>-->
        <!--        </dependency>-->
        <!-- 认证接口依赖 -->
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>auth-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.biezhi</groupId>
            <artifactId>TinyPinyin</artifactId>
            <version>2.0.3.RELEASE</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.alicp.jetcache</groupId>-->
        <!--            <artifactId>jetcache-starter-redis</artifactId>-->
        <!--            <version>2.7.3</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis-lettuce</artifactId>
            <version>2.7.3</version>
        </dependency>
        <!-- Kryo5 序列化实现 -->
        <dependency>
            <groupId>com.esotericsoftware.kryo</groupId>
            <artifactId>kryo5</artifactId>
            <version>5.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>adapter-rxjava2</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.20</version>
        </dependency>
        <dependency>
            <groupId>io.github.haibiiin</groupId>
            <artifactId>json-repair</artifactId>
            <version>0.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-core</artifactId>
            <version>9.1.22</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.xhtmlrenderer</groupId>-->
<!--            <artifactId>flying-saucer-pdf-openpdf</artifactId>-->
<!--            <version>9.1.22</version>-->
<!--        </dependency>-->

    </dependencies>
</project>
