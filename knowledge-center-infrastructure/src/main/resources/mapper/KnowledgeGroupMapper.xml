<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linker.fusion.knowledgecenter.infrastructure.mapper.KnowledgeGroupMapper">
    <select id="getMaxAndMin" resultType="com.linker.fusion.knowledgecenter.infrastructure.model.GroupExtModel">
        SELECT MIN(name) AS min,
        MAX(name) AS max
        FROM t_knowledge_group
        WHERE REGEXP_LIKE(name, '^(19[8-9][0-9]|20[0-9]{2}|2100)$') AND
        parent_id in
        <foreach collection="parentIds" separator="," open="(" close=")" item="parentId">
            #{parentId}
        </foreach>
    </select>

</mapper>
