<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linker.fusion.knowledgecenter.infrastructure.mapper.ResourceFaceMapper">

    <select id="listGroupBySourceIdOrderByCount" resultType="java.util.Map">
        SELECT source_id, COUNT(*) AS count
        FROM t_resource_face
        WHERE deleted = 0 AND doc_id IN
        <foreach collection="docIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY source_id
        ORDER BY count DESC
    </select>

</mapper>
