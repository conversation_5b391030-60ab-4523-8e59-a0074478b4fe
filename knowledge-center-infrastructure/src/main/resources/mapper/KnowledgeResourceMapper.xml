<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linker.fusion.knowledgecenter.infrastructure.mapper.KnowledgeResourceMapper">
    <select id="sumSize" resultType="java.lang.Long">
        SELECT sum(size) FROM t_knowledge_resource
        <where>
            tenant_id=#{tenantId} and `type`=#{type} and deleted = 0;
        </where>
    </select>
    <select id="sumSizeByVisibleType" resultType="java.lang.Long">
        SELECT sum(size) FROM t_knowledge_resource t0 left join t_knowledge_group t1 on t0.group_id=t1.id
        <where>
            t0.tenant_id=#{tenantId}
            and t1.deleted = 0
            and t0.deleted = 0
            and t1.visible_type=#{visibleType}
            and t1.type= 1
            and t1.is_sync=0
            <if test="userCode!=null and userCode !=''">
            and t0.creator_id=#{userCode}
            </if>
        </where>
    </select>
    <select id="sumSizeByGroupId" resultType="java.lang.Long">
        SELECT sum(size) FROM t_knowledge_resource
        <where>
            deleted = 0
            and group_id in
            <foreach collection="groupIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="staByLib" resultType="com.linker.fusion.knowledgecenter.infrastructure.model.ResourceStaModel">
        select CASE WHEN path LIKE '/%/%/%/%' THEN (SUBSTRING_INDEX(SUBSTRING_INDEX(t2.path, '/', 3), '/', -1) || "/" || (select t3.name from gb_chat_knowledge_center:t_knowledge_group t3
        where t3.id= SUBSTRING_INDEX(SUBSTRING_INDEX(t2.path, '/', 4), '/', -1)))
        ELSE SUBSTRING_INDEX(path, '/', 2) || "/" || t2.name END AS prefix, count(1) as count, sum(size) as sum
        from gb_chat_knowledge_center:t_knowledge_resource t1 left join gb_chat_knowledge_center:t_knowledge_group t2 on t1.group_id =t2.id
        where t1.deleted = 0 AND
        <foreach collection="pathList" separator=" or " open="(" close=")" item="path">
            path like #{path}
        </foreach>
        AND t1.type = #{fileType}
        group by prefix
    </select>

</mapper>
