<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linker.fusion.knowledgecenter.infrastructure.mapper.ResourceSegmentMapper">
    <select id="page" resultType="com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceSegmentEntity">
        select *
        from (SELECT (@row_number := @row_number + 1) AS number,
        segment_id,
        file_type,
        doc_id,
        title,
        content,
        sort,
        page,
        position,
        start_timestamp,
        end_timestamp,
        status
        FROM t_resource_segment,
        (SELECT @row_number := 0) AS t
        where doc_id = #{docId}
        and deleted = false
        order by sort asc) as t
        <where>
            <if test="content!=null and content!=''">
                and t.content like #{content}
            </if>
            <if test="status!=null and status.size>0 ">
                and t.status in
                <foreach collection="status" separator="," open="(" close=")" item="name">
                    #{name}
                </foreach>
            </if>
        </where>
    </select>
    <select id="maxSort">
        select max(sort)
        FROM t_resource_segment
        where doc_id = #{docId}
          and deleted = false
    </select>
</mapper>
