{"group_id": {"comment": "分组id", "type": "long"}, "url": {"comment": "帧数据url", "type": "keyword"}, "frame_id": {"comment": "帧id", "type": "keyword"}, "segment_id": {"comment": "视频分段id", "type": "keyword"}, "doc_id": {"comment": "文件资源id", "type": "keyword"}, "file_title": {"comment": "标题，双重字段，text+keyword", "index": "bm25", "type": "text", "lang": "zh", "fields": {"keyword": {"type": "keyword", "normalizer": "lowercase_normalizer"}}}, "content": {"comment": "帧的caption，待确定是否需要", "index": "bm25", "type": "text", "lang": "zh"}, "image_vector": {"comment": "图片向量化", "index": "true", "type": "vector", "num_dim": "1024"}, "meta": {"comment": "元数据信息，老的文档索引上叫错了mate，现在统一纠正为meta", "type": "object"}, "persons_labels": {"comment": "人物标签数组", "type": "keyword", "normalizer": "lowercase_normalizer"}, "object_labels": {"comment": "实体标签数组", "type": "keyword", "normalizer": "lowercase_normalizer"}, "text_labels": {"comment": "专名文本标签数组", "type": "keyword", "normalizer": "lowercase_normalizer"}, "sort": {"comment": "排序", "type": "double"}, "time_point": {"comment": "时间点", "type": "double"}, "enable": {"comment": "启用=1/禁用=0 ", "type": "integer"}, "create_time": {"comment": "创建时间", "type": "datetime", "date_format": "yyyy-MM-dd HH:mm:ss"}, "creator_id": {"comment": "创建人，用户code", "type": "keyword"}, "tenant_id": {"comment": "租户id", "type": "keyword"}}