{"group_id": {"comment": "分组id", "type": "long"}, "segment_id": {"comment": "分段id", "type": "keyword"}, "file_type": {"comment": "知识类型 1-文档 2-图片 3-视频 4-音频", "type": "keyword"}, "chunk_id": {"comment": "分块id", "type": "keyword"}, "doc_id": {"comment": "文件资源id", "type": "keyword"}, "file_title": {"comment": "标题，双重字段，text+keyword", "index": "bm25", "type": "text", "lang": "zh", "fields": {"keyword": {"type": "keyword", "normalizer": "lowercase_normalizer"}}}, "content": {"comment": "asr的结果", "index": "bm25", "type": "text", "lang": "zh"}, "content_vector": {"comment": "图片caption内容的向量化", "index": "true", "type": "vector", "num_dim": "1024"}, "meta": {"comment": "元数据信息，老的文档索引上叫错了mate，现在统一纠正为meta", "type": "object"}, "persons_labels": {"comment": "人物标签数组", "type": "keyword", "normalizer": "lowercase_normalizer"}, "object_labels": {"comment": "实体标签数组", "type": "keyword", "normalizer": "lowercase_normalizer"}, "text_labels": {"comment": "专名文本标签数组", "type": "keyword", "normalizer": "lowercase_normalizer"}, "sort": {"comment": "排序", "type": "double"}, "start_timestamp": {"comment": "开始时间", "type": "integer"}, "end_timestamp": {"comment": "结束时间", "type": "integer"}, "enable": {"comment": "启用=1/禁用=0 ", "type": "integer"}, "create_time": {"comment": "创建时间", "type": "datetime", "date_format": "yyyy-MM-dd HH:mm:ss"}, "speaker": {"comment": "发言人", "type": "keyword", "normalizer": "lowercase_normalizer"}, "creator_id": {"comment": "创建人，用户code", "type": "keyword"}, "tenant_id": {"comment": "租户id", "type": "keyword"}}