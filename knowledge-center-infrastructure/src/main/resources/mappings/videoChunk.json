{"group_id": {"comment": "分组id", "type": "long"}, "segment_id": {"comment": "分段id", "type": "keyword"}, "chunk_id": {"comment": "分块id", "type": "keyword"}, "doc_id": {"comment": "文件资源id", "type": "keyword"}, "file_title": {"comment": "标题，双重字段，text+keyword", "index": "bm25", "type": "text", "lang": "zh", "fields": {"keyword": {"type": "keyword", "normalizer": "lowercase_normalizer"}}}, "content": {"comment": "视频的summary，场景中的详细描述，分为以下字段{\"time\":当前视频片段在时段（如上午或晚上）、季节（如春季或秋季）方面的时间信息，或者具体的年份和时间点\"location\":描述当前事件发生的地点，包括场景细节\"character\":当前任务的详细描述，包括人名，关系和他们正在做什么\"events\":按时间顺序列出的详细描述视频内容中的所有事件\"scene\":视频场景进行一些详细描述。这包括但不限于场景信息、文字信息、人物状态表情以及视频中呈现的事件\"summary\":这段视频剪辑的内容进行详细的整体描述和总结}", "index": "bm25", "type": "text", "lang": "zh"}, "content_vector": {"comment": "图片caption内容的向量化", "index": "true", "type": "vector", "num_dim": "1024"}, "meta": {"comment": "元数据信息，老的文档索引上叫错了mate，现在统一纠正为meta", "type": "object"}, "persons_labels": {"comment": "人物标签数组", "type": "keyword", "normalizer": "lowercase_normalizer"}, "object_labels": {"comment": "实体标签数组", "type": "keyword", "normalizer": "lowercase_normalizer"}, "text_labels": {"comment": "专名文本标签数组", "type": "keyword", "normalizer": "lowercase_normalizer"}, "sort": {"comment": "排序", "type": "double"}, "start_timestamp": {"comment": "开始时间", "type": "integer"}, "end_timestamp": {"comment": "结束时间", "type": "integer"}, "enable": {"comment": "启用=1/禁用=0 ", "type": "integer"}, "create_time": {"comment": "创建时间", "type": "datetime", "date_format": "yyyy-MM-dd HH:mm:ss"}, "creator_id": {"comment": "创建人，用户code", "type": "keyword"}, "tenant_id": {"comment": "租户id", "type": "keyword"}}