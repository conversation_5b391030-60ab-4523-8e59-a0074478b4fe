ALTER TABLE t_knowledge_group
    ADD COLUMN `visible_type` tinyint not null DEFAULT 1 COMMENT '可见类型：1公开 2个人';
ALTER TABLE t_knowledge_group
    ADD COLUMN `important_level` tinyint not null DEFAULT 0 COMMENT '重要等级,0 普通 1重要';


CREATE TABLE IF NOT EXISTS `t_sub_video`
(
    `id`          bigint(20)                               NOT NULL AUTO_INCREMENT,
    `create_time` datetime                                 NOT NULL COMMENT '创建时间',
    `creator_id`  char(36) COLLATE utf8mb4_unicode_ci               DEFAULT NULL,
    `update_time` datetime                                          DEFAULT NULL COMMENT '更新时间',
    `update_id`   char(36) COLLATE utf8mb4_unicode_ci               DEFAULT NULL,
    `tenant_id`   char(36) COLLATE utf8mb4_unicode_ci      NOT NULL COMMENT '租户或组织的唯一标识符',
    `deleted`     tinyint(1)                               NOT NULL DEFAULT '0' COMMENT '逻辑删除',
    `doc_id`      char(36) CHARACTER SET utf8mb4                    DEFAULT NULL COMMENT '关联的主视频docId',
    `file_id`     char(36) CHARACTER SET utf8mb4           NOT NULL COMMENT '文件ID',
    `group_id`    bigint(20)                               NOT NULL COMMENT '组ID',
    `title`       varchar(255) COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '资源名称',
    `suffix`      varchar(20) COLLATE utf8mb4_unicode_ci   NOT NULL COMMENT '文件后缀',
    `url`         varchar(2000) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源地址',
    `size`        bigint(20)                               NOT NULL COMMENT '文件资源大小（byte）',
    `duration`  bigint(20)                              DEFAULT NULL,
    `thumbnail` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `operation`   tinyint(2)                               NOT NULL COMMENT '0-已合成 1-新增 2-删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='分段视频表';

CREATE TABLE IF NOT EXISTS `t_log_record`
(
    `id`          bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`   varchar(63) NOT NULL COMMENT '租户ID',
    `ip`         varchar(63)  NULL COMMENT 'ip',
    `biz_id`     varchar(63)  NOT NULL COMMENT '业务id',
    `action`     varchar(511) DEFAULT NULL COMMENT '操作详情',
    `extra`      text         DEFAULT NULL COMMENT '额外数据',
    `type`        varchar(63) NOT NULL COMMENT '操作类型，',
    `level`      varchar(31)  NULL COMMENT '安全等级',
    `username`   varchar(128) NULL COMMENT '用户账号',
    `operator`   varchar(63)  NULL COMMENT '操作人名称',
    `sub_type`    varchar(63) NOT NULL COMMENT '操作子类型',
    `status`      tinyint(3)  NOT NULL COMMENT '状态 1成功，0失败',
    `deleted`     tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
    `creator_id` varchar(63)  NOT NULL COMMENT '创建人ID',
    `create_time` datetime    NOT NULL COMMENT '创建时间',
    `update_id`  varchar(63)  NOT NULL COMMENT '更新人ID',
    `update_time` datetime    NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='日志记录表';
