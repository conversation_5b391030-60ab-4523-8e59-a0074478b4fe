CREATE TABLE if not exists t_migration_task
(
    id          BIGINT AUTO_INCREMENT PRIMARY KEY,
    type        VARCHAR(255) NOT NULL COMMENT '迁移类型',
    content_id  VARCHAR(255) NOT NULL COMMENT '迁移的数据ID或唯一标识符',
    success     TINYINT(1)   NULL DEFAULT 0 COMMENT '迁移是否成功',
    message     TEXT              DEFAULT NULL COMMENT '迁移失败时的错误信息',
    create_time DATETIME     NOT NULL COMMENT '创建时间',
    update_time DATETIME     NULL COMMENT '更新时间'
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT = '记录数据迁移历史的日志表';

INSERT INTO t_migration_task (type, content_id, success, message, create_time, update_time)
SELECT
    'faq_set_group_id' AS type,
    id AS content_id,
    0 AS success,
    NULL AS message,
    NOW() AS create_time,
    NULL AS update_time
FROM t_faq_info where deleted = 0;

INSERT INTO t_migration_task (type, content_id, success, message, create_time, update_time)
SELECT
    'table_set_group_id' AS type,
    id AS content_id,
    0 AS success,
    NULL AS message,
    NOW() AS create_time,
    NULL AS update_time
FROM t_knowledge_resource WHERE type = 6 and deleted = 0;