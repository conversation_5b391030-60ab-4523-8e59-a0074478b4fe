create table t_index_tenant_mapping
(
    id          bigint auto_increment
        primary key,
    tenant_id   char(36)     not null comment '租户或组织的唯一标识符',
    `index`     varchar(255) not null comment '索引名称' unique,
    type        tinyint(2)           not null comment '知识类型 1-文档 2-图片 3-视频 4-音频 5-FAQ 6-表格',
    isTemplate  tinyint(1) default 0 not null comment '是否用模板 -> 0：否、1：是',
    readA<PERSON><PERSON>   varchar(255) null comment '读别名',
    write<PERSON><PERSON><PERSON>  varchar(255) null comment '写别名',
    create_time datetime     not null comment '创建时间',
    update_time datetime null comment '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='租户索引表';