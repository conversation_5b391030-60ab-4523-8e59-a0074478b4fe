create table if not exists t_feedback_setting
(
    id           bigint auto_increment
        primary key,
    agent_id     bigint     not null comment '智能体ID',
    learn_enable tinyint(1) null default 0 comment 'AI学习开关 0-关闭 1-开启',
    create_time  datetime   not null comment '创建时间',
    creator_id   char(36)   not null comment '创建人ID',
    update_time  datetime   null comment '更新时间',
    update_id    char(36)   null,
    tenant_id    char(36)   not null comment '租户或组织的唯一标识符',
    deleted      tinyint(1)      default 0 not null comment '逻辑删除'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='反馈学习用户设置表';

create table if not exists t_feedback
(
    id             bigint auto_increment
        primary key,
    uid            char(36)   null comment 'ES文档uid',
    agent_id       bigint     not null comment '智能体ID',
    question       text       not null comment '问题内容',
    content        text       null comment '反馈内容',
    learn_status   tinyint(2) null default 0 comment 'AI学习状态 0-待学习 1-学习通过 2-学习不通过',
    operation_tag  tinyint(2) null comment '人为操作 0-未操作 1-采纳 2-忽略',
    operator_id    char(36)   null comment '操作人ID',
    operation_time datetime   null comment '操作时间',
    faq_id         bigint     null comment '采纳时关联FAQ ID',
    create_time    datetime   not null comment '创建时间',
    creator_id     char(36)   not null comment '创建人ID',
    update_time    datetime   null comment '更新时间',
    update_id      char(36)   null,
    tenant_id      char(36)   not null comment '租户或组织的唯一标识符',
    deleted        tinyint(1)      default 0 not null comment '逻辑删除'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='反馈学习表';