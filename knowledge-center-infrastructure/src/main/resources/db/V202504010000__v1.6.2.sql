CREATE TABLE IF NOT EXISTS `t_task`
(
    `id`          BIGINT AUTO_INCREMENT PRIMARY KEY,
    `status`      tinyint NOT NULL COMMENT '任务状态',
    `read_status` tinyint NOT NULL COMMENT '已读状态',
    `group_id`    BIGINT Not NULL COMMENT '分组id',
    `content`     text     NOT NULL COMMENT '任务内容',
    `result`      text     NULL COMMENT '任务结果',
    `type`        tinyint     not null comment '类型  1 生成问答对 2 生成题库',
    `create_time` datetime    NOT NULL,
    `creator_id`  char(36)    NOT NULL,
    `update_time` datetime    DEFAULT NULL,
    `update_id`   char(36)    DEFAULT NULL,
    `tenant_id`   char(36)    NOT NULL,
    `deleted`     tinyint(1)  NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='任务列表';

CREATE TABLE IF NOT EXISTS `t_question_bank` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
  `uid` varchar(64) NOT NULL COMMENT 'uid',
  `group_id` bigint NOT NULL COMMENT '所属分组ID',
  `question` varchar(200) NOT NULL COMMENT '问题内容',
  `answer` text NOT NULL COMMENT '答案内容',
  `analysis` varchar(3000) DEFAULT NULL COMMENT '解析说明',
  `options` json DEFAULT NULL COMMENT '选项JSON',
  `question_type` varchar(50) NOT NULL COMMENT '题目类型:CHOICE-选择题,TRUE_FALSE-判断题',
  `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用 1-启用',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  `creator_id` varchar(50) NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_id` varchar(50) NOT NULL COMMENT '更新人ID',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_group` (`tenant_id`, `group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='题库表';

-- 试卷表
CREATE TABLE IF NOT EXISTS t_examination_paper
(
    id            bigint auto_increment comment '主键'
        primary key,
    create_time   datetime default CURRENT_TIMESTAMP null comment '创建时间',
    creator_id    varchar(64)                        null comment '创建人',
    update_time   datetime                           null on update CURRENT_TIMESTAMP comment '修改时间',
    update_id     varchar(64)                        null comment '修改人',
    deleted       int      default 0                 null comment '是否删除 0 否 1 是',
    tenant_id     varchar(32)                        not null comment '企业代码',
    bind_question_base_ids     varchar(512)          not null comment '绑定批量问题库标识',
    generate_question_types     varchar(512)         null comment '生成问题库类型, CHOICE:单选题, TRUE_FALSE:判断题',
    paper_unique_id     varchar(32)                  not null comment '试卷唯一标识',
    paper_content     json                           null comment '试卷内容',
    expect_question_number   int                     null comment '期望题目数量',
    real_question_number   int                       null comment '真实题目数量',
    paper_answer_content     json                    null comment '试卷回答内容',
    paper_status   int(1)         default 0          null comment '试卷状态, 0:回答中, 1:完成',
    paper_score     varchar(256)                     null comment '试卷成绩'
)
    comment '试卷表'  collate = utf8mb4_unicode_ci;
