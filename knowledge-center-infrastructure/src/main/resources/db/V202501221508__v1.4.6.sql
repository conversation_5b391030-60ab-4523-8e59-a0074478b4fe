alter table t_migration_task
    add tenant_id varchar(50) null after message;

INSERT INTO t_migration_task (type, content_id, success, message, tenant_id, create_time, update_time)
SELECT
    'fix_segment_content' AS type,
    id AS content_id,
    0 AS success,
    NULL AS message,
    tenant_id AS tenant_id,
    NOW() AS create_time,
    NULL AS update_time
FROM t_knowledge_resource WHERE type = 1 and deleted = 0;