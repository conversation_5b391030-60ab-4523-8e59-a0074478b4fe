CREATE TABLE IF NOT EXISTS `t_npc_search_log`
(
    `id`             bigint(20)                              NOT NULL AUTO_INCREMENT,
    `create_time`    datetime                                NOT NULL COMMENT '创建时间',
    `creator_id`     char(36) COLLATE utf8mb4_unicode_ci              DEFAULT NULL,
    `update_time`    datetime                                         DEFAULT NULL COMMENT '更新时间',
    `update_id`      char(36) COLLATE utf8mb4_unicode_ci              DEFAULT NULL,
    `tenant_id`      char(36) COLLATE utf8mb4_unicode_ci     NOT NULL COMMENT '租户或组织的唯一标识符',
    `deleted`        tinyint(1)                              NOT NULL DEFAULT '0' COMMENT '逻辑删除',

    `search_type`    tinyint(2)                              NOT NULL COMMENT '按什么搜索 1-会议（标题） 2-人员（人脸库） 3-内容（ASR）',
    `search_content` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '搜索内容，输入或者勾选人物ID',
    `input`          text COLLATE utf8mb4_unicode_ci                  DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='人大视频资料查询记录';

CREATE TABLE IF NOT EXISTS `t_auth_approve`
(
    `id`                  bigint(20)                              NOT NULL AUTO_INCREMENT,
    `create_time`         datetime                                NOT NULL COMMENT '创建时间',
    `creator_id`          char(36) COLLATE utf8mb4_unicode_ci              DEFAULT NULL,
    `update_time`         datetime                                         DEFAULT NULL COMMENT '更新时间',
    `update_id`           char(36) COLLATE utf8mb4_unicode_ci              DEFAULT NULL,
    `tenant_id`           char(36) COLLATE utf8mb4_unicode_ci     NOT NULL COMMENT '租户或组织的唯一标识符',
    `deleted`             tinyint(1)                              NOT NULL DEFAULT '0' COMMENT '逻辑删除',

    `source_type`         tinyint(2)                              NOT NULL COMMENT '审批资源类型 1-目录 2-文件 3-表格',
    `source_id`           varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '审批资源唯一标识',
    `auth_level`          tinyint(2)                              NOT NULL COMMENT '申请权限等级',
    `apply_reason`      varchar(255) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '申请原因',
    `approve_process_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '审批流key',
    `approve_instance_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '审批实例key',
    `state`               tinyint(2)                              NOT NULL COMMENT '审批流程状态 1-申请中 2-同意 3-拒绝',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='权限申请列表';

ALTER TABLE t_knowledge_group
    ADD COLUMN `approve_process_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审批流程key';
