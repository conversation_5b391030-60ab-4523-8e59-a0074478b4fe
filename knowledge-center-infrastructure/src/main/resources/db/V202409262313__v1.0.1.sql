create table if not exists t_analysis_strategy
(
    id           bigint auto_increment primary key,
    name         varchar(255)        NULL COMMENT '策略名称',
    type         tinyint(2)              NOT NULL DEFAULT 0 COMMENT '策略类型 0类目策略 1单个文档解析策略',
    setup_json   text                 NULL COMMENT '策略设置json',
    group_id     bigint               NULL COMMENT '类目id',
    create_time  datetime             NOT NULL COMMENT '创建时间',
    creator_id   char(36)          NOT NULL COMMENT '创建人',
    update_time  datetime             null COMMENT '修改时间',
    update_id    char(36)          NULL COMMENT '修改人',
    tenant_id    char(36)          NOT NULL COMMENT '租户或组织的唯一标识符',
    deleted      tinyint(1)           NOT NULL DEFAULT 0 COMMENT '逻辑删除'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT '解析策略表';
