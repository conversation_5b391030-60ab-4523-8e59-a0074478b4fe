CREATE TABLE IF NOT EXISTS `t_template`
(
    `id`          BIGINT AUTO_INCREMENT PRIMARY KEY,
    `name`        varchar(50) NOT NULL COMMENT '模版名称',
    `description` varchar(200) DEFAULT NULL COMMENT '模版描述',
    `source`      int(11)     NOT NULL COMMENT '模版来源 0-内置 1-自定义',
    `type`        int(11)     NOT NULL COMMENT '知识类型 1-文档 2-图片 3-视频 4-音频',
    `sort`        int(11)      DEFAULT NULL COMMENT '排序',
    `enable`      tinyint(1)  NOT NULL COMMENT '是否生效',
    `is_default`  tinyint(1)  NOT NULL COMMENT '是否默认',
    `strategy`    text        NOT NULL COMMENT '策略JSON字符串',
    `preview_url` varchar(500) DEFAULT NULL,
    `create_time` datetime    NOT NULL,
    `creator_id`  char(36)    NOT NULL,
    `update_time` datetime     DEFAULT NULL,
    `update_id`   char(36)     DEFAULT NULL,
    `tenant_id`   char(36)    NOT NULL,
    `deleted`     tinyint(1)  NOT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='策略模版';

alter table t_knowledge_resource
    add template_id bigint null comment '模版ID' after strategy;
alter table t_resource_od_tag
    add `type` tinyint not null default 0 comment '类型';
alter table t_resource_face
    add `ext` text  null comment '附加数据';
CREATE TABLE `t_resource_ext`
(
    `id`         BIGINT   NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `doc_id`     char(36) NOT NULL COMMENT '资源id',
    `type`       varchar(128) DEFAULT NULL COMMENT '类型',
    `field1`  text DEFAULT NULL COMMENT '内容1',
    `field2`  text DEFAULT NULL COMMENT '内容2',
    `field3`  text DEFAULT NULL COMMENT '内容3',
    create_time  datetime NOT NULL COMMENT '创建时间',
    creator_id   char(36) NULL COMMENT '创建人',
    update_time  datetime NULL COMMENT '修改时间',
    update_id    char(36) NULL COMMENT '修改人',
    tenant_id    char(36) NOT NULL COMMENT '租户或组织的唯一标识符',
    deleted      tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='文件扩展信息表';




