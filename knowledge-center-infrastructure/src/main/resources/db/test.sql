CREATE TABLE `t_test`
(
    `id`          bigint(20)                           NOT NULL AUTO_INCREMENT,
    `type`        int(11)                              NOT NULL COMMENT '1单行输入框，2多行输入框，3数字输入框，4单选框，5多选框',
    `name`        varchar(255) COLLATE utf8_unicode_ci NOT NULL,
    `content`     varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
    `deleted`     tinyint(4)                           DEFAULT NULL,
    `create_time` datetime                             DEFAULT NULL,
    `update_time` datetime                             DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  COLLATE = utf8_unicode_ci COMMENT ='测试表';