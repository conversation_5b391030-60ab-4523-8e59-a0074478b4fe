create table t_knowledge_group
(
    id          bigint auto_increment
        primary key,
    tenant_id   char(36)          not null comment '租户或组织的唯一标识符',
    parent_id   bigint               not null comment '父级ID',
    type        tinyint(2)           not null comment '知识类型 1-文档 2-图片 3-视频 4-音频 5-FAQ 6-表格',
    name        varchar(255)         not null comment '分组名',
    sort        double               not null comment '排序',
    deleted     tinyint(1) default 0 not null comment '逻辑删除',
    create_time datetime             not null comment '创建时间',
    creator_id  char(36)          null,
    update_time datetime             null comment '更新时间',
    update_id   char(36)          null
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='知识分组表';

create table t_faq_info
(
    id              bigint auto_increment
        primary key,
    uid             char(36)          not null comment 'ES文档uid',
    tenant_id       char(36)          not null comment '租户或组织的唯一标识符',
    group_id        bigint               not null comment '组ID',
    group_path      varchar(255)         not null comment '组路径',
    type            tinyint(2)           not null comment '0-标准问题 1-相似问题',
    standard_id     bigint               null comment '相似问题关联标准问题ID',
    question        varchar(500)         not null comment '问题内容',
    is_llm_enhanced tinyint(1)           null comment '是否开启大模型回答润色',
    answer_type     tinyint(2)           null comment '0-纯文本 1-富文本',
    answers         text                 null comment '回答内容',
    enable          tinyint(1) default 1 not null comment '是否启用',
    deleted         tinyint(1) default 0 not null comment '逻辑删除',
    create_time     datetime             not null comment '创建时间',
    creator_id      char(36)          null,
    update_time     datetime             null comment '更新时间',
    update_id       char(36)          null
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='FAQ表';
