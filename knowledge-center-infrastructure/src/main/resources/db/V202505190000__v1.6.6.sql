create table t_auth_level
(
    id          bigint auto_increment primary key,
    `name`      varchar(50) not null comment '名称',
    `level`     int         not null comment '等级',
    codes       text        not null comment '授权节点列表',
    is_system   tinyint     not null comment '系统内置',
    tenant_id   char(36)    not null comment '租户或组织的唯一标识符',
    deleted     tinyint(1) default 0 not null comment '逻辑删除',
    create_time datetime    not null comment '创建时间',
    creator_id  char(36) null,
    update_time datetime null comment '更新时间',
    update_id   char(36) null
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='权限等级表';

INSERT INTO `t_auth_level`
VALUES (1, '可管理', 4,
        '[\"RESOURCE_PREVIEW\",\"TABLE_PREVIEW\",\"FAQ_PREVIEW\",\"QUESTION_PREVIEW\",\"LIBRARY_ADD_GROUP\",\"LIBRARY_SET\",\"GROUP_AUTH\",\"GROUP_RENAME\",\"GROUP_DELETE\",\"GROUP_ADD_SUB\",\"GROUP_MOVE\",\"RESOURCE_IMPORT\",\"RESOURCE_EDIT\",\"RESOURCE_DOWNLOAD\",\"RESOURCE_DELETE\",\"RESOURCE_MOVE\",\"RESOURCE_ENABLE\",\"RESOURCE_AUTH\",\"RESOURCE_REDO\",\"RESOURCE_MAINTENANCE\",\"RESOURCE_COPY\",\"RESOURCE_RENAME\",\"RESOURCE_SAVE_PART\",\"RESOURCE_ARCHIVE\",\"TABLE_IMPORT\",\"TABLE_EDIT\",\"TABLE_REDO\",\"TABLE_EDIT_SEG\",\"TABLE_DELETE\",\"TABLE_AUTH\",\"TABLE_ENABLE\",\"TABLE_MOVE\",\"TABLE_DOWNLOAD\",\"FAQ_EXPORT\",\"FAQ_CREATE\",\"FAQ_MOVE\",\"FAQ_ENABLE\",\"FAQ_DELETE\",\"FAQ_EDIT\",\"QUESTION_CREATE\",\"QUESTION_EDIT\",\"QUESTION_MOVE\",\"QUESTION_DELETE\",\"QUESTION_ENABLE\"]',
        1, '', 0, '2025-05-13 10:45:16', '', '2025-05-13 10:45:16', ''),
       (2, '可编辑', 3,
        '[\"RESOURCE_PREVIEW\",\"TABLE_PREVIEW\",\"FAQ_PREVIEW\",\"QUESTION_PREVIEW\",\"LIBRARY_ADD_GROUP\",\"RESOURCE_ENABLE\",\"RESOURCE_COPY\",\"RESOURCE_RENAME\",\"RESOURCE_IMPORT\",\"RESOURCE_EDIT\",\"RESOURCE_REDO\",\"RESOURCE_SAVE_PART\",\"RESOURCE_ARCHIVE\",\"RESOURCE_MOVE\",\"RESOURCE_DOWNLOAD\",\"RESOURCE_DELETE\",\"RESOURCE_MAINTENANCE\",\"GROUP_RENAME\",\"GROUP_DELETE\",\"GROUP_ADD_SUB\",\"GROUP_MOVE\",\"TABLE_IMPORT\",\"TABLE_ENABLE\",\"TABLE_MOVE\",\"FAQ_CREATE\",\"FAQ_ENABLE\",\"FAQ_MOVE\",\"QUESTION_ENABLE\",\"QUESTION_CREATE\",\"QUESTION_EDIT\",\"FAQ_EDIT\",\"TABLE_REDO\",\"TABLE_EDIT\",\"TABLE_DOWNLOAD\",\"TABLE_EDIT_SEG\",\"TABLE_DELETE\",\"FAQ_DELETE\",\"FAQ_EXPORT\",\"QUESTION_DELETE\",\"QUESTION_MOVE\"]',
        1, '', 0, '2025-05-13 10:45:31', '', '2025-05-13 10:45:31', ''),
       (3, '可查看/下载', 2,
        '[\"RESOURCE_PREVIEW\",\"TABLE_PREVIEW\",\"FAQ_PREVIEW\",\"QUESTION_PREVIEW\",\"RESOURCE_DOWNLOAD\",\"TABLE_DOWNLOAD\",\"FAQ_EXPORT\"]',
        1, '', 0, '2025-05-13 10:45:42', '', '2025-05-13 10:45:42', ''),
       (4, '可查看', 1, '[\"RESOURCE_PREVIEW\",\"TABLE_PREVIEW\",\"FAQ_PREVIEW\",\"QUESTION_PREVIEW\"]',
        1, '', 0, '2025-05-13 10:45:54', '', '2025-05-13 10:45:54', '');