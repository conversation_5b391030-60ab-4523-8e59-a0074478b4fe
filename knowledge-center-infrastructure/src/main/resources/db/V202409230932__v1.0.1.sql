ALTER TABLE t_index_tenant_mapping
    CHANGE isTemplate is_template tinyint(1) default 0 not null comment '是否用模板 -> 0：否、1：是';
ALTER TABLE t_index_tenant_mapping
    CHANGE readAlias read_alias varchar (255) null comment '读别名';
ALTER TABLE t_index_tenant_mapping
    CHANGE writeAlias write_alias varchar (255) null comment '写别名';

create table t_knowledge_resource
(
    id            bigint auto_increment   primary key,
    create_time   datetime     not null comment '创建时间',
    creator_id    char(36) null,
    update_time   datetime null comment '更新时间',
    update_id     char(36) null,
    tenant_id     char(36)  not null comment '租户或组织的唯一标识符',
    deleted       tinyint(1) default 0 not null comment '逻辑删除',
    doc_id        char(36)  not null comment 'docId' unique,
    work_flow_id  char(36) null comment 'workFlowId',
    type          tinyint(2)           not null comment '知识类型 1-文档 2-图片 3-视频 4-音频 5-FAQ 6-表格',
    group_id      bigint       not null comment '组ID',
    title         varchar(255) not null comment '资源名称',
    suffix        varchar(20)  not null comment '文件后缀',
    description   varchar(3000) null comment '描述',
    url           varchar(255) not null comment '资源地址',
    preview_src   varchar(255) null comment '预览地址',
    size          bigint       not null comment '文件资源大小（byte）',
    count         bigint       not null comment '文档：页数；表格：行数',
    handle_status tinyint(2)                not null comment '1：学习中、2：学习成功、3：学习失败',
    enable        tinyint(1) default 1 not null comment '是否启用',
    extInfo       text null comment '扩展信息字段json'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='资源表';

create index idx_title
    on t_knowledge_resource (title);

create index idx_group_id
    on t_knowledge_resource (group_id);