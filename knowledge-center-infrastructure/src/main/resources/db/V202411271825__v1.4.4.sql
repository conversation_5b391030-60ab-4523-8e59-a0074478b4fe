alter table t_knowledge_group
    add is_library tinyint(1) default 0 null comment '是否是知识库 true-知识库 false-目录' after id;

alter table t_knowledge_group
    add description varchar(500) null comment '知识库描述' after is_library;

alter table t_knowledge_group
    add logo varchar(500) null comment '知识库logo' after description;

alter table t_knowledge_group
    add is_sync tinyint(1) default 0 null comment 'true-关联扫盘任务';

update t_knowledge_group set is_library = 1 where parent_id = 0;

CREATE TABLE `t_words_info` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                `tenant_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户或组织的唯一标识符',
                                `group_id` bigint(20) NOT NULL COMMENT '词库ID',
                                `group_type` tinyint(2) NOT NULL COMMENT '词库类型',
                                `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '词条名',
                                `import_source` tinyint(2) NOT NULL DEFAULT '0' COMMENT '导入源 0手动 1 EXCEL',
                                `state` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态 0无效 1生效',
                                `synonyms` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '同义词',
                                `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除',
                                `create_time` datetime NOT NULL COMMENT '创建时间',
                                `creator_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                `update_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                `delete_time` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `idx_tenant_group_name` (`tenant_id`,`group_id`,`name`,`delete_time`)
) ENGINE=InnoDB AUTO_INCREMENT=105 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='词组';

alter table t_knowledge_custom add  `tab_display` tinyint(1) NOT NULL DEFAULT '0' COMMENT '标签是否展示' after enable ;


