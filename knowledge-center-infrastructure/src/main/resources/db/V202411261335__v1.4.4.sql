create table t_auth(
	id bigint primary key auto_increment,
    source_id varchar(36) not null comment '分组/文件 id',
    parent_id bigint not null comment '父级目录id',
    source_type tinyint not null comment '1 分组  2文件',
    auth_id varchar(36) not null comment '用户/部门/租户 id',
    auth_type tinyint not null comment '1用户，2部门,3 租户',
    auth_level int not null comment '权限等级 0无权限 1仅查看 2可查看/下载 3可编辑 4可管理',
    tenant_id varchar(50) not null comment '租户id',
    deleted tinyint not null comment '是否删除',
    create_time datetime not null comment '创建时间',
    creator_id varchar(36) not null comment '创建人id',
    update_time datetime not null comment '更新时间',
    update_id varchar(36) not null comment '更新人id'
);