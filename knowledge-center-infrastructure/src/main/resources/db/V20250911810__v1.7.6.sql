create table t_index_auth
(
    id                  bigint auto_increment
        primary key,
    create_time         datetime             not null comment '创建时间',
    creator_id          char(36)             null,
    update_time         datetime             null comment '更新时间',
    update_id           char(36)             null,
    tenant_id           char(36)             not null comment '租户或组织的唯一标识符',
    index_time_schedule_open         tinyint(2)           default 0 comment '索引是否开启定时任务 1-开启 0-关闭',
    index_time_config        varchar(255)         null comment '索引定时任务配置'
)
    comment '索引定时配置' collate = utf8mb4_unicode_ci;
    