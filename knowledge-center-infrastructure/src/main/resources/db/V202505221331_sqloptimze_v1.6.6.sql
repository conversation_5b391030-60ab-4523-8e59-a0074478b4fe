-- sql优化脚本


ALTER TABLE `t_resource_asr`
    ADD INDEX `idx_doc_id` (`doc_id` ASC);

ALTER TABLE `t_resource_asr`
    ADD INDEX `idx_segment_id` (`segment_id` ASC);

ALTER TABLE `t_resource_face`
    ADD INDEX `idx_doc_id` (`doc_id` ASC);

ALTER TABLE `t_resource_face`
    ADD INDEX `idx_segment_id` (`segment_id` ASC);

ALTER TABLE `t_resource_od_tag`
    ADD INDEX `idx_segment_id` (`segment_id` ASC);

ALTER TABLE `t_resource_terminology`
    ADD INDEX `idx_doc_id` (`doc_id` ASC);

ALTER TABLE `t_resource_terminology`
    ADD INDEX `idx_segment_id` (`segment_id` ASC);

ALTER TABLE `t_knowledge_group`
    ADD INDEX `idx_tenantId_creatorId_type_visibleType_deleted` (`tenant_id` asc,`creator_id` asc,`type` asc,`visible_type` asc ,`deleted` asc);

ALTER TABLE `t_knowledge_group`
    ADD INDEX `idx_tenantId_type_visibleType_deleted` (`tenant_id` asc,`type` asc,`visible_type` asc ,`deleted` asc);

ALTER TABLE `t_knowledge_group`
    ADD INDEX `idx_path` (`path` ASC);

ALTER TABLE `t_resource_segment`
    ADD INDEX `index_doc_id` (`doc_id` ASC);

ALTER TABLE `t_resource_segment`
    ADD UNIQUE KEY `uk_segment_id` (`segment_id`) USING BTREE;