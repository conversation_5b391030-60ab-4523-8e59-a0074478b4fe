DELETE FROM t_index_tenant_mapping where type = 6;

INSERT INTO t_migration_task (type, content_id, success, message, create_time, update_time)
SELECT
    'table_replace_row' AS type,
    id AS content_id,
    0 AS success,
    NULL AS message,
    NOW() AS create_time,
    NULL AS update_time
FROM t_knowledge_resource WHERE type = 6 and deleted = 0;

#
# INSERT INTO t_migration_task (type, content_id, success, message, create_time, update_time)
# SELECT
#     'doc_add_group_id' AS type,
#     id AS content_id,
#     0 AS success,
#     NULL AS message,
#     NOW() AS create_time,
#     NULL AS update_time
# FROM t_knowledge_resource WHERE type = 1 and deleted = 0;