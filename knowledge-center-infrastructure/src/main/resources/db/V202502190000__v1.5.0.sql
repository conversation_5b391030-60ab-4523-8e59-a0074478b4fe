alter table t_analysis_strategy
    change setup_json strategy_doc text null comment '文档切分策略';

alter table t_analysis_strategy
    add strategy_image text null comment '图片切分策略' after strategy_doc;

alter table t_analysis_strategy
    add strategy_video text null comment '视频切分策略' after strategy_image;

alter table t_analysis_strategy
    add strategy_audio text null comment '语音切分策略' after strategy_video;

INSERT INTO t_migration_task (type, content_id, success, message, tenant_id, create_time, update_time)
SELECT 'DOC_SEGMENT_TO_DB' AS type,
       id                  AS content_id,
       0                   AS success,
       NULL                AS message,
       tenant_id           AS tenant_id,
       NOW()               AS create_time,
       NULL                AS update_time
FROM t_knowledge_resource
WHERE type = 1
  and deleted = 0;

CREATE TABLE `t_resource_segment`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `segment_id`      char(36) NOT NULL COMMENT '分段id',
    `file_type`       tinyint(2) NOT NULL COMMENT '知识类型 1-文档 2-图片 3-视频 4-音频',
    `doc_id`          char(36) NOT NULL COMMENT '资源id',
    `title`           text     DEFAULT NULL COMMENT '标题',
    `content`         longtext COMMENT '分段内容',
    `sort`            double   DEFAULT NULL COMMENT '排序',
    `number`          int(11) DEFAULT NULL COMMENT '用户设置排序',
    `page`            int(11) DEFAULT NULL COMMENT '起始页码（文档类型）',
    `position`        text COMMENT '位置（文档类型）',
    `start_timestamp` bigint(20) DEFAULT NULL COMMENT '开始时间 单位毫秒 （音视频）',
    `end_timestamp`   bigint(20) DEFAULT NULL COMMENT '结束时间 单位毫秒 （音视频）',
    `status`          tinyint(2) NOT NULL COMMENT '学习状态',
    `create_time`     datetime NOT NULL COMMENT '创建时间',
    `creator_id`      char(36) DEFAULT NULL COMMENT '创建人',
    `update_time`     datetime DEFAULT NULL COMMENT '修改时间',
    `update_id`       char(36) DEFAULT NULL COMMENT '修改人',
    `tenant_id`       char(36) NOT NULL COMMENT '租户或组织的唯一标识符',
    `deleted`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='分段信息表';

CREATE TABLE `t_resource_face`
(
    `id`         BIGINT   NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `source_id`  char(36) NOT NULL COMMENT '人脸库id',
    `segment_id` char(36) NOT NULL COMMENT '分段Id',
    `doc_id`     char(36) NOT NULL COMMENT '资源id',
    `frame_id`   char(36)     DEFAULT NULL COMMENT '抽帧id/镜头Id',
    `name`       varchar(128) DEFAULT NULL COMMENT '人脸名称',
    `head_url`   text         DEFAULT NULL COMMENT '头像地址',
    `position`   varchar(255) DEFAULT NULL COMMENT '位置',
    `time_point` bigint       DEFAULT NULL COMMENT '帧时间 单位毫秒',
    create_time  datetime NOT NULL COMMENT '创建时间',
    creator_id   char(36) NULL COMMENT '创建人',
    update_time  datetime NULL COMMENT '修改时间',
    update_id    char(36) NULL COMMENT '修改人',
    tenant_id    char(36) NOT NULL COMMENT '租户或组织的唯一标识符',
    deleted      tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='人脸信息表';

CREATE TABLE `t_resource_od_tag`
(
    `id`         BIGINT   NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `segment_id` char(36) NOT NULL COMMENT '分段Id',
    `doc_id`     char(36) NOT NULL COMMENT '资源id',
    `frame_id`   char(36)     DEFAULT NULL COMMENT '抽帧id',
    `name`       varchar(128) DEFAULT NULL COMMENT '名称',
    `position`   varchar(255) DEFAULT NULL COMMENT '标签在图片中的坐标',
    `time_point` bigint       DEFAULT NULL COMMENT '帧时间 单位毫秒',
    create_time  datetime NOT NULL COMMENT '创建时间',
    creator_id   char(36) NULL COMMENT '创建人',
    update_time  datetime NULL COMMENT '修改时间',
    update_id    char(36) NULL COMMENT '修改人',
    tenant_id    char(36) NOT NULL COMMENT '租户或组织的唯一标识符',
    deleted      tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='实体标签信息表';

CREATE TABLE `t_resource_terminology`
(
    `id`         BIGINT      NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `segment_id` char(36)    NOT NULL COMMENT '分段Id',
    `doc_id`     char(36)    NOT NULL COMMENT '资源id',
    `name`       varchar(128) DEFAULT NULL COMMENT '名称',
    `type`       varchar(20) NOT NULL COMMENT '标签类型',
    `position`   varchar(255) DEFAULT NULL COMMENT '标签在图片中的坐标',
    `time_point` bigint       DEFAULT NULL COMMENT '帧时间 单位毫秒',
    create_time  datetime    NOT NULL COMMENT '创建时间',
    creator_id   char(36) NULL COMMENT '创建人',
    update_time  datetime NULL COMMENT '修改时间',
    update_id    char(36) NULL COMMENT '修改人',
    tenant_id    char(36)    NOT NULL COMMENT '租户或组织的唯一标识符',
    deleted      tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='专名标签信息表';

CREATE TABLE `t_resource_asr`
(
    `id`              BIGINT   NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `segment_id`      char(36) NOT NULL COMMENT '分段Id',
    `doc_id`          char(36) NOT NULL COMMENT '资源id',
    `text`            varchar(128)        DEFAULT NULL COMMENT '语音信息',
    `start_timestamp` bigint              DEFAULT NULL COMMENT '开始时间 单位毫秒 （音视频）',
    `end_timestamp`   bigint              DEFAULT NULL COMMENT '结束时间 单位毫秒 （音视频）',
    `speakers`        varchar(128)        DEFAULT NULL COMMENT '发言人数组',
    `ext`             text                DEFAULT NULL COMMENT '附件数据 json 字符串 ',
    create_time       datetime NOT NULL COMMENT '创建时间',
    creator_id        char(36) NULL COMMENT '创建人',
    update_time       datetime NULL COMMENT '修改时间',
    update_id         char(36) NULL COMMENT '修改人',
    tenant_id         char(36) NOT NULL COMMENT '租户或组织的唯一标识符',
    deleted           tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='语音信息表';

create table sys_label
(
    `id`   BIGINT primary key NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `name` varchar(200)       not null comment '名词'
) comment '系统标签';
INSERT INTO sys_label (id, name) VALUES (1, '玻璃窗');
INSERT INTO sys_label (id, name) VALUES (2, '椅子');
INSERT INTO sys_label (id, name) VALUES (3, '桌子');
INSERT INTO sys_label (id, name) VALUES (4, '摄像头');
INSERT INTO sys_label (id, name) VALUES (5, '录音设备');
INSERT INTO sys_label (id, name) VALUES (6, '录像机');
INSERT INTO sys_label (id, name) VALUES (7, '手铐');
INSERT INTO sys_label (id, name) VALUES (8, '报警按钮');
INSERT INTO sys_label (id, name) VALUES (9, '灯具');
INSERT INTO sys_label (id, name) VALUES (10, '通风口');
INSERT INTO sys_label (id, name) VALUES (11, '插座');
INSERT INTO sys_label (id, name) VALUES (12, '杯子');
INSERT INTO sys_label (id, name) VALUES (13, '纸质文件');
INSERT INTO sys_label (id, name) VALUES (14, '电脑');
INSERT INTO sys_label (id, name) VALUES (15, '文件柜');
INSERT INTO sys_label (id, name) VALUES (16, '打印机');
INSERT INTO sys_label (id, name) VALUES (17, '时钟');
INSERT INTO sys_label (id, name) VALUES (18, '白板');
INSERT INTO sys_label (id, name) VALUES (19, '饮水机');
INSERT INTO sys_label (id, name) VALUES (20, '空调');
INSERT INTO sys_label (id, name) VALUES (21, '灭火器');
INSERT INTO sys_label (id, name) VALUES (22, '急救箱');
INSERT INTO sys_label (id, name) VALUES (23, '话筒');
INSERT INTO sys_label (id, name) VALUES (24, '档案袋');
INSERT INTO sys_label (id, name) VALUES (25, '耳机');
INSERT INTO sys_label (id, name) VALUES (26, '笔');
INSERT INTO sys_label (id, name) VALUES (27, '门牌');
INSERT INTO sys_label (id, name) VALUES (28, '垃圾桶');
INSERT INTO sys_label (id, name) VALUES (29, '黑板擦');
INSERT INTO sys_label (id, name) VALUES (30, '投影仪');
INSERT INTO sys_label (id, name) VALUES (31, '茶几');
INSERT INTO sys_label (id, name) VALUES (32, '窗帘');
INSERT INTO sys_label (id, name) VALUES (33, '手表');
INSERT INTO sys_label (id, name) VALUES (34, '口罩');
INSERT INTO sys_label (id, name) VALUES (35, '纸巾盒');
INSERT INTO sys_label (id, name) VALUES (36, '帽子');
INSERT INTO sys_label (id, name) VALUES (37, '汽车');
INSERT INTO sys_label (id, name) VALUES (38, '自行车');
INSERT INTO sys_label (id, name) VALUES (39, '风筝');
INSERT INTO sys_label (id, name) VALUES (40, '书架');
INSERT INTO sys_label (id, name) VALUES (41, '茶几');
INSERT INTO sys_label (id, name) VALUES (42, '地毯');
INSERT INTO sys_label (id, name) VALUES (43, '窗帘');
INSERT INTO sys_label (id, name) VALUES (44, '风筝');
INSERT INTO sys_label (id, name) VALUES (45, '眼镜');
INSERT INTO sys_label (id, name) VALUES (46, '书籍');
INSERT INTO sys_label (id, name) VALUES (47, '电视');
INSERT INTO sys_label (id, name) VALUES (48, '沙发');
INSERT INTO sys_label (id, name) VALUES (49, '手机');
INSERT INTO sys_label (id, name) VALUES (50, '水瓶');
