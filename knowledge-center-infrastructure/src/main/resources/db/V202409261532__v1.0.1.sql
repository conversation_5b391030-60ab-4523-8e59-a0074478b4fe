create table if not exists t_knowledge_custom
(
    id           bigint auto_increment primary key,
    manage_type  int                  NOT NULL COMMENT '知识类型 1-文档 2-图片 3-视频 4-音频',
    field_type   int                  NOT NULL COMMENT '字段类型 0-字符串 1-数字 2-时间 3-标签 4-单选 5-多选 6-附件',
    name         varchar(255)          NOT NULL COMMENT '字段名称',
    field        varchar(255)         NOT NULL COMMENT '字段ID',
    options      text                 NULL COMMENT '多选信息',
    defaultValue varchar(255)         NULL COMMENT '默认值',
    required     tinyint(1)           NOT NULL COMMENT '是否必填',
    sort         int                  NOT NULL COMMENT '排序',
    enable       tinyint(1)           NOT NULL COMMENT '是否生效 -> 0：否、1：是',
    create_time  datetime             NOT NULL COMMENT '创建时间',
    creator_id   char(36)          NOT NULL COMMENT '创建人',
    update_time  datetime             null COMMENT '修改时间',
    update_id    char(36)          NULL COMMENT '修改人',
    tenant_id    char(36)          NOT NULL COMMENT '租户或组织的唯一标识符',
    deleted      tinyint(1) default 0 NOT NULL COMMENT '逻辑删除'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT '扩展字段表';
