package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceVisibleTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IKnowledgeResourceManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.KnowledgeResourceMapper;
import com.linker.fusion.knowledgecenter.infrastructure.model.ResourceStaModel;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class KnowledgeResourceImpl extends ServiceImpl<KnowledgeResourceMapper, KnowledgeResourceEntity> implements IKnowledgeResourceManager {

    @Override
    public KnowledgeResourceEntity getByResId(String docId) {
        List<KnowledgeResourceEntity> list = lambdaQuery()
                .eq(KnowledgeResourceEntity::getDeleted, 0)
                .eq(KnowledgeResourceEntity::getDocId, docId)
                .list();
        if (CollectionUtils.isNotEmpty(list))
            return list.get(0);
        return null;
    }

    public Boolean isExistTitle(String title, String tenantId, String creatorId) {
        return lambdaQuery()
                .eq(KnowledgeResourceEntity::getDeleted, 0)
                .eq(StringUtils.isNotBlank(tenantId), KnowledgeResourceEntity::getTenantId, tenantId)
                .eq(StringUtils.isNotBlank(creatorId), KnowledgeResourceEntity::getCreatorId, creatorId)
                .eq(KnowledgeResourceEntity::getTitle, title.trim()).count() > 0;
    }

    public Boolean isExistTitle(Long id, String title, String tenantId, String creatorId) {
        return lambdaQuery()
                .ne(id != null && id > 0, KnowledgeResourceEntity::getId, id)
                .eq(KnowledgeResourceEntity::getDeleted, 0)
                .eq(StringUtils.isNotBlank(creatorId), KnowledgeResourceEntity::getCreatorId, creatorId)
                .eq(StringUtils.isNotBlank(tenantId), KnowledgeResourceEntity::getTenantId, tenantId)
                .eq(KnowledgeResourceEntity::getTitle, title.trim()).count() > 0;
    }

    public List<String> getAllUserId() {
        LambdaQueryWrapper<KnowledgeResourceEntity> queryWrapper = new LambdaQueryWrapper<KnowledgeResourceEntity>()
                .select(KnowledgeResourceEntity::getCreatorId)
                .isNotNull(KnowledgeResourceEntity::getCreatorId)
                .groupBy(KnowledgeResourceEntity::getCreatorId);
        return list(queryWrapper)
                .stream()
                .map(KnowledgeResourceEntity::getCreatorId)
                .collect(Collectors.toList());
    }


    public List<KnowledgeResourceEntity> getListByIds(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            return lambdaQuery().in(KnowledgeResourceEntity::getId, ids).list();
        }
        return new ArrayList<>();
    }

    public List<KnowledgeResourceEntity> listByResIds(List<String> resIds) {
        if (CollectionUtils.isNotEmpty(resIds)) {
            return lambdaQuery().in(KnowledgeResourceEntity::getDocId, resIds).list();
        }
        return new ArrayList<>();
    }

    @Override
    public List<KnowledgeResourceEntity> listByTemplateId(Integer limit, List<Long> templateIds, Long lastId) {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(KnowledgeResourceEntity::getTemplateId, templateIds)
                .eq(KnowledgeResourceEntity::getDeleted, false)
                .eq(KnowledgeResourceEntity::getHandleStatus, ProcessEnum.Success.getValue())
                .gt(KnowledgeResourceEntity::getId, lastId)
                .orderByAsc(KnowledgeResourceEntity::getId)
                .last(" limit " + limit);
        return list(wrapper);
    }

    @Override
    public List<String> listDocIds(String tenantId, KnowledgeTypeEnum type, List<Long> groupIds, String groupPath) {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getType, type.getType())
                .eq(KnowledgeResourceEntity::getTenantId, tenantId)
                .eq(KnowledgeResourceEntity::getDeleted, false)
                .eq(KnowledgeResourceEntity::getEnable, true)
                .eq(KnowledgeResourceEntity::getHandleStatus, 2);
        if (CollectionUtils.isNotEmpty(groupIds)) {
            wrapper.lambda().in(KnowledgeResourceEntity::getGroupId, groupIds);
        }
        if (StringUtils.isNotBlank(groupPath))
            wrapper.lambda().likeRight(KnowledgeResourceEntity::getGroupPath, groupPath);
        return list(wrapper.lambda().select(KnowledgeResourceEntity::getDocId)).stream().map(x -> x.getDocId()).collect(Collectors.toList());
    }

    @Override
    public void updateGroupId(List<Long> ids, Long groupId, String groupPath) {
        UpdateWrapper<KnowledgeResourceEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(KnowledgeResourceEntity::getId, ids)
                .set(KnowledgeResourceEntity::getGroupId, groupId)
                .set(KnowledgeResourceEntity::getGroupPath, groupPath);
        update(wrapper);
    }

    @Override
    public void updateCount(String docId, Integer count) {
        UpdateWrapper<KnowledgeResourceEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getDocId, docId)
                .set(KnowledgeResourceEntity::getCount, count);
        update(wrapper);
    }

    @Override
    public void updateExtInfo(String docId, String extInfo) {
        UpdateWrapper<KnowledgeResourceEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getDocId, docId);
        wrapper.lambda().set(KnowledgeResourceEntity::getExtInfo, extInfo);
        wrapper.lambda().set(KnowledgeResourceEntity::getUpdateTime, LocalDateTime.now());
        update(wrapper);
    }

    @Override
    public void updateTitle(Long id, String title) {
        UpdateWrapper<KnowledgeResourceEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getId, id);
        wrapper.lambda().set(KnowledgeResourceEntity::getTitle, title);
        wrapper.lambda().set(KnowledgeResourceEntity::getUpdateTime, LocalDateTime.now());
        update(wrapper);
    }

    @Override
    public void updateStatus(List<Long> ids, ProcessEnum status) {
        lambdaUpdate()
                .set(KnowledgeResourceEntity::getHandleStatus, status.getValue())
                .set(KnowledgeResourceEntity::getUpdateTime, LocalDateTime.now())
                .in(KnowledgeResourceEntity::getId, ids)
                .update();
    }

    @Override
    public Long getCount(String tenantId) {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getTenantId, tenantId)
                .eq(KnowledgeResourceEntity::getDeleted, false)
                .ne(KnowledgeResourceEntity::getGroupId, -1);
        return count(wrapper);
    }

    @Override
    public Long getCount(Long groupId) {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getGroupId, groupId)
                .eq(KnowledgeResourceEntity::getDeleted, false);
        return count(wrapper);
    }


    @Override
    public Long getTempCount(String tenantId) {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getTenantId, tenantId)
                .eq(KnowledgeResourceEntity::getDeleted, false)
                .eq(KnowledgeResourceEntity::getGroupId, -1);
        return count(wrapper);
    }

    @Override
    public List<KnowledgeResourceEntity> listByGroupId(String tenantId, String userCode, int groupId) {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getTenantId, tenantId)
                .eq(StringUtils.isNotBlank(userCode), KnowledgeResourceEntity::getCreatorId, userCode)
                .eq(KnowledgeResourceEntity::getGroupId, groupId);
        return list(wrapper);
    }

    @Override
    public List<KnowledgeResourceEntity> listByGroupIds(List<Long> groupIds) {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(KnowledgeResourceEntity::getDeleted, false)
                .in(CollectionUtils.isNotEmpty(groupIds), KnowledgeResourceEntity::getGroupId, groupIds);
        return list(wrapper);
    }

    @Override
    public Long getCount(String tenantId, List<FileTypeEnum> fileTypeList, List<ProcessEnum> statusList, LocalDateTime startTime) {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getTenantId, tenantId)
                .eq(KnowledgeResourceEntity::getDeleted, false);
        if (CollectionUtils.isNotEmpty(fileTypeList))
            wrapper.lambda().in(KnowledgeResourceEntity::getType, fileTypeList.stream().map(s -> s.getType()).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(statusList))
            wrapper.lambda().in(KnowledgeResourceEntity::getHandleStatus, statusList.stream().map(s -> s.getValue()).collect(Collectors.toList()));
        if (Objects.nonNull(startTime))
            wrapper.lambda().gt(KnowledgeResourceEntity::getUpdateTime, startTime);
        return count(wrapper);
    }

    @Override
    public Long sumSize(String tenantId, FileTypeEnum fileType) {
        return baseMapper.sumSize(tenantId, fileType.getType());
    }

    @Override
    public List<ResourceStaModel> staByLib(List<String> pathList, Integer fileType) {
        return baseMapper.staByLib(pathList, fileType);
    }

    @Override
    public void logicalDelete(List<Long> ids) {
        lambdaUpdate()
                .set(KnowledgeResourceEntity::getDeleted, true)
                .set(KnowledgeResourceEntity::getUpdateTime, LocalDateTime.now())
                .in(KnowledgeResourceEntity::getId, ids).update();
    }

    @Override
    public void logicalDeleteByResIds(List<String> resIds) {
        lambdaUpdate()
                .set(KnowledgeResourceEntity::getDeleted, true)
                .set(KnowledgeResourceEntity::getUpdateTime, LocalDateTime.now())
                .in(KnowledgeResourceEntity::getDocId, resIds).update();
    }

    @Override
    public void updateSize(String docId, Long size) {
        UpdateWrapper<KnowledgeResourceEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getDocId, docId);
        wrapper.lambda().set(KnowledgeResourceEntity::getSize, size);
        update(wrapper);
    }

    @Override
    public void updateGroupIdByDocIds(List<String> resIds, Long groupId) {
        UpdateWrapper<KnowledgeResourceEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(KnowledgeResourceEntity::getDocId, resIds);
        wrapper.lambda().set(KnowledgeResourceEntity::getUpdateTime, LocalDateTime.now());
        wrapper.lambda().set(KnowledgeResourceEntity::getGroupId, groupId);
        update(wrapper);
    }

    @Override
    public void updateEnable(List<Long> ids, Boolean enable) {
        LocalDateTime now = LocalDateTime.now();
        lambdaUpdate()
                .set(KnowledgeResourceEntity::getEnable, enable)
                .set(KnowledgeResourceEntity::getUpdateTime, now)
                .in(KnowledgeResourceEntity::getId, ids)
                .update();
    }

    @Override
    public KnowledgeResourceEntity getByGroupId_Title_Suffix(Long groupId, String title, String suffix, Boolean delete) {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(delete != null, KnowledgeResourceEntity::getDeleted, delete)
                .eq(KnowledgeResourceEntity::getGroupId, groupId)
                .eq(KnowledgeResourceEntity::getTitle, title)
                .eq(Objects.nonNull(suffix), KnowledgeResourceEntity::getSuffix, suffix);
        List<KnowledgeResourceEntity> list = list(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public void updateStrategy(String docId, String strategy) {
        UpdateWrapper<KnowledgeResourceEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getDocId, docId);
        wrapper.lambda().set(KnowledgeResourceEntity::getStrategy, strategy);
        update(wrapper);
    }

    @Override
    public void updateByWorkflowId(String workflowId, ProcessEnum status) {
        UpdateWrapper<KnowledgeResourceEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getWorkflowId, workflowId);
        wrapper.lambda().set(KnowledgeResourceEntity::getHandleStatus, status.getValue());
        update(wrapper);
    }

    /**
     * @description: 示例库文件查询
     * @author: hejianbao
     * @date: 2025/7/25 13:57
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    @Override
    public List<KnowledgeResourceEntity> listSampleResource(Long sampleGroupId, Integer type, Integer handleStatus) {
        LambdaQueryWrapper<KnowledgeResourceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(sampleGroupId), KnowledgeResourceEntity::getGroupId, sampleGroupId)
                .eq(ObjectUtil.isNotEmpty(type), KnowledgeResourceEntity::getType, type)
                .eq(ObjectUtil.isNotEmpty(handleStatus), KnowledgeResourceEntity::getHandleStatus, handleStatus)
                .eq(KnowledgeResourceEntity::getEnable, true)
                .eq(KnowledgeResourceEntity::getDeleted, false)
                .orderByAsc(KnowledgeResourceEntity::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public KnowledgeResourceEntity getTestImage() {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getEnable, true)
                .eq(KnowledgeResourceEntity::getType, FileTypeEnum.IMAGE.getType())
                .eq(KnowledgeResourceEntity::getDeleted, false)
                .orderByAsc(KnowledgeResourceEntity::getCreateTime);
        return this.list(wrapper).get(0);
    }

    @Override
    public List<KnowledgeResourceEntity> getFileFromTimeRange(ArrayList<Integer> status, LocalDateTime startTime, LocalDateTime endTime) {

        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(KnowledgeResourceEntity::getHandleStatus, status)
                .gt(KnowledgeResourceEntity::getUpdateTime, startTime)
                .lt(KnowledgeResourceEntity::getUpdateTime, endTime);
        return list(wrapper);
    }

    @Override
    public Long sumSizeByVisibleType(String tenantId, String userCode, ResourceVisibleTypeEnum resourceVisibleTypeEnum) {
        return baseMapper.sumSizeByVisibleType(tenantId,userCode,resourceVisibleTypeEnum.getValue());
    }

    @Override
    public List<KnowledgeResourceEntity> listByGroupIdsAndFileType(List<Long> groupIds, List<Integer> types, Integer status) {
        return list(new LambdaQueryWrapper<>(KnowledgeResourceEntity.class)
                .in(KnowledgeResourceEntity::getType, types)
                .eq(KnowledgeResourceEntity::getHandleStatus, status)
                .in(KnowledgeResourceEntity::getGroupId, groupIds));
    }

    @Override
    public Long sumSizeByGroupId(List<Long> groupIds) {
        return baseMapper.sumSizeByGroupId(groupIds);
    }

    @Override
    public List<KnowledgeResourceEntity> list(FileTypeEnum type, Long groupId) {
        return list(new LambdaQueryWrapper<>(KnowledgeResourceEntity.class)
                .eq(KnowledgeResourceEntity::getType, type.getType())
                .eq(KnowledgeResourceEntity::getGroupId, groupId));
    }

    @Override
    public List<KnowledgeResourceEntity> list(String tenantId) {
        return list(new LambdaQueryWrapper<>(KnowledgeResourceEntity.class)
                .eq(KnowledgeResourceEntity::getTenantId, tenantId)
                .eq(KnowledgeResourceEntity::getDeleted, false));
    }


    @Override
    public List<KnowledgeResourceEntity> listByGroupIds(String tenantId, List<Long> groupIds) {
        QueryWrapper<KnowledgeResourceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(KnowledgeResourceEntity::getTenantId, tenantId)
                .in(KnowledgeResourceEntity::getGroupId, groupIds);
        return list(wrapper);
    }

    @Override
    public List<KnowledgeResourceEntity> search(String tenantId, Integer type, String keyword, List<Long> groupIds) {
        QueryWrapper<KnowledgeResourceEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(KnowledgeResourceEntity::getDeleted, false)
                .eq(KnowledgeResourceEntity::getTenantId, tenantId)
                .in(KnowledgeResourceEntity::getType, type)
                .in(CollectionUtils.isNotEmpty(groupIds), KnowledgeResourceEntity::getGroupId, groupIds)
                .eq(KnowledgeResourceEntity::getEnable, true)
                .in(KnowledgeResourceEntity::getHandleStatus, Arrays.asList(ProcessEnum.ARCHIVE.getValue(), ProcessEnum.Success.getValue()))
                .like(ObjectUtil.isNotEmpty(keyword), KnowledgeResourceEntity::getTitle, StringComUtils.replaceSqlEsc(keyword));
        return list(queryWrapper);
    }

    public List<KnowledgeResourceEntity> top(String tenantId, Boolean enable, ProcessEnum status, FileTypeEnum fileType, Integer size) {
        QueryWrapper<KnowledgeResourceEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(StringUtils.isNotBlank(tenantId), KnowledgeResourceEntity::getTenantId, tenantId)
                .eq(KnowledgeResourceEntity::getDeleted, 0)
                .eq(Objects.nonNull(enable), KnowledgeResourceEntity::getEnable, enable)
                .eq(KnowledgeResourceEntity::getHandleStatus, status.getValue())
                .eq(KnowledgeResourceEntity::getType, fileType.getType())
                .orderByAsc(KnowledgeResourceEntity::getSize)
                .last("limit " + size);
        return list(queryWrapper);
    }

    @Override
    public void deleteByGroupIds(List<Long> groupIds) {
        this.update(new LambdaUpdateWrapper<KnowledgeResourceEntity>()
                .in(KnowledgeResourceEntity::getGroupId, groupIds)
                .set(KnowledgeResourceEntity::getDeleted, true));
    }


}