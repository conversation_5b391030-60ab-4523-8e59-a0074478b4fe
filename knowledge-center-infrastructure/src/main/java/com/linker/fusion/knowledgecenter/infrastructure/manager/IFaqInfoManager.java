package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FaqInfoEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FaqQuestionTypeEnum;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * FAQ表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
public interface IFaqInfoManager extends IService<FaqInfoEntity> {

    default long countByQuestion(String tenantId, List<String> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return 0;
        }
        return count(
                new LambdaQueryWrapper<>(FaqInfoEntity.class)
                        .eq(FaqInfoEntity::getTenantId, tenantId)
                        .eq(FaqInfoEntity::getDeleted, false)
                        .in(FaqInfoEntity::getQuestion, questions)
        );
    }

    default List<FaqInfoEntity> selectOneBySimilarQuestion(String tenantId, List<String> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<>(FaqInfoEntity.class)
                        .eq(FaqInfoEntity::getTenantId, tenantId)
                        .eq(FaqInfoEntity::getDeleted, false)
                        .eq(FaqInfoEntity::getType, FaqQuestionTypeEnum.SIMILAR.getValue())
                        .in(FaqInfoEntity::getQuestion, questions)
        );
    }

    default List<FaqInfoEntity> listByQuestion(String tenantId, Collection<String> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<>(FaqInfoEntity.class)
                        .eq(FaqInfoEntity::getTenantId, tenantId)
                        .eq(FaqInfoEntity::getDeleted, false)
                        .in(FaqInfoEntity::getQuestion, questions)
        );
    }

    default long countStandardByIds(String tenantId, Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return count(
                new LambdaQueryWrapper<>(FaqInfoEntity.class)
                        .eq(FaqInfoEntity::getTenantId, tenantId)
                        .eq(FaqInfoEntity::getDeleted, false)
                        .eq(FaqInfoEntity::getType, FaqQuestionTypeEnum.STANDARD.getValue())
                        .in(FaqInfoEntity::getId, ids)
        );
    }

    default List<FaqInfoEntity> listSimilarByStandardIds(List<Long> standardIds) {
        if (CollectionUtils.isEmpty(standardIds)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<>(FaqInfoEntity.class)
                        .eq(FaqInfoEntity::getDeleted, false)
                        .in(FaqInfoEntity::getStandardId, standardIds)
                        .orderByAsc(FaqInfoEntity::getId)
        );
    }

    default FaqInfoEntity getByUid(String tenantId, String uid) {
        return getOne(
                new LambdaQueryWrapper<>(FaqInfoEntity.class)
                        .eq(FaqInfoEntity::getTenantId, tenantId)
                        .eq(FaqInfoEntity::getUid, uid)
        );
    }

    List<FaqInfoEntity> selectByQuestionAndGroupIds(String tenantId, String question, List<Long> groupIds, Integer limit);

    List<FaqInfoEntity> getByIds(String tenantId, List<Long> ids);

    Long getCount(String tenantId);

    List<FaqInfoEntity> listByGroupIds(List<Long> groupIds, Integer type);

    List<FaqInfoEntity> getByUids(String tenantId, Set<String> uIds);
}
