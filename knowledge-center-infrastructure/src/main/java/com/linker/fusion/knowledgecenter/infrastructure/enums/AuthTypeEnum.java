package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum AuthTypeEnum {
    User(1,"用户"),
    Department(2, "部门"),
    Tenant(3, "租户");
    private final Integer value;
    private final String name;
    public Integer getValue() {
        return this.value;
    }
    public String getDesc() {
        return this.name;
    }
}
