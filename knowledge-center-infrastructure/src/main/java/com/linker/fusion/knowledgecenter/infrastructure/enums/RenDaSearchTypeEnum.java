package com.linker.fusion.knowledgecenter.infrastructure.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RenDaSearchTypeEnum {

    TITLE(1, "会议"),
    PERSON(2, "人员"),

    // 视频-ASR 文档-文本内容
    CONTENT(3, "内容");

    private final Integer value;

    private final String name;

    public static RenDaSearchTypeEnum valueOf(Integer value) {
        return ArrayUtil.firstMatch(s -> s.getValue().equals(value), RenDaSearchTypeEnum.values());
    }
}
