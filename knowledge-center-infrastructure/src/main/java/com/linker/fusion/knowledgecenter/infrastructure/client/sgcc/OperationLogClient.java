package com.linker.fusion.knowledgecenter.infrastructure.client.sgcc;


import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.linker.core.base.baseclass.BaseResp;
import org.springframework.stereotype.Service;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * 第三方日志接口
 *
 * <AUTHOR>
 */
@Service
@RetrofitClient(baseUrl = "${sgcc.operateLog.baseUrl:http://123.com}")
public interface OperationLogClient {

    /**
     * 新增系统事件
     */
    @POST("audit/sysLog/add")
    BaseResp sysLogAdd(@Body ThirdAuditLogAddReq req);

    /**
     * 新增业务事件
     */
    @POST("audit/bizLog/add")
    BaseResp bizLogAdd(@Body ThirdAuditLogAddReq req);
}
