package com.linker.fusion.knowledgecenter.infrastructure.client.pdfParse;

import lombok.Data;

import java.util.List;

@Data
public class PdfParseResultBboxListItem {
    /**
     * #正文: text,
     * #标题: title,
     * #图片: figure,
     * #图片标题: figure_caption,
     * #表格: table,
     * #表格标题: table_caption,
     * #页眉: header,
     * #页脚: footer,
     * #注释: annotate,
     * #公式: equation,
     * #参考文献: reference,
     * #词汇解释: glossary,
     * #目录: catalog,
     * #封面: cover,
     * #附录: appendix
     */
    private List<String> label;
    /**
     * 坐标
     */
    private List<Float> bbox;
    /**
     * 坐标百分比
     */
    private List<Double> coordinates;
    /**
     * 尺寸
     */
    private List<Float> size;
    /**
     * 置信度
     */
    private List<Float> conf;
    /**
     * 状态
     */
    private Boolean status;
    /**
     * 标识
     */
    private Object track_id;
    private Object match_id;
    private Object match_conf;
    private Boolean stranger;
}
