package com.linker.fusion.knowledgecenter.infrastructure.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.linker.omagent.core.data.message.Content;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 对话链路中使用的工具类
 */
public class ChatCharacterUtils {

//    private static final String REGEX = "[0-9/_#]";
    /**
     * 过滤除去 字母/特殊字符串(/\.-) 之外的字符串
     */
    private static final String REGEX = "[^0-9a-zA-Z\\u4E00-\\u9FA5/\\\\\\.、\\-，。《》“”？！：；‘’{}\\[\\]\":,◆]+";

    private static final String ENGLISH_LETTERS_PATTERN = "[a-zA-Z]+";


    /**
     * 使用正则表达式过滤掉无效的字符串
     * todo yxx此处代码后续有时间抽取成工具类
     *
     * @param text
     * @return
     */
    public static String removeUnwantedCharacters(String text) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        // 正则表达式匹配所有数字、斜杠、下划线和井号
        // 将匹配到的字符替换为空字符串
        text = text.replaceAll(REGEX, "");
        if (text.matches(ENGLISH_LETTERS_PATTERN)) {
            return "";
        }
        text = text.replaceAll("◆", " ");
        return text;
    }


    public static String mergeContents(List<Content> contents) {
        StringBuilder res = new StringBuilder();
        if (CollectionUtil.isNotEmpty(contents)) {
            for (Content content : contents) {
                if (content instanceof com.linker.omagent.core.data.message.TextContent) {
                    String text = ((com.linker.omagent.core.data.message.TextContent) content).text();
                    if (StringUtils.isNotBlank(text)) {
                        res.append(text).append(" ");
                    }
                }
            }
        }
        return res.toString();
    }

    public static String extractBetweenTags(String input, String startTag, String endTag) {
        int startIndex = input.indexOf(startTag);
        if (startIndex == -1) {
            return input; // startTag not found
        }
        startIndex += startTag.length();

        int endIndex = input.indexOf(endTag, startIndex);
        if (endIndex == -1) {
            return input; // endTag not found
        }

        return input.substring(startIndex, endIndex);
    }

}
