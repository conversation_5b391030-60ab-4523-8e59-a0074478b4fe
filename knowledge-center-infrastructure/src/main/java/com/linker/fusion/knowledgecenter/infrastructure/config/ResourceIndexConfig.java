package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025年09月18日
 * @version: 1.0
 * @description: TODO
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "resource-index")
@RefreshScope
public class ResourceIndexConfig {

    /**
     * 索引任务拉取间隔，单位ms
     */
    private Long indexPullInterval = 1000L;

    /**
     * 索引任务批量拉取数量
     */
    private Integer indexPullBatchSize = 10;
}
