package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Getter
@Setter
@TableName("t_template")
public class TemplateEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模版名称
     */
    @TableField("name")
    private String name;

    /**
     * 模版描述
     */
    @TableField("description")
    private String description;

    /**
     * 模版来源 0-内置 1-自定义
     */
    @TableField("source")
    private Integer source;

    /**
     * 知识类型 1-文档 2-图片 3-视频 4-音频
     */
    @TableField("type")
    private Integer type;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 是否生效
     */
    @TableField("enable")
    private Boolean enable;

    @TableField("is_default")
    private Boolean isDefault;

    /**
     * 策略JSON字符串
     */
    @TableField("strategy")
    private String strategy;

    /**
     * 示例图片URL
     */
    @TableField("preview_url")
    private String previewUrl;

    public static Integer defaultSort() {
        return 99;
    }

}
