package com.linker.fusion.knowledgecenter.infrastructure.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * StringComUtils
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-03-18 10:43
 */
public class StringComUtils {

    /**
     * 处理 sql 转义
     *
     * @param str
     * @return String
     */
    public static String replaceSqlEsc(String str) {
        return ObjectUtils.isNotEmpty(str) ? str.trim().replace("_", "\\_").replace("%", "\\%") : "";
    }

    public static String joinUrl(String url) {
        if (StringUtils.isNotEmpty(url)
                && StringUtils.startsWith(url, "/minio/")
        ) {
            return url.replaceFirst("^/minio/", "http://minio.mid:9000/");
        }
        return url;
    }

    /**
     * 提取图片地址
     *
     * @param content
     * @return
     */
    public static String getImgUrl(String content) {
        String regex = "<img\\s+[^>]*src=['\"]([^'\"]+)['\"][^>]*>";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return "";
        }
    }

    /**
     * 提取多张图片
     *
     * @param content
     * @param imgList
     * @return
     */
    public static String getImgUrlsAndReplace(String content, List<String> imgList) {
        if (StringUtils.isBlank(content))
            return content;
        Pattern pattern = Pattern.compile("<img.*?src=['\"](.*?)['\"].*?[^>]*>");
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            imgList.add(matcher.group(1));
        }
        content = content.replaceAll("<img[^>]*>", "");
        return content;
    }

    public static List<String> getUrls(List<String> contents) {
        if (CollectionUtils.isEmpty(contents))
            return new ArrayList<>();
        List<String> ret = new ArrayList<>();
        contents.forEach(content -> {
            ret.addAll(getUrls(content));
        });
        return ret;
    }

    /**
     * 获取字符串中的所有图片和附件地址
     *
     * @param content
     * @return
     */
    public static List<String> getUrls(String content) {
        List<String> ret = new ArrayList<>();
        if (StringUtils.isBlank(content))
            return ret;
        Pattern pattern = Pattern.compile("<img.*?src=['\"](.*?)['\"].*?[^>]*>");
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            ret.add(matcher.group(1));
        }
        pattern = Pattern.compile("<a.*?href=['\"](.*?)['\"].*?[^>]*>");
        matcher = pattern.matcher(content);
        while (matcher.find()) {
            ret.add(matcher.group(1));
        }
        return ret.stream().distinct().collect(Collectors.toList());
    }

    public static String getTables(String content, List<String> tableList) {
        Pattern pattern = Pattern.compile("<table[^>]*>.*?</table>");
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            tableList.add(matcher.group(0));
        }
        content = content.replaceAll("<table[^>]*>.*?</table>", "");
        return content;
    }

    /**
     * 去除标签
     *
     * @param content
     * @param tags
     * @return
     */
    public static String removeTags(String content, List<String> tags) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        for (String tag : tags) {
            content = content.replaceAll("<" + tag + "[^>]*>", "")
                    .replaceAll("</" + tag + ">", "");
        }
        return content;

    }


    // 获取文件后缀名
    public static String getFileExtension(String fileName) {
        if (fileName == null) {
            return "";
        }
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex + 1);
    }

    public static Double convPercentage(Double val) {
        if (val == null) {
            val = 0.0;
        }
        return (val != null && val > 0) ? (val / 100f) : val;
    }

    public static Integer tryParse(String value) {
        try {
            return Integer.parseInt(value);
        } catch (Exception ex) {
        }
        return null;
    }

    public static LocalDateTime convertDate(String value) {
        return convertDate(value, null);
    }

    public static LocalDateTime convertDate(String value, LocalDateTime defaultValue) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            return LocalDateTime.parse(value, dateTimeFormatter);
        } catch (Exception ex) {

        }
        return defaultValue;
    }

    /**
     * 格式化秒
     * @param totalSeconds
     * @return
     */
    public static String formatSeconds(long totalSeconds) {
        long hours = totalSeconds / 3600;
        long minutes = (totalSeconds % 3600) / 60;
        long seconds = totalSeconds % 60;
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }

    /**
     * 将 yyyy-MM-dd HH:mm:ss 转换为时间戳（毫秒）
     *
     * @param value yyyy-MM-dd HH:mm:ss
     * @return 时间戳（毫秒）
     */
    public static long convertToTimestamp(String value) {
        LocalDateTime date = convertDate(value);

        if (date == null) {
            return 0L; // 或者你可以选择抛出异常或返回其他默认值
        }
        Instant instant = date.atZone(ZoneId.systemDefault()).toInstant();
        return instant.toEpochMilli();
    }

    /**
     * 拼接Url
     *
     * @param basePath
     * @param path
     * @return
     */

    public static String JoinUrl(String basePath, String path) {
        if (StringUtils.isBlank(basePath) && StringUtils.isBlank(path))
            return null;
        if (StringUtils.isBlank(basePath))
            return path;
        if (StringUtils.isBlank(path))
            return basePath;
        if (basePath.endsWith("/"))
            basePath = basePath.substring(0, basePath.length() - 1);
        if (!path.startsWith("/"))
            path = "/" + path;
        return basePath + path;
    }

    /**
     替换单引号
     */
    public static String replaceIbaseEscapeWords(String value) {
        if (value == null) return null;
        // 匹配单个单引号，前后都不是单引号
        return value.replaceAll("(?<!')'(?!')", "''");
    }

    public static void main(String[] args) {
        String a = "i''m O'K";
        System.out.println(replaceIbaseEscapeWords(a));
    }

    public static String getUrlSuffix(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }

        // 获取最后一个斜杠后的部分
        String fileName = url.substring(url.lastIndexOf('/') + 1);

        // 获取文件名中的最后一个点后的部分
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex == -1) {
            return null; // 没有后缀名
        }

        return fileName.substring(dotIndex + 1);
    }

    public static String truncateString(String str, int length) {
        if (str == null || str.length() <= length) {
            return str;
        }
        return str.substring(0, length);
    }

    public static String convertStr(LocalDateTime date) {
        return LocalDateTimeUtil.format(date, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static Long convertTimestamp(LocalDateTime date) {
        if (date == null) {
            return null;
        }
        return date.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static boolean isUrlFileCorrupted(String url, Integer minSize) {
        try {
            HttpResponse response = HttpUtil.createGet(url).execute();
            int statusCode = response.getStatus();
            if (statusCode == 200) {
                String contentLength = response.header("Content-Length");
                if (contentLength != null && !contentLength.isEmpty()) {
                    long fileSize = Long.parseLong(contentLength);
                    if (fileSize > minSize) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean isUrlFileCorruptedLeast(String url) {
        return isUrlFileCorrupted(url, 500);
    }

    /**
     * 图片的base64
     *
     * @param url
     * @return
     * @throws IOException
     */
    public static String imageBase64(String url) throws IOException {
        HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
        connection.setRequestMethod("GET");
        InputStream inputStream = connection.getInputStream();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }
        byte[] imageBytes = outputStream.toByteArray();
        inputStream.close();
        outputStream.close();
        return Base64.encode(imageBytes);
    }

    public static <T> List<List<T>> partitionList(List<T> list, int partitionSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += partitionSize) {
            partitions.add(list.subList(i, Math.min(i + partitionSize, list.size())));
        }
        return partitions;
    }

    /**
     * 将 BufferedImage 转换为字节数组
     *
     * @param image  BufferedImage 对象
     * @param format 图片格式（如 "jpg", "png" 等）
     * @return 字节数组
     * @throws IOException 如果转换过程中发生错误
     */
    public static byte[] convertBufferedImageToByteArray(BufferedImage image, String format) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ImageIO.write(image, format, byteArrayOutputStream);
        return byteArrayOutputStream.toByteArray();
    }

    public static String convertMarkdownToHtml(String markdownTable) {
        StringBuilder htmlTable = new StringBuilder();
        htmlTable.append("<table>\n");

        String[] lines = markdownTable.split("\n");
        boolean isHeader = true;

        for (String line : lines) {
            if (line.trim().isEmpty()) continue;

            if (line.contains("----")) {
                isHeader = false;
                continue;
            }

            String[] cells = line.split("\\|");
            htmlTable.append("  <tr>\n");

            for (int i = 1; i < cells.length; i++) {
                String cell = cells[i].trim();
                if (isHeader) {
                    htmlTable.append("    <th>").append(cell).append("</th>\n");
                } else {
                    htmlTable.append("    <td>").append(cell).append("</td>\n");
                }
            }

            htmlTable.append("  </tr>\n");
        }

        htmlTable.append("</table>");
        return htmlTable.toString();
    }

    public static String convertMillis(long millis) {
        if (millis < 1000) {
            return millis + "毫秒";
        }

        long seconds = (millis / 1000) % 60;
        long minutes = (millis / (1000 * 60)) % 60;
        long hours = (millis / (1000 * 60 * 60)) % 24;

        StringBuilder result = new StringBuilder();

        if (hours > 0) {
            result.append(hours).append("时");
        }
        if (minutes > 0) {
            result.append(minutes).append("分");
        }
        if (seconds > 0) {
            result.append(seconds).append("秒");
        }

        return result.toString();
    }

    /**
     * 转换文件大小
     * 支持单位：B,KB,MB,GB,TB
     * @param mySpace 100MB
     * @return
     */
    public static Long convertSize(String mySpace) {
        if(StringUtils.isBlank(mySpace)) {
            return 0L;
        }
        String size = mySpace.replaceAll("[^0-9.]", "");
        String unit = mySpace.replaceAll("[0-9.]", "");
        if(StringUtils.isBlank(unit)) {
            unit = "B";
        }
        double sizeDouble = Double.parseDouble(size);
        switch (unit) {
            case "B":
                return (long) sizeDouble;
            case "KB":
                return (long) (sizeDouble * 1024);
            case "M":
                return (long) (sizeDouble * 1024 * 1024);
            case "G":
                return (long) (sizeDouble * 1024 * 1024 * 1024);
            case "T":
                return (long) (sizeDouble * 1024 * 1024 * 1024 * 1024);
            default:
                return 0L;
        }
    }
}