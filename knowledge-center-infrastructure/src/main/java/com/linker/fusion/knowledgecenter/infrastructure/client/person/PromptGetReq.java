package com.linker.fusion.knowledgecenter.infrastructure.client.person;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description: 类说明
 * @Date: Created in 2025/7/18
 */
@Data
@Accessors(chain = true)
public class PromptGetReq {

    /**
     * 状态（0禁用/1启用）
     */
    private Integer status;

    /**
     * 提示词key
     */
    private List<String> promptKeyList;
}
