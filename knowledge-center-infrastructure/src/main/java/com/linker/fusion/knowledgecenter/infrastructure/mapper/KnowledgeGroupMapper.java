package com.linker.fusion.knowledgecenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.model.GroupExtModel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 知识分组表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
@Mapper
public interface KnowledgeGroupMapper extends BaseMapper<KnowledgeGroupEntity> {
    GroupExtModel getMaxAndMin(List<Long> parentIds);
}
