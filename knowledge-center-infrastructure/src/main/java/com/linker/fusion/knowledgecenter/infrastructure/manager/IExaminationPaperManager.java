package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ExaminationPaperEntity;

/**
 * <p>
 * 试卷表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface IExaminationPaperManager extends IService<ExaminationPaperEntity> {

    ExaminationPaperEntity getDetail(String tenantId, String paperUniqueId);

    ExaminationPaperEntity create(String tenantId, String userCode, String questionBaseIdsJson, String generateQuestionTypesJson,
                                  Integer generateQuestionNumber, Integer generateRealNumber, String paperContentJson);

    void updatePaper(String tenantId, String userCode, String paperAnswerContentJson, Integer paperStatus, String paperScoreRespJson, String paperUniqueId);

    void finalSubmit(String tenantId, String userCode, String paperUniqueId, String paperScoreRespJson, String paperAnswerInfo);
}
