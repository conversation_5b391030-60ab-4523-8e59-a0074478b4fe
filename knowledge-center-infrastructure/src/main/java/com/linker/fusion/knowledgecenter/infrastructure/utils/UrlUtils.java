package com.linker.fusion.knowledgecenter.infrastructure.utils;

import cn.hutool.core.util.URLUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.Charset;
import java.util.List;

/**
 * 该类的定义说明、使用范围
 *
 * <AUTHOR>
 * @version 对应产品版本号
 * @since 2022/9/24 10:18
 */
@Slf4j
public class UrlUtils {
    static List<String> escapeList = Lists.newArrayList(",", ";", "+", "'", "!", "@");

    public static String urlReEncode(String fileUrl) {
        if (fileUrl.contains("Signature")) {
            return fileUrl;
        }
        String decode = URLUtil.decode(fileUrl, Charset.defaultCharset(), false);
        log.info("解码后的url:{}", decode);
        String encodeFileUrl = URLUtil.encode(decode);
        log.info("编码后的url:{}", encodeFileUrl);

        boolean b = escapeList.stream().anyMatch(encodeFileUrl::contains);
        boolean b1 = escapeList.stream().anyMatch(fileUrl::contains);
        if (b && !b1) {
            log.info("编码后的url含有特殊字符，原路返回:{}", fileUrl);
            return fileUrl;
        }
        return encodeFileUrl;
    }

}