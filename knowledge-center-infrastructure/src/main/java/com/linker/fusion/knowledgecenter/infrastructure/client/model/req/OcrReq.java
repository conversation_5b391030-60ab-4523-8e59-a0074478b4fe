package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Data
public class OcrReq {

    private String model_id = "ppocr_v4_3090";

    private List<String> data;

    /**
     * base64 | url
     */
    private String src_type;

    private Double threshold = 0.1;

    private Double nms_threshold = 0.5;

    private List<String> tasks = Arrays.asList("");

    private List<String> include_classes = Arrays.asList("");

    private JSONObject kwargs = new JSONObject();

    private JSONObject class_threshold_map = new JSONObject();

//    private String model_type = "omocr_ocr";

    public OcrReq(List<String> data, String srcType, String model) {
        this.data = data;
        this.src_type = srcType;
        this.model_id = model;
    }

    public static OcrReq fromBase64(String base64, String model) {
        return new OcrReq(Collections.singletonList(base64), "base64", model);
    }

    public static OcrReq fromUrl(String url, String model) {
        return new OcrReq(Collections.singletonList(url), "http", model);
    }
}
