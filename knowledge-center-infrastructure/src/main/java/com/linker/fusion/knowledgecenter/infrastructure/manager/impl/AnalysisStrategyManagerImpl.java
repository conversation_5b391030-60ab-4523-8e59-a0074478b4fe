package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AnalysisStrategyEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IAnalysisStrategyManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.AnalysisStrategyMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 解析策略表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@Service
public class AnalysisStrategyManagerImpl extends ServiceImpl<AnalysisStrategyMapper, AnalysisStrategyEntity> implements IAnalysisStrategyManager {

}
