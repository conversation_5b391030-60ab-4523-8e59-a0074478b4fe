package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.dto.query.LogRecordPageQuery;
import com.linker.fusion.knowledgecenter.infrastructure.entity.LogRecordEntity;

/**
 * <p>
 * 日志记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface ILogRecordManager extends IService<LogRecordEntity> {

    IPage<LogRecordEntity> getPageList(LogRecordPageQuery query);
}
