package com.linker.fusion.knowledgecenter.infrastructure.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TerminologyTypeEnum {

    PERSON(1, "PERSO<PERSON>", "人物"),

    GPE(2, "GPE", "地点"),

    ORG(3, "ORG", "机构"),
    OTHER(4, "", "其他"),
    TIME(5, "TIME", "时间"),

    ;

    private final Integer code;

    private final String value;

    private final String description;

    public static TerminologyTypeEnum fromCode(Integer code) {
        return ArrayUtil.firstMatch(s -> s.getCode().equals(code), TerminologyTypeEnum.values());
    }

    public static TerminologyTypeEnum fromValue(String value) {
        return ArrayUtil.firstMatch(s -> s.getValue().equalsIgnoreCase(value), TerminologyTypeEnum.values());
    }
}
