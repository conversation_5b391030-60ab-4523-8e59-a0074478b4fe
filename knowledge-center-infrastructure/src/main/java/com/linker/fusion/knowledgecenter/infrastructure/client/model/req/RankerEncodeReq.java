package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import com.linker.fusion.knowledgecenter.infrastructure.config.BGEPropConfig;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
public class RankerEncodeReq {


    private String model_id = BGEPropConfig.RerankModelId;

    private List<List<String>> text;

    public RankerEncodeReq(List<List<String>> text) {
        this.text = text;
    }
}
