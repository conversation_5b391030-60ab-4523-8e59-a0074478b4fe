package com.linker.fusion.knowledgecenter.infrastructure.client.usercenter;

import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OperatorTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceVisibleTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件类型枚举
 */
@AllArgsConstructor
@Getter
public enum MenuKeyEnum {
    KNOWLEDGE_CENTER("knowledgeAccessControl", "知识中心", OperatorTypeEnum.MANAGER, null, null),
    KNOWLEDGE_MANAGER("KnowledgeManager", "知识中心管理员", null, null, null),
    FAQ_MANAGE("faqManage", "问答对菜单", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.FAQ, null),
    FAQ_PERSONAL_SPACE("btn_faq_personalSpace", "FAQ个人空间", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PERSONAL),
    FAQ_PERSONAL_SPACE_CREATE_LIB("btn_faq_personalSpace_createLib", "新建知识库", OperatorTypeEnum.LIB_CREATE, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PERSONAL),
    FAQ_PERSONAL_SPACE_CREATE("btn_faq_personalSpace_create", "新建", OperatorTypeEnum.CREATE, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PERSONAL),
    FAQ_PERSONAL_SPACE_IMPORT("btn_faq_personalSpace_import", "导入", OperatorTypeEnum.IMPORT, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PERSONAL),
    FAQ_PERSONAL_SPACE_AI_GEN("btn_faq_personalSpace_aiGen", "AI生成", OperatorTypeEnum.AI_GENERATE, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PERSONAL),
    FAQ_PERSONAL_SPACE_EDIT("btn_faq_personalSpace_edit", "编辑", OperatorTypeEnum.EDIT, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PERSONAL),
    FAQ_PERSONAL_SPACE_EXPORT("btn_faq_personalSpace_export", "导出", OperatorTypeEnum.EXPORT, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PERSONAL),
    FAQ_PERSONAL_SPACE_MOVE("btn_faq_personalSpace_move", "移动", OperatorTypeEnum.MOVE, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PERSONAL),
    FAQ_PERSONAL_SPACE_DELETE("btn_faq_personalSpace_delete", "删除", OperatorTypeEnum.DELETE, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PERSONAL),
    FAQ_PUBLIC_SPACE("btn_faq_publicSpace", "FAQ公开库", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PUBLIC),
    FAQ_PUBLIC_SPACE_CREATE_LIB("btn_faq_publicSpace_createLib", "新建知识库", OperatorTypeEnum.LIB_CREATE, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PUBLIC),
    FAQ_PUBLIC_SPACE_CREATE("btn_faq_publicSpace_create", "新建", OperatorTypeEnum.CREATE, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PUBLIC),
    FAQ_PUBLIC_SPACE_IMPORT("btn_faq_publicSpace_import", "导入", OperatorTypeEnum.IMPORT, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PUBLIC),
    FAQ_PUBLIC_SPACE_AI_GEN("btn_faq_publicSpace_aiGen", "AI生成", OperatorTypeEnum.AI_GENERATE, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PUBLIC),
    FAQ_PUBLIC_SPACE_EDIT("btn_faq_publicSpace_edit", "编辑", OperatorTypeEnum.EDIT, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PUBLIC),
    FAQ_PUBLIC_SPACE_EXPORT("btn_faq_publicSpace_export", "导出", OperatorTypeEnum.EXPORT, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PUBLIC),
    FAQ_PUBLIC_SPACE_MOVE("btn_faq_publicSpace_move", "移动", OperatorTypeEnum.MOVE, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PUBLIC),
    FAQ_PUBLIC_SPACE_DELETE("btn_faq_publicSpace_delete", "删除", OperatorTypeEnum.DELETE, KnowledgeTypeEnum.FAQ, ResourceVisibleTypeEnum.PUBLIC),

    RESOURCE_MANAGE("docsManage", "文件管理", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.FILE, null),
    RESOURCE_PERSONAL_SPACE("btn_file_personalSpace", "个人空间", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_CREATE_LIB("btn_file_personalSpace_createLib", "新增知识库", OperatorTypeEnum.LIB_CREATE, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_IMPORT("btn_file_personalSpace_docsImport", "导入", OperatorTypeEnum.IMPORT, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_PREVIEW("btn_file_personalSpace_docsPreview", "预览", OperatorTypeEnum.PREVIEW, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_EDIT("btn_file_personalSpace_docsEdit", "编辑", OperatorTypeEnum.EDIT, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_DELETE("btn_file_personalSpace_docsDelete", "删除", OperatorTypeEnum.DELETE, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_DOWNLOAD("btn_file_personalSpace_docsDownload", "下载", OperatorTypeEnum.DOWNLOAD, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_RE_LEARN("btn_file_personalSpace_docsReLearn", "重新学习", OperatorTypeEnum.REDO, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_SEGMENTED_MAINTENANCE("btn_file_personalSpace_docsSegmentedMaintance", "知识维护", OperatorTypeEnum.MAINTENANCE, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_SHARE_TO("btn_file_personalSpace_shareTo", "分享", OperatorTypeEnum.COPY, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_MOVE("btn_file_personalSpace_docsMove", "移动", OperatorTypeEnum.MOVE, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_RENAME("btn_file_personalSpace_docsRename", "重命名", OperatorTypeEnum.RENAME, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_ARCHIVE("btn_file_personalSpace_docsArchive", "归档", OperatorTypeEnum.ARCHIVE, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_SAVE_MEDIA_CLIP("btn_file_personalSpace_saveMediaClip", "分段保存", OperatorTypeEnum.SAVE_PART, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_DOWNLOAD_MEDIA_CLIP("btn_file_personalSpace_downloadMediaClip", "分段下载", OperatorTypeEnum.DOWNLOAD_PART, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_SCAN_DETAIL("btn_file_personalSpace_docsScanDetail", "巡检", OperatorTypeEnum.INSPECTION, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PERSONAL_SPACE_STRATEGY("btn_file_personalSpace_docsStrategy", "解析策略", OperatorTypeEnum.ANALYZE_SETTING, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PERSONAL),
    RESOURCE_PUBLIC_SPACE("btn_file_publicSpace", "公共空间", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_CREATE_LIB("btn_file_publicSpace_createLib", "新增知识库", OperatorTypeEnum.LIB_CREATE, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_IMPORT("btn_file_publicSpace_docsImport", "导入", OperatorTypeEnum.IMPORT, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_PREVIEW("btn_file_publicSpace_docsPreview", "预览", OperatorTypeEnum.PREVIEW, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_EDIT("btn_file_publicSpace_docsEdit", "编辑", OperatorTypeEnum.EDIT, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_DOWNLOAD("btn_file_publicSpace_docsDownload", "下载", OperatorTypeEnum.DOWNLOAD, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_DELETE("btn_file_publicSpace_docsDelete", "删除", OperatorTypeEnum.DELETE, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_RE_LEARN("btn_file_publicSpace_docsReLearn", "重新学习", OperatorTypeEnum.REDO, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_SEGMENTED_MAINTENANCE("btn_file_publicSpace_docsSegmentedMaintance", "知识维护", OperatorTypeEnum.MAINTENANCE, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_SHARE_TO("btn_file_publicSpace_shareTo", "分享", OperatorTypeEnum.COPY, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_MOVE("btn_file_publicSpace_docsMove", "移动", OperatorTypeEnum.MOVE, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_RENAME("btn_file_publicSpace_docsRename", "重命名", OperatorTypeEnum.RENAME, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_ARCHIVE("btn_file_publicSpace_docsArchive", "归档", OperatorTypeEnum.ARCHIVE, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_SAVE_MEDIA_CLIP("btn_file_publicSpace_saveMediaClip", "分段保存", OperatorTypeEnum.SAVE_PART, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_DOWNLOAD_MEDIA_CLIP("btn_file_publicSpace_downloadMediaClip", "分段下载", OperatorTypeEnum.DOWNLOAD_PART, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_SCAN_DETAIL("btn_file_publicSpace_docsScanDetail", "巡检", OperatorTypeEnum.INSPECTION, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_STRATEGY("btn_file_publicSpace_docsStrategy", "解析策略", OperatorTypeEnum.ANALYZE_SETTING, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_REQUEST_ACCESS("btn_file_publicSpace_requestAccess", "权限管理", OperatorTypeEnum.AUTH, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),
    RESOURCE_PUBLIC_SPACE_MERGE_SPLIT("btn_file_publicSpace_mergeSplit", "合并拆分视频", OperatorTypeEnum.VIDEO_MERGE_SPLIT, KnowledgeTypeEnum.FILE, ResourceVisibleTypeEnum.PUBLIC),

    TABLE_MANAGE("tableManage", "表格菜单", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.TABLE, null),
    TABLE_PERSONAL_SPACE("btn_table_personalSpace", "个人空间", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PERSONAL),
    TABLE_PERSONAL_SPACE_CREATE_LIB("btn_table_personalSpace_createLib", "新增知识库", OperatorTypeEnum.LIB_CREATE, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PERSONAL),
    TABLE_PERSONAL_SPACE_IMPORT("btn_table_personalSpace_import", "导入", OperatorTypeEnum.IMPORT, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PERSONAL),
    TABLE_PERSONAL_SPACE_PREVIEW("btn_table_personalSpace_preview", "预览", OperatorTypeEnum.PREVIEW, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PERSONAL),
    TABLE_PERSONAL_SPACE_EDIT("btn_table_personalSpace_edit", "编辑", OperatorTypeEnum.EDIT, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PERSONAL),
    TABLE_PERSONAL_SPACE_DOWNLOAD("btn_table_personalSpace_download", "下载", OperatorTypeEnum.DOWNLOAD, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PERSONAL),
    TABLE_PERSONAL_SPACE_DELETE("btn_table_personalSpace_delete", "删除", OperatorTypeEnum.DELETE, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PERSONAL),
    TABLE_PERSONAL_SPACE_RE_LEARN("btn_table_personalSpace_reLearn", "重新学习", OperatorTypeEnum.REDO, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PERSONAL),
    TABLE_PERSONAL_SPACE_SLICE_EDIT_DELETE("btn_table_personalSpace_sliceEditDelete", "分段维护", OperatorTypeEnum.MAINTENANCE, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PERSONAL),
    TABLE_PERSONAL_SPACE_MOVE("btn_table_personalSpace_move", "移动", OperatorTypeEnum.MOVE, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PERSONAL),
    TABLE_PUBLIC_SPACE("btn_table_publicSpace", "公共空间", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),
    TABLE_PUBLIC_SPACE_CREATE_LIB("btn_table_publicSpace_createLib", "新增知识库", OperatorTypeEnum.LIB_CREATE, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),
    TABLE_PUBLIC_SPACE_IMPORT("btn_table_publicSpace_import", "导入", OperatorTypeEnum.IMPORT, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),
    TABLE_PUBLIC_SPACE_PREVIEW("btn_table_publicSpace_preview", "预览", OperatorTypeEnum.PREVIEW, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),
    TABLE_PUBLIC_SPACE_EDIT("btn_table_publicSpace_edit", "编辑", OperatorTypeEnum.EDIT, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),
    TABLE_PUBLIC_SPACE_DOWNLOAD("btn_table_publicSpace_download", "下载", OperatorTypeEnum.DOWNLOAD, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),
    TABLE_PUBLIC_SPACE_DELETE("btn_table_publicSpace_delete", "删除", OperatorTypeEnum.DELETE, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),
    TABLE_PUBLIC_SPACE_RE_LEARN("btn_table_publicSpace_reLearn", "重新学习", OperatorTypeEnum.REDO, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),
    TABLE_PUBLIC_SPACE_SLICE_EDIT_DELETE("btn_table_publicSpace_sliceEditDelete", "分段维护", OperatorTypeEnum.MAINTENANCE, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),
    TABLE_PUBLIC_SPACE_MOVE("btn_table_publicSpace_move", "移动", OperatorTypeEnum.MOVE, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),
    TABLE_PUBLIC_SPACE_AUTH("btn_table_publicSpace_auth", "权限", OperatorTypeEnum.AUTH, KnowledgeTypeEnum.TABLE, ResourceVisibleTypeEnum.PUBLIC),


    QUESTION_BANK("questionBank", "题库菜单", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.QUESTION, null),
    QUESTION_PERSONAL_SPACE("btn_question_personalSpace", "个人空间", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PERSONAL),
    QUESTION_PERSONAL_SPACE_CREATE_LIB("btn_question_personalSpace_createLib", "新建知识库", OperatorTypeEnum.LIB_CREATE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PERSONAL),
    QUESTION_PERSONAL_SPACE_CREATE("btn_question_personalSpace_create", "新建", OperatorTypeEnum.CREATE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PERSONAL),
    QUESTION_PERSONAL_SPACE_DOC_GEN("btn_question_personalSpace_docGen", "文档生成", OperatorTypeEnum.DOC_GENERATE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PERSONAL),
    QUESTION_PERSONAL_SPACE_AI_GEN("btn_question_personalSpace_aiGen", "AI生成", OperatorTypeEnum.AI_GENERATE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PERSONAL),
    QUESTION_PERSONAL_SPACE_EDIT("btn_question_personalSpace_edit", "编辑", OperatorTypeEnum.EDIT, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PERSONAL),
    QUESTION_PERSONAL_SPACE_MOVE("btn_question_personalSpace_move", "移动", OperatorTypeEnum.MOVE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PERSONAL),
    QUESTION_PERSONAL_SPACE_DELETE("btn_question_personalSpace_delete", "删除", OperatorTypeEnum.DELETE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PERSONAL),
    QUESTION_PUBLIC_SPACE("btn_question_publicSpace", "公共空间", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PUBLIC),
    QUESTION_PUBLIC_SPACE_CREATE_LIB("btn_question_publicSpace_createLib", "新建知识库", OperatorTypeEnum.LIB_CREATE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PUBLIC),
    QUESTION_PUBLIC_SPACE_CREATE("btn_question_publicSpace_create", "新建", OperatorTypeEnum.CREATE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PUBLIC),
    QUESTION_PUBLIC_SPACE_DOC_GEN("btn_question_publicSpace_docGen", "文档生成", OperatorTypeEnum.DOC_GENERATE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PUBLIC),
    QUESTION_PUBLIC_SPACE_AI_GEN("btn_question_publicSpace_aiGen", "AI生成", OperatorTypeEnum.AI_GENERATE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PUBLIC),
    QUESTION_PUBLIC_SPACE_EDIT("btn_question_publicSpace_edit", "编辑", OperatorTypeEnum.EDIT, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PUBLIC),
    QUESTION_PUBLIC_SPACE_MOVE("btn_question_publicSpace_move", "移动", OperatorTypeEnum.MOVE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PUBLIC),
    QUESTION_PUBLIC_SPACE_DELETE("btn_question_publicSpace_delete", "删除", OperatorTypeEnum.DELETE, KnowledgeTypeEnum.QUESTION, ResourceVisibleTypeEnum.PUBLIC),


    PROFESSIONAL_LEXICON_MANAGE("professionalLexiconManage", "专有词库菜单", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.PRO, null),
    PROFESSIONAL_LEXICON_CREATE("btn_ProfessionalLexiconCreate", "专有词库创建", OperatorTypeEnum.LIB_CREATE, KnowledgeTypeEnum.PRO, null),
    PROFESSIONAL_LEXICON_EDIT("btn_ProfessionalLexiconEdit", "专有名词编辑", OperatorTypeEnum.LIB_EDIT, KnowledgeTypeEnum.PRO, null),
    PROFESSIONAL_LEXICON_DELETE("btn_ProfessionalLexiconDelete", "专有名词删除", OperatorTypeEnum.LIB_DELETE, KnowledgeTypeEnum.PRO, null),
    PROFESSIONAL_LEXICON_TEXT_CREATE("btn_ProfessionalTextCreate", "新建", OperatorTypeEnum.CREATE, KnowledgeTypeEnum.PRO, null),
    PROFESSIONAL_LEXICON_TEXT_DELETE("btn_ProfessionalTextDelete", "删除", OperatorTypeEnum.DELETE, KnowledgeTypeEnum.PRO, null),
    PROFESSIONAL_LEXICON_TEXT_EDIT("btn_ProfessionalTextEdit", "编辑", OperatorTypeEnum.EDIT, KnowledgeTypeEnum.PRO, null),
    PROFESSIONAL_LEXICON_TEXT_IMPORT("btn_ProfessionalTextImport", "导入", OperatorTypeEnum.IMPORT, KnowledgeTypeEnum.PRO, null),
    PROFESSIONAL_LEXICON_TEXT_ENABLE("btn_ProfessionalTextEnable", "失效", OperatorTypeEnum.ENABLE, KnowledgeTypeEnum.PRO, null),
    PROFESSIONAL_LEXICON_TEXT_EXPORT("btn_ProfessionalTextExport", "导出", OperatorTypeEnum.EXPORT, KnowledgeTypeEnum.PRO, null),


    SENSITIVE_LEXICON_MANAGE("sensitiveLexiconManage", "敏感词菜单", OperatorTypeEnum.MANAGER, KnowledgeTypeEnum.SENSITIVE, null),
    SENSITIVE_LEXICON_CREATE("btn_SensitiveLexiconCreate", "敏感词库创建", OperatorTypeEnum.LIB_CREATE, KnowledgeTypeEnum.SENSITIVE, null),
    SENSITIVE_LEXICON_EDIT("btn_SensitiveLexiconEdit", "敏感词库修改", OperatorTypeEnum.LIB_EDIT, KnowledgeTypeEnum.SENSITIVE, null),
    SENSITIVE_LEXICON_DELETE("btn_SensitiveLexiconDelete", "敏感词库删除", OperatorTypeEnum.LIB_DELETE, KnowledgeTypeEnum.SENSITIVE, null),
    SENSITIVE_LEXICON_TEXT_CREATE("btn_SensitiveTextCreate", "新建", OperatorTypeEnum.CREATE, KnowledgeTypeEnum.SENSITIVE, null),
    SENSITIVE_LEXICON_TEXT_EDIT("btn_SensitiveTextEdit", "编辑", OperatorTypeEnum.EDIT, KnowledgeTypeEnum.SENSITIVE, null),
    SENSITIVE_LEXICON_TEXT_DELETE("btn_SensitiveTextDelete", "删除", OperatorTypeEnum.DELETE, KnowledgeTypeEnum.SENSITIVE, null),
    SENSITIVE_LEXICON_TEXT_IMPORT("btn_SensitiveTextImport", "导入", OperatorTypeEnum.IMPORT, KnowledgeTypeEnum.SENSITIVE, null),
    SENSITIVE_LEXICON_TEXT_ENABLE("btn_SensitiveTextEnable", "启用", OperatorTypeEnum.ENABLE, KnowledgeTypeEnum.SENSITIVE, null),
    SENSITIVE_LEXICON_TEXT_EXPORT("btn_SensitiveTextExport", "导出", OperatorTypeEnum.EXPORT, KnowledgeTypeEnum.SENSITIVE, null),
    ;
    private final String key;
    private final String desc;
    private final OperatorTypeEnum operatorType;
    private final KnowledgeTypeEnum type;
    private final ResourceVisibleTypeEnum visibleType;


}
