package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum VideoCutTypeEnum {
    Scene(1, "镜头切分"),
    BreakPoint(2, "逻辑断点切分"),
    Asr(3, "语音切分"),
    Duration(4, "时长切分"),

    ;
    /**
     * 代码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    public static WordsStateEnum valueOf(Integer code) {
        for (WordsStateEnum e : WordsStateEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static WordsStateEnum valueOfDesc(String desc) {
        for (WordsStateEnum e : WordsStateEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e;
            }
        }
        return null;
    }
}
