package com.linker.fusion.knowledgecenter.infrastructure.model;

import com.linker.fusion.knowledgecenter.infrastructure.dto.ODModelInput;
import com.linker.fusion.knowledgecenter.infrastructure.dto.data.PromptInfoDTO;
import com.linker.fusion.knowledgecenter.infrastructure.dto.data.VideoExtract;
import com.linker.fusion.knowledgecenter.infrastructure.enums.VideoCutTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 视频策略
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VideoStrategyModel extends StrategyModelBasic {

    @ApiModelProperty(value = "语音转写 1-开启，0 关闭", hidden = true)
    private Integer transcription = 1;

    /**
     * 切分类型
     * <p>
     * {@link VideoCutTypeEnum}
     */
    @ApiModelProperty("切分类型 1-镜头切分 2-逻辑断点切分 3-语音切分 4-时长切分,默认为按时长切分")
    @NotNull(message = "分段设置不能为空")
    private Integer cutType = 4;

    @ApiModelProperty("按时长切分间隔 单位秒 限制10-3600 默认60")
    private int durationCutInterval = 60;

    @ApiModelProperty(value = "分段分块最大长度:200~1000", hidden = true)
    @Range(min = 200, max = 1000, message = "分段分块最大长度不合法")
    private Integer maxLength = 200;
    /**
     * 分段分块最短比例:1~60
     */
    @ApiModelProperty(value = "分段分块最短比例:1~60", hidden = true)
    @Range(min = 1, max = 60, message = "分段分块最短比例不合法")
    private Integer ratio = 25;

    @ApiModelProperty("实体识别 0-关闭 1-开启 ")
    private Integer enableObjectDetection = 0;

    @ApiModelProperty("人脸识别 0-关闭 1-开启 ")
    private Integer enableFaceRecognition = 1;
    @ApiModelProperty("是否启用视频转码，默认开启 ")
    private Integer enableVideoTranscode = 1;
    @ApiModelProperty("专名识别 0-关闭 1-开启 ")
    private Integer enableTerminology = 0;
    @ApiModelProperty("分段识别")
    private Integer enableSegment = 1;
    @ApiModelProperty("信息抽取")
    private VideoExtract videoExtract;

    @ApiModelProperty(value = "大模型总结 0-关闭 1-开启 默认值0", hidden = true)
    private Integer summaryEnhance = 1;

    @ApiModelProperty(value = "OD模版信息列表", hidden = true)
    private List<ODModelInput> odList;


    @ApiModelProperty("事件设置")
    private EventSetting eventSetting;
    @ApiModelProperty("事件分析 0-关闭 1-开启 默认值0")
    private Integer enableEvent = 0;
    @ApiModelProperty(value = "提示词", hidden = true)
    private List<PromptInfoDTO> promptInfos;
    @ApiModelProperty("事件问题")
    private String eventQuestion;


    @Data
    public static class EventSetting {
        @ApiModelProperty("开启关闭")
        private Integer enable;
        @ApiModelProperty(value = "提示词")
        private List<PromptInfoDTO> promptInfos;
        @ApiModelProperty("抽帧间隔")
        private int interval;
        @ApiModelProperty("识别类型 1全画面识别 2区域识别")
        private int type;
        @ApiModelProperty("事件总结问题")
        private String question;
    }

    @Override
    public void setDefault() {
        transcription = 1;
        if (cutType == null) {
            cutType = 1;
        }
        if (enableObjectDetection == null) {
            enableObjectDetection = 1;
        }
        if (enableFaceRecognition == null) {
            enableFaceRecognition = 1;
        }
        if (enableTerminology == null) {
            enableTerminology = 1;
        }
        if (enableEvent == null) {
            enableEvent = 0;
        }
        if (summaryEnhance == null) {
            summaryEnhance = 1;
        }
    }
}
