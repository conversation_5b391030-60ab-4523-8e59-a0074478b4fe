package com.linker.fusion.knowledgecenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceSegmentEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ResourceSegmentMapper extends BaseMapper<ResourceSegmentEntity> {

    Page<ResourceSegmentEntity> page(String docId, String content, List<Integer> status, Page page);

    Double maxSort(String docId);
}
