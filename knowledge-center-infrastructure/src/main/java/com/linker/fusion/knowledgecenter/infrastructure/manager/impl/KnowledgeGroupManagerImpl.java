package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IKnowledgeGroupManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.KnowledgeGroupMapper;
import com.linker.fusion.knowledgecenter.infrastructure.model.GroupExtModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 知识分组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
@Service
public class KnowledgeGroupManagerImpl extends ServiceImpl<KnowledgeGroupMapper, KnowledgeGroupEntity> implements IKnowledgeGroupManager {

    @Override
    public List<KnowledgeGroupEntity> list(String tenantId) {
        QueryWrapper<KnowledgeGroupEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(KnowledgeGroupEntity::getDeleted, false)
                .eq(KnowledgeGroupEntity::getTenantId, tenantId);
        return list(wrapper);
    }

    @Override
    public List<KnowledgeGroupEntity> list(String tenantId, String userId, Integer type, Long parentId, Integer visibleType) {
        QueryWrapper<KnowledgeGroupEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(KnowledgeGroupEntity::getDeleted, false)
                .eq(KnowledgeGroupEntity::getTenantId, tenantId)
                .eq(StringUtils.isNotBlank(userId), KnowledgeGroupEntity::getCreatorId, userId)
                .eq(KnowledgeGroupEntity::getType, type)
                .eq(KnowledgeGroupEntity::getParentId, parentId)
                .eq(Objects.nonNull(visibleType), KnowledgeGroupEntity::getVisibleType, visibleType);
        ;
        return list(wrapper);
    }

    @Override
    public KnowledgeGroupEntity getRoot(KnowledgeGroupEntity group) {
        if (Objects.isNull(group))
            return null;
        if (group.getParentId().equals(0L))
            return group;
        if (Objects.nonNull(group.getRootId())) {
            return getById(group.getRootId());
        }
        KnowledgeGroupEntity parent = getById(group.getParentId());
        return getRoot(parent);
    }

    @Override
    public void updatePath(Long id, String path) {
        UpdateWrapper<KnowledgeGroupEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(KnowledgeGroupEntity::getId, id);
        updateWrapper.lambda().set(KnowledgeGroupEntity::getPath, path);
        update(updateWrapper);
    }

    @Override
    public List<KnowledgeGroupEntity> listByPath(String path) {
        QueryWrapper<KnowledgeGroupEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().like(KnowledgeGroupEntity::getPath, path + "%");
        wrapper.lambda().eq(KnowledgeGroupEntity::getDeleted, false);
        return list(wrapper);
    }

    @Override
    public void logicDeleteBatch(List<Long> ids) {
        UpdateWrapper<KnowledgeGroupEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(KnowledgeGroupEntity::getId, ids)
                .set(KnowledgeGroupEntity::getDeleted, true);
        update(wrapper);
    }

    @Override
    public List<KnowledgeGroupEntity> getByGuid(String guid) {
        QueryWrapper<KnowledgeGroupEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(KnowledgeGroupEntity::getGuid, guid);
        wrapper.lambda().eq(KnowledgeGroupEntity::getDeleted, false);
        return list(wrapper);
    }

    @Override
    public GroupExtModel getMaxAndMin(List<Long> parentIds) {
        return baseMapper.getMaxAndMin(parentIds);
    }

    @Override
    public void updateImportLevelByPath(String searchPath, Integer importantLevel) {
        UpdateWrapper<KnowledgeGroupEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().like(KnowledgeGroupEntity::getPath, searchPath + "%")
                .set(KnowledgeGroupEntity::getImportantLevel, importantLevel);
        update(wrapper);
    }

    @Override
    public void updateApproveProcessKey(Long id, String key) {
        UpdateWrapper<KnowledgeGroupEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(KnowledgeGroupEntity::getId, id)
                .set(KnowledgeGroupEntity::getApproveProcessKey, key);
        update(wrapper);
    }

    @Override
    public KnowledgeGroupEntity get(String tenantId, String userId,Long parentId, String name) {
        QueryWrapper<KnowledgeGroupEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(KnowledgeGroupEntity::getParentId, parentId);
        queryWrapper.lambda().eq(KnowledgeGroupEntity::getName, name);
        queryWrapper.lambda().eq(KnowledgeGroupEntity::getTenantId, tenantId);
        queryWrapper.lambda().eq(KnowledgeGroupEntity::getCreatorId, userId);
        queryWrapper.lambda().eq(KnowledgeGroupEntity::getDeleted, false);
        List<KnowledgeGroupEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) return null;
        return list.get(0);
    }

    @Override
    public KnowledgeGroupEntity getByBiz(String tenantId, String userId,Integer bizType) {
        QueryWrapper<KnowledgeGroupEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(KnowledgeGroupEntity::getTenantId, tenantId);
        queryWrapper.lambda().eq(KnowledgeGroupEntity::getCreatorId, userId);
        queryWrapper.lambda().eq(KnowledgeGroupEntity::getDeleted, false);
        queryWrapper.lambda().eq(KnowledgeGroupEntity::getBizType, bizType);
        List<KnowledgeGroupEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) return null;
        return list.get(0);
    }
}
