package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("t_index_tenant_mapping")
public class IndexTenantMappingEntity implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户或组织的唯一标识符
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 索引名称
     */
    @TableField(value = "`index`")
    private String index;

    /**
     * 知识类型 1-文档 2-图片 3-视频 4-音频 5-FAQ 6-表格
     */
    @TableField("type")
    private Integer type;

    /**
     * 是否用模板 -> 0：否、1：是
     */
    @TableField("is_template")
    private Boolean isTemplate;

    /**
     * 读别名
     */
    @TableField("read_alias")
    private String readAlias;

    /**
     * 写别名
     */
    @TableField("write_alias")
    private String writeAlias;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
