package com.linker.fusion.knowledgecenter.infrastructure.es;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 抽帧数据表
 */
@Data
public class VideoFrameEntity implements Serializable {
    @JsonProperty("_uid")
    private String uid;
    @ApiModelProperty("帧id")
    @JsonProperty("frame_id")
    private String frameId;
    @ApiModelProperty("分组id")
    @JsonProperty("group_id")
    private Long groupId;
    @ApiModelProperty("帧图片地址")
    @JsonProperty("url")
    private String url;
    @ApiModelProperty("分段id")
    @JsonProperty("segment_id")
    private String segmentId;
    @ApiModelProperty("文档id")
    @JsonProperty("doc_id")
    private String docId;
    @ApiModelProperty("文件标题")
    @JSONField(name = "file_title")
    @JsonProperty("file_title")
    private String title;
    @ApiModelProperty("帧的caption")
    private String content;
    @ApiModelProperty("图片向量")
    @JsonProperty("image_vector")
    private List<Double> imageVector;
    @ApiModelProperty("扩展信息")
    @JsonProperty("meta")
    private JSONObject meta;
    @ApiModelProperty("人脸标签数组")
    @JsonProperty("persons_labels")
    private List<String> personsLabels;
    @ApiModelProperty("实体标签数组")
    @JsonProperty("object_labels")
    private List<String> objectLabels;
    @ApiModelProperty("专名标签数组")
    @JsonProperty("text_labels")
    private List<String> textLabels;
    @ApiModelProperty("排序")
    @JsonProperty("sort")
    private Double sort;
    @ApiModelProperty("时间戳")
    @JsonProperty("time_point")
    private Long timePoint;
    @ApiModelProperty("启用")
    @JsonProperty("enable")
    private Integer enable;
    @ApiModelProperty("创建时间")
    @JsonProperty("create_time")
    private String createTime;
    @ApiModelProperty("租户id")
    @JsonProperty("tenant_id")
    private String tenantId;
}
