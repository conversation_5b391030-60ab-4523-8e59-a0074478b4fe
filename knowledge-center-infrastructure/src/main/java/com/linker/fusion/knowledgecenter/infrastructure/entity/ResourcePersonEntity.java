package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_resource_person")
@Accessors(chain = true)
public class ResourcePersonEntity extends BaseEntity {
    /**
     * 分段id
     */
    @TableField("segment_id")
    private String segmentId;
    /**
     * 文档id
     */
    @TableField("doc_id")
    private String docId;
    /**
     * 镜头id
     */
    @TableField("frame_id")
    private String frameId;
    /**
     * 标签名称
     */
    @TableField("name")
    private String name;
    /**
     * 位置
     */
    @TableField("position")
    private String position;
    /**
     * 位置
     */
    @TableField("time_point")
    private Long timePoint;
    /**
     * 是否告警
     */
    @TableField("is_alarm")
    private Boolean isAlarm;
}
