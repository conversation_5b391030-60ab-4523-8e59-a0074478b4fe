package com.linker.fusion.knowledgecenter.infrastructure.model;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 策略配置对象响应数据结构
 */
@Slf4j
@Data
public class DocStrategyModel extends StrategyModelBasic {

    @ApiModelProperty("文档转码 默认值1 不可改变")
    @Range(min = 1, max = 1, message = "文档转码必须开启")
    private Integer transcode = 1;

    @ApiModelProperty("文字提取 默认值1 不可改变")
    @Range(min = 1, max = 1, message = "文字提取必须开启")
    private Integer readText = 1;

    @ApiModelProperty("图片解析增强，0否 1是 默认值1 可以改变")
    @Range(min = 0, max = 1, message = "图片解析增强不合法")
    private Integer imageEnhance = 1;

    @ApiModelProperty("PPT解析增强，0否 1是 默认值1 可以改变")
    @Range(min = 0, max = 1, message = "PPT解析增强不合法")
    private Integer pptEnhance = 1;

    /**
     * 切分类型:0智能切分 1自定义切分
     */
//    @Deprecated
//    @ApiModelProperty("切分类型:0智能切分 1自定义切分")
//    @Range(min = 0, max = 1, message = "切分类型不合法")
//    private Integer splitType = 0;

    /**
     * 清洗规则(1),是否删除连续空格、换行、制表符，0否 1是
     */
    @ApiModelProperty("清洗规则,是否删除连续空格、换行、制表符，0否 1是")
    private Integer cleanRuleTab = 1;

    /**
     * 清洗规则(2),是否删除页眉、页脚，0否 1是
     */
    @ApiModelProperty("清洗规则,是否删除页眉、页脚，0否 1是")
    private Integer cleanRuleFooter = 1;

    @ApiModelProperty("清洗规则，是否去除水印，0否 1是")
    private Integer cleanRuleWatermark = 1;

    /**
     * 分段方式:1目录切分 0递归拆分
     */
    @ApiModelProperty("1目录切分 0递归拆分")
    @Range(min = 0, max = 1, message = "分段方式不合法")
    private Integer segmentType = 1;

    /**
     * 分段分块标识符
     */
    @ApiModelProperty("分段分块标识符")
    @NotNull(message = "分段分块标识符不能为空")
    private List<String> splitTextList = Arrays.asList(
            "...",
            "。",
            ".",
            "!",
            "?",
            "！",
            "？");

    /**
     * 分段分块标识数据源
     */
    @ApiModelProperty("分段分块标识数据源")
    private List<JSONObject> splitTextOptions = new ArrayList<>();

    /**
     * 是否合并短句标识，0否 1是
     */
    @ApiModelProperty("是否合并短句标识，0否 1是")
    @Range(min = 0, max = 1, message = "是否合并短句标识不合法")
    private Integer mergeShort = 1;

    /**
     * 分段分块最大长度:200~1000
     */
    @ApiModelProperty("分段分块最大长度:200~1000")
    @Range(min = 200, max = 1000, message = "分段分块最大长度不合法")
    private Integer maxLength = 500;

    /**
     * 分段分块最短比例:1~60
     */
    @ApiModelProperty("分段分块最短比例:1~60")
    @Range(min = 1, max = 60, message = "分段分块最短比例不合法")
    private Integer ratio = 30;

    /**
     * 是否目录增强，0否 1是
     */
    @ApiModelProperty("是否目录增强，0否 1是")
    private Integer catalogEnhance = 0;

    /**
     * 是否段落分块:0否 1是
     */
    @ApiModelProperty("是否段落分块:0否 1是")
    private Integer chunkFlag = 1;

    @Override
    public void setDefault() {
//        log.info("文档切分策略设置默认值");
        transcode = 1;
        readText = 1;
        if (cleanRuleTab == null) {
            cleanRuleTab = 1;
        }
        if (cleanRuleFooter == null) {
            cleanRuleFooter = 1;
        }
        if (cleanRuleWatermark == null) {
            cleanRuleWatermark = 1;
        }
        if (segmentType == null) {
            segmentType = 1;
        }
        if (splitTextList == null) {
            splitTextList = Arrays.asList(
                    "...",
                    "。",
                    ".",
                    "!",
                    "?",
                    "！",
                    "？");
        }
        if (mergeShort == null) {
            mergeShort = 1;
        }
        if (maxLength == null) {
            maxLength = 500;
        }
        if (ratio == null) {
            ratio = 30;
        }
        if (catalogEnhance == null) {
            catalogEnhance = 0;
        }
        if (chunkFlag == null) {
            chunkFlag = 1;
        }
        if (imageEnhance == null) {
            imageEnhance = 1;
        }
        if (pptEnhance == null) {
            pptEnhance = 1;
        }
        if (splitTextOptions == null) {
            splitTextOptions = new ArrayList<>();
        }
    }
}
