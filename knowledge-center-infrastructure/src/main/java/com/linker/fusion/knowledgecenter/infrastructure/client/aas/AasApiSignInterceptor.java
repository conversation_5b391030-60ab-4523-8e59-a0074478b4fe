package com.linker.fusion.knowledgecenter.infrastructure.client.aas;

import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import com.linker.core.utils.ApiSignUtil;
import com.linker.fusion.knowledgecenter.infrastructure.config.AasWorkflowProperties;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

@Component
public class AasApiSignInterceptor extends BasePathMatchInterceptor {

    private final static String LINKER_SIGN_HEADER = "linker-sign";


    private final static String TENANT_ID = "tenantId";

    @Resource
    private AasWorkflowProperties aasWorkflowProperties;

    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        Request request = chain.request();
        Request newRequest = request.newBuilder()
                .method(request.method(), request.body())
                .headers(request.headers())
                .addHeader(TENANT_ID, aasWorkflowProperties.getTenantId())
                .addHeader(LINKER_SIGN_HEADER, getLinkerSign(request.body())).build();
        return chain.proceed(newRequest);
    }


    private String bodyToString(final RequestBody requestBody) {
        try {
            final Buffer buffer = new Buffer();
            if (requestBody != null) {
                requestBody.writeTo(buffer);
                return buffer.readUtf8();
            }
            return "";
        } catch (final IOException e) {
            return "Unable to read request body";
        }
    }


    String getLinkerSign(RequestBody requestBody) {
        return ApiSignUtil.encrypt(bodyToString(requestBody), aasWorkflowProperties.getAccessKey(), aasWorkflowProperties.getSecretKey());
    }

}