package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 词条状态枚举
 **/
@AllArgsConstructor
@Getter
public enum WordsStateEnum {

    ENABLE(1, "启用"),
    UN_ENABLE(0, "禁用");

    /**
     * 代码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    public static WordsStateEnum valueOf(Integer code) {
        for (WordsStateEnum e : WordsStateEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
    public static WordsStateEnum valueOfDesc(String desc) {
        for (WordsStateEnum e : WordsStateEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e;
            }
        }
        return null;
    }

}
