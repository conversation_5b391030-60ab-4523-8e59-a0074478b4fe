package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "t_sub_video", autoResultMap = true)
public class SubVideoEntity extends BaseEntity {

    /**
     * 关联的docId
     * {@link KnowledgeResourceEntity#getDocId()}
     */
    @TableField("doc_id")
    private String docId;

    /**
     * 文件UUID
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 组ID
     */
    @TableField("group_id")
    private Long groupId;

    /**
     * 资源名称
     */
    @TableField("title")
    private String title;

    /**
     * 文件后缀
     */
    @TableField("suffix")
    private String suffix;

    /**
     * 资源地址
     */
    @TableField("url")
    private String url;

    /**
     * 文件资源大小（byte）
     */
    @TableField("size")
    private Long size;

    /**
     * 时长 毫秒
     */
    @TableField("duration")
    private Long duration;

    /**
     * 封面URL
     */
    @TableField("thumbnail")
    private String thumbnail;

    /**
     * 0-已合成 1-新增 2-删除
     */
    @TableField("operation")
    private Integer operation;

    private Integer sort;
}