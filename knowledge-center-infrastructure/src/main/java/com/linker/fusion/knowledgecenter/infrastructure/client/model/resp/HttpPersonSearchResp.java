package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 人脸检索返回对象
 */
@Data
@NoArgsConstructor
public class HttpPersonSearchResp {

    /**
     * 返回代码
     */
    private String code;

    /**
     * 人脸列表
     */
    private List<PersonSearchFaceRecognitionResp> data;

    /**
     * 消息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 人脸列表
     */
    @Data
    public static class PersonSearchFaceRecognitionResp {

        /**
         * 人脸信息详情
         */
        private List<PersonSearchFaceRecognitionDetailResp> faceInfos;

        /**
         * 输入的url
         */
        private String url;

        /**
         * 资源唯一ID
         */
        private String id;

    }


    /**
     * 人脸信息详情
     */
    @Data
    public static class PersonSearchFaceRecognitionDetailResp {


        /**
         * 人脸剪切图
         */
        private String image;

        /**
         * 人脸坐标
         */
        private List<Integer> bbox;

        /**
         * 是否匹配
         */
        private Boolean matched;

        /**
         * 人脸id
         */
        private String personId;

        /**
         * 人员信息
         */
        private PersonInfoDTO personInfo;
    }

}
