package com.linker.fusion.knowledgecenter.infrastructure.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FaqAction {
    @ApiModelProperty("新建")
    private boolean create = false;
    @ApiModelProperty("导入")
    private boolean upload = false;
    @ApiModelProperty("编辑")
    private boolean edit = false;
    @ApiModelProperty("导出")
    private boolean export = false;
    @ApiModelProperty("删除")
    private boolean delete = false;
    @ApiModelProperty("移动")
    private boolean move=false;
    @ApiModelProperty("失效")
    private boolean expire = false;
    @ApiModelProperty("浏览")
    private  boolean view =false;
}
