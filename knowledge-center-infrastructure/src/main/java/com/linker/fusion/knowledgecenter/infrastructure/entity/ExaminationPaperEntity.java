package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 试卷表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Getter
@Setter
@TableName("t_examination_paper")
public class ExaminationPaperEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 绑定批量问题库标识
     */
    @TableField("bind_question_base_ids")
    private String bindQuestionBaseIds;

    /**
     * 生成问题库类型, CHOICE:单选题, TRUE_FALSE:判断题
     */
    @TableField("generate_question_types")
    private String generateQuestionTypes;

    /**
     * 试卷唯一标识
     */
    @TableField("paper_unique_id")
    private String paperUniqueId;

    /**
     * 试卷内容
     */
    @TableField("paper_content")
    private String paperContent;

    /**
     * 期望题目数量
     */
    @TableField("expect_question_number")
    private Integer expectQuestionNumber;

    /**
     * 真实题目数量
     */
    @TableField("real_question_number")
    private Integer realQuestionNumber;

    /**
     * 试卷回答内容
     */
    @TableField("paper_answer_content")
    private String paperAnswerContent;

    /**
     * 试卷状态, 0:回答中, 1:完成
     */
    @TableField("paper_status")
    private Integer paperStatus;

    /**
     * 试卷成绩
     */
    @TableField("paper_score")
    private String paperScore;
}
