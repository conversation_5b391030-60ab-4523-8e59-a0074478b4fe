package com.linker.fusion.knowledgecenter.infrastructure.es;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import lombok.Data;

@Deprecated
@Data
public class ResourceEntity {

    @JsonProperty("doc_id")
    private String docId;
    /**
     * 知识类型 1-文档 2-图片 3-视频 4-音频 5-FAQ 6-表格
     */
    @JsonProperty("type")
    private Integer type;
    /**
     * 组ID
     */
    @JsonProperty("group_id")
    private Long groupId;

    /**
     * 分组路径
     */
    @JsonProperty("group_path")
    private String groupPath;

    /**
     * 资源名称
     */
    @JsonProperty("title")
    private String title;

    /**
     * 文件后缀
     */
    @JsonProperty("suffix")
    private String suffix;

    /**
     * 描述
     */
    @JsonProperty("description")
    private String description;

    /**
     * 资源地址
     */
    @JsonProperty("url")
    private String url;


    /**
     * 文件资源大小（byte）
     */
    @JsonProperty("size")
    private Long size;

    /**
     * 文档：页数；表格：行数
     */
    @JsonProperty("count")
    private Long count;
    /**
     * 是否生效 -> 0：否、1：是
     */
    @JsonProperty("enable")
    private Integer enable;

    /**
     * 扩展信息字段json
     */
    @TableField("ext_info")
    private JSONObject extInfo;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private String createTime;
    /**
     * 修改时间
     */
    @TableField("create_id")
    private String createId;
    /**
     * 租户Id
     */
    @TableField("tenant_id")
    private String tenantId;

    public ResourceEntity() {

    }

    public ResourceEntity(KnowledgeResourceEntity knowledgeResource) {
        this.docId = knowledgeResource.getDocId();
        this.type = knowledgeResource.getType();
        this.groupId = knowledgeResource.getGroupId();
        this.groupPath = knowledgeResource.getGroupPath();
        this.title = knowledgeResource.getTitle();
        this.suffix = knowledgeResource.getSuffix();
        this.description = knowledgeResource.getDescription();
        this.url = knowledgeResource.getUrl();
        this.size = knowledgeResource.getSize();
        this.count = knowledgeResource.getCount();
        this.enable = knowledgeResource.getEnable() ? 1 : 0;
        this.extInfo = JSONObject.parseObject(knowledgeResource.getExtInfo());
        this.createTime= StringComUtils.convertStr( knowledgeResource.getCreateTime());
        this.createId=knowledgeResource.getCreatorId();
        this.tenantId=knowledgeResource.getTenantId();
    }
}
