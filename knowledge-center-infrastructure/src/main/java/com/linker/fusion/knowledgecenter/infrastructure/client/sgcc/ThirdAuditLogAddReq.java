package com.linker.fusion.knowledgecenter.infrastructure.client.sgcc;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.linker.fusion.knowledgecenter.infrastructure.entity.LogRecordEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OprationLogRecordSubTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OprationLogRecordTypeEnum;
import lombok.Data;

/**
 * 安徽配网日志
 */
@Data
public class ThirdAuditLogAddReq {

    /**
     * 操作账号
     */
    private String account;

    /**
     * 操作人姓名
     */
    private String operator;

    /**
     * 操作人ID
     */
    private String creatorId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 服务编码
     */
    private String serviceCode;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 操作IP地址
     */
    private String ipAddress;

    /**
     * 系统模块
     */
    private String sysModule;
    /**
     * 业务模块
     */
    private String bizModule;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作详情
     */
    private String operateDetail;

    /**
     * 操作状态：1-成功，0-失败
     */
    private Integer operateStatus;

    /**
     * 异常等级：low / middle / high
     */
    private String exceptionLevel;

    public static ThirdAuditLogAddReq build(LogRecordEntity logRecordEntity) {
        ThirdAuditLogAddReq thirdAuditLogAddReq = new ThirdAuditLogAddReq();
        thirdAuditLogAddReq.setAccount(logRecordEntity.getUsername());
        thirdAuditLogAddReq.setOperator(logRecordEntity.getOperator());
        thirdAuditLogAddReq.setCreatorId(logRecordEntity.getCreatorId());
        thirdAuditLogAddReq.setTenantId(logRecordEntity.getTenantId());
        thirdAuditLogAddReq.setServiceCode("knowledge-center");
        thirdAuditLogAddReq.setOperateTime(LocalDateTimeUtil.formatNormal(logRecordEntity.getCreateTime()));
        thirdAuditLogAddReq.setIpAddress(logRecordEntity.getIp());
        thirdAuditLogAddReq.setSysModule(OprationLogRecordTypeEnum.getByName(logRecordEntity.getType()));
        thirdAuditLogAddReq.setBizModule(OprationLogRecordSubTypeEnum.getByName(logRecordEntity.getSubType()));
        thirdAuditLogAddReq.setOperateType(OprationLogRecordSubTypeEnum.getByName(logRecordEntity.getSubType()));
        thirdAuditLogAddReq.setOperateDetail(logRecordEntity.getAction());
        thirdAuditLogAddReq.setOperateStatus(logRecordEntity.getStatus());
        thirdAuditLogAddReq.setExceptionLevel(logRecordEntity.getLevel());
        return thirdAuditLogAddReq;

    }
}
