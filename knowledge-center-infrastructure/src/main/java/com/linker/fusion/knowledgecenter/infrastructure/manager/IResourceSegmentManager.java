package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceSegmentEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;

import java.util.List;

/**
 * <p>
 * 知识分组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
public interface IResourceSegmentManager extends IService<ResourceSegmentEntity> {

    Page<ResourceSegmentEntity> page(long page, long pageSize, String docId, FileTypeEnum fileTypeEnum, String content, List<Integer> status);

    ResourceSegmentEntity get(String segmentId);

    List<ResourceSegmentEntity> listBySegmentIds(List<String> segmentIds);

    void deleteByDocId(String docId);

    void deleteBySegmentId(String segmentId);

    void deleteBySegmentIds(List<String> segmentIds);

    void clearByDocId(String docId);

    List<ResourceSegmentEntity> listByDocId(String docId);

    List<ResourceSegmentEntity> listByDocIds(List<String> docIds);

    List<ResourceSegmentEntity> listByDocId(String docId, Integer take, Integer skip);


    Double maxSort(String docId);


    List<ResourceSegmentEntity> listRangeByDocId(String docId, Double min, Double max);

    ResourceSegmentEntity getPre(String docId, Double min);


    void updateStatus(Long id, Integer status);
    void updateStatus(String segId, Integer status);

    void updateThumbnail(String segmentId, String url);
}
