package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.core.base.enums.GlobalErrorCodeEnum;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.common.IndexConstant;
import com.linker.fusion.knowledgecenter.infrastructure.entity.IndexTenantMappingEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexTenantMappingManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.IndexTenantMappingMapper;
import com.mysql.cj.util.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class IndexTenantMappingManagerImpl extends ServiceImpl<IndexTenantMappingMapper, IndexTenantMappingEntity> implements IIndexTenantMappingManager {

    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    @Override
    public String getIndex(String tenantId, IndexPrefixEnum indexPrefixEnum) {
        if (StringUtils.isNullOrEmpty(tenantId) || indexPrefixEnum == null) {
            throw new ServiceException(GlobalErrorCodeEnum.MISSING_PARAMETER, "租户id或类型为空");
        }
        IndexTenantMappingEntity one = getOne(tenantId, indexPrefixEnum.getType());
        if (one != null) {
            return one.getIndex();
        } else {
            return save(tenantId, indexPrefixEnum.getIndexPrefix(), indexPrefixEnum.getType()).getIndex();
        }
    }

    @Override
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public String getIndex(String tenantId, IndexPrefixEnum indexPrefixEnum, boolean isTemplate, boolean write) {
        if (StringUtils.isNullOrEmpty(tenantId) || indexPrefixEnum == null) {
            throw new ServiceException(GlobalErrorCodeEnum.MISSING_PARAMETER, "租户id或类型为空");
        }
        IndexTenantMappingEntity one = getOne(tenantId, indexPrefixEnum.getType());
        if (one != null) {
            return one.getIndex();
        } else {
            return saveTemplate(tenantId, indexPrefixEnum.getIndexPrefix(), indexPrefixEnum.getType()).getIndex();
        }
    }


    private IndexTenantMappingEntity saveTemplate(String tenantId, String indexPrefix, Integer type) {
        IndexTenantMappingEntity indexTenantMappingEntity = new IndexTenantMappingEntity();
        indexTenantMappingEntity.setTenantId(tenantId);
        indexTenantMappingEntity.setIndex(indexPrefix);
        indexTenantMappingEntity.setType(type);
        indexTenantMappingEntity.setReadAlias(IndexConstant.READ_ALIAS);
        indexTenantMappingEntity.setReadAlias(IndexConstant.WRITE_ALIAS);
        indexTenantMappingEntity.setIsTemplate(true);
        indexTenantMappingEntity.setCreateTime(LocalDateTime.now());
        try {
            save(indexTenantMappingEntity);
            return indexTenantMappingEntity;
        } catch (DuplicateKeyException e) {
            log.error("DuplicateKeyException ", e);
        }
        return indexTenantMappingEntity;
    }
//
//    public synchronized IndexTenantMappingEntity save(String tenantId, KnowledgeTypeEnum knowledgeTypeEnum) {
//        IndexTenantMappingEntity indexTenantMappingEntity = new IndexTenantMappingEntity();
//        indexTenantMappingEntity.setTenantId(tenantId);
//        indexTenantMappingEntity.setIndex(knowledgeTypeEnum.getIndexPrefix() + tenantId.toLowerCase());
//        indexTenantMappingEntity.setType(knowledgeTypeEnum.getType());
//        indexTenantMappingEntity.setIsTemplate(false);
//        indexTenantMappingEntity.setCreateTime(LocalDateTime.now());
//        IndexTenantMappingEntity one = getOne(tenantId, knowledgeTypeEnum.getType());
//        if (one == null) {
//            return save(indexTenantMappingEntity) ? indexTenantMappingEntity : null;
//        } else {
//            return one;
//        }
//    }

    public synchronized IndexTenantMappingEntity save(String tenantId, String indexPrefix, Integer type) {
        IndexTenantMappingEntity indexTenantMappingEntity = new IndexTenantMappingEntity();
        indexTenantMappingEntity.setTenantId(tenantId);
        indexTenantMappingEntity.setIndex(indexPrefix + tenantId.toLowerCase());
        indexTenantMappingEntity.setType(type);
        indexTenantMappingEntity.setIsTemplate(false);
        indexTenantMappingEntity.setCreateTime(LocalDateTime.now());
        IndexTenantMappingEntity one = getOne(tenantId, type);
        if (one == null) {
            return save(indexTenantMappingEntity) ? indexTenantMappingEntity : null;
        } else {
            return one;
        }
    }

    IndexTenantMappingEntity getOne(String tenantId, Integer type) {
        return lambdaQuery()
                .eq(IndexTenantMappingEntity::getTenantId, tenantId)
                .eq(IndexTenantMappingEntity::getType, type).one();
    }

}
