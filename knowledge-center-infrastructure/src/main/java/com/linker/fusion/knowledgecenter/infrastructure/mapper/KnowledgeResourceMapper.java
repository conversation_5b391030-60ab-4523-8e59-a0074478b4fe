package com.linker.fusion.knowledgecenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.model.ResourceStaModel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
public interface KnowledgeResourceMapper extends BaseMapper<KnowledgeResourceEntity> {
    Long sumSize(String tenantId, Integer type);

    List<ResourceStaModel> staByLib(List<String> pathList, Integer fileType);

    /**
     * 统计文件大小
     *
     * @param tenantId 租户id
     * @param userCode 用户id
     * @param visibleType 资源可见类型
     * @return  文件大小
     */
    Long sumSizeByVisibleType(String tenantId, String userCode, Integer visibleType);
    /**
     * 统计分组下的文件大小
     * @param groupIds 分组id列表
     * @return 文件大小
     */
    Long sumSizeByGroupId(List<Long> groupIds);
}