package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TemplateEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.ITemplateManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.StrategyTemplateMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Service
public class TemplateManagerImpl extends ServiceImpl<StrategyTemplateMapper, TemplateEntity> implements ITemplateManager {

}
