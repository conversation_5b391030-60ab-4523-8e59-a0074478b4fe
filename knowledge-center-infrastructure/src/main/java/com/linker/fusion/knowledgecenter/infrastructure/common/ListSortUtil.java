package com.linker.fusion.knowledgecenter.infrastructure.common;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.comparator.CompareUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ListSortUtil
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-03-25 11:07
 */
public class ListSortUtil {

    public static <T> List<T> sortListByField(List<T> list, String order, Class<T> tClass) {
        if (StringUtils.isNotBlank(order)) {
            List<String> sorts = Arrays.stream(order.split(" ")).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(sorts) && sorts.size() == 2) {
                list = ListSortUtil.sortListByField(list, sorts.get(0), "desc".equals(sorts.get(1)), false, tClass);
            }
        }
        return list;
    }


    /**
     * 自定义字段排序
     *
     * @param list
     * @param sortName
     * @param desc
     * @param descNull
     * @return
     */
    public static <T> List<T> sortListByField(List<T> list, String sortName, boolean desc, boolean descNull, Class<T> tClass) {
        return list.stream()
                .map(convertBeanToMap())
                .sorted(getMapComparator(sortName, desc, descNull))
                .map(convertMapToBean(tClass))
                .collect(Collectors.toList());
    }

    public static <T> List<?> sortListByField(List<T> list, String sortName, boolean desc, Class<T> tClass) {
        return sortListByField(list, sortName, desc, true, tClass);
    }

    private static Comparator<Map<String, Object>> getMapComparator(String sortName, boolean desc, boolean descNull) {
        if (desc) {
            return ((Comparator<Map<String, Object>>) (o1, o2) -> getCompareUtil(sortName, descNull, o1, o2)).reversed();
        }
        return (o1, o2) -> getCompareUtil(sortName, descNull, o1, o2);
    }

    /**
     * 比较方法
     *
     * @param sortName 排序字段
     * @param descNull null值排序规则
     * @param o1
     * @param o2
     * @return
     */
    private static int getCompareUtil(String sortName, boolean descNull, Map<String, Object> o1, Map<String, Object> o2) {
        return CompareUtil.compare(o1.get(sortName), o2.get(sortName), descNull);
    }


    private static <T> Function<Map<String, Object>, T> convertMapToBean(Class<T> tClass) {
        return map -> BeanUtil.toBean(map, tClass);
    }

    private static <T> Function<T, Map<String, Object>> convertBeanToMap() {
        return BeanUtil::beanToMap;
    }
}