package com.linker.fusion.knowledgecenter.infrastructure.client.person;

import com.linker.fusion.knowledgecenter.infrastructure.dto.data.VariableDTO;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * @Author: YangQiyan
 * @Description: 类说明
 * @Date: Created in 2025/7/18
 */
@Data
public class PromptResp {

    /**
     * 企业代码
     */
    private String tenantId;

    /**
     * 创建人名
     */
    private String creatorName;

    /**
     * 修改人名
     */
    private String updateName;

    /**
     * 名称
     */
    private String name;

    /**
     * 名称拼音
     */
    private String namePinyin;

    /**
     * 提示词key
     */
    private String promptKey;

    /**
     * 类别ID
     */
    private Long categoryId;

    /**
     * 类别名
     */
    private String categoryName;

    /**
     * 是否公开（1公开/0私有）
     */
    private Integer isPublic;

    /**
     * 状态（0禁用/1启用）
     */
    private Integer status;

    /**
     * 变量定义
     */
    private String variables;

    /**
     * 提示词内容
     */
    private String prompts;

    /**
     * 提示词内容
     */
    private List<PromptDto> promptInfos;

    /**
     * 变量定义
     */
    private List<VariableDTO> variableInfos;

    /**
     * 排序字段
     */
    private Integer sort;

    @Data
    public static class PromptDto {
        /**
         * 角色
         */
        private String role;

        /**
         * 内容
         */
        private String content;
    }


    @Data
    public static class PromptContentDto {
        /**
         * 角色
         */
        private String role;

        /**
         * 内容
         */
        private LinkedList<String> contents;
    }

}
