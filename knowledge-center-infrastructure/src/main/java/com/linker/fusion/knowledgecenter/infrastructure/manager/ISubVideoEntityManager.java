package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.SubVideoEntity;

import java.util.Collection;
import java.util.List;

public interface ISubVideoEntityManager extends IService<SubVideoEntity> {

    List<SubVideoEntity> listByDocId(String docId);

    void deleteByDocIds(List<String> docIds);

    void deleteByIds(Collection<Long> ids);

}
