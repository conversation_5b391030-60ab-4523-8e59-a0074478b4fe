package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.WordsEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IWordsManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.WordsFactoryMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 **/
@Repository
public class WordManagerImpl extends ServiceImpl<WordsFactoryMapper, WordsEntity> implements IWordsManager {
    @Override
    public void deleteByGroupIds(List<Long> groupIds) {
        this.update(new LambdaUpdateWrapper<WordsEntity>()
                .in(WordsEntity::getGroupId, groupIds)
                .set(WordsEntity::getDeleted, 1));
    }

    @Override
    public WordsEntity getByName(String tenantId, Integer type, String name) {
        List<WordsEntity> list= this.list(new LambdaQueryWrapper<WordsEntity>()
                .eq(WordsEntity::getTenantId, tenantId)
                .eq(WordsEntity::getType, type)
                .eq(WordsEntity::getName, name));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
