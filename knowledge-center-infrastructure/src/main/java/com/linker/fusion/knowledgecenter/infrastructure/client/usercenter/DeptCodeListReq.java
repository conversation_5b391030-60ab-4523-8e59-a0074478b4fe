package com.linker.fusion.knowledgecenter.infrastructure.client.usercenter;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeptCodeListReq {
    private String tenantId;
    private String departmentCode;
    private List<String> departmentCodeList;

    public DeptCodeListReq(String tenantId, List<String> departmentCodeList) {
        this.tenantId = tenantId;
        this.departmentCodeList = departmentCodeList;
    }
}
