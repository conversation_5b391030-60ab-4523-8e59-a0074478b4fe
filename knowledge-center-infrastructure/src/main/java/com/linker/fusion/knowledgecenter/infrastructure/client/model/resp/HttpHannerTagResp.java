package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import lombok.Data;

import java.util.List;

/**
 * 专名识别 返回对象
 */
@Data
public class HttpHannerTagResp {


    /**
     * 返回代码
     */
    private Integer code;

    /**
     * 调度中心信息
     */
    private Object schedulingCenter;

    /**
     * 主体信息
     */
    private HttpHannerTagBodyResp body;

    /**
     * 结果列表
     */
    private List<HttpHannerTagResultResp> results;

    /**
     * 是否成功
     */
    private Boolean isSuccess;

    /**
     * 主体信息类
     */
    @Data
    public static class HttpHannerTagBodyResp {
        /**
         * 耗时
         */
        private Integer took;

        /**
         * 消息
         */
        private String message;

        /**
         * 任务ID
         */
        private String taskId;
    }

    /**
     * 结果类
     */
    @Data
    public static class HttpHannerTagResultResp {
        /**
         * 命名实体识别结果
         */
        private List<List<Object>> ner;

        /**
         * 时间戳
         */
        private Integer timestamp;
    }
}
