package com.linker.fusion.knowledgecenter.infrastructure.dto;

import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025年09月18日
 * @version: 1.0
 * @description: TODO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeIndexTaskDTO implements Serializable {

    /**
     * 资源id
     */
    private Long knowledgeResourceId;

    /**
     * 下一个状态
     */
    private Integer nextStatus;

    /**
     * 工作流异步运行请求
     */
    private WorkflowAsyncRunReq workflowAsyncRunReq;
}
