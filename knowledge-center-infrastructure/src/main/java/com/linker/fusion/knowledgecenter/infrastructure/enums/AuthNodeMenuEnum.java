package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum AuthNodeMenuEnum {

    LIBRARY_MANAGER(0, "知识库管理"),
    GROUP_MANAGER(1, "分组管理"),
    RESOURCE_MANAGER(2, "文件管理"),
    TABLE_MANAGER(2, "表格管理"),
    FAQ_MANAGER(3, "问答对管理"),
    QUESTION_MANAGER(4, "题目管理");
    @Getter
    private final Integer value;
    @Getter
    private final String name;
}
