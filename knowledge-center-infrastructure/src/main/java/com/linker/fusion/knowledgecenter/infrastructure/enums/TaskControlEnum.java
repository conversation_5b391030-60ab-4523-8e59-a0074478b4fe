package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaskControlEnum {
    RESUME(1, "继续任务"),
    PAUSE(2, "暂停任务"),
    CANCEL(3, "取消任务");

    private final Integer code;
    private final String description;

    /**
     * 根据控制参数值获取对应的枚举实例
     *
     * @param code 控制参数值
     * @return 对应的枚举实例，若未找到则返回null
     */
    public static TaskControlEnum getByCode(Integer code) {
        for (TaskControlEnum taskControl : values()) {
            if (taskControl.code.equals(code)) {
                return taskControl;
            }
        }
        return null;
    }
}
