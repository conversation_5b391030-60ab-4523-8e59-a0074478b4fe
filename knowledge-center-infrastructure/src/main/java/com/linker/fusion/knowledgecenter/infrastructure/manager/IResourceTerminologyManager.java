package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceTerminologyEntity;

import java.util.List;


public interface IResourceTerminologyManager extends IService<ResourceTerminologyEntity> {

    List<ResourceTerminologyEntity> list(String docId, String segmentId);

    void clearByDocId(String docId);

    void clearByFileIds(List<String> fileIds);
}
