package com.linker.fusion.knowledgecenter.infrastructure.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件类型枚举
 */
@AllArgsConstructor
@Getter
public enum ResourceExtInfoTypeEnum {

    K2Status("sys_K2Status", "巡检结论状态")
    ;

    private final String type;

    private final String name;


    public static ResourceExtInfoTypeEnum valueOf(Integer value) {
        return ArrayUtil.firstMatch(s -> s.getType().equals(value), ResourceExtInfoTypeEnum.values());
    }

}
