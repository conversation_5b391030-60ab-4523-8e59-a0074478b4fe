package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum LibraryTypeEnum {

    File(1, "文件库"),
    Term(2, "专业"),
    Sensitive(3, "敏感");

    private final Integer value;

    private final String name;
    public Integer getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.name;
    }
}
