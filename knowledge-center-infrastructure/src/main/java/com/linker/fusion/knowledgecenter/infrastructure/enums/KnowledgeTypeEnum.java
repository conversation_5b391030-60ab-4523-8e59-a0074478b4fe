package com.linker.fusion.knowledgecenter.infrastructure.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 知识类型枚举
 */
@AllArgsConstructor
@Getter
public enum KnowledgeTypeEnum implements IntArrayValuable {

    // 1.4.4版本将文档、图片、视频、音频统一放在文件库
    FILE(1, "文件", "文件库"),

    FAQ(5, "FAQ", "FAQ库"),

    TABLE(6, "表格", "数据表库"),

    SENSITIVE(8, "敏感词", "敏感词库"),

    PRO(9, "专业词", "专业词库"),

    Other(7, "其他", "其他库"),

    QUESTION(10, "题库", "题库"),
    ;

    private final Integer type;

    private final String name;

    private final String pathName;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(KnowledgeTypeEnum::getType).toArray();

    public static KnowledgeTypeEnum valueOf(Integer value) {
        return ArrayUtil.firstMatch(s -> s.getType().equals(value), KnowledgeTypeEnum.values());
    }

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
