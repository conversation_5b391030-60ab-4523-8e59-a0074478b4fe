package com.linker.fusion.knowledgecenter.infrastructure.dto.data;


import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class VariableDTO {
    /**
     * 变量名称
     */

    private String name;

    /**
     * 变量类型，可以是 String、File、Image 等
     */
    private String type;

    /**
     * 赋值方式：input（输入）或 reference（引用）
     */
    private String assignMode;

    /**
     * 变量描述
     */
    private String desc;

    /**
     * 变量值
     */
    private Object value;
    public VariableDTO(String name,  Object value) {
        this.name = name;
        this.value = value;
    }
}