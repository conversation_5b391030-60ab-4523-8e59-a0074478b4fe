package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FeedbackSettingEntity;

public interface IFeedbackSettingManager extends IService<FeedbackSettingEntity> {

    default FeedbackSettingEntity getSetting(String tenantId, Long agentId) {
        return getOne(
                new LambdaQueryWrapper<>(FeedbackSettingEntity.class)
                        .eq(FeedbackSettingEntity::getTenantId, tenantId)
                        .eq(FeedbackSettingEntity::getAgentId, agentId)
                        .last("limit 1")
        );
    }
}
