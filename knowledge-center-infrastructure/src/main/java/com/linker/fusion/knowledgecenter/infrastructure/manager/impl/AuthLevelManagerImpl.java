package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AuthLevelEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IAuthLevelManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.AuthLevelMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 库实现类
 */
@Service
public class AuthLevelManagerImpl extends ServiceImpl<AuthLevelMapper, AuthLevelEntity> implements IAuthLevelManager {

    @Override
    public AuthLevelEntity get(String tenantId, String name, Integer level) {
        QueryWrapper<AuthLevelEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(AuthLevelEntity::getName, name)
                .ne(AuthLevelEntity::getLevel, level).
                and(inner -> inner.eq(AuthLevelEntity::getTenantId, tenantId).or().eq(AuthLevelEntity::getIsSystem, 1));
        ;
        List<AuthLevelEntity> list = list(queryWrapper);
        if (!list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<AuthLevelEntity> list(String tenantId) {
        QueryWrapper<AuthLevelEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AuthLevelEntity::getTenantId, tenantId)
                .or().eq(AuthLevelEntity::getIsSystem, 1);
        return list(queryWrapper);
    }
}
