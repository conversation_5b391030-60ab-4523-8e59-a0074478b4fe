
package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceODTagEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IResourceODTagManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.ResourceODTagMapper;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


@Service
public class ResourceODTagManagerImpl extends ServiceImpl<ResourceODTagMapper, ResourceODTagEntity> implements IResourceODTagManager {

    @Override
    public List<ResourceODTagEntity> list(String docId, String segmentId) {
        return list(
                new LambdaQueryWrapper<ResourceODTagEntity>()
                        .eq(ResourceODTagEntity::getDeleted, false)
                        .eq(StringUtils.isNotBlank(docId), ResourceODTagEntity::getDocId, docId)
                        .eq(StringUtils.isNotBlank(segmentId), ResourceODTagEntity::getSegmentId, segmentId)
        );
    }

    @Override
    public void deleteByDocId(String docId) {
        this.lambdaUpdate()
                .eq(ResourceODTagEntity::getDocId, docId)
                .set(ResourceODTagEntity::getDeleted, true)
                .update();
    }

    @Override
    public void clearByDocId(String docId) {
        QueryWrapper<ResourceODTagEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceODTagEntity::getDocId, docId);
        remove(wrapper);
    }

    @Override
    public void clearByDocIds(List<String> fileIds) {
        if (CollectionUtils.isEmpty(fileIds))
            return;
        QueryWrapper<ResourceODTagEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ResourceODTagEntity::getDocId, fileIds);
        remove(wrapper);
    }

    @Override
    public List<ResourceODTagEntity> listByResId(String docId, Integer type, String tagName) {
        QueryWrapper<ResourceODTagEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceODTagEntity::getDocId, docId);
        wrapper.lambda().eq(ResourceODTagEntity::getType, type);
        wrapper.lambda().eq(ResourceODTagEntity::getDeleted, false);
        wrapper.lambda().like(StringUtils.isNotBlank(tagName), ResourceODTagEntity::getName, StringComUtils.replaceSqlEsc(tagName));
        return list(wrapper);
    }

    @Override
    public void deleteByDocId_FrameId_Name(String docId, String frameId, List<String> tagNames) {
        UpdateWrapper<ResourceODTagEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(ResourceODTagEntity::getDocId, docId);
        wrapper.lambda().eq(ResourceODTagEntity::getFrameId, frameId);
        wrapper.lambda().in(ResourceODTagEntity::getName, tagNames);
        wrapper.lambda().set(ResourceODTagEntity::getDeleted, true);
        update(wrapper);
    }

    @Override
    public List<ResourceODTagEntity> listByResIds(List<String> resIds, Integer type, List<String> sampleIds) {
        QueryWrapper<ResourceODTagEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ResourceODTagEntity::getDocId, resIds);
        wrapper.lambda().eq(Objects.nonNull(type), ResourceODTagEntity::getType, type);
        wrapper.lambda().eq(ResourceODTagEntity::getDeleted, false);
        wrapper.lambda().in(CollectionUtils.isNotEmpty(sampleIds), ResourceODTagEntity::getSampleId, sampleIds);
        return list(wrapper);
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        UpdateWrapper<ResourceODTagEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(ResourceODTagEntity::getId, ids);
        wrapper.lambda().set(ResourceODTagEntity::getDeleted, true);
        update(wrapper);
    }

    /**
     * @description: 查询事件列表
     * @author: hejianbao
     * @date: 2025/7/25 09:12
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    @Override
    public List<ResourceODTagEntity> listResourceOdTag(List<String> docIdList, String name, Integer type) {
        LambdaQueryWrapper<ResourceODTagEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(docIdList), ResourceODTagEntity::getDocId, docIdList)
                .like(StringUtils.isNotBlank(name), ResourceODTagEntity::getName, name)
                .eq(Objects.nonNull(type), ResourceODTagEntity::getType, type)
                .orderByDesc(ResourceODTagEntity::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public IPage<ResourceODTagEntity> pageResourceOdTag(List<String> docIdList, String name, Integer type, IPage<ResourceODTagEntity> page) {

        LambdaQueryWrapper<ResourceODTagEntity> queryWrapper = new LambdaQueryWrapper<ResourceODTagEntity>()
                .select(ResourceODTagEntity::getName)
                .in(CollectionUtils.isNotEmpty(docIdList), ResourceODTagEntity::getDocId, docIdList)
                .like(StringUtils.isNotBlank(name), ResourceODTagEntity::getName, name)
                .ne(ResourceODTagEntity::getName, "")
                .eq(Objects.nonNull(type), ResourceODTagEntity::getType, type)
                .orderByDesc(ResourceODTagEntity::getCreateTime);
        queryWrapper.groupBy(ResourceODTagEntity::getName);

        return baseMapper.selectPage(page, queryWrapper);
    }
}
