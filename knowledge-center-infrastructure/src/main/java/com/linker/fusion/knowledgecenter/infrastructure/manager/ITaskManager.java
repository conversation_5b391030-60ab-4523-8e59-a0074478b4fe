package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;

import java.util.List;

public interface ITaskManager extends IService<TaskEntity> {
    void updateReadStatus(Long groupId, String tenantId, String creatorId);

    void updateStatusToFail(String tenantId, String creatorId, String key);

    List<TaskEntity> listUnread(String tenantId, String userCode);

    TaskEntity getLastRun(String tenantId, String userCode, Long groupId);

    TaskEntity getByKey(String key);

    List<TaskEntity> listBykeys(List<String> keys);
}
