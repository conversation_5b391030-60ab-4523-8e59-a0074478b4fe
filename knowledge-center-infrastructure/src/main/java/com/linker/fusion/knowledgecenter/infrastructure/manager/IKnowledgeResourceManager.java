package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceVisibleTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.ResourceStaModel;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


public interface IKnowledgeResourceManager extends IService<KnowledgeResourceEntity> {
    KnowledgeResourceEntity getByResId(String docId);

    Boolean isExistTitle(String title, String tenantId, String creatorId);

    Boolean isExistTitle(Long id, String title, String tenantId, String creatorId);

    List<String> getAllUserId();

    List<KnowledgeResourceEntity> getListByIds(List<Long> ids);

    List<KnowledgeResourceEntity> listByResIds(List<String> docIds);

    List<KnowledgeResourceEntity> listByTemplateId(Integer limit, List<Long> templateIds, Long lastId);

    List<String> listDocIds(String tenantId, KnowledgeTypeEnum type, List<Long> groupIds, String groupPath);

    void updateGroupId(List<Long> ids, Long groupId, String groupPath);

    void updateCount(String docId, Integer count);

    void updateExtInfo(String docId, String extInfo);

    void updateTitle(Long id, String title);

    void updateStatus(List<Long> ids, ProcessEnum status);

    Long getCount(String tenantId);

    Long getCount(Long groupId);

    Long getTempCount(String tenantId);

    List<KnowledgeResourceEntity> listByGroupId(String tenantId, String userCode, int groupId);

    List<KnowledgeResourceEntity> listByGroupIds(String tenantId, List<Long> groupIds);

    List<KnowledgeResourceEntity> search(String tenantId, Integer type, String keyword, List<Long> groupIds);

    void deleteByGroupIds(List<Long> groupIds);


    List<KnowledgeResourceEntity> listByGroupIds(List<Long> groupIds);

    Long getCount(String tenantId, List<FileTypeEnum> fileType, List<ProcessEnum> statusList, LocalDateTime startTime);

    Long sumSize(String tenantId, FileTypeEnum fileType);

    List<ResourceStaModel> staByLib(List<String> pathList, Integer fileType);

    void logicalDelete(List<Long> ids);

    void logicalDeleteByResIds(List<String> resIds);

    void updateSize(String docId, Long size);

    List<KnowledgeResourceEntity> list(FileTypeEnum type, Long groupId);

    List<KnowledgeResourceEntity> list(String tenantId);

    List<KnowledgeResourceEntity> top(String tenantId, Boolean enable, ProcessEnum status, FileTypeEnum fileType, Integer size);

    void updateGroupIdByDocIds(List<String> resIds, Long groupId);

    void updateEnable(List<Long> ids, Boolean enable);

    KnowledgeResourceEntity getByGroupId_Title_Suffix(Long groupId, String title, String suffix, Boolean delete);


    void updateStrategy(String docId, String strategy);

    void updateByWorkflowId(String workflowId, ProcessEnum status);

    List<KnowledgeResourceEntity> listSampleResource(Long sampleGroupId, Integer type, Integer handleStatus);

    KnowledgeResourceEntity getTestImage();

    Long sumSizeByVisibleType(String tenantId, String userCode, ResourceVisibleTypeEnum resourceVisibleTypeEnum);

    List<KnowledgeResourceEntity> listByGroupIdsAndFileType(List<Long> groupIds, List<Integer> types, Integer status);

    Long sumSizeByGroupId(List<Long> groupIds);

    List<KnowledgeResourceEntity> getFileFromTimeRange(ArrayList<Integer> status, LocalDateTime startTime, LocalDateTime endTime);
}