package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OdTagTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@TableName("t_resource_od_tag")
@Accessors(chain = true)
public class ResourceODTagEntity extends BaseEntity {
    /**
     * 分段id
     */
    @TableField("segment_id")
    private String segmentId;
    /**
     * 文档id
     */
    @TableField("doc_id")
    private String docId;
    /**
     * 镜头id
     */
    @TableField("frame_id")
    private String frameId;

    /**
     * 标签类型
     * {@link OdTagTypeEnum}
     */
    @TableField("`type`")
    private Integer type;
    /**
     * 标签名称
     */
    @TableField("name")
    private String name;
    /**
     * 位置
     */
    @TableField("position")
    private String position;
    /**
     * 位置
     */
    @TableField("time_point")
    private Long timePoint;

    /**
     * 样本ID 例如特征库中的特征ID
     */
    @TableField("sample_id")
    private String sampleId;
    /**
     * 扩展信息
     */
    @TableField("ext_info")
    private String extInfo;
}
