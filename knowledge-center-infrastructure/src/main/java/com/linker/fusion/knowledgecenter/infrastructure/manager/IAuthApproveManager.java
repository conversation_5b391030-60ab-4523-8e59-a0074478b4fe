package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AuthApproveEntity;

import java.util.List;

/**
 * <p>
 * 申请列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface IAuthApproveManager extends IService<AuthApproveEntity> {

    AuthApproveEntity getByInstanceId(String instanceId);

    boolean exist(String tenantId, String userCode, Integer sourceType, String sourceId);

    List<Long> listAllIngIds(String tenantId, String userCode, Integer sourceType);

}
