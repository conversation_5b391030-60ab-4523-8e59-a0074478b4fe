package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceSegmentEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IResourceSegmentManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.ResourceSegmentMapper;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Service
public class ResourceSegmentManagerImpl extends ServiceImpl<ResourceSegmentMapper, ResourceSegmentEntity> implements IResourceSegmentManager {


    @Override
    public Page<ResourceSegmentEntity> page(long page, long pageSize, String docId, FileTypeEnum fileTypeEnum, String content, List<Integer> status) {
        String searchContent = null;
        if (StringUtils.isNotEmpty(content)) {
            searchContent = StringComUtils.replaceSqlEsc(content);
//            if (fileTypeEnum.equals(FileTypeEnum.VIDEO))
//                searchContent = "\"summary\": %" + searchContent+"%\"";
        }

        QueryWrapper<ResourceSegmentEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceSegmentEntity::getDocId, docId)
                .eq(ResourceSegmentEntity::getDeleted, false)
                .like(ObjectUtil.isNotEmpty(searchContent), ResourceSegmentEntity::getContent, searchContent)
                .in(CollectionUtils.isNotEmpty(status), ResourceSegmentEntity::getStatus, status)
                .orderByAsc(ResourceSegmentEntity::getSort)
        ;
        return page(new Page<>(page, pageSize), wrapper);
//        if (StringUtils.isNotBlank(content)) {
//            content = "%" + StringComUtils.replaceSqlEsc(content) + "%";
//        }
//        return baseMapper.page(docId, content, status, new Page<>(page, pageSize));
    }

    @Override
    public ResourceSegmentEntity get(String segmentId) {
        QueryWrapper<ResourceSegmentEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceSegmentEntity::getSegmentId, segmentId)
                .eq(ResourceSegmentEntity::getDeleted, false)
        ;
        List<ResourceSegmentEntity> list= list(wrapper);
        if(list.size()>0) return list.get(0);
        return null;

    }

    @Override
    public void deleteByDocId(String docId) {
        this.lambdaUpdate()
                .eq(ResourceSegmentEntity::getDocId, docId)
                .set(ResourceSegmentEntity::getDeleted, true)
                .update();
    }

    @Override
    public void deleteBySegmentId(String segmentId) {
        this.lambdaUpdate()
                .eq(ResourceSegmentEntity::getSegmentId, segmentId)
                .set(ResourceSegmentEntity::getDeleted, true)
                .update();
    }

    @Override
    public void deleteBySegmentIds(List<String> segmentIds) {
        this.lambdaUpdate()
                .in(ResourceSegmentEntity::getSegmentId, segmentIds)
                .set(ResourceSegmentEntity::getDeleted, true)
                .update();
    }

    @Override
    public void clearByDocId(String docId) {
        QueryWrapper<ResourceSegmentEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceSegmentEntity::getDocId, docId);
        remove(wrapper);
    }

    @Override
    public List<ResourceSegmentEntity> listByDocId(String docId) {
        return lambdaQuery().eq(ResourceSegmentEntity::getDocId, docId)
                .eq(ResourceSegmentEntity::getDeleted, false).list();
    }

    @Override
    public List<ResourceSegmentEntity> listByDocIds(List<String> docIds) {
        if (CollectionUtils.isEmpty(docIds))
            return new ArrayList<>();
        return lambdaQuery().in(ResourceSegmentEntity::getDocId, docIds)
                .eq(ResourceSegmentEntity::getDeleted, false).list();
    }

    @Override
    public List<ResourceSegmentEntity> listByDocId(String docId, Integer skip, Integer take) {
        QueryWrapper<ResourceSegmentEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceSegmentEntity::getDocId, docId);
        wrapper.lambda().eq(ResourceSegmentEntity::getDeleted, false);
        wrapper.lambda().orderByAsc(ResourceSegmentEntity::getSort);
        wrapper.last(" limit " + take + " offset " + skip);


        return list(wrapper);
    }

    @Override
    public Double maxSort(String docId) {
        return baseMapper.maxSort(docId);
    }

    @Override
    public List<ResourceSegmentEntity> listRangeByDocId(String docId, Double min, Double max) {
        QueryWrapper<ResourceSegmentEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceSegmentEntity::getDocId, docId);
        wrapper.lambda().eq(ResourceSegmentEntity::getDeleted, false);
        wrapper.lambda().le(Objects.nonNull(max), ResourceSegmentEntity::getSort, max);
        wrapper.lambda().ge(Objects.nonNull(min), ResourceSegmentEntity::getSort, min);
        wrapper.lambda().orderByAsc(ResourceSegmentEntity::getSort);
        return list(wrapper);
    }

    @Override
    public ResourceSegmentEntity getPre(String docId, Double min) {
        QueryWrapper<ResourceSegmentEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceSegmentEntity::getDocId, docId);
        wrapper.lambda().eq(ResourceSegmentEntity::getDeleted, false);
        wrapper.lambda().lt(Objects.nonNull(min), ResourceSegmentEntity::getSort, min);
        wrapper.lambda().orderByDesc(ResourceSegmentEntity::getSort);
        wrapper.last(" limit 1");
        List<ResourceSegmentEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list))
            return null;
        return list.get(0);
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        this.lambdaUpdate()
                .eq(ResourceSegmentEntity::getId, id)
                .set(ResourceSegmentEntity::getStatus, status)
                .update();
    }
    @Override
    public void updateStatus(String segId, Integer status) {
        this.lambdaUpdate()
                .eq(ResourceSegmentEntity::getSegmentId, segId)
                .set(ResourceSegmentEntity::getStatus, status)
                .update();
    }

    @Override
    public void updateThumbnail(String segmentId, String url) {
        this.lambdaUpdate()
                .eq(ResourceSegmentEntity::getSegmentId, segmentId)
                .set(ResourceSegmentEntity::getThumbnail, url)
                .update();
    }

    @Override
    public List<ResourceSegmentEntity> listBySegmentIds(List<String> segmentIds) {
        return lambdaQuery().in(ResourceSegmentEntity::getSegmentId, segmentIds)
                .eq(ResourceSegmentEntity::getDeleted, false).list();
    }
}
