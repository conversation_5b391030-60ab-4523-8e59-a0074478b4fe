package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.NpcSearchLogEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.INpcSearchLogManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.NpcSearchLogMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 人大视频搜索记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Service
public class NpcSearchLogManagerImpl extends ServiceImpl<NpcSearchLogMapper, NpcSearchLogEntity> implements INpcSearchLogManager {

    @Override
    public List<NpcSearchLogEntity> list(String tenantId, String userCode, Integer searchType, LocalDateTime startTime, Integer limit) {
        return list(
                new LambdaQueryWrapper<>(NpcSearchLogEntity.class)
                        .eq(NpcSearchLogEntity::getTenantId, tenantId)
                        .eq(StringUtils.isNotBlank(userCode), NpcSearchLogEntity::getCreatorId, userCode)
                        .eq(searchType != null, NpcSearchLogEntity::getSearchType, searchType)
                        .ge(startTime != null, NpcSearchLogEntity::getCreateTime, startTime)
                        .orderByDesc(NpcSearchLogEntity::getCreateTime)
                        .last(limit != null, "limit " + limit)
        );
    }
}
