package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import com.linker.fusion.knowledgecenter.infrastructure.config.BGEPropConfig;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class ImageEncodeReq {
    private String model_id = BGEPropConfig.ImageModelId;
    private List<String> image;
    /**
     * base64 | url
     */
    private String src_type;

    public ImageEncodeReq(List<String> images, String srcType) {
        image = images;

        src_type = srcType;
    }
}
