package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.TabDisplayUseCustomer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@TableName(value = "t_knowledge_resource", autoResultMap = true)
public class KnowledgeResourceEntity extends BaseEntity {

    /**
     * docId
     */
    @TableField("doc_id")
    private String docId;

    /**
     * workFlowId
     */
    @TableField("work_flow_id")
    private String workflowId;

    /**
     * 知识类型 1-文档 2-图片 3-视频 4-音频 6-表格 7-其他
     * <p>
     * {@link FileTypeEnum}
     */
    @TableField("type")
    private Integer type;

    /**
     * 组ID
     */
    @TableField("group_id")
    private Long groupId;

    /**
     * 分组路径
     */
    @TableField("group_path")
    private String groupPath;

    /**
     * 资源名称
     */
    @TableField("title")
    private String title;

    /**
     * 副标题
     */
    @TableField("subheading")
    private String subheading;

    /**
     * 文件后缀
     */
    @TableField("suffix")
    private String suffix;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 资源地址
     */
    @TableField("url")
    private String url;

    /**
     * 预览地址
     */
    @TableField("preview_src")
    private String previewSrc;

    /**
     * 是否已去除水印 -> 0：否、1：是，清洗规则开启去除水印、且上传的文档类型是PDF、且成功去除了水印
     */
    @TableField("clean_watermark_flag")
    private Boolean cleanWatermarkFlag;

    /**
     * 去除水印的url，仅切分和分段维护时使用
     */
    @TableField("clean_watermark_url")
    private String cleanWatermarkUrl;

    /**
     * 文件资源大小（byte）
     */
    @TableField("size")
    private Long size;

    /**
     * 文档：页数；表格：行数
     */
    @TableField("count")
    private Long count;

    /**
     * 1：学习中、2：学习成功、3：学习失败
     * {@link ProcessEnum}
     */
    @TableField("handle_status")
    private Integer handleStatus;

    /**
     * 处理失败原因
     */
    @TableField(value = "handle_fail_reason", typeHandler = FastjsonTypeHandler.class)
    private HandleFailReason handleFailReason;

    /**
     * 是否生效 -> 0：否、1：是
     */
    @TableField("enable")
    private Boolean enable;

    /**
     * 扩展信息字段json
     */
    @TableField("extInfo")
    private String extInfo;

    /**
     * 策略信息
     */
    @TableField("strategy")
    private String strategy;

    /**
     * 模版ID
     */
    @TableField("template_id")
    private Long templateId;
    /**
     * 封面地址
     */
    private String thumbnail;
    /**
     * 文件全名
     */
    @TableField(exist = false)
    private String fileName;

    @TableField(exist = false)
    private List<TabDisplayUseCustomer> tabDisplayUseCustomers;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HandleFailReason {

        private Integer code;

        private String message;

        private Long costTime;

        public HandleFailReason(Integer code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    public String getFileName() {
        if (StringUtils.isBlank(fileName)) {
            fileName = title + "." + suffix;
        }
        return fileName;
    }

    /**
     * 获取json格式的扩展字段
     */
    public JSONObject getJsonExtInfo() {

        if (StringUtils.isNotBlank(extInfo))
            return JSONObject.parseObject(extInfo);
        return new JSONObject();
    }

    public boolean isMerge() {
        return getJsonExtInfo().containsKey("system_merge");
    }
}