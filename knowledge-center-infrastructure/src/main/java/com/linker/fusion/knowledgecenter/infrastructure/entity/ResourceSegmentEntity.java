package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linker.fusion.knowledgecenter.infrastructure.dto.KnowledgeResourceDTO;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@TableName("t_resource_segment")
@Accessors(chain = true)
public class ResourceSegmentEntity extends BaseEntity {
    /**
     * 分段Id
     */
    @TableField("segment_id")
    private String segmentId;
    /**
     * 文档类型
     */
    @TableField("file_type")
    private Integer fileType;
    /**
     * 文档Id
     */
    @TableField("doc_id")
    private String docId;
    /**
     * 分段标题
     */
    @TableField("title")
    private String title;
    /**
     * 分段内容
     */
    @TableField("content")
    private String content;
    /**
     * 排序
     */
    @TableField("sort")
    private Double sort;
    @TableField("number")
    private Integer number;
    /**
     * 其实页码
     */
    @TableField("page")
    private Integer page;
    /**
     * 位置信息
     */
    @TableField("position")
    private String position;
    /**
     * 开始时间
     */
    @TableField("start_timestamp")
    private Long startTimestamp;
    /**
     * 结束时间
     */
    @TableField("end_timestamp")
    private Long endTimestamp;
    /**
     * 学习状态
     */
    @TableField("status")
    private Integer status;
    /**
     * 扩展字段
     */
    @TableField("ext")
    private String ext;

    /**
     * 缩略图
     */
    @TableField("thumbnail")
    private String thumbnail;

    public static ResourceSegmentEntity create(KnowledgeResourceDTO resource) {
        ResourceSegmentEntity segment = new ResourceSegmentEntity();
        segment.setPage(0);
        segment.setTitle("");
        segment.setContent("");
        segment.setPosition("[]");
        segment.setCreateTime(LocalDateTime.now());
        segment.setSegmentId(UUID.randomUUID().toString());
        segment.setCreatorId(resource.getCreatorId());
        segment.setTenantId(resource.getTenantId());
        segment.setDocId(resource.getDocId());
        segment.setStatus(ProcessEnum.Success.getValue());
        return segment;
    }
}
