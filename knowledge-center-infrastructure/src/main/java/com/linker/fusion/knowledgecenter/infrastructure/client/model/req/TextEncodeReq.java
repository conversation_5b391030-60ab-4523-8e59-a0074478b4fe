package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import com.linker.fusion.knowledgecenter.infrastructure.config.BGEPropConfig;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class TextEncodeReq {
    private String model_id = BGEPropConfig.TextModelId;

    private List<String> text;

    public TextEncodeReq(List<String> text) {
        this.text = text;
    }
}
