package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.IndexTenantMappingEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;

public interface IIndexTenantMappingManager extends IService<IndexTenantMappingEntity> {

    /**
     * 获取对应索引（不存在即添加）
     *
     * @param tenantId
     * @param indexPrefixEnum
     * @return
     */
    String getIndex(String tenantId, IndexPrefixEnum indexPrefixEnum);


    /**
     * 获取读的索引
     * @param tenantId
     * @param indexPrefixEnum
     * @param isTemplate
     * @return
     */
    String getIndex(String tenantId, IndexPrefixEnum indexPrefixEnum, boolean isTemplate, boolean write);
}
