package com.linker.fusion.knowledgecenter.infrastructure.enums;

import com.linker.fusion.knowledgecenter.infrastructure.model.AuthMenuModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.AuthNodeModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@AllArgsConstructor
public enum AuthNodeEnum {
    LIBRARY_SET(1, "设置知识库", AuthNodeMenuEnum.LIBRARY_MANAGER, false, true),
    LIBRARY_ADD_GROUP(2, "添加目录", AuthNodeMenuEnum.LIBRARY_MANAGER, false, false),
    GROUP_AUTH(1, "权限管理", AuthNodeMenuEnum.GROUP_MANAGER, false, true),
    GROUP_RENAME(2, "重命名", AuthNodeMenuEnum.GROUP_MANAGER, false, false),
    GROUP_DELETE(3, "删除", AuthNodeMenuEnum.GROUP_MANAGER, false, true),
    GROUP_ADD_SUB(4, "添加子目录", AuthNodeMenuEnum.GROUP_MANAGER, false, false),
    GROUP_MOVE(5, "移动目录", AuthNodeMenuEnum.GROUP_MANAGER, false, false),
    RESOURCE_PREVIEW(1, "文件预览", AuthNodeMenuEnum.RESOURCE_MANAGER, true, true),
    RESOURCE_IMPORT(2, "导入", AuthNodeMenuEnum.RESOURCE_MANAGER, false, false),
    RESOURCE_EDIT(3, "编辑文件", AuthNodeMenuEnum.RESOURCE_MANAGER, false, true),
    RESOURCE_DOWNLOAD(4, "下载", AuthNodeMenuEnum.RESOURCE_MANAGER, false, true),
    RESOURCE_DELETE(5, "删除", AuthNodeMenuEnum.RESOURCE_MANAGER, false, false),
    RESOURCE_ENABLE(6, "失效/生效", AuthNodeMenuEnum.RESOURCE_MANAGER, false, true),
    RESOURCE_AUTH(7, "权限管理", AuthNodeMenuEnum.RESOURCE_MANAGER, false, true),
    RESOURCE_REDO(8, "重新学习", AuthNodeMenuEnum.RESOURCE_MANAGER, false, true),
    RESOURCE_MOVE(9, "移动文件", AuthNodeMenuEnum.RESOURCE_MANAGER, false, false),
    RESOURCE_MAINTENANCE(10, "知识维护", AuthNodeMenuEnum.RESOURCE_MANAGER, false, true),
    RESOURCE_COPY(11, "共享文件到", AuthNodeMenuEnum.RESOURCE_MANAGER, false, false),
    RESOURCE_RENAME(12, "重命名", AuthNodeMenuEnum.RESOURCE_MANAGER, false, false),
    RESOURCE_SAVE_PART(13, "保存分段", AuthNodeMenuEnum.RESOURCE_MANAGER, false, true),
    RESOURCE_ARCHIVE(14, "归档", AuthNodeMenuEnum.RESOURCE_MANAGER, false, true),
    RESOURCE_MERGE_SPLIT(15, "合并/拆分视频", AuthNodeMenuEnum.RESOURCE_MANAGER, false, false),
    TABLE_PREVIEW(1, "表格预览", AuthNodeMenuEnum.TABLE_MANAGER, true, false),
    TABLE_IMPORT(2, "导入", AuthNodeMenuEnum.TABLE_MANAGER, false, false),
    TABLE_EDIT(3, "编辑基本信息", AuthNodeMenuEnum.TABLE_MANAGER, false, false),
    TABLE_DOWNLOAD(4, "下载", AuthNodeMenuEnum.TABLE_MANAGER, false, false),
    TABLE_DELETE(5, "删除", AuthNodeMenuEnum.TABLE_MANAGER, false, false),
    TABLE_ENABLE(6, "失效/生效", AuthNodeMenuEnum.TABLE_MANAGER, false, false),
    TABLE_AUTH(7, "权限管理", AuthNodeMenuEnum.TABLE_MANAGER, false, false),
    TABLE_REDO(8, "重新学习", AuthNodeMenuEnum.TABLE_MANAGER, false, false),
    TABLE_EDIT_SEG(9, "编辑/删除切片分段", AuthNodeMenuEnum.TABLE_MANAGER, false, false),
    TABLE_MOVE(10, "移动表格", AuthNodeMenuEnum.TABLE_MANAGER, false, false),
    FAQ_PREVIEW(1, "列表预览", AuthNodeMenuEnum.FAQ_MANAGER, true, false),
    FAQ_CREATE(2, "新建问答对", AuthNodeMenuEnum.FAQ_MANAGER, false, false),
    FAQ_EDIT(3, "编辑", AuthNodeMenuEnum.FAQ_MANAGER, false, false),
    FAQ_EXPORT(4, "导出", AuthNodeMenuEnum.FAQ_MANAGER, false, false),
    FAQ_DELETE(5, "删除", AuthNodeMenuEnum.FAQ_MANAGER, false, false),
    FAQ_MOVE(6, "移动问答对", AuthNodeMenuEnum.FAQ_MANAGER, false, false),
    FAQ_ENABLE(7, "失效/生效", AuthNodeMenuEnum.FAQ_MANAGER, false, false),
    QUESTION_PREVIEW(1, "列表预览", AuthNodeMenuEnum.QUESTION_MANAGER, true, false),
    QUESTION_CREATE(2, "新建题目", AuthNodeMenuEnum.QUESTION_MANAGER, false, false),
    QUESTION_EDIT(3, "编辑", AuthNodeMenuEnum.QUESTION_MANAGER, false, false),
    QUESTION_DELETE(4, "删除", AuthNodeMenuEnum.QUESTION_MANAGER, false, false),
    QUESTION_MOVE(5, "移动题目", AuthNodeMenuEnum.QUESTION_MANAGER, false, false),
    QUESTION_ENABLE(6, "失效/生效", AuthNodeMenuEnum.QUESTION_MANAGER, false, false),
    ;
    @Getter
    private final Integer sort;
    @Getter
    private final String name;
    @Getter
    private final AuthNodeMenuEnum menu;
    @Getter
    private final Boolean selected;
    /**
     * 同步目录是否可操作
     */
    @Getter
    private final Boolean sync;

    public static List<AuthMenuModel> getList() {
        List<AuthMenuModel> list = new ArrayList<>();
        List<AuthNodeEnum> nodeEnums = Arrays.stream(AuthNodeEnum.values()).collect(Collectors.toList());
        for (AuthNodeMenuEnum authNodeMenu : AuthNodeMenuEnum.values()) {
            AuthMenuModel authMenuModel = new AuthMenuModel();
            authMenuModel.setName(authNodeMenu.getName());
            List<AuthNodeModel> authNodes = nodeEnums.stream()
                    .filter(n -> n.getMenu().equals(authNodeMenu))
                    .sorted(Comparator.comparing(AuthNodeEnum::getSort))
                    .map(n -> new AuthNodeModel(n.toString(), n.getName(), n.getSelected())).collect(Collectors.toList());
            authMenuModel.setNodes(authNodes);
            list.add(authMenuModel);
        }
        return list;
    }

    /**
     * 同步目录编码
     *
     * @return
     */

    public static List<String> syncCodes() {
        return Arrays.stream(AuthNodeEnum.values()).filter(AuthNodeEnum::getSync).map(Enum::toString).collect(Collectors.toList());
    }

    /**
     * 获取新建权限
     *
     * @param type 类型
     * @return
     */
    public static AuthNodeEnum getCreateNode(KnowledgeTypeEnum type) {
        switch (type) {
            case FILE:
                return RESOURCE_IMPORT;
            case TABLE:
                return TABLE_IMPORT;
            case FAQ:
                return FAQ_CREATE;
            case QUESTION:
                return QUESTION_CREATE;
        }
        return RESOURCE_IMPORT;
    }
}
