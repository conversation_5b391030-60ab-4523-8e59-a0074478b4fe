package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourcePersonEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IResourcePersonManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.ResourcePersonMapper;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ResourcePersonManagerImpl extends ServiceImpl<ResourcePersonMapper, ResourcePersonEntity> implements IResourcePersonManager {

    @Override
    public List<ResourcePersonEntity> list(String docId) {
        QueryWrapper<ResourcePersonEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourcePersonEntity::getDocId, docId);
        wrapper.lambda().eq(ResourcePersonEntity::getDeleted, false);
        return list(wrapper);
    }

    @Override
    public void deleteByDocId(String docId) {
        this.lambdaUpdate()
                .eq(ResourcePersonEntity::getDocId, docId)
                .set(ResourcePersonEntity::getDeleted, true)
                .update();
    }

    @Override
    public void clearByDocId(String docId) {
        QueryWrapper<ResourcePersonEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourcePersonEntity::getDocId, docId);
        remove(wrapper);
    }
    
}
