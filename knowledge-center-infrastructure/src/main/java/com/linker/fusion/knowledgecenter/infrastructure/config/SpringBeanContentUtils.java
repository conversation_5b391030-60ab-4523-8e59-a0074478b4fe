package com.linker.fusion.knowledgecenter.infrastructure.config;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 **/
@Component
public class SpringBeanContentUtils implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public static <T> T getBean(Class<T> beanClass,String name) {
        if (applicationContext != null) {
            return applicationContext.getBean(name,beanClass);
        } else {
            throw new IllegalStateException("ApplicationContext is not initialized yet!");
        }
    }
}
