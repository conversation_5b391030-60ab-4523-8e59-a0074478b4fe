package com.linker.fusion.knowledgecenter.infrastructure.client.usercenter;

import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.core.BaseClientResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.MenuItem;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.MenuReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.UserBaseInfoReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.UserSearchByIdsReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.UserBaseInfoResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.UserInfoResp;
import com.linker.user.api.req.PowerRecordInfoReq;
import com.linker.user.api.resp.PowerRecordResp;
import org.springframework.stereotype.Service;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;

import java.util.List;

/**
 * 用户中台服务
 */
@RetrofitClient(baseUrl = "${user.auth.endpoint}", sourceOkHttpClient = "okHttpClientKnowledge")
@Service
@Intercept(handler = UserApiSignInterceptor.class)
public interface IUserCenter {

    @POST("menuPoint/getMenuListByUser")
    @Cached(expire = 3, cacheType = CacheType.LOCAL)
    @CachePenetrationProtect
    BaseResp<List<MenuItem>> menus(@Header("token") String token, @Body MenuReq req);

    @POST("user/getUserByCode")
    @Cached(expire = 3, cacheType = CacheType.LOCAL)
    @CachePenetrationProtect
    BaseClientResp<List<UserInfoResp>> getUserByCode(@Body UserSearchByIdsReq req);
    /**
     * 根据租户id查询用户信息和租户信息
     */
    @POST("user/getTenantAdmin")
    BaseResp<UserBaseInfoResp> getUserInfoByTenantId(@Body UserBaseInfoReq req);

    /**
     * 获取部门信息
     *
     * @param req 请求体
     * @return 包含用户信息的部门列表
     */
    @POST("department/listUser")
    BaseClientResp<List<DepartmentResp>> getDepartments(@Header("token") String token, @Body DepartmentReq req);

    /**
     * 获取租户部门
     *
     * @param token token
     * @param req   当前所属部门列表
     * @return 部门列表
     */
    @POST("department/getDeptCodeList")
    @Cached(expire = 300, cacheType = CacheType.LOCAL)
    @CachePenetrationProtect
    BaseClientResp<List<String>> getDeptCodeList(@Header("token") String token, @Body DeptCodeListReq req);

    /**
     * 获取租户权益
     * @param req
     * @return
     */
    @POST("powerRecord/getInfoByReq")
    @Cached(expire = 3, cacheType = CacheType.LOCAL)
    @CachePenetrationProtect
    BaseClientResp<PowerRecordResp> getPowerRecord(@Body PowerRecordInfoReq req);

}
