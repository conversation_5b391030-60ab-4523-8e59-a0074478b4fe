package com.linker.fusion.knowledgecenter.infrastructure.constants;

import com.linker.fusion.knowledgecenter.infrastructure.enums.OprationLogRecordSubTypeEnum;

/**
 * 操作日志子类型常量类
 * <AUTHOR>
 */
public final class LogRecordSubType {

    /**
     * 子类型常量
     * @see OprationLogRecordSubTypeEnum
     */

    /* 文件管理类操作 */
    /** 导入文件操作 */
    public static final String IMPORT_FILE = "IMPORT_FILE";
    /** 编辑文件操作 */
    public static final String EDIT_FILE = "EDIT_FILE";
    /** 下载文件操作 */
    public static final String DOWNLOAD_FILE = "DOWNLOAD_FILE";
    /** 申请下载操作 */
    public static final String REQUEST_DOWNLOAD = "REQUEST_DOWNLOAD";
    /** 删除文件操作 */
    public static final String DELETE_FILE = "DELETE_FILE";
    /** 文件生效操作 */
    public static final String ACTIVATE = "ACTIVATE";
    /** 文件失效操作 */
    public static final String DEACTIVATE = "DEACTIVATE";
    /** 重新学习操作 */
    public static final String RELEARN = "RELEARN";
    /** 移动文件操作 */
    public static final String MOVE_FILE = "MOVE_FILE";
    /** 预览文件操作 */
    public static final String PREVIEW_FILE = "PREVIEW_FILE";
    /** 共享文件操作 */
    public static final String SHARE_FILE = "SHARE_FILE";
    /** 重命名文件操作 */
    public static final String RENAME_FILE = "RENAME_FILE";
    /** 保存片段操作 */
    public static final String SAVE_FRAGMENT = "SAVE_FRAGMENT";
    /** 归档操作 */
    public static final String ARCHIVE = "ARCHIVE";
    /** 增删改分段分块操作 */
    public static final String MODIFY_SEGMENTS = "MODIFY_SEGMENTS";
    /** 下载片段操作 */
    public static final String DOWNLOAD_FRAGMENT = "DOWNLOAD_FRAGMENT";

    /* 知识库管理类操作 */
    /** 创建知识库操作 */
    public static final String CREATE_KNOWLEDGE_BASE = "CREATE_KNOWLEDGE_BASE";
    /** 删除知识库操作 */
    public static final String DELETE_KNOWLEDGE_BASE = "DELETE_KNOWLEDGE_BASE";
    /** 添加目录操作 */
    public static final String ADD_DIRECTORY = "ADD_DIRECTORY";
    /** 修改知识库基本信息操作 */
    public static final String UPDATE_BASE_INFO = "UPDATE_BASE_INFO";
    /** 重命名目录操作 */
    public static final String RENAME_DIRECTORY = "RENAME_DIRECTORY";
    /** 删除目录操作 */
    public static final String DELETE_DIRECTORY = "DELETE_DIRECTORY";
    /** 移动目录操作 */
    public static final String MOVE_DIRECTORY = "MOVE_DIRECTORY";

    private LogRecordSubType() {
        // 防止实例化
    }
}