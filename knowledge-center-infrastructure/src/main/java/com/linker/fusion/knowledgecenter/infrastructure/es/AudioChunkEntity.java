package com.linker.fusion.knowledgecenter.infrastructure.es;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AudioChunkEntity extends BaseChunkEntity {
    /**
     * 人脸标签列表
     */
    @JsonProperty("persons_labels")
    private List<String> personsLabels;
    /**
     * 发言人
     */
    @JsonProperty("speaker")
    private String speaker;
    /**
     * 实体标签
     */
    @JsonProperty("object_labels")
    private List<String> objectLabels;
    /**
     * 专名标签
     */
    @JsonProperty("text_labels")
    private List<String> textLabels;
    /**
     * 排序
     */
    @JsonProperty("sort")
    private Double sort;
    /**
     * 开始时间
     */
    @JsonProperty("start_timestamp")
    private Long startTimestamp;
    /**
     * 结束时间
     */
    @JsonProperty("end_timestamp")
    private Long endTimestamp;

    /**
     * 文件类型
     */
    @JsonProperty("file_type")
    private Integer fileType;

}
