package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceExtEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceExtTypeEnum;

import java.util.List;

/**
 * <p>
 * 资源扩展信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
public interface IResourceExtManager extends IService<ResourceExtEntity> {

    void removeByDocIdType(String resIds, ResourceExtTypeEnum resourceExtTypeEnum);

    List<ResourceExtEntity> listLt(ResourceExtTypeEnum resourceExtTypeEnum, String value);

    void deleteByResIds(List<String> resIds);

    List<ResourceExtEntity> list(List<String> resIds, ResourceExtTypeEnum resourceExtTypeEnum);

    List<ResourceExtEntity> list(ResourceExtTypeEnum resourceExtTypeEnum, List<String> values);

}
