package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "conductor")
public class ConductorConfigProperties {

    /**
     * conductor ip域名 示例http://***********:31311/api/
     */
    private String endpoint;
    private String workflowName;
    private Task task;

    @Data
    public static class Task {
        /**
         * 数据隔离 示例 local、prod、 linker
         */
        private String globalDomain;
    }

}