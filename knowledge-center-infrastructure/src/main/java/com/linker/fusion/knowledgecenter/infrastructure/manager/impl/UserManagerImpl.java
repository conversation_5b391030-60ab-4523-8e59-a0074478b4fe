package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.UserEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IUserManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.UserMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Service
public class UserManagerImpl extends ServiceImpl<UserMapper, UserEntity> implements IUserManager {

}
