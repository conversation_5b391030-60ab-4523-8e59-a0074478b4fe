
package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceFaceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IResourceFaceManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.ResourceFaceMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Service
public class ResourceFaceManagerImpl extends ServiceImpl<ResourceFaceMapper, ResourceFaceEntity> implements IResourceFaceManager {

    @Override
    public Set<String> queryDocIdByFaceId(List<String> faceIds) {
        if (CollectionUtils.isEmpty(faceIds)) {
            return Collections.emptySet();
        }
        return list(
                new LambdaQueryWrapper<ResourceFaceEntity>()
                        .eq(ResourceFaceEntity::getDeleted, false)
                        .in(ResourceFaceEntity::getSourceId, faceIds)
                        .select(ResourceFaceEntity::getDocId)
        ).stream().map(ResourceFaceEntity::getDocId).collect(Collectors.toSet());
    }

    @Override
    public List<ResourceFaceEntity> list(String docId, String segmentId) {
        QueryWrapper<ResourceFaceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(StringUtils.isNotBlank(docId), ResourceFaceEntity::getDocId, docId);
        wrapper.lambda().eq(StringUtils.isNotBlank(segmentId), ResourceFaceEntity::getSegmentId, segmentId);
        wrapper.lambda().eq(ResourceFaceEntity::getDeleted, false);
        return list(wrapper);
    }

    @Override
    public List<ResourceFaceEntity> listByDocIdAndFaceId(List<String> docIds, String faceId) {
        if (CollectionUtils.isEmpty(docIds)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<ResourceFaceEntity>()
                        .eq(ResourceFaceEntity::getDeleted, false)
                        .in(ResourceFaceEntity::getDocId, docIds)
                        .eq(StringUtils.isNotBlank(faceId), ResourceFaceEntity::getSourceId, faceId)
        );
    }

    @Override
    public void deleteByDocId(String docId) {
        this.lambdaUpdate()
                .eq(ResourceFaceEntity::getDocId, docId)
                .set(ResourceFaceEntity::getDeleted, true)
                .update();
    }

    @Override
    public void clearByDocId(String docId) {
        QueryWrapper<ResourceFaceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceFaceEntity::getDocId, docId);
        remove(wrapper);
    }

    @Override
    public void clearByDocIds(List<String> fileIds) {
        if (CollectionUtils.isEmpty(fileIds))
            return;
        QueryWrapper<ResourceFaceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ResourceFaceEntity::getDocId, fileIds);
        remove(wrapper);
    }

    @Override
    public List<Map<String, Object>> listGroupBySourceIdOrderByCount(List<String> docIds) {
        return this.baseMapper.listGroupBySourceIdOrderByCount(docIds);
    }


    /**
     * @description: 查询人脸列表
     * @author: hejianbao
     * @date: 2025/7/25 09:12
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    @Override
    public List<ResourceFaceEntity> listResourceFace(List<String> docIdList, String name) {
        LambdaQueryWrapper<ResourceFaceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(docIdList), ResourceFaceEntity::getDocId, docIdList)
                .like(StringUtils.isNotBlank(name), ResourceFaceEntity::getName, name)
                .orderByDesc(ResourceFaceEntity::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public void updateName(String id, String name) {
        UpdateWrapper<ResourceFaceEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(ResourceFaceEntity::getName, name)
                .eq(ResourceFaceEntity::getSourceId, id);
        update(updateWrapper);
    }

}
