package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@TableName("t_resource_ext")
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ResourceExtEntity extends BaseEntity {
    @TableField("doc_id")
    private String docId;
    @TableField("`type`")
    private String type;
    @TableField("field1")
    private String field1;
    @TableField("field2")
    private String field2;
    @TableField("field3")
    private String field3;
}
