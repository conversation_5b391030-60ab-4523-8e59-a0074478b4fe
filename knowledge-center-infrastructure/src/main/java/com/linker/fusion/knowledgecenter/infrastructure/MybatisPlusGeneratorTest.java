package com.linker.fusion.knowledgecenter.infrastructure;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;

import java.util.Arrays;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2022/11/7 下午5:44
 * @DESC: mybatis plus代码生成
 */
public class MybatisPlusGeneratorTest {

    public static void main(String[] args) {
        //数据库配置信息
        String jdbcUrl = "***************************************************";
        String userName = "root";
        String password = "123456";
        //多模块项目中的子模块
        String moduleSubName = "knowledge-center-infrastructure";
        //包名
        String packageConfig = "com.linker.fusion.knowledgecenter.infrastructure";
        MybatisPlusGeneratorTest.generate(jdbcUrl, userName, password, moduleSubName, packageConfig);
    }


    /**
     * 代码生成
     */
    public static void generate(String jdbcUrl, String userName, String password, String moduleSubName, String packageConfig) {
        DataSourceConfig.Builder source = new DataSourceConfig
                .Builder(jdbcUrl, userName, password);

        HashMap<OutputFile, String> pathMap = new HashMap<>();
//        HashMap<OutputFile, String> pathMap2 = new HashMap<>();
//        String apiModule = moduleSubName+"-api";
//        String apiPackage = packageConfig+".api";
//        apiPackage ="/src/main/java/"+ apiPackage.replaceAll("\\.","\\/");
//        pathMap1.put(OutputFile.entity,System.getProperty("user.dir") + apiModule+apiPackage);
        pathMap.put(OutputFile.xml, System.getProperty("user.dir") + moduleSubName + "/src/main/resources/mapper");


        FastAutoGenerator.create(source).globalConfig((scanner, builder) -> builder
                        // （重要）配置输出的文件夹，springboot项目可以使用如下方式
                        .outputDir(System.getProperty("user.dir") + "/" + moduleSubName + "/src/main/java")
                        // （重要）时间类型，看你喜欢用sql包中的Date、
                        // util包中的Date还是更新的LocalDateTime
                        .dateType(DateType.TIME_PACK)
                        // 配置生成文件中的author
                        .author(scanner.apply("请输入作者名称"))
                        // 注释日期的格式
                        .commentDate("yyyy-MM-dd")
                        .build())
                .packageConfig(builder -> builder.parent(packageConfig)
                        .pathInfo(pathMap)   // 设置父包名:一般情况下使用默认即可；对于不需要的可生成在delete中，后期移除
                        .service("manager")
                        .serviceImpl("manager.impl"))
                .strategyConfig((scanner, builder) -> builder
                        .addInclude(scanner.apply("请输入表名，多个表名用,隔开").split(","))
                        // 设置过滤表前缀
                        .addTablePrefix("t_", "tb_")
                        // 跳过视图的生成
                        .enableSkipView()
                        .entityBuilder()
                        // （重要）主键模式，这里设置自动模式，配合mysql的自增主键
                        .idType(IdType.AUTO)
                        .enableLombok()

                        //去除is头
                        .enableRemoveIsPrefix()
                        // entity文件名，这里配置后面统一加Entity后缀
                        .formatFileName("%sEntity")
                        // activeRecord模式，使用上来说就是
                        // 可以直接在entity对象上执行insert、update等操作
//                        .enableActiveRecord()
                        // 添加tableField注解

                        .enableTableFieldAnnotation()

                        // 自动填充字段
                        .addTableFills(Arrays.asList(
                                new Column("create_time", FieldFill.INSERT),
                                new Column("creator_id", FieldFill.INSERT),
                                new Column("update_time", FieldFill.INSERT_UPDATE),
                                new Column("update_id", FieldFill.INSERT_UPDATE)
                        ))
                        .build())
                .strategyConfig(builder -> builder
                        .mapperBuilder()
                        .enableMapperAnnotation()
                        .enableBaseColumnList()
                        .enableBaseResultMap().build()
                ).strategyConfig(builder -> builder.serviceBuilder()
                        .formatServiceFileName("I%sManager")
                        .formatServiceImplFileName("%sManagerImpl")
                ).strategyConfig(builder -> builder.controllerBuilder().disable())
                // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }

}
