package com.linker.fusion.knowledgecenter.infrastructure.client;

import com.alibaba.fastjson.JSONObject;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.VideoCutFrameReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.VideoSplitReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.VideoSplitResp;
import retrofit2.http.Body;
import retrofit2.http.POST;

@RetrofitClient(baseUrl = "${http.transcode.endpoint:http://192.1.2.239:30136/}", readTimeoutMs = 60000)
public interface TranscodeClient {

    @POST("video/api/v2/videoSplitFrame")
    BaseResp<VideoSplitResp> videoSplit(@Body VideoSplitReq req);

    @POST("video/api/videoCutFrame")
    JSONObject videoCutFrame(@Body VideoCutFrameReq req);
}
