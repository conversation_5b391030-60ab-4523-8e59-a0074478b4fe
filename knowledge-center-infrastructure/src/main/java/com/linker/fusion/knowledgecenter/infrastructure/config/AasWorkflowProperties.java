package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "workflow.execute")
public class AasWorkflowProperties {

    private String endpoint;

    private String tenantId;
    private String accessKey;
    private String secretKey;

    private WorkflowId workflowId;

    @Data
    public static class WorkflowId {

        private String importDoc;

        private String importTable;

        private String importImage;

        private String importVideo;

        private String importAudio;

        private String importCallback;

        private String aiSearch;
        private String task;
        private String taskCut;

        /**
         * 从文档生成题目的工作流ID
         */
        private String generateQbFromDoc;

        /**
         * 视频合成工作流ID
         */
        private String videoMerge = "242ec8a1bab24f13a0f813f69b8c2345";

        /**
         * 高级检索
         */
        private String advancedSearch = "ded764801088433fbbec231e8871dd1a";

    }
}
