package com.linker.fusion.knowledgecenter.infrastructure.utils;

import cn.hutool.core.io.FileUtil;
import org.xhtmlrenderer.swing.Java2DRenderer;
import org.xhtmlrenderer.util.FSImageWriter;

import java.awt.image.BufferedImage;
import java.io.File;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MarkdownUtil {

    public static void main(String[] args) {
        try {
            // Example HTML and CSS
            String md = "|时间|文件及发布单位|内容|\n" +
                    "|---|---|---|\n" +
                    "|2015.9|《促进大数据发展行动 纲要》（中华人民共和 国国务院）|探索开展交通、公安、气象、安监、地震、测 绘等跨部门、跨地域数据融合和协同创新。建 立综合交通服务大数据平台，共同利用大数据 提升协同管理和公共服务能力，积极吸引社会 优质资源，利用交通大数据开展出行信息服 务、交通诱导等增值服务。|\n" +
                    "|2022.1|《要素市场化配置综合 改革试点总体方案》（国 务院办公厅）|建立健全高效的公共数据共享协调机制，支持 打造公共数据基础支撑平台，推进公共数据归 集整合、有序流通和共享。探索完善公共数据 共享、开放、运营服务、安全保障的管理体制。 优先推进企业登记监管、卫生健康、交通运输、 气象等高价值数据集向社会开放。探索开展政 府数据授权运营。|\n" +
                    "\n" +
                    "|2023.2|《数字中国建设整体布 局规划》（中共中央、 国务院）|推动数字技术和实体经济深度融合，在农业、 工业、金融、教育、医疗、交通、能源等重点 领域，加快数字技术创新应用。支持数字企业 发展壮大，健全大中小企业融通创新工作机 制，发挥“绿灯”投资案例引导作用，推动平 台企业规范健康发展。|\n" +
                    "|---|---|---|\n" +
                    "|2024.1|《“数据要素×”三年 行动计划（2024-2026 年）》（国家数据局、 交通运输部等17 个部 门）|挖掘数据复用价值，融合“两客一危”、网络 货运等重点车辆数据，构建覆盖车辆营运行 为、事故统计等高质量动态数据集，为差异化 信贷、保险服务、二手车消费等提供数据支撑。 支持交通运输龙头企业推进高质量数据集建 设和复用，加强人工智能工具应用，助力企业 提升运输效率。|\n" +
                    "\n" +
                    "|时间|文件|内容|\n" +
                    "|---|---|---|\n" +
                    "|2016.5|《交通运输信息化“十 三五”发展规划》（交 通运输部）|重点开展“三推进、五提升、两保障”行业信 息化工程，到 2020 年，实现以下具体目标： 要素信息开放共享、行业管理在线协同、综合 运输便捷互联、信息服务提质增效、信息安全 自主可控、发展环境协调高效。|\n" +
                    "|2016.8|《关于推进交通运输行 业数据资源开放共享的 实施意见》（交通运输 部办公厅）|遵循以目录管理数据资产、以共享促进数据融 合、以开放实现数据增值的总体思路，坚持需 求导向和问题导向，加强统筹协调，激发市场 活力，着力突破交通运输大数据发展机制与技 术障碍，提升行业数据资源开发利用价值，为 打造精准治理、多方协作的行业治理模式，构|\n" +
                    "\n" +
                    "|Col1|Col2|建贴近需求、便捷高效的运输服务体系提供有 力支撑，促进安全便捷、畅通高效、绿色智能 的现代综合交通运输体系建设。|\n" +
                    "|---|---|---|\n" +
                    "|2019.9|《交通强国建设纲要》 （中共中央、国务院）|提出推动大数据、互联网、人工智能、区块链、 超级计算等新技术与交通行业深度融合。推进 数据资源赋能交通发展，加速交通基础设施 网、运输服务网、能源网与信息网络融合发展， 构建泛在先进的交通信息基础设施。构建综合 交通大数据中心体系，深化交通公共服务和电 子政务发展。推进北斗卫星导航系统应用。|\n" +
                    "|2019.12|《推进综合交通运输大 数据发展行动纲要 （2020—2025 年）》（交 通运输部）|提出到 2025 年，综合交通运输大数据标准体 系更加完善，基础设施、运载工具等成规模、 成体系的大数据集基本建成。政务大数据有效 支撑综合交通运输体系建设，交通运输行业数 字化水平显著提升。综合交通运输信息资源深 入共享开放。大数据在综合交通运输各业务领 域应用更加广泛。大数据安全得到有力保障。 符合新时代信息化发展规律的大数据体制机 制取得突破。综合交通大数据中心体系基本构 建，为加快建设交通强国，助力数字经济勃兴 提供坚强支撑。|\n" +
                    "|2021.12|《数字交通“十四五” 发展规划》（交通运输 部）|打造综合交通运输“数据大脑”。完善部、省 两级综合交通运输信息平台架构，推进综合交 通大数据中心体系建设，加强数据资源的整合 共享、综合开发和智能应用。|\n" +
                    "|2023.10|《关于推进城市公共交 通健康可持续发展的若 干意见》（交通运输部、 国家发改委等九部门）|促进公交服务提质增效。持续优化城市公共交 通线网，促进城市公共汽电车与城市轨道交通 在线网、站点及运营层面的衔接融合，通过大 数据应用提升城市公共汽电车运营效率。|\n" +
                    "\n" +
                    "|Col1|表3 驾驶员数据细分类型|\n" +
                    "|---|---|\n" +
                    "|驾驶员数据类型|描述|\n" +
                    "|驾驶员基本信息|包括驾驶员的姓名、性别、年龄、驾驶证号码、从业 经历等基本信息，为管理驾驶员提供依据|\n" +
                    "|驾驶员驾驶行为|包括驾驶员的驾驶习惯、驾驶技能、安全意识等，能 够帮助交通管理部门及时发现和纠正潜在的驾驶问题|\n" +
                    "|驾驶员健康状况|包括驾驶员的身体健康状况、心理状态等，能够帮助 交通管理部门及时发现和解决潜在的健康问题|\n" +
                    "\n" +
                    "|Col1|表4 行人数据细分类型|\n" +
                    "|---|---|\n" +
                    "|行人数据类型|描述|\n" +
                    "|行人流量|通过监测特定区域内的行人流量，了解该区域的交通 状况和行人出行情况，能够为交通管理部门提供决策 依据|\n" +
                    "|行人行为|通过监测行人的行为，掌握行人的出行习惯和行为特 征，如步速、方向、穿越马路的方式等，能够帮助交 通管理部门优化交通组织，提高行人安全和交通效率|\n" +
                    "|行人安全|通过各种传感器和监控系统采集行人的安全状况数 据，及时发现和解决潜在的安全隐患，提高行人的安 全意识|\n" +
                    "|行人需求|通过调查和数据分析得到行人的出行需求，为制定更 加人性化的交通服务措施提供科学依据，如设置合理 的交通标志、提供便捷的公共交通等|\n" +
                    "\n" +
                    "|Col1|表5 交通行业实时监控和预警案例|Col3|\n" +
                    "|---|---|---|\n" +
                    "|监控预警类型|描述||\n" +
                    "|路网运行评价 指标|整体展示路网运行评价统 计、拥堵、事件、设施状态、 收费等关键评价指标。||\n" +
                    "|路网运行流量 指数|针对路网、路段流量进行指 数级评价，对整体评价单元 的流量形成量化指标。||\n" +
                    "|拥堵趋势预警|依托稳定流量及收费门架 断面流量，对路网拥堵态势 进行预警。||\n" +
                    "\n" +
                    "|Col1|表6 交通行业数据分析案例|Col3|\n" +
                    "|---|---|---|\n" +
                    "|数据分析类型|描述||\n" +
                    "|路网运行研判 分析|依托历史运行数据及研判 算法模型，针对节假日对路 网运行中长期路网运行态 势进行研判分析，形成研判 报告。||\n" +
                    "|客货运输分布 分析|依托客运和货运的历史运 行数据进行分析研判，制定 客货分布地图，为线路规 划、流量调控提供参考。||\n" +
                    "|气象环境评价 分析|依托气象环境监测数据，对 路网、路段交通气象环境指 标进行综合分析评价，并且 形成交通气象风险评价指 标。||\n" +
                    "\n";

            String[] split = md.split("\n\n");
            for (int i = 0; i < split.length; i++) {
                BufferedImage bufferedImage = MarkdownUtil.table2Image(split[i], 720);

                String outputFilePath = "output" + i + ".png";
                FSImageWriter imageWriter = new FSImageWriter();
                imageWriter.write(bufferedImage, outputFilePath);
                System.out.println("HTML content converted to image: " + outputFilePath);
            }
            for (String table : split) {

            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static BufferedImage table2Image(String markdownTable, int imageWidth) throws Exception {
        File tempHtmlFile = FileUtil.createTempFile(".html", true);
        try {
            String htmlContent = markdownTableToHtml(markdownTable);
            Files.write(tempHtmlFile.toPath(), htmlContent.getBytes());
            Java2DRenderer renderer = new Java2DRenderer(tempHtmlFile, imageWidth);
            return renderer.getImage();
        } finally {
            tempHtmlFile.delete();
        }
    }

    public static String markdownTableToHtml(String markdownTable) {
        // 拆行处理
        String[] lines = markdownTable.split("\\r?\\n");
        List<List<String>> rows = new ArrayList<>();
        for (String line : lines) {
            line = line.trim();
            // 跳过分隔线
            if (line.isEmpty()) continue;
            if (line.startsWith("|") && line.endsWith("|")) {
                String[] cols = line.substring(1, line.length() - 1).split("\\|");
                List<String> row = new ArrayList<>();
                for (String col : cols) row.add(col.trim());
                rows.add(row);
            }
        }
        // 简单校验，两行以下不是表格
        if (rows.size() < 2) throw new IllegalArgumentException("Markdown表格不合法！");
        // 第二行为分割线，从表格数据删除
        rows.remove(1);

        StringBuilder html = new StringBuilder();
        html.append("<html>");
        html.append("<head>");
        html.append(defaultStyle());
        html.append("</head>");
        html.append("<body><table>");
        // header
        html.append("<thead><tr>");
        for (String col : rows.get(0)) {
            html.append("<th>").append(escapeHtml(col)).append("</th>");
        }
        html.append("</tr></thead>");
        // body
        html.append("<tbody>");
        for (int i = 1; i < rows.size(); i++) {
            List<String> row = rows.get(i);
//            if (row.stream().allMatch(x -> x.trim().matches("-+"))) {
//                continue;
//            }
            html.append("<tr>");
            for (String col : row) {
                html.append("<td>").append(escapeHtml(col)).append("</td>");
            }
            html.append("</tr>");
        }
        html.append("</tbody>");
        html.append("</table>                </body>\n" +
                "                </html>");

        return html.toString();
    }

    private static String escapeHtml(String s) {
        return s.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;");
    }

    private static String defaultStyle() {
        return "<style>\n" +
                "    body {\n" +
                "       font-family: Helvetica, arial, sans-serif;\n" +
                "       font-size: 14px;\n" +
                "       line-height: 1.6;\n" +
                "       padding-top: 10px;\n" +
                "       padding-bottom: 10px;\n" +
                "       background-color: white;\n" +
                "       padding: 30px; }\n" +
                "\n" +
                "    body > *:first-child {\n" +
                "       margin-top: 0 !important; }\n" +
                "    body > *:last-child {\n" +
                "       margin-bottom: 0 !important; }\n" +
                "\n" +
                "    a {\n" +
                "       color: #4183C4; }\n" +
                "    a.absent {\n" +
                "       color: #cc0000; }\n" +
                "    a.anchor {\n" +
                "       display: block;\n" +
                "       padding-left: 30px;\n" +
                "       margin-left: -30px;\n" +
                "       cursor: pointer;\n" +
                "       position: absolute;\n" +
                "       top: 0;\n" +
                "       left: 0;\n" +
                "       bottom: 0; }\n" +
                "\n" +
                "    h1, h2, h3, h4, h5, h6 {\n" +
                "       margin: 20px 0 10px;\n" +
                "       padding: 0;\n" +
                "       font-weight: bold;\n" +
                "       -webkit-font-smoothing: antialiased;\n" +
                "       cursor: text;\n" +
                "       position: relative; }\n" +
                "\n" +
                "    h1:hover a.anchor, h2:hover a.anchor, h3:hover a.anchor, h4:hover a.anchor, h5:hover a.anchor, h6:hover a.anchor {\n" +
                "       text-decoration: none; }\n" +
                "\n" +
                "    h1 tt, h1 code {\n" +
                "       font-size: inherit; }\n" +
                "\n" +
                "    h2 tt, h2 code {\n" +
                "       font-size: inherit; }\n" +
                "\n" +
                "    h3 tt, h3 code {\n" +
                "       font-size: inherit; }\n" +
                "\n" +
                "    h4 tt, h4 code {\n" +
                "       font-size: inherit; }\n" +
                "\n" +
                "    h5 tt, h5 code {\n" +
                "       font-size: inherit; }\n" +
                "\n" +
                "    h6 tt, h6 code {\n" +
                "       font-size: inherit; }\n" +
                "\n" +
                "    h1 {\n" +
                "       font-size: 28px;\n" +
                "       color: black; }\n" +
                "\n" +
                "    h2 {\n" +
                "       font-size: 24px;\n" +
                "       border-bottom: 1px solid #cccccc;\n" +
                "       color: black; }\n" +
                "\n" +
                "    h3 {\n" +
                "       font-size: 18px; }\n" +
                "\n" +
                "    h4 {\n" +
                "       font-size: 16px; }\n" +
                "\n" +
                "    h5 {\n" +
                "       font-size: 14px; }\n" +
                "\n" +
                "    h6 {\n" +
                "       color: #777777;\n" +
                "       font-size: 14px; }\n" +
                "\n" +
                "    p, blockquote, ul, ol, dl, li, table, pre {\n" +
                "       margin: 15px 0; }\n" +
                "\n" +
                "    hr {\n" +
                "       border: 0 none;\n" +
                "       color: #cccccc;\n" +
                "       height: 4px;\n" +
                "       padding: 0;\n" +
                "    }\n" +
                "\n" +
                "    body > h2:first-child {\n" +
                "       margin-top: 0;\n" +
                "       padding-top: 0; }\n" +
                "    body > h1:first-child {\n" +
                "       margin-top: 0;\n" +
                "       padding-top: 0; }\n" +
                "    body > h1:first-child + h2 {\n" +
                "       margin-top: 0;\n" +
                "       padding-top: 0; }\n" +
                "    body > h3:first-child, body > h4:first-child, body > h5:first-child, body > h6:first-child {\n" +
                "       margin-top: 0;\n" +
                "       padding-top: 0; }\n" +
                "\n" +
                "    a:first-child h1, a:first-child h2, a:first-child h3, a:first-child h4, a:first-child h5, a:first-child h6 {\n" +
                "       margin-top: 0;\n" +
                "       padding-top: 0; }\n" +
                "\n" +
                "    h1 p, h2 p, h3 p, h4 p, h5 p, h6 p {\n" +
                "       margin-top: 0; }\n" +
                "\n" +
                "    li p.first {\n" +
                "       display: inline-block; }\n" +
                "    li {\n" +
                "       margin: 0; }\n" +
                "    ul, ol {\n" +
                "       padding-left: 30px; }\n" +
                "\n" +
                "    ul :first-child, ol :first-child {\n" +
                "       margin-top: 0; }\n" +
                "\n" +
                "    dl {\n" +
                "       padding: 0; }\n" +
                "    dl dt {\n" +
                "       font-size: 14px;\n" +
                "       font-weight: bold;\n" +
                "       font-style: italic;\n" +
                "       padding: 0;\n" +
                "       margin: 15px 0 5px; }\n" +
                "    dl dt:first-child {\n" +
                "       padding: 0; }\n" +
                "    dl dt > :first-child {\n" +
                "       margin-top: 0; }\n" +
                "    dl dt > :last-child {\n" +
                "       margin-bottom: 0; }\n" +
                "    dl dd {\n" +
                "       margin: 0 0 15px;\n" +
                "       padding: 0 15px; }\n" +
                "    dl dd > :first-child {\n" +
                "       margin-top: 0; }\n" +
                "    dl dd > :last-child {\n" +
                "       margin-bottom: 0; }\n" +
                "\n" +
                "    blockquote {\n" +
                "       border-left: 4px solid #dddddd;\n" +
                "       padding: 0 15px;\n" +
                "       color: #777777; }\n" +
                "    blockquote > :first-child {\n" +
                "       margin-top: 0; }\n" +
                "    blockquote > :last-child {\n" +
                "       margin-bottom: 0; }\n" +
                "\n" +
                "    table {\n" +
                "       padding: 0;border-collapse: collapse; }\n" +
                "    table tr {\n" +
                "       border-top: 1px solid #cccccc;\n" +
                "       background-color: white;\n" +
                "       margin: 0;\n" +
                "       padding: 0; }\n" +
                "    table tr:nth-child(2n) {\n" +
                "       background-color: #f8f8f8; }\n" +
                "    table tr th {\n" +
                "       font-weight: bold;\n" +
                "       border: 1px solid #cccccc;\n" +
                "       margin: 0;\n" +
                "       padding: 6px 13px; }\n" +
                "    table tr td {\n" +
                "       border: 1px solid #cccccc;\n" +
                "       margin: 0;\n" +
                "       padding: 6px 13px; }\n" +
                "    table tr th :first-child, table tr td :first-child {\n" +
                "       margin-top: 0; }\n" +
                "    table tr th :last-child, table tr td :last-child {\n" +
                "       margin-bottom: 0; }\n" +
                "\n" +
                "    img {\n" +
                "       max-width: 100%; }\n" +
                "\n" +
                "    span.frame {\n" +
                "       display: block;\n" +
                "       overflow: hidden; }\n" +
                "    span.frame > span {\n" +
                "       border: 1px solid #dddddd;\n" +
                "       display: block;\n" +
                "       float: left;\n" +
                "       overflow: hidden;\n" +
                "       margin: 13px 0 0;\n" +
                "       padding: 7px;\n" +
                "       width: auto; }\n" +
                "    span.frame span img {\n" +
                "       display: block;\n" +
                "       float: left; }\n" +
                "    span.frame span span {\n" +
                "       clear: both;\n" +
                "       color: #333333;\n" +
                "       display: block;\n" +
                "       padding: 5px 0 0; }\n" +
                "    span.align-center {\n" +
                "       display: block;\n" +
                "       overflow: hidden;\n" +
                "       clear: both; }\n" +
                "    span.align-center > span {\n" +
                "       display: block;\n" +
                "       overflow: hidden;\n" +
                "       margin: 13px auto 0;\n" +
                "       text-align: center; }\n" +
                "    span.align-center span img {\n" +
                "       margin: 0 auto;\n" +
                "       text-align: center; }\n" +
                "    span.align-right {\n" +
                "       display: block;\n" +
                "       overflow: hidden;\n" +
                "       clear: both; }\n" +
                "    span.align-right > span {\n" +
                "       display: block;\n" +
                "       overflow: hidden;\n" +
                "       margin: 13px 0 0;\n" +
                "       text-align: right; }\n" +
                "    span.align-right span img {\n" +
                "       margin: 0;\n" +
                "       text-align: right; }\n" +
                "    span.float-left {\n" +
                "       display: block;\n" +
                "       margin-right: 13px;\n" +
                "       overflow: hidden;\n" +
                "       float: left; }\n" +
                "    span.float-left span {\n" +
                "       margin: 13px 0 0; }\n" +
                "    span.float-right {\n" +
                "       display: block;\n" +
                "       margin-left: 13px;\n" +
                "       overflow: hidden;\n" +
                "       float: right; }\n" +
                "    span.float-right > span {\n" +
                "       display: block;\n" +
                "       overflow: hidden;\n" +
                "       margin: 13px auto 0;\n" +
                "       text-align: right; }\n" +
                "\n" +
                "    code, tt {\n" +
                "       margin: 0 2px;\n" +
                "       padding: 0 5px;\n" +
                "       white-space: nowrap;\n" +
                "       border: 1px solid #eaeaea;\n" +
                "       background-color: #f8f8f8;\n" +
                "       border-radius: 3px; }\n" +
                "\n" +
                "    pre code {\n" +
                "       margin: 0;\n" +
                "       padding: 0;\n" +
                "       white-space: pre;\n" +
                "       border: none;\n" +
                "       background: transparent; }\n" +
                "\n" +
                "    .highlight pre {\n" +
                "       background-color: #f8f8f8;\n" +
                "       border: 1px solid #cccccc;\n" +
                "       font-size: 13px;\n" +
                "       line-height: 19px;\n" +
                "       overflow: auto;\n" +
                "       padding: 6px 10px;\n" +
                "       border-radius: 3px; }\n" +
                "\n" +
                "    pre {\n" +
                "       background-color: #f8f8f8;\n" +
                "       border: 1px solid #cccccc;\n" +
                "       font-size: 13px;\n" +
                "       line-height: 19px;\n" +
                "       overflow: auto;\n" +
                "       padding: 6px 10px;\n" +
                "       border-radius: 3px; }\n" +
                "    pre code, pre tt {\n" +
                "       background-color: transparent;\n" +
                "       border: none; }\n" +
                "\n" +
                "    sup {\n" +
                "       font-size: 0.83em;\n" +
                "       vertical-align: super;\n" +
                "       line-height: 0;\n" +
                "    }\n" +
                "    * {\n" +
                "       -webkit-print-color-adjust: exact;\n" +
                "    }\n" +
                "    @media screen and (min-width: 914px) {\n" +
                "       body {\n" +
                "          width: 854px;\n" +
                "          margin:0 auto;\n" +
                "       }\n" +
                "    }\n" +
                "    @media print {\n" +
                "       table, pre {\n" +
                "          page-break-inside: avoid;\n" +
                "       }\n" +
                "       pre {\n" +
                "          word-wrap: break-word;\n" +
                "       }\n" +
                "    }\n" +
                "</style>";
    }

}