package com.linker.fusion.knowledgecenter.infrastructure.client;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.HttpPersonInfoReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.HttpPersonSearchReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.HttpPersonSearchResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.PersonInfoDTO;
import com.linker.fusion.knowledgecenter.infrastructure.client.person.ODLibReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.person.ODLibResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.person.SceneMatchResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.person.SceneSearchReq;
import org.springframework.stereotype.Service;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

@Service
@RetrofitClient(baseUrl = "${http.person.endpoint}", readTimeoutMs = 12 * 3600 * 1000, writeTimeoutMs = 12 * 3600 * 1000)
public interface HttpPersonClient {

    @POST("rpc/person/search")
    HttpPersonSearchResp personSearch(@Body HttpPersonSearchReq req);

    @POST("rpc/person/info")
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    @CacheRefresh(refresh = 13, stopRefreshAfterLastAccess = 24 * 3600)
    BaseResp<List<PersonInfoDTO>> personInfo(@Body HttpPersonInfoReq req);
    /**
     * 场景标签检索
     * @param req 请求
     * @return 标签列表
     */
    @POST("rpc/scene/search")
    BaseResp<List<SceneMatchResp>> sceneSearch(@Body SceneSearchReq req);

    /**
     * OD 库列表
     *
     * @param req 列表请求参数
     * @return {@link BaseResp }<{@link List }<{@link ODLibResp }>>
     */
    @POST("rpc/od/lib/list/by/guid")
    BaseResp<List<ODLibResp>> listLibByGuidList(@Body ODLibReq req);

}
