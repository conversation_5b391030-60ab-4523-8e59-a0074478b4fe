package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pdf-parse")
@RefreshScope
public class PdfParseProperties {

    @Deprecated
    private String url;
    @Deprecated
    private String callbackUrl;

    private Storage storage;

    @Data
    public static class Storage {

        private String type = "oss";

        private String username;

        private String password;

        private String endpoint;

        private String bucket;

        private Integer secure;
    }

    private String version = "pro";
}
