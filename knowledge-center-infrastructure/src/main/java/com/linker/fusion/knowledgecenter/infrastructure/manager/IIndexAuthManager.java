package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.linker.fusion.knowledgecenter.infrastructure.entity.IndexAuthEntity;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 索引定时配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
public interface IIndexAuthManager extends IService<IndexAuthEntity> {


    /**
     * 获取索引定时任务配置
     * @param tenantId 租户ID
     * @return 索引定时任务配置
     */
    String getIndexTimeConfig(String tenantId);


    Boolean createOrUpdateIndexAuth(IndexAuthEntity indexAuthEntity);

}
