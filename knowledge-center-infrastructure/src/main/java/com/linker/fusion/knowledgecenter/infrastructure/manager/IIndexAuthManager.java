package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.linker.fusion.knowledgecenter.infrastructure.entity.IndexAuthEntity;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 索引定时配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
public interface IIndexAuthManager extends IService<IndexAuthEntity> {


    /**
     * 获取索引定时任务配置&增加缓存
     *
     * @param tenantId 租户ID
     * @return 索引定时任务配置
     */
    IndexAuthEntity getIndexTimeConfig(String tenantId);


    /**
     * 创建或更新索引定时任务配置变更缓存
     *
     * @param
     * @return 是否成功
     */
    void freshIndexAuthCache(String tenantId);

}
