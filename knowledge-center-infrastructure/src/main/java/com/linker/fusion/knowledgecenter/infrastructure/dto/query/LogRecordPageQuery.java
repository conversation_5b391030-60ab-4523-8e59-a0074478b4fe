package com.linker.fusion.knowledgecenter.infrastructure.dto.query;

import com.linker.core.base.baseclass.page.BasePaginReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LogRecordPageQuery extends BasePaginReq {
    /**
     * 账号
     */
    private String username;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作时间筛选开始
     */
    private String operateTimeBegin;

    /**
     * 操作时间筛选结束
     */
    private String operateTimeEnd;

    /**
     * 操作菜单
     */
    private String operationType;

    /**
     * 操作子类型
     */
    private String operationSubType;

    /**
     * 操作子类型
     */
    private String searchContent;

    /**
     * 租户id
     */
    private String tenantId;
    /*
     * 操作状态
     */
    private Boolean status;
}
