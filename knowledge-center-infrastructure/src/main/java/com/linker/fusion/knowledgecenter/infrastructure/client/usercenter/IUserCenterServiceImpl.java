package com.linker.fusion.knowledgecenter.infrastructure.client.usercenter;

import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.core.BaseClientResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.MenuItem;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.MenuReq;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.user.api.dto.UserInfo;
import com.linker.user.api.req.PowerRecordInfoReq;
import com.linker.user.api.resp.PowerRecordResp;
import com.linker.user.api.resp.PowerRecordRulesReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IUserCenterServiceImpl implements IUserCenterService {

    @Resource
    private IUserCenter userCenter;

    @Value("${user.auth.appId}")
    private String appId;
    @Value("${user.auth.strategyCode:''}")
    private String strategyCode;

    @Override
    @Cached(expire = 3, cacheType = CacheType.LOCAL)
    @CachePenetrationProtect
    public List<MenuItem> menus(String token, String tenantId, String userCode) {
        return userCenter.menus(token, new MenuReq(appId, tenantId, userCode)).getData();
    }

    public boolean hasAuth(String token, String tenantId, String userCode, MenuKeyEnum menuKey, boolean throwError) {
        return hasAuth(token, tenantId, userCode, Collections.singletonList(menuKey), throwError);
    }

    public boolean hasAuth(String token, String tenantId, String userCode, List<MenuKeyEnum> menuKeys, boolean throwError) {
        BaseResp<List<MenuItem>> menusResp = userCenter.menus(token, new MenuReq(appId, tenantId, userCode));
        if (Objects.isNull(menusResp)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.USER_CENTER_ERROR);
        }
        if (!menusResp.isSuccess()) {
            throw new ServiceException(menusResp.getCode(), menusResp.getMessage());
        }
        boolean hasAuth = true;
        for (MenuKeyEnum menuKey : menuKeys) {
            if (menusResp.getData().stream().noneMatch(m -> m.getMenuName().equalsIgnoreCase(menuKey.getKey()))) {
                hasAuth = false;
                if (throwError) {
                    throw new ServiceException(KnowledgeCenterErrorCodeEnum.NO_MENU_AUTH, menuKey.getDesc());
                }
                break;
            }
        }
        return hasAuth;
    }

    @Override
    @Cached(expire = 3, cacheType = CacheType.LOCAL)
    public boolean isManager(String token, String tenantId, String userCode) {
        try {
            return hasAuth(token, tenantId, userCode, MenuKeyEnum.KNOWLEDGE_MANAGER, false);
        } catch (Exception e) {
            log.error("isManager error", e);
            return false;
        }

    }

    @Override
    public List<String> getDepartmentCodes(List<UserInfo.Department> departments, String token, String tenantId) {
        List<String> departmentCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(departments)) {
            departmentCodes = departments.stream().map(UserInfo.Department::getDepartmentCode).collect(Collectors.toList());
            BaseClientResp<List<String>> allCodeResp = userCenter.getDeptCodeList(token, new DeptCodeListReq(tenantId, departmentCodes));
            if (Objects.nonNull(allCodeResp) && Objects.nonNull(allCodeResp.getData())) {
                departmentCodes.addAll(allCodeResp.getData());
            }
        }
        return departmentCodes;
    }

    @Override
    @Cached(expire = 3, cacheType = CacheType.LOCAL)
    public IKnowPowerResp getIknowPower(String tenantId) {
        IKnowPowerResp resp = new IKnowPowerResp();
        if (StringUtils.isBlank(strategyCode)) {
            return resp;
        }
        PowerRecordInfoReq req = new PowerRecordInfoReq();
        req.setBelongingCode(tenantId);
        req.setStrategyCode(strategyCode);
        req.setRecordType(2);
        BaseClientResp<PowerRecordResp> powerRecordResp = userCenter.getPowerRecord(req);
        if (Objects.nonNull(powerRecordResp) && Objects.nonNull(powerRecordResp.getData()) && CollectionUtils.isNotEmpty(powerRecordResp.getData().getRulesMappingList())) {
            PowerRecordRulesReq myFileRule = powerRecordResp.getData().getRulesMappingList().stream().filter(r -> r.getRuleCode().startsWith("mySpace_") && r.getStatus().equals(1)).findFirst().orElse(null);
            if (Objects.nonNull(myFileRule)) {
                resp.setMySpace(StringComUtils.convertSize(myFileRule.getRuleCode().replace("mySpace_", "")));
            }
            PowerRecordRulesReq orgFileRule = powerRecordResp.getData().getRulesMappingList().stream().filter(r -> r.getRuleCode().startsWith("orgSpace_") && r.getStatus().equals(1)).findFirst().orElse(null);
            if (Objects.nonNull(orgFileRule)) {
                resp.setOrgSpace(StringComUtils.convertSize(orgFileRule.getRuleCode().replace("orgSpace_", "")));
            }
            PowerRecordRulesReq maxAnalysisRule = powerRecordResp.getData().getRulesMappingList().stream().filter(r -> r.getRuleCode().startsWith("maxAnalysisFiles_") && r.getStatus().equals(1)).findFirst().orElse(null);
            if (Objects.nonNull(maxAnalysisRule)) {
                resp.setMaxAnalysis(Long.parseLong(maxAnalysisRule.getRuleCode().replace("maxAnalysisFiles_", "")));
            }
        }
        if(Objects.isNull(resp.getOrgSpace())){
            resp.setOrgSpace(0L);
        }
        if(Objects.isNull(resp.getMySpace())){
            resp.setMySpace(0L);
        }
        if(Objects.isNull(resp.getMaxAnalysis())){
            resp.setMaxAnalysis(0L);
        }
        return resp;
    }
}
