package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoSplitReq {

    private Data data;

    @lombok.Data
    public static class Data {
        /**
         * 原视频文件路径，视频转码、抽帧使用
         */
        @ApiModelProperty("原视频文件路径，视频转码、抽帧使用")
        private String sourceUrl;
        /**
         * 分辨率高度
         */
        @ApiModelProperty("分辨率高度，不传默认原分辨率")
        private int height;
        /**
         * 分辨率宽度
         */
        @ApiModelProperty("分辨率宽度，不传默认原分辨率")
        private int width;

        /**
         * 抽帧返回格式  jpg 、png
         */
        @ApiModelProperty(" 抽帧返回格式  jpg 或png，不传默认为jpg")
        private String imgFormat = "jpg";

        /**
         * 任务来源
         */
        @ApiModelProperty("webdav上传客户端id")
        private String clientId;

        /**
         * 源文件MD5
         */
        @ApiModelProperty("源文件MD5")
        private String md5;
        /**
         * 输出图片质量参数 2 - 31
         */
        @ApiModelProperty("输出图片质量参数 1 - 31，默认为8,数字越小图片质量越好")
        private Integer qscale = 8;
        /**
         * 视频抽帧上传类型  默认1：为原上传模式  2：抽帧图片压缩成zip包上传
         */
        @ApiModelProperty("视频抽帧上传类型  默认1：为原上传模式  2：抽帧图片压缩成zip包上传")
        private int uploadType = 1;
        /**
         * 租户code
         */
        @ApiModelProperty("租户code，不传为默认org-0")
        private String orgCode;
        /**
         * 资源id
         */
        @ApiModelProperty("资源id")
        private String resourceId;
        @ApiModelProperty("抽帧间隔，默认为1秒一抽")
        private Double interval = 1.0;
        @ApiModelProperty("1,只抽关键帧，2相似度抽帧")
        private Integer splitType;
        @ApiModelProperty("相似度 0-1")
        private Double similarDegree = 0.1;
        /**
         * 抽帧开始时间(单位：ms)
         */
        @ApiModelProperty(value = "开始时间(单位：ms)", required = true)
        private Long startTime;

        /**
         * 抽帧结束时间(单位：ms)
         */
        @ApiModelProperty(value = "结束时间(单位：ms)", required = true)
        private Long endTime;

        @ApiModelProperty("抽帧帧率")
        private Double targetFrameRate;
    }

}
