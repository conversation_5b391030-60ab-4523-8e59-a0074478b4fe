package com.linker.fusion.knowledgecenter.infrastructure.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 该类的定义说明、使用范围
 *
 * <AUTHOR>
 * @version 对应产品版本号
 * @since 2023/1/6 13:05
 */
@Slf4j
public class BatchTaskUtils {


    public static <T> void asyncBatchHandle(List<T> list, Integer maxSize, Consumer<T> consumer) {
        List<List<T>> lists = splitList(list, maxSize);
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
        log.info("任务被拆分成{}份", CollectionUtils.size(lists));
        if (CollectionUtils.isEmpty(lists)) {
            return;
        }
        for (List<T> ts : lists) {
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture
                    .runAsync(RunnableWrapper.of(() -> {
                        for (T t : ts) {
                            consumer.accept(t);
                        }
                    }));
            completableFutures.add(voidCompletableFuture);
        }
        CompletableFuture[] completableArray = completableFutures.toArray(new CompletableFuture[completableFutures.size()]);

        CompletableFuture.allOf(completableArray).join();
    }

    public static <T> void asyncBatchHandleList(List<T> list, Integer maxSize, Consumer<List<T>> consumer) {
        List<List<T>> lists = splitList(list, maxSize);
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
        log.info("任务被拆分成{}份", CollectionUtils.size(lists));
        if (CollectionUtils.isEmpty(lists)) {
            return;
        }
        for (List<T> ts : lists) {
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture
                    .runAsync(() -> {
                        consumer.accept(ts);
                    });
            completableFutures.add(voidCompletableFuture);
        }
        CompletableFuture[] completableArray = completableFutures.toArray(new CompletableFuture[completableFutures.size()]);

        CompletableFuture.allOf(completableArray).join();
    }

    /**
     * 将一个 List 按 maxSize 拆分为若干子任务并发执行，
     * 每个子任务返回一批 DTO，最后把所有结果合并返回。
     *
     * @param list     原始数据
     * @param maxSize  每批最大条数
     * @param mapper   单个子任务的处理器：List<T> -> List<R>
     * @param executor 用于并发执行的线程池，可传 null 使用默认 commonPool
     * @param <T>      源数据类型
     * @param <R>      目标 DTO 类型
     * @return 合并后的 DTO 列表
     */
    public static <T, R> List<R> batchQuery(List<T> list,
                                            int maxSize,
                                            Function<List<T>, List<R>> mapper,
                                            Executor executor) {

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        // 1. 拆分
        List<List<T>> partitions = splitList(list, maxSize);
        log.info("任务被拆分成 {} 份", partitions.size());

        // 2. 提交异步任务
        List<CompletableFuture<List<R>>> futures = partitions.stream()
                .map(p -> CompletableFuture
                        .supplyAsync(() ->
                                mapper.apply(p), executor == null ? ForkJoinPool.commonPool() : executor))
                .collect(Collectors.toList());

        // 3. 等待全部完成并合并结果
        return futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static int generateBatchSize(List list) {
        int maxSize = CollectionUtils.size(list) / 10;
        if (maxSize < 300) {
            maxSize = 300;
        }
        return maxSize;
    }

    public static int generateBatchSize(List list, int initMaxSize) {
        int maxSize = CollectionUtils.size(list) / 10;
        if (maxSize < initMaxSize) {
            maxSize = initMaxSize;
        }
        return maxSize;
    }

    /**
     * 将list分成多份list
     *
     * @param list list对象
     * @param num  每份的数量
     * @return List<List < T>>
     */
    public static <T> List<List<T>> splitList(List<T> list, Integer num) {
        int listSize = list.size();
        ArrayList<List<T>> objects1 = new ArrayList<>();
        int n = 0;
        for (int i = 0; i < listSize / num; i++) {
            ArrayList<T> objects = new ArrayList<>();
            for (; n < num * (i + 1); n++) {
                objects.add(list.get(n));
            }
            objects1.add(objects);
        }
        //处理余数问题
        int a = listSize % num != 0 ? listSize % num : 0;
        for (int i = 0; i < a; i++) {
            ArrayList<T> objects = new ArrayList<>();
            for (; n < num * (listSize / num) + a; n++) {
                objects.add(list.get(n));
            }
            if (!CollectionUtils.isEmpty(objects)) {
                objects1.add(objects);
            }
        }
        return objects1;
    }

}
