package com.linker.fusion.knowledgecenter.infrastructure.model;

import com.linker.fusion.knowledgecenter.infrastructure.entity.QBAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AuthActionModel {
    @ApiModelProperty("设置知识库")
    private LibraryAction libraryAction = new LibraryAction();
    @ApiModelProperty("目录")
    private GroupAction groupAction = new GroupAction();
    @ApiModelProperty("文档")
    private DocAction docAction = new DocAction();
    @ApiModelProperty("表格")
    private TableAction tableAction = new TableAction();
    @ApiModelProperty("faq")
    private FaqAction faqAction = new FaqAction();
    @ApiModelProperty("题库")
    private QBAction qbAction = new QBAction();

    /**
     * 获取权限信息
     *
     * @param level  权限等级
     * @param isRoot 是否是根目录
     * @param isSync 是否是同步目录
     * @return
     */
    public static AuthActionModel create(Integer level, boolean isRoot, boolean isSync) {
        AuthActionModel authMenu = new AuthActionModel();
        switch (level) {
            case 4:
                if (isRoot) {
                    authMenu.libraryAction.setAuth(true);
                    authMenu.libraryAction.setAnalysis(true);
                    authMenu.libraryAction.setEdit(true);
                    authMenu.libraryAction.setDelete(true);
                } else {
                    authMenu.groupAction.setAuth(true);
                    authMenu.groupAction.setMove((!isSync));
                }
            case 3:
                if (isRoot)
                    authMenu.groupAction.setCreate((!isSync) && true);
                else {
                    authMenu.groupAction.setCreateSub((!isSync) && true);
                    authMenu.groupAction.setRename((!isSync) && true);
                    authMenu.groupAction.setDelete((!isSync) && true);
                    authMenu.groupAction.setMove((!isSync) && true);
                    authMenu.groupAction.setAiGenerate((!isSync) && true);
                }
                authMenu.docAction.setUpload((!isSync) && true);
                authMenu.docAction.setEdit(true);
                authMenu.docAction.setDownload(true);
                authMenu.docAction.setDelete((!isSync) && true);
                authMenu.docAction.setExpire(true);
                authMenu.docAction.setRedo(true);
                authMenu.docAction.setMove((!isSync) && true);
                authMenu.docAction.setSegmentEdit(true);
                authMenu.docAction.setChunkEdit(true);
                authMenu.docAction.setRename((!isSync) && true);


                authMenu.tableAction.setUpload((!isSync) && true);
                authMenu.tableAction.setEdit(true);
                authMenu.tableAction.setDelete((!isSync) && true);
                authMenu.tableAction.setMove((!isSync) && true);
                authMenu.tableAction.setExpire(true);
                authMenu.tableAction.setRedo(true);
                authMenu.tableAction.setChunkEdit(true);

                authMenu.faqAction.setCreate((!isSync) && true);
                authMenu.faqAction.setUpload((!isSync) && true);
                authMenu.faqAction.setEdit(true);
                authMenu.faqAction.setDelete((!isSync) && true);
                authMenu.faqAction.setMove((!isSync) && true);
                authMenu.faqAction.setExpire(true);


                authMenu.qbAction.setCreate(true);
                authMenu.qbAction.setEdit(true);
                authMenu.qbAction.setDelete(true);
                authMenu.qbAction.setMove(true);
                authMenu.qbAction.setExpire(true);

            case 2:
                authMenu.docAction.setDownload(true);
                authMenu.tableAction.setDownload(true);
                authMenu.faqAction.setExport(true);
            case 1:
                authMenu.docAction.setView(true);
                authMenu.tableAction.setView(true);
                authMenu.faqAction.setView(true);
                authMenu.qbAction.setView(true);
                break;
        }
        return authMenu;
    }
}
