package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourcePersonEntity;

import java.util.List;


public interface IResourcePersonManager extends IService<ResourcePersonEntity> {
    List<ResourcePersonEntity> list(String docId);

    void deleteByDocId(String docId);

    void clearByDocId(String docId);
}
