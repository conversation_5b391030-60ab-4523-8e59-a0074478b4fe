package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 记录数据迁移历史的日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Getter
@Setter
@TableName("t_migration_task")
public class MigrationTaskEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 迁移类型
     */
    @TableField("type")
    private String type;

    /**
     * 迁移的数据ID或唯一标识符
     */
    @TableField("content_id")
    private String contentId;

    /**
     * 迁移是否成功
     */
    @TableField("success")
    private Boolean success;

    /**
     * 迁移失败时的错误信息
     */
    @TableField("message")
    private String message;

    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
