package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.MigrationTaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IMigrationTaskManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.MigrationTaskMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 记录数据迁移历史的日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
public class MigrationTaskManagerImpl extends ServiceImpl<MigrationTaskMapper, MigrationTaskEntity> implements IMigrationTaskManager {

}
