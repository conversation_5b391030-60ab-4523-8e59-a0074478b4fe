package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.entity.WordsEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ImportTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.WordsStateEnum;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.user.api.dto.UserInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.WORD_NAME_DUPLICATE;

/**
 * <AUTHOR>
 * @description 词条仓储接口
 **/
public interface IWordsManager extends IService<WordsEntity> {
    Logger log = LoggerFactory.getLogger(IWordsManager.class);

    default WordsEntity getOne(Long id) {
        LambdaQueryWrapper<WordsEntity> wrapper =
                new LambdaQueryWrapper<>(WordsEntity.class)
                        .eq(WordsEntity::getId, id);
        return getOne(wrapper);
    }

    default void batchDelete(Collection<Long> ids) {
        LambdaUpdateWrapper<WordsEntity> wrapper =
                new LambdaUpdateWrapper<>(WordsEntity.class)
                        .in(WordsEntity::getId, ids)
                        .eq(WordsEntity::getTenantId, UserContext.getUser().getTenantInfoDTO().getTenantId())
                        .set(WordsEntity::getDeleted, true)
                        .set(WordsEntity::getDeleteTime, System.currentTimeMillis());
        update(wrapper);
    }

    default void updateByIds(List<WordsEntity> words) {
        updateBatchById(words, 1000);
    }

    default void batchChangeState(Collection<Long> ids, Integer state) {
        LambdaUpdateWrapper<WordsEntity> wrapper =
                new LambdaUpdateWrapper<>(WordsEntity.class)
                        .in(WordsEntity::getId, ids)
                        .eq(WordsEntity::getTenantId, UserContext.getUser().getTenantInfoDTO().getTenantId())
                        .eq(WordsEntity::getDeleted, false)
                        .set(WordsEntity::getState, state);

        update(wrapper);
    }

    default void modifyByName(WordsEntity entity) {
        LambdaUpdateWrapper<WordsEntity> wrapper = new LambdaUpdateWrapper<>(WordsEntity.class)
                .eq(WordsEntity::getDeleted, false)
                .eq(WordsEntity::getTenantId, UserContext.getUser().getTenantInfoDTO().getTenantId())
                .eq(WordsEntity::getName, entity.getName())
                .set(StringUtils.isNotBlank(entity.getProSynonyms()), WordsEntity::getProSynonyms, entity.getProSynonyms())
                .set(Objects.nonNull(entity.getState()), WordsEntity::getState, entity.getState());
        try {
            update(wrapper);
        } catch (DuplicateKeyException e) {
            log.warn(Throwables.getStackTraceAsString(e));
            throw new ServiceException(WORD_NAME_DUPLICATE);
        }
    }

    default void modify(WordsEntity entity) {
        LambdaUpdateWrapper<WordsEntity> wrapper = new LambdaUpdateWrapper<>(WordsEntity.class)
                .eq(WordsEntity::getDeleted, false)
                .eq(WordsEntity::getTenantId, UserContext.getUser().getTenantInfoDTO().getTenantId())
                .eq(WordsEntity::getId, entity.getId())
                .set(StringUtils.isNotBlank(entity.getName()), WordsEntity::getName, entity.getName())
                .set(StringUtils.isNotBlank(entity.getProSynonyms()), WordsEntity::getProSynonyms, entity.getProSynonyms())
                .set(Objects.nonNull(entity.getState()), WordsEntity::getState, entity.getState());
        try {
            update(wrapper);
        } catch (DuplicateKeyException e) {
            log.warn(Throwables.getStackTraceAsString(e));
            throw new ServiceException(WORD_NAME_DUPLICATE);
        }
    }

    default Page<WordsEntity> page(int page, int pageSize, String name, Long groupId, Integer type, WordsStateEnum wordsStateEnum) {
        UserInfo.TenantInfoDTO tenantInfoDTO = UserContext.getUser().getTenantInfoDTO();
        LambdaQueryWrapper<WordsEntity> wrapper =
                new LambdaQueryWrapper<>(WordsEntity.class)
                        .eq(WordsEntity::getTenantId, tenantInfoDTO.getTenantId())
                        .eq(WordsEntity::getGroupId, groupId)
                        .eq(WordsEntity::getType, type)
                        .eq(WordsEntity::getDeleted, false)
                        .orderByDesc(WordsEntity::getId);
        if (Objects.nonNull(wordsStateEnum)) {
            wrapper.eq(WordsEntity::getState, wordsStateEnum.getCode());
        }

        wrapper.and(StringUtils.isNotBlank(name), w ->
                w.like(StringUtils.isNotBlank(name), WordsEntity::getName, StringComUtils.replaceSqlEsc(name))
                        .or()
                        .like(StringUtils.isNotBlank(name), WordsEntity::getProSynonyms, StringComUtils.replaceSqlEsc(name))
        );
        Page<WordsEntity> result = Page.of(page, pageSize);
        List<WordsEntity> wordsEntities = getBaseMapper().selectList(result, wrapper);
        result.setRecords(wordsEntities);
        return result;
    }


    default Long insert(WordsEntity entity) {

        if (Objects.isNull(entity.getImportSource())) {
            entity.setImportSource(ImportTypeEnum.MANUAL.getCode());
        }
        entity.setDeleteTime(" ");
        return (long) getBaseMapper().insert(entity);
    }

    default List<WordsEntity> list(String content, Integer state, Long groupId, Integer type, String tenantId) {
        LambdaQueryWrapper<WordsEntity> wrapper =
                new LambdaQueryWrapper<>(WordsEntity.class)
                        .eq(StringUtils.isNotBlank(tenantId), WordsEntity::getTenantId, tenantId)
                        .eq(WordsEntity::getGroupId, groupId)
                        .eq(WordsEntity::getType, type)
                        .eq(Objects.nonNull(state), WordsEntity::getState, state)
                        .eq(WordsEntity::getDeleted, false)
                        .orderByDesc(WordsEntity::getId);
        wrapper.and(StringUtils.isNotBlank(content), w ->
                w.like(StringUtils.isNotBlank(content), WordsEntity::getName, StringComUtils.replaceSqlEsc(content))
                        .or()
                        .like(StringUtils.isNotBlank(content), WordsEntity::getProSynonyms, StringComUtils.replaceSqlEsc(content))
        );
        return getBaseMapper().selectList(wrapper);
    }

    default List<WordsEntity> list(List<Long> groupIds, Integer type, String tenantId) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<WordsEntity> wrapper =
                new LambdaQueryWrapper<>(WordsEntity.class)
                        .eq(StringUtils.isNotBlank(tenantId), WordsEntity::getTenantId, tenantId)
                        .in(CollectionUtils.isNotEmpty(groupIds), WordsEntity::getGroupId, groupIds)
                        .eq(WordsEntity::getType, type)
                        .eq(WordsEntity::getState, WordsStateEnum.ENABLE.getCode())
                        .eq(WordsEntity::getDeleted, false)
                        .orderByDesc(WordsEntity::getId);
        return getBaseMapper().selectList(wrapper);
    }

    default List<WordsEntity> list(Collection<Long> ids, Boolean deleted) {
        return list(UserContext.getUser().getTenantInfoDTO().getTenantId(), ids, deleted, false);
    }

    default List<WordsEntity> list(String tenantId, Collection<Long> ids, Boolean deleted, boolean orderByIdDesc) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<WordsEntity> wrapper =
                new LambdaQueryWrapper<>(WordsEntity.class)
                        .in(WordsEntity::getId, ids)
                        .eq(WordsEntity::getTenantId, tenantId)
                        .eq(Objects.nonNull(deleted), WordsEntity::getDeleted, deleted)
                        .orderByDesc(orderByIdDesc, WordsEntity::getId);
        return getBaseMapper().selectList(wrapper);
    }

    void deleteByGroupIds(List<Long> groupIds);

    default List<WordsEntity> listEnabled(String tenantId, Collection<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<WordsEntity> wrapper =
                new LambdaQueryWrapper<>(WordsEntity.class)
                        .eq(WordsEntity::getTenantId, tenantId)
                        .in(WordsEntity::getGroupId, groupIds)
                        .eq(WordsEntity::getState, WordsStateEnum.ENABLE.getCode())
                        .eq(WordsEntity::getDeleted, false);
        return getBaseMapper().selectList(wrapper);
    }

    WordsEntity getByName(String tenantId, Integer type, String name);
}
