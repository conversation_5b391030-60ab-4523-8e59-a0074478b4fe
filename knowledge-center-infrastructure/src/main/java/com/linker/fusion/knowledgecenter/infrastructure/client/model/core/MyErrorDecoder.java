package com.linker.fusion.knowledgecenter.infrastructure.client.model.core;

import com.github.lianjiatech.retrofit.spring.boot.core.ErrorDecoder;
import com.github.lianjiatech.retrofit.spring.boot.exception.ReadResponseBodyException;
import com.github.lianjiatech.retrofit.spring.boot.exception.RetrofitException;
import com.github.lianjiatech.retrofit.spring.boot.exception.RetrofitIOException;
import com.github.lianjiatech.retrofit.spring.boot.util.RetrofitUtils;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;

/**
 * 该类的定义说明、使用范围
 *
 * <AUTHOR>
 * @version 对应产品版本号
 * @since 2023/5/19 17:28
 */
@Component
public class MyErrorDecoder implements ErrorDecoder {
    @Override
    public RuntimeException invalidRespDecode(Request request, Response response) {
        if (response.isSuccessful()) {
            return null;
        }
        String message = response.message();
        int code = response.code();
        String msg = String.format(" code:%s,message:%s", code, message);
        try {
            String responseBody = RetrofitUtils.readResponseBody(response);
            if (StringUtils.hasText(responseBody)) {
                msg += ", body=" + responseBody;
            }
        } catch (ReadResponseBodyException e) {
            throw new RetrofitException(
                    String.format("read ResponseBody error!  response=%s", response), e);
        } finally {
            response.close();
        }
        return new RetrofitException(msg);
    }

    @Override
    public RuntimeException ioExceptionDecode(Request request, IOException cause) {
        return new RetrofitIOException(cause.getMessage(), cause);
    }

    @Override
    public RuntimeException exceptionDecode(Request request, Exception cause) {
        if (cause instanceof RetrofitException) {
            return (RetrofitException) cause;
        }
        return new RetrofitException(cause.getMessage(), cause);
    }
}
