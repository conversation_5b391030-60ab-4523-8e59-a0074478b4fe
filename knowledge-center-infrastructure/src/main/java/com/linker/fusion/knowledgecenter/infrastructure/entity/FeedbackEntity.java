package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 反馈学习表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_feedback")
public class FeedbackEntity extends BaseEntity {

    /**
     * ES文档uid
     */
    @TableField("uid")
    private String uid;

    /**
     * 智能体ID
     */
    @TableField("agent_id")
    private Long agentId;

    /**
     * 问题内容
     */
    @TableField("question")
    private String question;

    /**
     * 反馈内容
     */
    @TableField("content")
    private String content;

    /**
     * AI学习状态 0-待学习 1-学习通过 2-学习不通过
     */
    @TableField("learn_status")
    private Integer learnStatus;

    /**
     * 人为操作 0-初始化 1-采纳 2-忽略
     */
    @TableField("operation_tag")
    private Integer operationTag;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private String operatorId;

    /**
     * 操作时间
     */
    @TableField("operation_time")
    private LocalDateTime operationTime;

    /**
     * 采纳时关联FAQ ID
     */
    @TableField("faq_id")
    private Long faqId;
}
