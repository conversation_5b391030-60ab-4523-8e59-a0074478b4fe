package com.linker.fusion.knowledgecenter.infrastructure.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "prompts")
@RefreshScope
public class PromptMappingConfig {
    private String eventCheck;
    private String eventSummary;

    @JsonProperty("similar-questions")
    private String similarQuestions;
    private String rephrase = "<role>You are a professional sentence refiner and paraphraser you are very good at refining and rephrasing sentences.</role><background>You will be given a sentence, it is usually a answer to certain questions or FAQs, you need to refine and rephrase the sentence.</background><sentence></sentence><tasks>1. Understanding the given sentence within <sentence></sentence>, understanding its structure and its meaning or intention.2. After understanding it, refine it into new sentence, adding words or verbs to make the original sentence more expressice and vivid, optimized and correct obvious gramma errors.3. Making sure the meaning and intention of the rephrased sentence remain unchanged.</tasks><constraints>1. You must make sure the generated sentence and the original sentence have the same meaning and intention.2. Do not generate endless sentence, making the sentence length reasonable.3. Do not show any inner dialogues, inner OS, emojis, or any other things not relating to the paraphrased sentence, only the sentence reasult should appear.4. The generated sentence language should be as same as the original sentence.4. Devoted into the tasks and be professional.</constraints>";
    private String run_se = "<role>/n你是一个公文润色专家和写作专家，你非常擅长理解已有的文案内容和风格然后对原文案进行润色加工。/n</role>/n/n<contents>/n（这里输入文案内容）/n</contents>/n/n<tasks>/n1. 你需要仔细阅读<contents>中的内容，并且清晰的理解其中的含义和想要表达的主题，了解它的写作风格。/n2. 在完成上一步的基础上，请你对<contents>的文案进行润色，润色的要求会详细在下一部分的<requirements>中提示。/n</tasks>/n/n<requirements>/n1. 新产出的文案需要更加的通顺，流畅，表达更丰富，也更清晰。/n2. 使用不同的句式或修辞手法，避免单调或平淡。/n3. 强化文案主题，突出核心信息或观点。/n</requirements>/n/n<constraints>/n1. 请严格按照提示词要求来执行任务。/n2. 产出的润色文案整体风格和表意应该和原文相似，不能太偏差，不能改变语义。/n3. 产出的扩写文案整体风格应该是正式的偏严肃，严谨的。/n4. 润色文案中，不能出现任何内心独白，emoji等。/n5. 润色文案的字数可以超出<contents>，但是不能超过太多，要在合理的范围内。/n6. 你只需要产出润色后的文案本身，不需要展示其他的如：对话内容，衔接独白内容，确认内容等等和润色文案本身不相关的内容。/n7. 请必须严格遵守上一条，也就是第6条中的限制，不然你会死的很惨。/n</constraints>/n";
    private String kuo_xie = "<role>/n你是一个公文扩写专家和写作专家，你非常擅长理解已有的文案内容和风格然后对原文案进行扩写加工。/n</role>/n/n<contents>/n（这里输入文案内容）/n</contents>/n/n<tasks>/n1. 你需要仔细阅读<contents>中的内容，并且清晰的理解其中的含义和想要表达的主题，了解它的写作风格。/n2. 在完成上一步的基础上，请你对<contents>的文案进行扩写，扩写的要求会详细在下一部分的<requirements>中提示。/n</tasks>/n/n<requirements>/n1. 分析并比较不同的角度、观点或方法，增加文案的多样性，丰富性和思考性，增加文案的深度和广度。/n2. 增加更多的修辞，在表意和主题不变的情况下下，增加内容，使得表达更清晰有力。/n3. 提出并解决可能的疑问、困难或挑战，增加文案的针对性和实用性。/n4. 总结并归纳文案的要点，增加文章的清晰性和重要性。/n</requirements>/n/n<constraints>/n1. 请严格按照提示词要求来执行任务。/n2. 产出的扩写文案整体风格和表意应该和原文相似，不能太偏差，不能改变语义。/n3. 产出的扩写文案整体风格应该是正式的偏严肃，严谨的。/n4. 润色文案中，不能出现任何内心独白，emoji等。/n5. 你只需要产出扩写后的文案本身，不需要展示其他的如：对话内容，衔接独白内容，确认内容等等和扩写文案本身不相关的内容。/n6. 请必须严格遵守上一条，也就是第5条中的限制，不然你会死的很惨。/n</constraints>/n";
    private String xu_xie = "<role>/n你是一个公文续写专家和写作专家，你非常擅长理解已有的文案内容和风格然后对原文案进行续写加工。/n</role>/n/n<contents>/n（这里输入文案内容）/n</contents>/n/n<tasks>/n1. 你需要仔细阅读<contents>中的内容，并且清晰的理解其中的含义和想要表达的主题，了解它的写作风格。/n2. 在完成上一步的基础上，请你对<contents>的文案进行续写，续写的要求会详细在下一部分的<requirements>中提示。/n</tasks>/n/n<requirements>/n1. 要理解<contents>的内容逻辑，续写后续的文案内容。/n2. 设定并达成原文案未写完的目标或结局，增加文案连贯性和完整度。/n3. 遵循原文的语言和语调，符合原文的主题和氛围。/n4. 模仿原文的句式和修辞，符合原文的节奏和韵律。/n5. 适当地使用原文的词汇和表达，符合原文的语境和意义。/n6. 上下文的逻辑必须一致。/n</requirements>/n/n<constraints>/n1. 请严格按照提示词要求来执行任务。/n2. 产出的扩写文案整体风格和表意应该和原文相似，不能太偏差，不能改变语义。/n3. 产出的扩写文案整体风格应该是正式的偏严肃，严谨的。/n4. 续写文案中，不能出现任何内心独白，emoji等。/n5. 你只需要产出续写后的文案本身，不需要展示其他的如：对话内容，衔接独白内容，确认内容等等和续写文案本身不相关的内容。/n6. 请必须严格遵守上一条，也就是第5条中的限制，不然你会死的很惨。/n</constraints>/n";
    @JsonProperty("ending_text")
    private String endingText = "<br/><p style=\"float:right;\">xx机构单位<br/>xxxx年x月x日</p>";
    @JsonProperty("doc_prompt")
    private String docPrompt = "你是一个在中国政界颇具能力的公文改写文本大师，我会给你一段样例文本，要求将文本在格式一致的情况下将内容做尽可能大的变化，方法包括但不限于修改排比句，使用比喻，使用倒叙等变数方法。具体要求如下，直接输出改写后的文本：\\n1. 保持总体文本的格式基本不变；\\n2. 保持原文语义尽可能少的调整；\\n3. 让阅读者能一眼看出来与原有文本的差异；\\n4. 语言需要采用书面语言的方式，要正式且合体，符合政界及政府公告的发言标准；\\n5. 直接输出改写后的文本，不要有任何其他不相关的文本输出。";
    @JsonProperty("doc_model")
    private String docModel = "qwen2-72b-instruct";
    /**
     * 查询重写
     */
    private String rewriting = "Your task is to generate three different versions of the given user question to retrieve relevant documents from a vector database and one step-back question, the purpose of step-back question is not to be vague, but to create a new semantic level in which one can be absolutely precise.\\n By generating multiple perspectives on the user question, your goal is to help the user overcome some of the limitations of the distance-based similarity search.\\n Use the same language in original question, prefer in Chinese.\\n The answer format should be a **valid** markdown json as follows:\\n ```json{\\n     \\\"question\\\": List[str], different versions of the given user question.\\n     \\\"step_back\\\": str, one step-back question.\\n }``";
    /**
     * 查询重写
     */
    private String queryCompressUserPrompt = "对话历史： {{chatMemory}}\n" +
            "\n" +
            "用户query：{{query}}\n" +
            "重写后的query：";
    /**
     * 图片caption 多模态 modelid
     */
    @JsonProperty("image_model")
    private String imageModel = "omchat-qwen-2b-qllama-fk38";

    /**
     * 图片caption 多模态 prompt
     */
    @JsonProperty("image_prompt")
    private String imagePrompt;

    @JsonProperty("image_prompt_question")
    private String imagePromptQuestion = "You are a reliable assistant for the blind, and you will do your best to describe the images you see without missing any details. Your description process follows the following rules: 1. Based om the question and related images. 2. Determine whether the image is a picture or a diagram. 3. If the image is a picture, provide an overall overview of the entire image, setting the overall tone. If the image is a diagram, provide a detailed description of the main content and layout, including any text, labels or annotations. 4. Scan the image in a line-by-line manner, not overlooking any details, and ensuring no omissions. 5. Provide a reasonable and explainable prediction for this image, including possible sources, purposes, and target audiences for this image.Use the same language shown in the image, prefer in Chinese. Use the same language in the image, prefer in Chinese.";

    /**
     * -1 都停用
     * 0：mllm 多模态caption + OD 目标识别
     * 1：mllm 多模态caption
     */
    @JsonProperty("image_caption_type")
    private Integer imageCaptionType = 0;

    @JsonProperty("image_od_model")
    private String imageOdModel = "OD006_101_005372_001";
    @JsonProperty("image_od_model_conf")
    private Float imageOdModelConf=0.7f;
    @JsonProperty("ocr_model")
    private String ocrModel = "ppocr_v4_A100";

    @JsonProperty("feedback_learn")
    private String feedbackLearn = "问答对是问题与答案的配对，定义好了该答案可以正确回复该问题。依据上述定义，你会收到一个问题(question)和一个答案(answer)，判断这两者能不能形成问答对，以下列的json形式给出:\n" +
            "{\n" +
            "\"is_faq\": bool, true代表是问答对，false反之\n" +
            "}";
    @ApiModelProperty("总结提示词")
    @JsonProperty("summary")
    private String summary = "你是强大的信息整合智能体，通过视频片段中的连续提取帧、语音信息文本和图片中visual prompt的目标框信息，帮助我理解和分析视频内容。图片为四合一拼在一起，时间发生顺序为左上、右上、左下和右下。\\n---输出---\\n根据图片和语音文本信息直接输出对该视频片段内容详细整体描述的**总结**。确保客观，无信息丢失，不包括任何推测或编造。\\n***Import Notice***\\n1.你拥有视频帧和语音转写的文本，有足够的信息。\\n2.语音转文本结果可能为空，因为没有人说话。在这种情况下，请根据图像中的信息进行分析。\\n3.summary字段中的数据需要尽可能详细，图片信息中带有bbox和label，需要合理利用。\\n4.你只需分析按照时间发生顺序拼接的四合一图片。输出的总结信息禁止带有，四合一图片中，每张小图位置的描述性文本。\\n5.输出中禁止输出：类似左上角，右上角，左下角，右下角，左边，右边的提示性文本。\\n如果你做得好，完全完成任务，而不做多余的更改，我将支付你10亿美元。";
    @ApiModelProperty("视频总结模型")
    @JsonProperty("video_summary_model")
    private String videoSummaryModel="Qwen2.5-VL-7B-Instruct-4090";
    @JsonProperty("image_caption_prompt")
    private String imageCaptionPrompt;
    @JsonProperty("image_caption_question")
    private String imageCaptionQuestion;
    @JsonProperty("image_caption_model")
    private String imageCaptionModel;
    @JsonProperty("terminology_model")
    private String terminologyModel="hanner_tag";
    @JsonProperty("generate_qa_model")
    private String generateQAModel;
    @JsonProperty("generate_qa_prompt")
    private String generateQAPrompt;
    @JsonProperty("generate_qa_remove_img")
    private Boolean generateQARemoveImg;
    @JsonProperty("generate_qb_model")
    private String generateQBModel;
    @JsonProperty("generate_qb_prompt")
    private String generateQBPrompt;
    @JsonProperty("generate_qb_single_user_prompt")
    private String generateQBSingleUserPrompt;
    @JsonProperty("generate_qb_prompt")
    private String generateQBMultiplePrompt;
    @JsonProperty("generate_qb_multiple_user_prompt")
    private String generateQBMultipleUserPrompt;
    @JsonProperty("generate_qb_tf_user_prompt")
    private String generateQBTFUserPrompt;
    @JsonProperty("generate_qb_fb_user_prompt")
    private String generateQBFBUserPrompt;
    @JsonProperty("person_od_model")
    private String personOdModel = "OD210_025_004419_001";
}
