package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

//{
//        "task": "transcribe",
//        "language": "zh",
//        "duration": 60.00325,
//        "text": "同志们,大家好,新闻联播急速版,帮你快速了解重点新闻内容。开始之前可以先暂停看一下本期新闻标题,本期是2025年2月8日,现在开始本期内容。5、新春之际各地积极推进高质量发展,新疆加快西西东输备用管道建设,计划打造能源资源战略保障基地,这将义乌国际枢纽港等项目加速推进,计划完成综合交通投资3500亿元,天津启动汽车配套项目聚焦新能源汽车等产业链,陕西扩厂高世代基板玻璃生产线推进科技创新,黑龙将实施1000个重点产业项目,培育绿色工厂和智能工厂,山东明确实项重点改革任务,以改革创新推动高质量发展。6、哈尔滨第九届亚东会首日产生17枚金牌,中国00后选手理方会或自由式滑雪女子入行场地技巧金牌、张可兴或银牌。",
//        "words": [
//        {
//        "start": 0.0,
//        "end": 0.1,
//        "word": "同",
//        "probability": 0.9873046875
//        }
//        ],
//        "segments": [
//        {
//        "id": 1,
//        "seek": 2272,
//        "start": 0.0,
//        "end": 5.32,
//        "text": "同志们,大家好,新闻联播急速版,帮你快速了解重点新闻内容。",
//        "tokens": [
//        50365,
//        13089,
//        44700,
//        9497,
//        11,
//        15248,
//        11,
//        12560,
//        8259,
//        119,
//        8171,
//        242,
//        49993,
//        36379,
//        31217,
//        42096,
//        11,
//        4845,
//        106,
//        2166,
//        10251,
//        31217,
//        2289,
//        17278,
//        12624,
//        12579,
//        12560,
//        8259,
//        119,
//        34742,
//        25750,
//        1543,
//        50634
//        ],
//        "temperature": 0.0,
//        "avg_logprob": -0.056812958791852,
//        "compression_ratio": 1.2134831460674158,
//        "no_speech_prob": 0.14990234375,
//        "words": [
//        {
//        "start": 0.0,
//        "end": 0.1,
//        "word": "同",
//        "probability": 0.9873046875
//        },
//        {
//        "start": 0.1,
//        "end": 0.3,
//        "word": "志",
//        "probability": 0.9990234375
//        }
//        ]
//        }
//        ]
//        }


@NoArgsConstructor
@Data
public class TranscriptionsUrlResp {

    @JsonProperty("task")
    private String task;
    @JsonProperty("language")
    private String language;
    @JsonProperty("duration")
    private Double duration;
    @JsonProperty("text")
    private String text;
    @JsonProperty("words")
    private List<WordsDTO> words;
    @JsonProperty("segments")
    private List<SegmentsDTO> segments;

    @NoArgsConstructor
    @Data
    public static class WordsDTO {
        @JsonProperty("start")
        private Double start;
        @JsonProperty("end")
        private Double end;
        @JsonProperty("word")
        private String word;
        @JsonProperty("probability")
        private Double probability;
    }

    @NoArgsConstructor
    @Data
    public static class SegmentsDTO {
        @JsonProperty("id")
        private Integer id;
        @JsonProperty("seek")
        private Integer seek;
        @JsonProperty("start")
        private Double start;
        @JsonProperty("end")
        private Double end;
        @JsonProperty("text")
        private String text;
        @JsonProperty("tokens")
        private List<Integer> tokens;
        @JsonProperty("temperature")
        private Double temperature;
        @JsonProperty("avg_logprob")
        private Double avgLogprob;
        @JsonProperty("compression_ratio")
        private Double compressionRatio;
        @JsonProperty("no_speech_prob")
        private Double noSpeechProb;
        @JsonProperty("words")
        private List<WordsDTO> words;

        @NoArgsConstructor
        @Data
        public static class WordsDTO {
            @JsonProperty("start")
            private Double start;
            @JsonProperty("end")
            private Double end;
            @JsonProperty("word")
            private String word;
            @JsonProperty("probability")
            private Double probability;
        }
    }
}
