package com.linker.fusion.knowledgecenter.infrastructure.enums;

import com.linker.fusion.knowledgecenter.infrastructure.constants.LogRecordSubType;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum OprationLogRecordSubTypeEnum {
    /**
     * @see LogRecordSubType
     */
    // 文件管理类操作
    IMPORT_FILE("FILE_MANAGEMENT", "导入文件"),
    //    EDIT_FILE("FILE_MANAGEMENT", "编辑文件"),
    DOWNLOAD_FILE("FILE_MANAGEMENT", "下载文件"),
    //    REQUEST_DOWNLOAD("FILE_MANAGEMENT", "申请下载"),
    DELETE_FILE("FILE_MANAGEMENT", "删除文件"),
//    ACTIVATE("FILE_MANAGEMENT", "生效"),
//    DEACTIVATE("FILE_MANAGEMENT", "失效"),
//    RELEARN("FILE_MANAGEMENT", "重新学习"),
//    MOVE_FILE("FILE_MANAGEMENT", "移动文件"),
//    PREVIEW_FILE("FILE_MANAGEMENT", "预览文件"),
//    SHARE_FILE("FILE_MANAGEMENT", "共享文件"),
//    RENAME_FILE("FILE_MANAGEMENT", "重命名文件"),
//    SAVE_FRAGMENT("FILE_MANAGEMENT", "保存片段"),
//    ARCHIVE("FILE_MANAGEMENT", "归档"),
//    MODIFY_SEGMENTS("FILE_MANAGEMENT", "增删改分段分块"),
//    DOWNLOAD_FRAGMENT("FILE_MANAGEMENT", "下载片段"),

    // 知识库管理类操作
    CREATE_KNOWLEDGE_BASE("FILE_MANAGEMENT", "创建知识库"),
    DELETE_KNOWLEDGE_BASE("FILE_MANAGEMENT", "删除知识库"),
    ADD_DIRECTORY("FILE_MANAGEMENT", "添加目录"),
    //    UPDATE_BASE_INFO("FILE_MANAGEMENT", "修改知识库基本信息"),
//    RENAME_DIRECTORY("FILE_MANAGEMENT", "重命名目录"),
    DELETE_DIRECTORY("FILE_MANAGEMENT", "删除目录"),
    MOVE_DIRECTORY("FILE_MANAGEMENT", "移动目录");

    @Getter
    private final String parentCode;
    @Getter
    private final String desc;


    public static String getByName(String name) {
        for (OprationLogRecordSubTypeEnum oprationLogRecordSubTypeEnum : values()) {
            if (oprationLogRecordSubTypeEnum.name().equals(name)) {
                return oprationLogRecordSubTypeEnum.getDesc();
            }
        }
        return null;
    }

}