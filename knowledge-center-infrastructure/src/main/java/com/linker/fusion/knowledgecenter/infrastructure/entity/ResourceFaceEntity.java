package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Objects;

@Data
@TableName("t_resource_face")
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ResourceFaceEntity extends BaseEntity {
    /**
     * 人脸库id
     */
    @TableField("source_id")
    private String sourceId;
    /**
     * 分段id
     */
    @TableField("segment_id")
    private String segmentId;
    /**
     * 文档id
     */
    @TableField("doc_id")
    private String docId;
    /**
     * 镜头id
     */
    @TableField("frame_id")
    private String frameId;

    @TableField("name")
    private String name;
    /**
     * 简介
     */
    @TableField("`desc`")
    private String desc;
    /**
     * 头像地址
     */
    @TableField("head_url")
    private String headUrl;
    /**
     * 位置
     */
    @TableField("position")
    private String position;
    /**
     * 其他扩展数据
     */
    @TableField("ext")
    private String ext;
    /**
     * 位置
     */
    @TableField("time_point")
    private Long timePoint;

    public JSONObject getExtJson(){
        if(Objects.isNull(ext))
            return new JSONObject();
        else
            return JSONObject.parseObject(ext);
    }
}
