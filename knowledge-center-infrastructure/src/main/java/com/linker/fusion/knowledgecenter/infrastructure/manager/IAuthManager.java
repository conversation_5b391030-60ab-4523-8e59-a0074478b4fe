package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AuthEntity;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * AUTH表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
public interface IAuthManager extends IService<AuthEntity> {
     void remove(String sourceId, Integer sourceType, String authId, Integer authType);
     void removeBySourceId(String sourceId, Integer sourceType);
     void update(Long id, Integer level);

    List<AuthEntity> listBySourceIds(Collection<String> authIds, Integer autType);

    List<AuthEntity> listByParentId(Long parentId, Integer sourceType);

    List<AuthEntity> listAuths(String tenantId, String userCode, List<String> departmentList, List<String> sourceIds, Integer sourceType);

    AuthEntity get(String sourceId, Integer sourceType, String authId, Integer authType);

    List<AuthEntity> list(List<String> sourceIds, Integer sourceType, String authId, Integer authType);

    void update(String sourceId, Integer sourceType, String authId, Integer authType, Integer authLevel);

    List<AuthEntity> listAuthsByParentIds(List<Long> parentIds);
}
