package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 申请列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Getter
@Setter
@TableName("t_auth_approve")
public class AuthApproveEntity extends BaseEntity {

    /**
     * 审批资源类型 1-目录 2-文件
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 审批资源唯一标识
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 申请权限等级
     */
    @TableField("auth_level")
    private Integer authLevel;

    /**
     * 审批流程key
     */
    @TableField("apply_reason")
    private String applyReason;

    /**
     * 审批流程key
     */
    @TableField("approve_process_key")
    private String approveProcessKey;

    /**
     * 审批流程key
     */
    @TableField("approve_instance_id")
    private String approveInstanceId;

    /**
     * 审批流程状态 0-审批中 1-同意 2-拒绝 3-撤销
     */
    @TableField("state")
    private Integer state;
}
