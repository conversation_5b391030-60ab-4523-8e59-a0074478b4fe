package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FeedbackEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IFeedbackManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.FeedbackMapper;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 反馈学习表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Service
public class FeedbackManagerImpl extends ServiceImpl<FeedbackMapper, FeedbackEntity> implements IFeedbackManager {

    @Override
    public Collection<String> getAllUserId() {
        QueryWrapper<FeedbackEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("deleted", 0);
        queryWrapper.select("DISTINCT creator_id");
        List<String> ids = lambdaQuery().getBaseMapper().selectList(queryWrapper).stream().map(x -> x.getCreatorId()).collect(Collectors.toList());
        return ids;
    }
}
