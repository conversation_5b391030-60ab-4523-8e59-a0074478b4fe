package com.linker.fusion.knowledgecenter.infrastructure.client.aas.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class WorkflowAsyncRunResp {

    /**
     * 错误码
     */
    private String code;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 工作流的运行实例id，如果执行失败，该值为null
     */
    @JsonProperty("workflow_instance_id")
    private String workflowInstanceId;

    /**
     * 与接口文档不一样，实际是data字段返回得workflowInstanceId
     */
    private String data;

}