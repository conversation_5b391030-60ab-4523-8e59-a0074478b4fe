package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;


import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
public class ApproveStartReq {
    /**
     * 流程key
     */
    private String processKey;

    /**
     * 表单信息
     */
    private String processForm;

    /**
     * 审批人配置，{"nodeKey":{"type":1,"assigneeList":[{"id":"0","name":"CEO"}]}}
     */
    private Map<String, AssigneeItem> assigneeMap;

    private List<RequestAssignee> assigneeList;

    private List<DepartmentList> departmentList;

    /**
     * 摘要
     */
    private String des;

    private String tenantId;

    private String userCode;

    private String userName;

    @Data
    @Accessors(chain = true)
    public static class RequestAssignee {
        /**
         * 节点key
         */
        private String nodeKey;
        /**
         * 类型，1:指定用户
         */
        private Integer type;
        /**
         * 用户code
         */
        private String userCode;
        /**
         * 用户名称
         */
        private String userName;
    }

    @Data
    public static class AssigneeItem {

        private Integer type;

        private List<Assignee> assigneeList;
    }

    @Data
    public static class Assignee {

        private String id;
        private String name;
    }

    @Data
    public static class DepartmentList {
        /**
         * 部门code
         */
        private String departmentCode;
        /**
         * 节点code
         */
        private String nodeKey;
    }
}