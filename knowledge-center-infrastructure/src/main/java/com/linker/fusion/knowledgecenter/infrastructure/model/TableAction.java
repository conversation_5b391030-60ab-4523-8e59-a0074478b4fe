package com.linker.fusion.knowledgecenter.infrastructure.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TableAction {
    @ApiModelProperty("上传表格")
    private boolean upload = false;
    @ApiModelProperty("编辑基本信息")
    private boolean edit = false;
    @ApiModelProperty("下载")
    private boolean download = false;
    @ApiModelProperty("删除表格")
    private boolean delete = false;
    @ApiModelProperty("移动表格")
    private boolean move=false;
    @ApiModelProperty("失效")
    private boolean expire = false;
    @ApiModelProperty("重新学习")
    private boolean redo = false;
    @ApiModelProperty("表格详情")
    private boolean view = false;
    @ApiModelProperty("编辑/删除切片分段")
    private boolean chunkEdit = false;

}
