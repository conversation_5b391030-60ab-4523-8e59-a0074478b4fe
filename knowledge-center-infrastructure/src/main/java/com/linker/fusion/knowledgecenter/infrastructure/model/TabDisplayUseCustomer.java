package com.linker.fusion.knowledgecenter.infrastructure.model;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.linker.fusion.knowledgecenter.infrastructure.entity.CustomEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class TabDisplayUseCustomer implements Serializable {

    /**
     * 结构表中信息
     */
    private CustomEntity customEntity;

    /**
     * 给前端显示值
     */
    private String view;

    /**
     * 数据库存储value
     */
    private String value;
    /**
     * 文件
     */
    private List<FileInfo> file;


    public void fillField(CustomEntity customEntity, String value) {
        this.customEntity = customEntity;
        this.value = StringUtils.isBlank(value)?"":value;
        this.view = StringUtils.isBlank(value)?"":value;

        if (this.customEntity != null) {
            //字段类型 0字符串 1数字 2时间 3标签 4单选 5多选 6附件
            Integer fieldType = this.customEntity.getFieldType();
            if ((fieldType.equals(5) ||fieldType.equals(3)) && StringUtils.isNotBlank(value)) {
                this.view = String.join(",", JSON.parseArray(value, String.class));
                return;
            }
            if (fieldType.equals(6) && StringUtils.isNotBlank(value)) {
                this.view = null;
                ArrayList<FileInfo> fileInfos = Lists.newArrayList();

                JSONArray objects = JSON.parseArray(value);
                objects.forEach(o -> {
                    JSONObject parse = JSONObject.parse(o.toString());
                    FileInfo fileInfo = new FileInfo();
                    fileInfo.setName(parse.getString("name"));
                    fileInfo.setUrl(parse.getString("url"));
                    fileInfos.add(fileInfo);
                });
                this.file = fileInfos;
            }
        }

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileInfo {
        /**
         * 针对文档的下载地址
         */
        private String url;

        private String name;
    }



}
