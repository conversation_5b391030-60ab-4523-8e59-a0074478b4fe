package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 视觉标签类型枚举
 */
@AllArgsConstructor
@Getter
public enum OdTagTypeEnum {
    ENTITY(0, "目标", "Object Recognition"),
    EVENT(1, "行为", "Event Recognition"),
    SCENE(2, "场景", "Scene Recognition"),
    ATTRIBUTE(3, "属性", "Attribute Recognition");
    private final Integer value;
    private final String name;
    private final String englishName;
    public static OdTagTypeEnum valueOf(Integer value) {
        for (OdTagTypeEnum e : OdTagTypeEnum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return ENTITY;
    }


}
