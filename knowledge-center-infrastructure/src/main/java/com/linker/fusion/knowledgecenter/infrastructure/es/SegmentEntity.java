package com.linker.fusion.knowledgecenter.infrastructure.es;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Deprecated
@Data
@Accessors(chain = true)
public class SegmentEntity {

    @JsonProperty("_uid")
    private String uid;

    @JsonProperty("group_id")
    private Long groupId;
    /**
     * 文档id
     */
    @JsonProperty("doc_id")
    private String docId;
    /**
     * 标题
     */
    @JsonProperty("title")
    private String title;
    @JsonProperty("content")
    private String content;
    /**
     * 附加字段
     */
    @JsonProperty("mate")
    private JSONObject mate;
    /**
     * 页码
     */
    @JsonProperty("page")
    private Integer page;
    /**
     * 分段id
     */
    @JsonProperty("segment_id")
    private String segmentId;
    /**
     * 排序
     */
    @JsonProperty("sort")
    private Double sort;
    /**
     * 位置
     */
    @JsonProperty("position")
    private String position;
    /**
     * 启用
     */
    @JsonProperty("enable")
    private Integer enable;
    /**
     * 用户id
     */
    @JsonProperty("create_id")
    private String createId;
    /**
     * 租户id
     */
    @JsonProperty("tenant_id")
    private String tenantId;
    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("status")
    private Integer status;


    private Integer number;
}
