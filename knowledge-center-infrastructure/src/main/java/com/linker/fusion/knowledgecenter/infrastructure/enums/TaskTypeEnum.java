package com.linker.fusion.knowledgecenter.infrastructure.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum TaskTypeEnum {
    QA(1, "问答对"),
    QB(2, "题目"),
    DocumentToQB(3, "文档"),
    ReChunk(4, "重新分块"),
    SegRetry(5, "分块重试"),
    FileCut(6, "文件切分"),
    DeleteGroup(7, "删除分组"),
    VideoMerge(8, "视频合成"),
    UpdateResourceChunk(9, "批量更新资源分块"),
    DeleteRes(10, "删除资源"),
    SceneTagRerun(11, "场景标签"),
    FixChunkCreateTime(12, "修复分块时间"),
    ;

    private final Integer value;
    private final String desc;

    public Integer getValue() {
        return this.value;
    }

    public static TaskTypeEnum valueOf(Integer value) {
        return ArrayUtil.firstMatch(s -> s.getValue().equals(value), TaskTypeEnum.values());
    }

    public String getDesc() {
        return this.desc;
    }
}
