package com.linker.fusion.knowledgecenter.infrastructure.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.ImageEncodeReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.ImageTextEncodeReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.RankerEncodeReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.TextEncodeReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.ImageEncodeResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.RankerEncodeResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.TextEncodeResp;
import org.springframework.stereotype.Service;
import retrofit2.http.Body;
import retrofit2.http.POST;

@Service
@RetrofitClient(baseUrl = "${bge.endpoint}", sourceOkHttpClient = "okHttpClientBge")
public interface BGEClient {

    @POST("v2/serving/image_encode")
    ImageEncodeResp imageEncode(@Body ImageEncodeReq req);

    @POST("v2/serving/text_encode")
    TextEncodeResp textEncode(@Body TextEncodeReq req);

    @POST("v2/serving/ranker_encode")
    RankerEncodeResp rankerEncode(@Body RankerEncodeReq req);

    @POST("v2/serving/image_text_encode")
    ImageEncodeResp imageTextEncode(@Body ImageTextEncodeReq req);
}
