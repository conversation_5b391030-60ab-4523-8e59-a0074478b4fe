package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


//{
//        "code": 200,
//        "schedulingCenter": null,
//        "body": {
//        "took": 0,
//        "message": "Success",
//        "taskId": "31d7151f-ba95-436f-b3f4-3502efb93ca9"
//        },
//        "results": [
//        {
//        "ner": [
//        [
//        "晚上八点",
//        "TIME",
//        3,
//        6
//        ],
//        [
//        "习近平",
//        "PERSON",
//        9,
//        10
//        ],
//        [
//        "特朗普",
//        "PERSON",
//        12,
//        13
//        ]
//        ],
//        "timestamp": 0
//        }
//        ],
//        "isSuccess": true
//        }

@NoArgsConstructor
@Data
public class HannerTagResp {

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("schedulingCenter")
    private Object schedulingCenter;
    @JsonProperty("body")
    private BodyDTO body;
    @JsonProperty("results")
    private List<ResultsDTO> results;
    @JsonProperty("isSuccess")
    private Boolean isSuccess;

    @NoArgsConstructor
    @Data
    public static class BodyDTO {
        @JsonProperty("took")
        private Integer took;
        @JsonProperty("message")
        private String message;
        @JsonProperty("taskId")
        private String taskId;
    }

    @NoArgsConstructor
    @Data
    public static class ResultsDTO {
        @JsonProperty("ner")
        private List<List<String>> ner;
        @JsonProperty("timestamp")
        private Integer timestamp;
    }
}
