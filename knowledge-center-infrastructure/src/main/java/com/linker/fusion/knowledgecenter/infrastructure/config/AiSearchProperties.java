package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ai-search")
@RefreshScope
public class AiSearchProperties {

    private Integer maxResults = 200;

    private Integer topK = 1000;

    private Double threshold = 0.3;

    private String timeout = "60000";

    private IntentRecognition intentRecognition = new IntentRecognition();

    @Data
    public static class IntentRecognition {

        private String toolJson = "[{\"type\":\"function\",\"name\":\"ai_advanced_search\",\"description\":\"根据用户输入的关键词，自动给出高级搜索的指令\",\"parameters\":{\"type\":\"object\",\"properties\":{\"related_filename_info\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"filename\":{\"type\":\"string\",\"description\":\"文件名\"},\"condition\":{\"type\":\"string\",\"description\":\"条件\",\"enum\":[\"等于\",\"包含\",\"不包含\"]}}}},\"file_type\":{\"type\":\"array\",\"description\":\"文件类型，例如：视频、音频、图片、文档等\",\"items\":{\"type\":\"string\",\"enum\":[\"video\",\"audio\",\"image\",\"document\"]}},\"person_info\":{\"type\":\"array\",\"description\":\"人物信息，人名，如张三，李四等\",\"items\":{\"type\":\"string\"}},\"object_info\":{\"type\":\"array\",\"description\":\"物体名称，如眼镜，杯子等\",\"items\":{\"type\":\"string\"}},\"proper_noun_info\":{\"type\":\"object\",\"description\":\"专有名词信息\",\"properties\":{\"person\":{\"type\":\"array\",\"description\":\"人物信息，人名\",\"items\":{\"type\":\"string\"}},\"organization\":{\"type\":\"array\",\"description\":\"组织信息，组织名称或带有指代组织的名称\",\"items\":{\"type\":\"string\"}},\"location\":{\"type\":\"array\",\"description\":\"地点信息，地点名称或带有指代地点的名称\",\"items\":{\"type\":\"string\"}},\"other\":{\"type\":\"array\",\"description\":\"其他信息，其他信息名称或带有指代其他信息的名称\",\"items\":{\"type\":\"string\"}}}}},\"required\":[\"related_filename_info\",\"file_type\",\"face_info\",\"object_info\",\"proper_noun_info\"]}}]";
    }
}
