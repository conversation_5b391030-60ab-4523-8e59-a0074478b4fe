package com.linker.fusion.knowledgecenter.infrastructure.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 题库权限
 */
@Data
public class QBAction {
    @ApiModelProperty("新建")
    private boolean create = false;
    @ApiModelProperty("编辑")
    private boolean edit = false;
    @ApiModelProperty("删除")
    private boolean delete = false;
    @ApiModelProperty("移动")
    private boolean move=false;
    @ApiModelProperty("失效")
    private boolean expire = false;
    @ApiModelProperty("浏览")
    private  boolean view =false;
}
