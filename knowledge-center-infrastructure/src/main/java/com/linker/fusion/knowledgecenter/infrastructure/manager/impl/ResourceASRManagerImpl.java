
package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceASREntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IResourceASRManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.ResourceASRMapper;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service
public class ResourceASRManagerImpl extends ServiceImpl<ResourceASRMapper, ResourceASREntity> implements IResourceASRManager {

    @Override
    public List<ResourceASREntity> list(String segmentId) {
        QueryWrapper<ResourceASREntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceASREntity::getSegmentId, segmentId);
        wrapper.lambda().eq(ResourceASREntity::getDeleted, false);
        return list(wrapper);
    }

    @Override
    public void clear(String docId) {
        QueryWrapper<ResourceASREntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceASREntity::getDocId, docId);
        remove(wrapper);
    }

    @Override
    public List<ResourceASREntity> listByDocId(String docId) {
        QueryWrapper<ResourceASREntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceASREntity::getDocId, docId);
        wrapper.lambda().eq(ResourceASREntity::getDeleted, false);
        return list(wrapper);
    }

    @Override
    public void clearByFileIds(List<String> fileIds) {
        if (CollectionUtils.isEmpty(fileIds))
            return;
        QueryWrapper<ResourceASREntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ResourceASREntity::getDocId, fileIds);
        remove(wrapper);
    }

    @Override
    public Set<String> getDocIdsByContent(String tenantId, String asrContent) {
        if (StringUtils.isBlank(asrContent)) {
            return Collections.emptySet();
        }
        return list(
                new LambdaQueryWrapper<ResourceASREntity>()
                        .eq(ResourceASREntity::getDeleted, false)
                        .eq(ResourceASREntity::getTenantId, tenantId)
                        .like(ResourceASREntity::getText, StringComUtils.replaceSqlEsc(asrContent))
                        .select(ResourceASREntity::getDocId)
        ).stream().map(ResourceASREntity::getDocId).collect(Collectors.toSet());
    }

    @Override
    public List<ResourceASREntity> listByDocIdsAndContent(List<String> docIds, String asrContent) {
        if (CollectionUtils.isEmpty(docIds)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<ResourceASREntity>()
                        .eq(ResourceASREntity::getDeleted, false)
                        .in(ResourceASREntity::getDocId, docIds)
                        .like(StringUtils.isNotBlank(asrContent), ResourceASREntity::getText, asrContent)
        );
    }
}
