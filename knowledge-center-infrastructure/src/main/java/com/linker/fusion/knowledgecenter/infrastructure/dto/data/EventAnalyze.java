package com.linker.fusion.knowledgecenter.infrastructure.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 事件分析
 * <AUTHOR>
 */
@Data
public class EventAnalyze {
    @ApiModelProperty("事件分析 0-关闭 1-开启 默认值0")
    private Integer enableEvent = 0;
    @ApiModelProperty("识别提示词参数")
    private List<PromptInfoDTO> promptInfo;
    @ApiModelProperty("分析间隔")
    private Integer Interval = 10;
}
