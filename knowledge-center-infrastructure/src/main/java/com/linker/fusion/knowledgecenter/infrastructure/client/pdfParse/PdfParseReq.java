package com.linker.fusion.knowledgecenter.infrastructure.client.pdfParse;


import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PdfParseReq {

    public PdfParseReq(String taskId, String videoId, String url, Dest dest, String callback) {
        this.taskId = taskId;
        this.schedulingCenter = new JSONObject();
        this.tasks = new ArrayList<>();
        this.src = new ArrayList<>();
        this.dest = new ArrayList<>();
        this.src.add(new Src(videoId, url));
        this.dest.add(dest);
        this.callback = callback;
    }

    private String taskId;

    private List<String> tasks;

    private JSONObject schedulingCenter;

    private List<Src> src;

    private List<Dest> dest;

    private String callback;

    @Data
    public static class Src {

        private String srcType;

        private String data;

        private String eventTime;

        private String videoId;

        private String imageId;
        private JSONObject kwargs;

        public Src(String videoId, String data) {
            this.data = data;
            this.srcType = "url";
            this.eventTime = "";
            this.videoId = videoId;
            this.imageId = "";
            this.kwargs = new JSONObject();
        }
    }


    @Data
    public static class Dest {

        private String destType;

        private Linkage linkage;

        public Dest(String destType, Linkage linkage) {
            this.destType = destType;
            this.linkage = linkage;
        }

        @Data
        public static class Linkage {

            public Linkage(String url, String bucketName, String username, String password, Integer secure) {
                this.url = url;
                this.bucketName = bucketName;
                this.username = username;
                this.password = password;
                this.secure = secure.toString();
            }

            public Linkage(String url, String bucketName, String username, String password, Integer secure, String region) {
                this.url = url;
                this.bucketName = bucketName;
                this.username = username;
                this.password = password;
                this.secure = secure.toString();
                this.region = region;
            }

            private String url;

            private String bucketName;

            private String username;

            private String password;

            private String secure;

            private String region;
        }
    }
}
