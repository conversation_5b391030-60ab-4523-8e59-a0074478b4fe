package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.BaseEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.QuestionBankEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IQuestionBankManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.QuestionBankMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 题库管理实现类
 */
@Service
public class QuestionBankManagerImpl extends ServiceImpl<QuestionBankMapper, QuestionBankEntity> implements IQuestionBankManager {

    @Resource
    private QuestionBankMapper questionBankMapper;

    @Override
    public List<QuestionBankEntity> randList(String tenantId, List<Long> questionBaseIds, List<String> questionTypes, Integer generateQuestionNumber) {
        return this.list(new LambdaQueryWrapper<QuestionBankEntity>()
                .eq(QuestionBankEntity::getDeleted, 0)
                .eq(QuestionBankEntity::getTenantId, tenantId)
                .in(QuestionBankEntity::getGroupId, questionBaseIds)
                .in(CollectionUtils.isNotEmpty(questionTypes), QuestionBankEntity::getQuestionType, questionTypes)
                .eq(QuestionBankEntity::getEnable, 1)
                .last("order by RAND() limit " + generateQuestionNumber));
    }

    @Override
    public List<QuestionBankEntity> listByGroupIds(Collection<Long> groupIds) {
        return this.list(new LambdaQueryWrapper<QuestionBankEntity>()
                .eq(QuestionBankEntity::getDeleted, 0)
                .in(QuestionBankEntity::getGroupId, groupIds));
    }

    @Override
    public void deleteByGroupIds( List<Long> groupIds) {
        this.update(new LambdaUpdateWrapper<QuestionBankEntity>()
                .in(QuestionBankEntity::getGroupId, groupIds)
                .set(QuestionBankEntity::getDeleted,1)
        );
    }

    @Override
    public Map<Long, Integer> countByGroups(List<Long> groupIds, String questionType) {
        Map<Long, Integer> result = new HashMap<>();
        
        QueryWrapper<QuestionBankEntity> wrapper = new QueryWrapper<>();
        wrapper.in("group_id", groupIds);
        if (questionType != null) {
            wrapper.eq("question_type", questionType);
        }
        wrapper.select("group_id", "COUNT(*) as count");
        wrapper.groupBy("group_id");
        
        List<Map<String, Object>> counts = questionBankMapper.selectMaps(wrapper);
        
        counts.forEach(map -> {
            Long groupId = ((Number) map.get("group_id")).longValue();
            Integer count = ((Number) map.get("count")).intValue();
            result.put(groupId, count);
        });

        // 对于没有题目的分组，设置数量为0
        groupIds.forEach(groupId -> {
            result.putIfAbsent(groupId, 0);
        });

        return result;
    }

    @Override
    public QuestionBankEntity getByUid(String tenantId, String uid) {
        return this.lambdaQuery().eq(BaseEntity::getTenantId, tenantId).eq(QuestionBankEntity::getUid, uid).one();
    }
    
    @Override
    public boolean updateEnableByIds(List<Long> ids, Boolean enable, String updateId) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        return this.update(new LambdaUpdateWrapper<QuestionBankEntity>()
                .in(QuestionBankEntity::getId, ids)
                .set(QuestionBankEntity::getEnable, enable)
                .set(QuestionBankEntity::getUpdateId, updateId)
                .set(QuestionBankEntity::getUpdateTime, LocalDateTime.now())
        );
    }
    
    @Override
    public boolean updateGroupByIds(List<Long> ids, Long groupId, String updateId) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        return this.update(new LambdaUpdateWrapper<QuestionBankEntity>()
                .in(QuestionBankEntity::getId, ids)
                .set(QuestionBankEntity::getGroupId, groupId)
                .set(QuestionBankEntity::getUpdateId, updateId)
                .set(QuestionBankEntity::getUpdateTime, LocalDateTime.now())
        );
    }
}