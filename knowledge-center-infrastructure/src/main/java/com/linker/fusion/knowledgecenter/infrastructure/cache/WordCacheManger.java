package com.linker.fusion.knowledgecenter.infrastructure.cache;

import com.linker.fusion.knowledgecenter.infrastructure.entity.WordsEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IWordsManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class WordCacheManger {

    @Autowired
    private IWordsManager wordsManager;

    //    @Cached(expire = 60, cacheType = LOCAL)
    public List<String> getCacheWordList(String tenantId, List<Long> groupIds) {
        List<WordsEntity> list = wordsManager.listEnabled(tenantId, groupIds);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(WordsEntity::getName).collect(Collectors.toList());
    }
}
