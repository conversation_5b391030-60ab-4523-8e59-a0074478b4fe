package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum QuestionTypeEnum {
    
    CHOICE("选择题"),
    MULTIPLE_CHOICE("多选题"),
    TRUE_FALSE("判断题"),
    FILL_IN_BLANK("填空题");

    private final String desc;

    public static boolean contains(String type) {
        for (QuestionTypeEnum value : values()) {
            if (value.name().equals(type)) {
                return true;
            }
        }
        return false;
    }
} 