package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum AuthLevelEnum {
    NoAuth(0, "无权限", "无权限"),
    View(1, "查看", "四级权限"),
    Download(2, "查看/下载", "三级权限"),
    Edit(3, "编辑", "二级权限"),
    Manage(4, "管理", "一级权限");
    @Getter
    private final Integer value;
    @Getter
    private final String name;
    @Getter
    private final String desc;

    public static AuthLevelEnum valueOf(Integer value) {
        for (AuthLevelEnum e : AuthLevelEnum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return NoAuth;
    }
}
