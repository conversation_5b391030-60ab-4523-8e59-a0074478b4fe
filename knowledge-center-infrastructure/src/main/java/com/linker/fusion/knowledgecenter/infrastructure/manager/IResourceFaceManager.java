package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceFaceEntity;

import java.util.List;
import java.util.Map;
import java.util.Set;


public interface IResourceFaceManager extends IService<ResourceFaceEntity> {

    Set<String> queryDocIdByFaceId(List<String> faceIds);

    List<ResourceFaceEntity> list(String docId, String segmentId);

    List<ResourceFaceEntity> listByDocIdAndFaceId(List<String> docIds, String faceId);

    void deleteByDocId(String docId);

    void clearByDocId(String docId);

    void clearByDocIds(List<String> fileIds);

    List<Map<String, Object>> listGroupBySourceIdOrderByCount(List<String> docIds);


    /**
     * @description: 查询人脸列表
     * @author: he<PERSON><PERSON><PERSON>
     * @date: 2025/7/25 09:12
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    List<ResourceFaceEntity> listResourceFace(List<String> docIdList, String name);

    void updateName(String id, String name);
}
