package com.linker.fusion.knowledgecenter.infrastructure.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.ApproveProcessInfoReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.ApproveProcessListReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.ApproveStartReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.ApproveProcessResp;
import org.springframework.stereotype.Service;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

/**
 * 审批中心
 *
 * <AUTHOR>
 */
@Service
@RetrofitClient(baseUrl = "${http.approve.endpoint:http://dev-om.linker.cc/approval-center}", readTimeoutMs = 3600 * 1000)
public interface ApproveClient {

    /**
     * 查询审批流列表
     */
    @POST("process/queryProcessByTenantId")
    BaseResp<List<ApproveProcessResp>> processList(@Body ApproveProcessListReq req);

    /**
     * 查询审批流详情
     */
    @POST("process/queryProcessInfo")
    BaseResp<ApproveProcessResp> processInfo(@Body ApproveProcessInfoReq req);

    /**
     * 发起审批
     */
    @POST("instance/start")
    BaseResp<String> instanceStart(@Body ApproveStartReq req);
}
