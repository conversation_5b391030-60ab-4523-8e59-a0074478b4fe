package com.linker.fusion.knowledgecenter.infrastructure.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "app")
@RefreshScope
public class AppInfoConfig {
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("描述")
    private String desc;
    @ApiModelProperty("文件限制")
    private Integer fileLimit;
    @ApiModelProperty("临时文件限制")
    private Integer tempLimit;
    @ApiModelProperty("问答对限制")
    private Integer qaLimit;
    @ApiModelProperty("题目限制")
    private Integer questionLimit;
    @ApiModelProperty("过期时间")
    private String expire;
    @ApiModelProperty("示例库id")
    private Long sampleGroupId;
}
