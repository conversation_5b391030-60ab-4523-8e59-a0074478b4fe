package com.linker.fusion.knowledgecenter.infrastructure.client.usercenter;

import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import com.linker.core.utils.ApiSignUtil;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class UserApiSignInterceptor extends BasePathMatchInterceptor {

    private final static String LINKER_SIGN_HEADER = "linker-sign";
    @Value("${user.auth.projectName:oYYgSqgrLH000008}")
    private String projectName;
    @Value("${user.auth.appSecret:ea166b6cce467ae84b2c}")
    private String appSecret;

    private String bodyToString(final RequestBody requestBody) {
        try {
            final Buffer buffer = new Buffer();
            if (requestBody != null) {
                requestBody.writeTo(buffer);
                return buffer.readUtf8();
            }
            return "";
        } catch (final IOException e) {
            return "Unable to read request body";
        }
    }

    String getLinkerSign(RequestBody requestBody) {
        return ApiSignUtil.encrypt(bodyToString(requestBody), projectName, appSecret);
    }

    /**
     * @param chain
     * @return
     * @throws IOException
     */
    @Override
    protected Response doIntercept(Interceptor.Chain chain) throws IOException {
        Request request = chain.request();
        Request newRequest = request.newBuilder()
                .method(request.method(), request.body())
                .headers(request.headers())
                .addHeader(LINKER_SIGN_HEADER, getLinkerSign(request.body())).build();
        return chain.proceed(newRequest);
    }
}