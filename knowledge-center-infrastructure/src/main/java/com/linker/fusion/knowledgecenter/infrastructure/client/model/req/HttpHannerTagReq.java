package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.util.List;

/**
 * 专名识别 请求对象
 */
@Data
public class HttpHannerTagReq {
    /**
     * 任务id
     */
    private String                    taskId;

    private List<HttpHannerTagSrcReq> src;

    @Data
    public static class HttpHannerTagSrcReq {
        /**
         * 数据内容
         */
        private String              data;

        /**
         * 数据类型
         */
        private String              srcType;

        /**
         * 映射类型
         */
        private JSONObject kwargs;
    }
}
