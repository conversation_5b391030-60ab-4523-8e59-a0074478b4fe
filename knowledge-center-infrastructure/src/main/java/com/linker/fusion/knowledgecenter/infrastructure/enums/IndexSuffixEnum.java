package com.linker.fusion.knowledgecenter.infrastructure.enums;

/**
 * <AUTHOR>
 */

public enum IndexSuffixEnum {
    /**
     * 索引后缀
     * @see IndexPrefixEnum
     */
    DOCUMENT_CHUNK("文档", "_chunk"),

    IMAGE("图片", "_chunk"),

    VIDEO("视频", "_chunk"),

    AUDIO("音频", "_chunk"),
    FRAME("帧", "_frame"),
    FAQ("FAQ", ""),

    TABLE("表格", ""),

    SENSITIVE("敏感词", ""),
    ;
    public final String name;

    public final String suffix;

    IndexSuffixEnum(String name, String suffix) {
        this.name = name;
        this.suffix = suffix;
    }
}
