package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import lombok.Data;

@Data
public class ApproveProcessConfig {

    private String name;

    private String key;

    private NodeConfig nodeConfig;

    @Data
    public static class NodeConfig {
        private String nodeName;
        private String nodeKey;
        private Integer type;
        private ChildNode childNode;

        @Data
        public static class ChildNode {
            private String nodeName;
            private String nodeKey;
            private Integer type;
            private Integer setType;
            private Integer examineLevel;
            private Integer examineMode;
            private Integer directorLevel;
            private Integer directorMode;
            private Integer selectMode;
            private Boolean termAuto;
            private Integer term;
            private Integer termMode;
            private ChildNode childNode;
        }
    }
}