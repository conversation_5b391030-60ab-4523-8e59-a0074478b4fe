package com.linker.fusion.knowledgecenter.infrastructure.config;

import com.github.lianjiatech.retrofit.spring.boot.config.GlobalTimeoutProperty;
import com.github.lianjiatech.retrofit.spring.boot.config.RetrofitProperties;
import com.github.lianjiatech.retrofit.spring.boot.core.SourceOkHttpClientRegistrar;
import com.github.lianjiatech.retrofit.spring.boot.core.SourceOkHttpClientRegistry;
import com.github.lianjiatech.retrofit.spring.boot.log.AggregateLoggingInterceptor;
import com.github.lianjiatech.retrofit.spring.boot.log.GlobalLogProperty;
import com.github.lianjiatech.retrofit.spring.boot.retry.GlobalRetryProperty;
import com.github.lianjiatech.retrofit.spring.boot.retry.RetryInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class CustomSourceOkHttpClientRegistrar implements SourceOkHttpClientRegistrar {


    @Autowired
    RetrofitProperties retrofitProperties;
    @Value("${retrofit.maxIdleConnections:30}")
    private int maxIdleConnections;
    @Value("${retrofit.keepAliveDuration:360}")
    private int keepAliveDuration;
    @Value("${retrofit.maxRequests:1024}")
    private int maxRequests;
    @Value("${retrofit.maxRequestsPerHost:512}")
    private int maxRequestsPerHost;

    @Override
    public void register(SourceOkHttpClientRegistry registry) {
        GlobalTimeoutProperty globalTimeout = retrofitProperties.getGlobalTimeout();
        GlobalRetryProperty globalRetry = retrofitProperties.getGlobalRetry();
        GlobalLogProperty globalLogProperty = retrofitProperties.getGlobalLog();
        OkHttpClient okHttpClient = buildOkhttp(globalTimeout, globalLogProperty, globalRetry);

        OkHttpClient okHttpClient1 = buildOkhttp(globalTimeout, globalLogProperty, globalRetry);

        OkHttpClient okHttpClientActor = buildOkhttp(globalTimeout, globalLogProperty, globalRetry);
        OkHttpClient okHttpClientKnowledge = buildOkhttp(globalTimeout, globalLogProperty, globalRetry);
        // 添加testSourceOkHttpClient
        registry.register("detectOkHttpClient", okHttpClient);
        registry.register("kernelOkHttpClient", okHttpClient1);
        registry.register("okHttpClientBge", okHttpClientActor);
        registry.register("okHttpClientKnowledge", okHttpClientKnowledge);
    }


    private OkHttpClient buildOkhttp(GlobalTimeoutProperty globalTimeout, GlobalLogProperty globalLogProperty, GlobalRetryProperty globalRetry) {
        OkHttpClient okHttpClientActor = new OkHttpClient().newBuilder()
                .readTimeout(globalTimeout.getReadTimeoutMs(), TimeUnit.MILLISECONDS)
                .connectTimeout(globalTimeout.getConnectTimeoutMs(), TimeUnit.MILLISECONDS)
                .writeTimeout(globalTimeout.getWriteTimeoutMs(), TimeUnit.MILLISECONDS)
                .connectionPool(new ConnectionPool(maxIdleConnections, keepAliveDuration, TimeUnit.SECONDS))
                .retryOnConnectionFailure(true)
                .addInterceptor(new AggregateLoggingInterceptor(globalLogProperty))
                .addInterceptor(new RetryInterceptor(globalRetry))
                .eventListenerFactory(HttpEventListener.FACTORY)
                .build();
        okHttpClientActor.dispatcher().setMaxRequests(maxRequests);
        okHttpClientActor.dispatcher().setMaxRequestsPerHost(maxRequestsPerHost);
        return okHttpClientActor;
    }
}
