package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

@Data
@TableName("t_auth_level")
public class AuthLevelEntity extends BaseEntity {
    /**
     * 名称
     */
    private String name;
    /**
     * 等级
     */
    private Integer level;
    /**
     * 授权节点
     */
    private String codes;
    /**
     * 是否是内置
     */
    private Integer isSystem;
    public List<String> getCodeList() {
        return JSON.parseArray(codes, String.class);
    }
}
