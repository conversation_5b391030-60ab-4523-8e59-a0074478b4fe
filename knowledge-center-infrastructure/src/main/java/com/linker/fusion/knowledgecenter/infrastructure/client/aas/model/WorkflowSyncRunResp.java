package com.linker.fusion.knowledgecenter.infrastructure.client.aas.model;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class WorkflowSyncRunResp {
    /**
     * 错误码
     */
    private String code;
    /**
     * 错误信息
     */
    private String message;
    /**
     * 工作流输出
     */
    private JSONObject output;
    /**
     * 工作流执行的状态
     */
    private WorkflowStatus status;
    /**
     * 工作流实例ID
     */
    @JsonProperty("workflow_instance_id")
    private String workflowInstanceId;


}
