package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "auto-learn")
@RefreshScope
public class AutoLearnConfig {

    /**
     * 自动学习配置项
     */
    private List<AutoLearnItem> autoLearnItems = new ArrayList<>();


}
