package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "omos.ext")
public class OmosExtProperties {

    /**
     * 是否将ImageContent中的图片URL转换成base64
     */
    private Boolean imageUrlToBase64 = true;
}
