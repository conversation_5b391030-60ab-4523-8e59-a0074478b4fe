package com.linker.fusion.knowledgecenter.infrastructure.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Arrays;
import java.util.List;

@Data
public class QuestionBankOptionModel {
    @ApiModelProperty(value = "选项标识", required = true)
    @NotBlank(message = "选项标识不能为空")
    private String key;

    @ApiModelProperty(value = "选项内容", required = false)
    @Size(max = 200, message = "选项内容最多200字")
    private String value;

    public String getValue() {
        return StringUtils.trim(value);
    }

    @ApiModelProperty(value = "选项图片URL", required = false)
    @Size(max = 10, message = "图片URL最多10个")
    private List<String> images;

    /**
     * 获取判断题选项
     *
     * @return
     */
    public static List<QuestionBankOptionModel> getTrueFalseOptions() {
        QuestionBankOptionModel falseOptionModel = new QuestionBankOptionModel();
        falseOptionModel.setKey("0");
        falseOptionModel.setValue("错");
        QuestionBankOptionModel trueOptionModel = new QuestionBankOptionModel();
        trueOptionModel.setKey("1");
        trueOptionModel.setValue("对");
        return Arrays.asList(trueOptionModel, falseOptionModel);
    }
}
