package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceODTagEntity;

import java.util.List;


public interface IResourceODTagManager extends IService<ResourceODTagEntity> {

    List<ResourceODTagEntity> list(String docId, String segmentId);

    void deleteByDocId(String docId);

    void clearByDocId(String docId);

    void clearByDocIds(List<String> fileIds);

    List<ResourceODTagEntity> listByResId(String docId, Integer type, String tagName);

    void deleteByDocId_FrameId_Name(String docId, String frameId, List<String> tagNames);

    List<ResourceODTagEntity> listByResIds(List<String> resIds, Integer type,List<String> sampleIds);

    void deleteByIds(List<Long> ids);

    /**
     * @description: 查询事件列表
     * @author: heji<PERSON><PERSON>
     * @date: 2025/7/25 09:12
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    List<ResourceODTagEntity> listResourceOdTag(List<String> docIdList, String name, Integer type);

    IPage<ResourceODTagEntity> pageResourceOdTag(List<String> docIdList, String name, Integer type, IPage<ResourceODTagEntity> page);
}
