package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoSplitResp {

    private List<ShotGroupVo> shotGroup = new ArrayList<>();

    @Data
    public static class ShotGroupVo {

        private String image;

        private Double startPoint;

        private Double endPoint;

        private Integer length;

        private Integer id;

        /**
         * 大小
         */
        private Long destFileSize;


    }
}
