package com.linker.fusion.knowledgecenter.infrastructure.es;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseChunkEntity {
    @JsonProperty("_uid")
    private String uid;

    @JsonProperty("group_id")
    private Long groupId;
    /**
     * 分片id
     */
    @JsonProperty("chunk_id")
    private String chunkId;
    /**
     * 分段Id
     */
    @JsonProperty("segment_id")
    private String segmentId;
    /**
     * 文档id
     */
    @JsonProperty("doc_id")
    private String docId;
    /**
     * 图片地址
     */
    @JsonProperty("url")
    private String url;
    /**
     * 分片类型
     */
    @JsonProperty("src_type")
    private String srcType;
    /**
     * 标题
     */
    @JSONField(name = "file_title")
    @JsonProperty("file_title")
    private String title;
    /**
     * 内容
     */
    @JsonProperty("content")
    private String content;

    /**
     * 内容向量
     */
    @JsonProperty("content_vector")
    private List<Double> contentVector;
    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private String createTime;
    /**
     * 用户id
     */
    @JsonProperty("create_id")
    private String createId;
    /***
     * 租户id
     */
    @JsonProperty("tenant_id")
    private String tenantId;
    /**
     * 扩展数据
     */
    @JsonProperty("meta")
    private JSONObject meta;
    /**
     * 启用
     */
    @JsonProperty("enable")
    private Integer enable;
}
