package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 处理状态枚举类
 * <p>
 * 定义了知识资源在处理过程中的各种状态：
 * 1 - 学习中
 * 2 - 学习成功
 * 3 - 学习失败
 * 4 - 归档
 * 5 - 合并中
 * 6 - 合并失败
 * 7 - 预处理中（转码、抽帧）
 * 8 - 暂停学习
 * 9 - 取消学习
 */
@AllArgsConstructor
@Getter
public enum ProcessEnum {
    /**
     * 排队中
     */
    QUEUED(0, "排队中", 2),
    /**
     * 学习中 - 正在进行知识学习处理
     */
    Executing(1, "学习中", 2),

    /**
     * 学习成功 - 知识学习已完成且成功
     */
    Success(2, "成功", 3),

    /**
     * 学习失败 - 知识学习过程中发生错误
     */
    Fail(3, "失败", 3),

    /**
     * 归档 - 资源已被归档存储
     */
    ARCHIVE(4, "归档", 3),

    /**
     * 合并中 - 正在进行多个资源的合并操作
     */
    MERGING(5, "合并中", 2),

    /**
     * 合并失败 - 资源合并过程中发生错误
     */
    MERGE_FAILED(6, "合并失败", 3),

    /**
     * 预处理中 - 正在进行资源预处理（如视频转码、抽帧等）
     */
    PREPROCESSING(7, "预处理中", 2),

    /**
     * 暂停学习 - 学习过程已被暂停
     */
    PAUSED(8, "暂停学习", 2),

    /*
     * 取消学习 - 学习过程已被取消
     */
    CANCELLED(9, "终止学习", 3);

    private final Integer value;

    private final String name;
    /**
     * 阶段 1 未开始阶段 2：进行阶段、3终止阶段
     */
    private final Integer step;

    /**
     * 获取状态值
     *
     * @return 状态值
     */
    public Integer getValue() {
        return this.value;
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getDesc() {
        return this.name;
    }

    /**
     * 根据状态值获取枚举实例
     *
     * @param value 状态值
     * @return 对应的枚举实例，如果未找到则返回 null
     */
    public static ProcessEnum valueOf(Integer value) {
        for (ProcessEnum status : ProcessEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}