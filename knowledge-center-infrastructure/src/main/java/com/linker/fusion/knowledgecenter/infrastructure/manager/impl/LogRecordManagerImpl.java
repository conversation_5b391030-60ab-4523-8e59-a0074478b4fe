package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.dto.query.LogRecordPageQuery;
import com.linker.fusion.knowledgecenter.infrastructure.entity.LogRecordEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.ILogRecordManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.LogRecordMapper;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import org.springframework.stereotype.Service;

import static org.springframework.util.StringUtils.hasText;

/**
 * <p>
 * 日志记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Service
public class LogRecordManagerImpl extends ServiceImpl<LogRecordMapper, LogRecordEntity> implements ILogRecordManager {

    @Override
    public IPage<LogRecordEntity> getPageList(LogRecordPageQuery query) {
        Page<LogRecordEntity> page = Page.of(query.getPage(), query.getPageSize());
        QueryWrapper<LogRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(LogRecordEntity::getTenantId, query.getTenantId())
                .like(hasText(query.getUsername()), LogRecordEntity::getUsername, StringComUtils.replaceSqlEsc(query.getUsername()))
                .like(hasText(query.getOperator()), LogRecordEntity::getOperator, StringComUtils.replaceSqlEsc(query.getOperator()))
                .ge(hasText(query.getOperateTimeBegin()), LogRecordEntity::getCreateTime, query.getOperateTimeBegin())
                .le(hasText(query.getOperateTimeEnd()), LogRecordEntity::getCreateTime, query.getOperateTimeEnd())
                .eq(hasText(query.getOperationType()), LogRecordEntity::getType, query.getOperationType())
                .eq(hasText(query.getOperationSubType()), LogRecordEntity::getSubType, query.getOperationSubType())
                .like(hasText(query.getSearchContent()), LogRecordEntity::getAction, StringComUtils.replaceSqlEsc(query.getSearchContent()))
                .eq(query.getStatus() != null, LogRecordEntity::getStatus, query.getStatus())
                .orderByDesc(LogRecordEntity::getCreateTime)
                .orderByDesc(LogRecordEntity::getId);

        return page(page, queryWrapper);
    }
}
