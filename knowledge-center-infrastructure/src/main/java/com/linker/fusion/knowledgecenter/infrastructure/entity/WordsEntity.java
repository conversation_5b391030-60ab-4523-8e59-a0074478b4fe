package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ImportTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 词条entity
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_words_info", autoResultMap = true)
public class WordsEntity extends BaseEntity {


    /**
     * 词条名
     */
    private String name;

    /**
     * @see com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum
     */
    @TableField(value = "group_type")
    private Integer type;

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 词条状态
     */
    private Integer state;

    /**
     * 导入来源 默认手动
     *
     * @see ImportTypeEnum
     */
    private Integer importSource;

    /**
     * 专业词的同义词
     */
    @TableField(value = "synonyms")
    private String proSynonyms;

    /**
     * 扩展字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private WordsExtInfo extInfo;

    /**
     * 删除时间
     */
    private String deleteTime;

}
