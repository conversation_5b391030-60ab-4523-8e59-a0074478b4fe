package com.linker.fusion.knowledgecenter.infrastructure.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class AudioStrategyModel extends StrategyModelBasic {

    @ApiModelProperty("切分类型 1-语音切分")
    @NotNull(message = "分段设置不能为空")
    private Integer cutType = 1;

    @ApiModelProperty("语音转写 默认1-开启 不可改变")
    @Range(min = 1, max = 1, message = "语音转写必须开启")
    private Integer transcription = 1;

    @ApiModelProperty("专名识别 0-关闭 1-开启 ")
    private Integer enableTerminology = 1;

    @ApiModelProperty("分段分块最大长度:200~1000")
    @Range(min = 200, max = 1000, message = "分段分块最大长度不合法")
    private Integer maxLength = 200;
    /**
     * 分段分块最短比例:1~60
     */
    @ApiModelProperty("分段分块最短比例:1~60")
    @Range(min = 1, max = 60, message = "分段分块最短比例不合法")
    private Integer ratio = 25;
    @Override
    public void setDefault() {
        transcription = 1;
        if (cutType == null) {
            cutType = 1;
        }
        if (enableTerminology == null) {
            enableTerminology = 1;
        }
    }
}
