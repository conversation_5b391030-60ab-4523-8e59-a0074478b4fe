package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.CustomEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.ICustomManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.CustomMapper;
import org.springframework.stereotype.Service;


@Service
public class CustomManagerImpl extends ServiceImpl<CustomMapper, CustomEntity> implements ICustomManager {

}