package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import cn.hutool.core.util.BooleanUtil;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linker.fusion.knowledgecenter.infrastructure.entity.IndexAuthEntity;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.IndexAuthMapper;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexAuthManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 索引定时配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
@Service
@Slf4j
public class IndexAuthManagerImpl extends ServiceImpl<IndexAuthMapper, IndexAuthEntity> implements IIndexAuthManager {

    @Override
    @Cached(name = "cache:indexConfCache",expire = 300, cacheType = CacheType.BOTH)
    public IndexAuthEntity getIndexTimeConfig(String tenantId) {
        IndexAuthEntity indexAuthEntity = baseMapper.selectOne(new LambdaQueryWrapper<IndexAuthEntity>()
                .eq(IndexAuthEntity::getTenantId, tenantId));
        if (null == indexAuthEntity) {
            indexAuthEntity = new IndexAuthEntity();
            indexAuthEntity.setTenantId(tenantId);
            indexAuthEntity.setIndexTimeScheduleOpen(false);
            indexAuthEntity.setIndexTimeConfig("");
        }
        //开启配置才返回
        return indexAuthEntity;
    }

    @Override
    @CacheInvalidate(name = "cache:indexConfCache")
    public void freshIndexAuthCache(String tenantId) {
        log.info("删除缓存索引配置Cache：{}", tenantId);
    }
}
