package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import cn.hutool.core.util.BooleanUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linker.fusion.knowledgecenter.infrastructure.entity.IndexAuthEntity;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.IndexAuthMapper;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexAuthManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 索引定时配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
@Service
public class IndexAuthManagerImpl extends ServiceImpl<IndexAuthMapper, IndexAuthEntity> implements IIndexAuthManager {

    @Override
    @Cached(expire = 300, cacheType = CacheType.BOTH)
    public String getIndexTimeConfig(String tenantId) {
        IndexAuthEntity indexAuthEntity = baseMapper.selectOne(new LambdaQueryWrapper<IndexAuthEntity>()
                .eq(IndexAuthEntity::getTenantId, tenantId));
        //开启配置才返回
        return indexAuthEntity != null ? (BooleanUtil.isTrue(indexAuthEntity.getIndexTimeScheduleOpen()) ? indexAuthEntity.getIndexTimeConfig() : "") : "";
    }

    @Override
    public Boolean createOrUpdateIndexAuth(IndexAuthEntity indexAuthEntity) {
        return null;
    }
}
