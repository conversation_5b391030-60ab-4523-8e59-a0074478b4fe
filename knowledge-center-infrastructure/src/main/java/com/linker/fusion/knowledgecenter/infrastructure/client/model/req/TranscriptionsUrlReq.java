package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


//{
//        "file_url": "http://***********:18080/webdav/models/resources/60s.MP3",
//        "model": "Systran/faster-whisper-large-v3",
//        "language": "zh",
//        "prompt": "",
//        "response_format": "verbose_json",
//        "temperature": 0,
//        "timestamp_granularities": [
//        "word"
//        ],
//        "stream": false,
//        "hotwords": "",
//        "vad_filter": false
//        }

@NoArgsConstructor
@Data
public class TranscriptionsUrlReq {

    @JsonProperty("file_url")
    private String fileUrl;
    @JsonProperty("model")
    private String model;
    @JsonProperty("language")
    private String language;
    @JsonProperty("prompt")
    private String prompt;
    @JsonProperty("response_format")
    private String responseFormat;
    @JsonProperty("temperature")
    private Integer temperature;
    @JsonProperty("timestamp_granularities")
    private List<String> timestampGranularities;
    @JsonProperty("stream")
    private Boolean stream;
    @JsonProperty("hotwords")
    private String hotwords;
    @JsonProperty("vad_filter")
    private Boolean vadFilter;
}
