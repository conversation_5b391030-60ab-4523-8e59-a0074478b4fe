package com.linker.fusion.knowledgecenter.infrastructure.client.person;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * OD 库请求参数
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Data
public class ODLibReq implements Serializable {

    /**
     * 页
     */
    private Integer page;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * OD 库ID
     */
    private Long odLibId;

    /**
     * 唯一id
     */
    private String guid;

    /**
     * 名称
     */
    private String name;

    /**
     * 分类ID
     */
    private Long catalogId;

    /**
     * 特征描述
     */
    private String featureStr;

    /**
     * 置信度：0-1
     */
    private Double threshold;

    /**
     * 状态：0停用 1启用
     */
    private Integer enable;

    /**
     * 排序字段 1 创建时间 2 修改时间 3 名称
     */
    private Integer sortField;

    /**
     * 排序规则
     * i. 名称排序选项：从A-Z、从Z- A；默认从A-Z；
     * ii. 创建时间排序：由近到远、由远到近；默认由近到远；
     * iii. 修改时间排序：由近到远、由远到近；默认由近到远；
     */
    private Integer sortRule;

    /**
     * 模型
     */
    private String model;

    /**
     * 图片 URL
     */
    private String imageUrl;

    /**
     * GUID 列表
     */
    private List<String> guidList;

}
