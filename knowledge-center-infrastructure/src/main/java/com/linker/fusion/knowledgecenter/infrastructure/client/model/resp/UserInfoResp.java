package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import lombok.Data;

@Data
public class UserInfoResp {
    /**
     * userName : ragtest
     * userId : null
     * userCode : 3a2b9f6a-b4ec-400e-8137-8aed33eb0db6
     * nickName : ragtest
     * realName : null
     * avatar : null
     * email :
     * phone :
     * phoneEncrypt : null
     * status : 1
     * accountType : 2
     * roleTypes : null
     * createTime : 2024-08-12 11:10:42
     * remark :
     * sex : null
     */

    private String userName;
    private Object userId;
    private String userCode;
    private String nickName;
   // private Object realName;
    private String avatar;
   // private String email;
   // private String phone;
   // private Object phoneEncrypt;
    private int status;
    private int accountType;
    private Object roleTypes;
    private String createTime;
    private String remark;
    private Object sex;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Object getUserId() {
        return userId;
    }

    public void setUserId(Object userId) {
        this.userId = userId;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

//    public Object getRealName() {
//        return realName;
//    }

//    public void setRealName(Object realName) {
//        this.realName = realName;
//    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

//    public String getEmail() {
//        return email;
//    }

//    public void setEmail(String email) {
//        this.email = email;
//    }
//
//    public String getPhone() {
//        return phone;
//    }

//    public void setPhone(String phone) {
//        this.phone = phone;
//    }
//
//    public Object getPhoneEncrypt() {
//        return phoneEncrypt;
//    }

//    public void setPhoneEncrypt(Object phoneEncrypt) {
//        this.phoneEncrypt = phoneEncrypt;
//    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getAccountType() {
        return accountType;
    }

    public void setAccountType(int accountType) {
        this.accountType = accountType;
    }

    public Object getRoleTypes() {
        return roleTypes;
    }

    public void setRoleTypes(Object roleTypes) {
        this.roleTypes = roleTypes;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Object getSex() {
        return sex;
    }

    public void setSex(Object sex) {
        this.sex = sex;
    }
}
