package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import com.linker.fusion.knowledgecenter.infrastructure.config.BGEPropConfig;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class ImageTextEncodeReq {

    private String model_id = BGEPropConfig.ImageTextId;
    private List<ImageTextPair> image_text_pairs;
    /**
     * base64 | url
     */
    private String src_type;

    public ImageTextEncodeReq(List<ImageTextPair> imageTextPairs, String srcType) {
        this.image_text_pairs = imageTextPairs;
        src_type = srcType;
    }

    @Data
    public static class ImageTextPair {

        private String image;
        private String text;
        public ImageTextPair(String image, String text) {
            this.image = image;
            this.text = text;
        }
    }
}
