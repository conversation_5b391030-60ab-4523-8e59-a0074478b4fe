package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Data
@TableName("t_resource_asr")
public class ResourceASREntity extends BaseEntity {
    /**
     * 分段id
     */
    @TableField("segment_id")
    private String segmentId;
    /**
     * 文档id
     */
    @TableField("doc_id")
    private String docId;
    /**
     * 语音内容
     */
    @TableField("text")
    private String text;
    /**
     * 开始时间
     */
    @TableField("start_timestamp")
    private Long startTimestamp;
    /**
     * 结束时间
     */
    @TableField("end_timestamp")
    private Long endTimestamp;
    /**
     * 发言人
     */
    @TableField("speakers")
    private String speakers;
    /**
     * 扩展字段
     */
    @TableField("ext")
    private String ext;

    /**
     * 合并ASR短句
     *
     * @param segments   分段列表
     * @param maxSize    最大长度
     * @param threadHold 最小长度
     * @return 合并后的短句
     */
    public static List<ResourceASREntity> merge(List<ResourceASREntity> segments, Integer maxSize, Integer threadHold) {
        if (CollectionUtils.isEmpty(segments)) {
            return Collections.emptyList();
        }
        if (Objects.isNull(threadHold) || Objects.isNull(maxSize) || segments.size() == 1) {
            return segments;
        }
        //防止出现null 从而出现异常
        for (ResourceASREntity segment : segments) {
            if (Objects.isNull(segment.getText())) {
                segment.setText("");
            }
        }
        List<ResourceASREntity> result = new ArrayList();
        for (int i = 0; i < segments.size(); i++) {
            ResourceASREntity currentSegment = segments.get(i);
            //不需要合并
            if (i == 0 || currentSegment.getText().length() >= maxSize) {
                result.add(segments.get(i));
                continue;
            }
            ResourceASREntity preSegment = result.get(result.size() - 1);
            //向前合并
            boolean preAdd = false;
            //如果前一段长度+当前长度<最大值
            if ((preSegment.getText().length() + currentSegment.getText().length()) < maxSize) {
                preAdd = true;
            }
            //如果前一段长度+当前长度>=最大值
            //并且当前长度小于阈值
            //并且前一段长度<后一段长度(或者是最后一段)
            else if (currentSegment.getText().length() <= threadHold && (i == segments.size() - 1 || (preSegment.getText().length() < segments.get(i + 1).getText().length()))) {
                preAdd = true;
            }
            if (preAdd) {
                preSegment.setText(preSegment.getText() + currentSegment.getText());
                preSegment.setEndTimestamp(currentSegment.getEndTimestamp());
                continue;
            }
            //向后合并
            //如果当前长度小于阈值
            //并且并且前一段长度>后一段长度
            if (currentSegment.getText().length() <= threadHold
                    && i < segments.size() - 1
                    &&
                    preSegment.getText().length() > segments.get(i + 1).getText().length()
                    && segments.get(i + 1).getText().length() + currentSegment.getText().length() > maxSize
            ) {
                ResourceASREntity nextSegment = segments.get(i + 1);
                nextSegment.setText(currentSegment.getText() + nextSegment.getText());
                nextSegment.setStartTimestamp(currentSegment.getStartTimestamp());
                continue;
            }
            result.add(currentSegment);
        }
        return result;

    }
}
