package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.linker.fusion.knowledgecenter.infrastructure.model.AudioStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.DocStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.ImageStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.VideoStrategyModel;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 解析策略表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@Getter
@Setter
@TableName(value = "t_analysis_strategy", autoResultMap = true)
public class AnalysisStrategyEntity extends BaseEntity {

    /**
     * 策略名称
     */
    @TableField("name")
    private String name;

    /**
     * 策略类型 0类目策略 1单个文档解析策略
     */
    @TableField("type")
    private Integer type;

    /**
     * 文档切分策略
     */
    @TableField(value = "strategy_doc", typeHandler = FastjsonTypeHandler.class)
    private DocStrategyModel strategyDoc;

    /**
     * 文档切分策略
     */
    @TableField(value = "strategy_image", typeHandler = FastjsonTypeHandler.class)
    private ImageStrategyModel strategyImage;

    /**
     * 文档切分策略
     */
    @TableField(value = "strategy_video", typeHandler = FastjsonTypeHandler.class)
    private VideoStrategyModel strategyVideo;

    /**
     * 文档切分策略
     */
    @TableField(value = "strategy_audio", typeHandler = FastjsonTypeHandler.class)
    private AudioStrategyModel strategyAudio;

    /**
     * 类目id
     */
    @TableField("group_id")
    private Long groupId;
}
