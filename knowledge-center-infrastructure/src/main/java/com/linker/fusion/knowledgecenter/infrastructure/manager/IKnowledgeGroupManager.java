package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceVisibleTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.GroupExtModel;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 知识分组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
public interface IKnowledgeGroupManager extends IService<KnowledgeGroupEntity> {

    default String getNameById(Long id) {
        KnowledgeGroupEntity byId = getById(id);
        return id == 0 ? "全部" : (byId == null ? "-" : byId.getName());
    }

    default KnowledgeGroupEntity getOne(String tenantId, Integer type, Long parentId, String name) {
        return getOne(
                new LambdaQueryWrapper<>(KnowledgeGroupEntity.class)
                        .eq(KnowledgeGroupEntity::getTenantId, tenantId)
                        .eq(KnowledgeGroupEntity::getType, type)
                        .eq(KnowledgeGroupEntity::getParentId, parentId)
                        .eq(KnowledgeGroupEntity::getName, name)
                        .eq(KnowledgeGroupEntity::getDeleted, false)
                        .last("limit 1")
        );
    }

    default KnowledgeGroupEntity getMaxSortOne(String tenantId, Integer type, Long parentId) {
        return getOne(
                new LambdaQueryWrapper<>(KnowledgeGroupEntity.class)
                        .eq(KnowledgeGroupEntity::getTenantId, tenantId)
                        .eq(KnowledgeGroupEntity::getType, type)
                        .eq(KnowledgeGroupEntity::getParentId, parentId)
                        .orderByDesc(KnowledgeGroupEntity::getSort)
                        .last("limit 1")
        );
    }

    default List<KnowledgeGroupEntity> selectSortList(String tenantId, String userId, Integer type, Integer visibleType) {
        return list(
                new LambdaQueryWrapper<>(KnowledgeGroupEntity.class)
                        .eq(KnowledgeGroupEntity::getTenantId, tenantId)
                        .eq(ResourceVisibleTypeEnum.PERSONAL.getValue().equals(visibleType), KnowledgeGroupEntity::getCreatorId, userId)
                        .eq(KnowledgeGroupEntity::getType, type)
                        .eq(KnowledgeGroupEntity::getDeleted, false)
                        .eq(Objects.nonNull(visibleType), KnowledgeGroupEntity::getVisibleType, visibleType)
                        .orderByAsc(KnowledgeGroupEntity::getSort)
                        .orderByDesc(KnowledgeGroupEntity::getCreateTime)
        );
    }

    default List<Long> selectAllPublicIds(@NotNull String tenantId, Integer type) {
        return list(
                new LambdaQueryWrapper<>(KnowledgeGroupEntity.class)
                        .eq(KnowledgeGroupEntity::getTenantId, tenantId)
                        .eq(type != null, KnowledgeGroupEntity::getType, type)
                        .eq(KnowledgeGroupEntity::getDeleted, false)
                        .eq(KnowledgeGroupEntity::getVisibleType, ResourceVisibleTypeEnum.PUBLIC.getValue())
                        .select(KnowledgeGroupEntity::getId)
        ).stream().map(KnowledgeGroupEntity::getId).collect(Collectors.toList());
    }

    default List<KnowledgeGroupEntity> listByParentId(String tenantId, Long parentId) {
        return list(
                new LambdaQueryWrapper<>(KnowledgeGroupEntity.class)
                        .eq(KnowledgeGroupEntity::getTenantId, tenantId)
                        .eq(KnowledgeGroupEntity::getParentId, parentId)
                        .eq(KnowledgeGroupEntity::getDeleted, false)
        );
    }

    default void updateSortByParentId(String tenantId, Long parentId, Long id, Double sort) {
        list(
                new LambdaQueryWrapper<>(KnowledgeGroupEntity.class)
                        .eq(KnowledgeGroupEntity::getTenantId, tenantId)
                        .eq(KnowledgeGroupEntity::getParentId, parentId)
                        .eq(KnowledgeGroupEntity::getDeleted, false)
                        .ne(KnowledgeGroupEntity::getId, id)
                        .gt(KnowledgeGroupEntity::getSort, sort)
        ).forEach(
                knowledgeGroupEntity -> update(
                        new LambdaUpdateWrapper<>(KnowledgeGroupEntity.class)
                                .eq(KnowledgeGroupEntity::getId, knowledgeGroupEntity.getId())
                                .set(KnowledgeGroupEntity::getSort, knowledgeGroupEntity.getSort() + 1)
                )
        );
    }

    default void logicDelete(String tenantId, Long id) {
        update(
                new LambdaUpdateWrapper<>(KnowledgeGroupEntity.class)
                        .eq(KnowledgeGroupEntity::getTenantId, tenantId)
                        .eq(KnowledgeGroupEntity::getId, id)
                        .set(KnowledgeGroupEntity::getDeleted, true)
        );
    }

    List<KnowledgeGroupEntity> list(String tenantId);
    List<KnowledgeGroupEntity> list(String tenantId, String userId, Integer type, Long parentId, Integer visibleType);

    KnowledgeGroupEntity getRoot(KnowledgeGroupEntity group);

    void updatePath(Long id, String path);

    List<KnowledgeGroupEntity> listByPath(String path);

    void logicDeleteBatch(List<Long> ids);

    List<KnowledgeGroupEntity> getByGuid(String guid);

    GroupExtModel getMaxAndMin(List<Long> parentIds);

    void updateImportLevelByPath(String searchPath, Integer importantLevel);

    void updateApproveProcessKey(Long id, String key);

    KnowledgeGroupEntity get(String tenantId, String userId,Long parentId, String name);

    KnowledgeGroupEntity getByBiz(String tenantId, String userId,Integer bizType);
}
