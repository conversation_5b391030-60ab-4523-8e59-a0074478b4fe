package com.linker.fusion.knowledgecenter.infrastructure.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * 文件URL连接检查工具类
 */
@Slf4j
public class UrlCheckUtil {
    /**
     * 默认的连接超时时间（毫秒）
     */
    private static final int DEFAULT_TIMEOUT = 5000;

    /**
     * 检查文件URL是否可连接
     *
     * @param fileUrl 要检查的文件URL
     * @return 是否可以成功连接
     */
    public static boolean isUrlAccessible(String fileUrl) {
        return isUrlAccessible(fileUrl, DEFAULT_TIMEOUT);
    }

    /**
     * 检查文件URL是否可连接（可自定义超时时间）
     *
     * @param fileUrl  要检查的文件URL
     * @param timeout  连接超时时间（毫秒）
     * @return 是否可以成功连接
     */
    public static boolean isUrlAccessible(String fileUrl, int timeout) {
        // 检查URL是否为空
        if (fileUrl == null || fileUrl.trim().isEmpty()) {
            return false;
        }

        HttpURLConnection connection = null;
        try {
            // 创建URL对象
            URL url = new URL(fileUrl);

            // 打开连接
            connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法为HEAD，减少数据传输
            connection.setRequestMethod("HEAD");

            // 设置连接超时
            connection.setConnectTimeout(timeout);
            connection.setReadTimeout(timeout);

            // 获取响应码
            int responseCode = connection.getResponseCode();

            // 判断响应码是否为200（OK）
            return responseCode == HttpURLConnection.HTTP_OK;

        } catch (MalformedURLException e) {
            log.warn("非法的URL格式: " + fileUrl);
            return false;
        } catch (IOException e) {
            log.warn("连接URL时发生错误: " + e.getMessage());
            return false;
        } finally {
            // 关闭连接
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 获取文件URL的更多详细信息
     *
     * @param fileUrl 要检查的文件URL
     * @return 文件URL的详细信息
     */
    public static FileUrlInfo getUrlInfo(String fileUrl) {
        return getUrlInfo(fileUrl, DEFAULT_TIMEOUT);
    }

    /**
     * 获取文件URL的更多详细信息（可自定义超时时间）
     *
     * @param fileUrl  要检查的文件URL
     * @param timeout  连接超时时间（毫秒）
     * @return 文件URL的详细信息
     */
    public static FileUrlInfo getUrlInfo(String fileUrl, int timeout) {
        FileUrlInfo urlInfo = new FileUrlInfo();
        urlInfo.setUrl(fileUrl);

        if (fileUrl == null || fileUrl.trim().isEmpty()) {
            urlInfo.setAccessible(false);
            return urlInfo;
        }

        HttpURLConnection connection = null;
        try {
            URL url = new URL(fileUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(timeout);
            connection.setReadTimeout(timeout);

            int responseCode = connection.getResponseCode();
            urlInfo.setAccessible(responseCode == HttpURLConnection.HTTP_OK);
            urlInfo.setResponseCode(responseCode);
            urlInfo.setContentType(connection.getContentType());
            urlInfo.setContentLength(connection.getContentLength());

        } catch (MalformedURLException e) {
            urlInfo.setAccessible(false);
            urlInfo.setErrorMessage("非法的URL格式: " + e.getMessage());
        } catch (IOException e) {
            urlInfo.setAccessible(false);
            urlInfo.setErrorMessage("连接URL时发生错误: " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return urlInfo;
    }

    /**
     * 文件URL信息类
     */
    @Data
    public static class FileUrlInfo {
        private String url;
        private boolean accessible;
        private int responseCode;
        private String contentType;
        private long contentLength;
        private String errorMessage;
    }

    // 使用示例
    public static void main(String[] args) {
        // 测试URL是否可连接
        String testUrl1 = "https://dev-om.linker.cc/rclone/1734002393514/%E7%89%B9%E6%AE%8A%E5%AD%97%E7%AC%A6(%E5%A4%AA%E7%89%B9%E6%AE%8A%E7%9A%84%E6%96%87%E4%BB%B6%E5%A4%B9%E5%90%8D%E4%BC%9A%E5%AF%BC%E8%87%B4%E5%86%85%E9%83%A8%E6%96%87%E4%BB%B6%E6%97%A0%E6%B3%95%E6%89%93%E5%BC%80)%20~!@#$%%5E&()_+%7B%7D%5B%5D;',/(OK)%C4%81%C3%A1%C7%8E%C3%A0%C5%8D%C3%B3%C7%92%C3%B2%C3%AA%C4%93%C3%A9%C4%9B%C3%A8%C4%AB%C3%AD%C7%90%C3%AC%C5%AB%C3%BA%C7%94%C3%B9%C7%96%C7%98%C7%9A%C7%9C%C3%BC.docx";
        String testUrl2 = "https://dev-om.linker.cc/rclone/1734006789724/%E7%89%B9%E6%AE%8A%E5%AD%97%E7%AC%A6(%E5%A4%AA%E7%89%B9%E6%AE%8A%E7%9A%84%E6%96%87%E4%BB%B6%E5%A4%B9%E5%90%8D%E4%BC%9A%E5%AF%BC%E8%87%B4%E5%86%85%E9%83%A8%E6%96%87%E4%BB%B6%E6%97%A0%E6%B3%95%E6%89%93%E5%BC%80)%20~!@%23$%25%5E&()_+%7B%7D%5B%5D;',/%E6%8C%87%E6%8C%A5%E4%B8%AD%E5%BF%83%E5%A4%A7%E5%B1%8F%E5%BA%94%E6%A0%87V1.3.docx";

        String testUrl3 = "https://dev-om.linker.cc/rclone/1734057035620/%E6%9C%AA%E5%91%BD%E5%90%8D%E6%96%87%E4%BB%B6%E5%A4%B9/f006c4a9-4fc6-4b2d-889a-ba1de1657092.docx";

//        System.out.println(" 是否可连接: " + isUrlAccessible(testUrl1));
//        System.out.println(" 是否可连接: " + isUrlAccessible(testUrl2));

        // 获取详细信息
        FileUrlInfo urlInfo1 = getUrlInfo(testUrl3);
        System.out.println("URL信息: " + urlInfo1);
    }
}