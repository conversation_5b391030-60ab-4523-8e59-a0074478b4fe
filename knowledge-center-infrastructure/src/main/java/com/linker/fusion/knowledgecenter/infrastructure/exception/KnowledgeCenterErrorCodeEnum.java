package com.linker.fusion.knowledgecenter.infrastructure.exception;


import com.linker.core.base.baseclass.IResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 自定义错误码
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum KnowledgeCenterErrorCodeEnum implements IResponse {
    /**
     * 00000 ~ 99999  5位该服务的自定义错误码
     */
    ERROR("00000", "系统错误"),
    INVALID_ARGUMENT("00001", "无效参数%s"),
    RPC_ERROR("00001", "远程调用失败%s"),
    NOT_EXIST("10001", "%s不存在"),
    ALREADY_ANSWER("10002", "试卷已完成，无法作答"),
    PAPER_UNFINiSH("10003", "试卷未提交，无法查看结果"),
    UN_SYSTEM_URL("10004", "非系统地址"),
    ERROR_CONFIG("10005", "配置异常"),
    MOVE_ERROR("10006", "文件移动失败"),
    ERROR_STATUS("10007", "文件状态校验失败"),
    DUPLICATE_NAME("10008", "名称已存在"),
    NOT_EMPTY("10009", "%s不允许为空"),
    SQL_ERROR("10010", "数据库异常"),
    COPY_ERROR("10011", "文件复制失败"),
    NOT_ALLOW_SAVE("10012", "%s不支持保存"),
    NOT_ALLOW_MOVE("10013", "%s不支持移动"),
    NO_AUTH("10014", "无%s权限"),
    FILE_LOST("10015", "%s文件丢失"),
    NO_MENU_AUTH("10016", "无%s菜单权限"),
    APPLY_DUPLICATE("10017", "您提交的申请正在审批中，请审批结束后再试。"),
    SYNC_GROUP_LIMIT("10018", "同步库不能%s"),
    SAVE_PART_LIMIT("10009", "%s"),
    APPLY_AUTH_LEVEL_LIMIT("10020", "申请的权限必须高于当前权限"),
    USER_CENTER_ERROR("10021", "用户中心服务异常"),
    TIME_OUT_SEARCH("10022", "检索超时，请联系平台处理"),
    NOT_ALLOW_RENAME("10023", "%s不支持重命名"),
    NOT_ALLOW_EDIT("10024", "%s不支持修改"),
    NOT_ALLOW_DELETE("10025", "%s不支持删除"),
    SYNC_TASK_DELETE("10026", "同步任务删除失败"),
    CACHE_EXPIRE("10027", "缓存已过期"),
    CACHE_MISS("10028", "%s不存在"),
    CUT_ERROR("10029", "文件剪切失败"),
    WORKFLOW_ERROR("10029", "工作流异常,请联系平台处理"),
    // ---知识库、知识分组---
    GROUP_NOT_EXIST("10301", "分组不存在"),
    GROUP_NAME_DUPLICATE("10302", "名称重复"),
    GROUP_DEPTH_EXCEEDED("10303", "分组层级上限"),
    DELETE_ROOT_GROUP("10304", "不支持删除根分组"),
    GROUP_SYNC_LIMIT("10305", "同步库不支持%s"),

    // ---FAQ---
    FAQ_NOT_FOUND("10401", "FAQ不存在"),
    FAQ_QUESTION_DUPLICATE("10402", "FAQ问题重复"),
    IMPORT_QUESTION_DUPLICATE_EXIST_SIMILAR("10403", "已有相同的相似问题"),
    FAQ_ANSWER_EMPTY("10404", "FAQ空回答不可启用"),
    IMPORT_QUESTION_DUPLICATE("10405", "导入的标准问题重复"),
    ANSWER_TOO_LONG("10406", "FAQ回答过长"),
    ATTACHMENT_TOO_MANY("10407", "附件数量不可超过5个"),
    FAQ_COUNT_LIMIT("10408", "上传文件数超出剩余容量，当前剩余可上传容量%s个"),

    // ---扩展字段---
    CUSTOM_NAME_DUPLICATE("10501", "扩展字段名称重复"),
    CUSTOM_FIELD_DUPLICATE("10502", "扩展字段ID重复"),
    CUSTOM_NOT_FOUND("10503", "扩展字段不存在"),
    CUSTOM_NOT_EMPTY("10504", "属性：【%s】不能为空"),

    // ---数据表---
    TABLE_FILE_NULL("10601", "文件为空"),
    TABLE_TYPE_Forbid("10602", "不允许的文件类型"),
    TABLE_FILE_MAX("10603", "文件大小超过限制，最大为20MB"),
    TABLE_EXIST_ONE("10604", "至少存在一行数据"),
    TABLE_HEAD_NOT_NULL("10605", "表头不能为空"),
    // 表头字段不得超过100个
    TABLE_HEAD_MAX_100("10606", "上传失败，新上传表格表头不符合规范"),
    // 表头存在空列
    TABLE_HEAD_NULL_COL("10607", "上传失败，新上传表格表头不符合规范"),
    TABLE_TITLE_Redo("10608", "数据表名称不可重复"),
    TABLE_COl_MAX_2000("10609", "单文本框限制输入2000个字符"),

    // -----文件公共------
    RESOURCE_ARCHIVE_LIMIT("10701", "归档文件%s"),
    RESOURCE_SYNC_LIMIT("10702", "同步文件%s"),
    RESOURCE_SEGMENT_CONTENT_NOT_EMPTY("10703", "分段内容不能为空"),
    RESOURCE_SEGMENT_ADD_LIMIT("10704", "只有文档可以新增分段"),
    RESOURCE_IMAGE_SEGMENT_DELETE("10705", "图片分段不能删除"),
    RESOURCE_CLEAR_TEMP("10706", "非临时文件暂不支持清理"),
    RESOURCE_COUNT_LIMIT("10707", "上传文件数超出剩余容量，当前剩余可上传容量%s个"),
    RESOURCE_SIZE_LIMIT("10708", "Upload failed, Insufficient remaining space."),

    // -----词条------
    WORD_NAME_DUPLICATE("11001", "词条不可重名"),

    // -----模版------
    TEMPLATE_NAME_DUPLICATE("11101", "模版名称重复"),
    TEMPLATE_SYSTEM_LIMIT("11102", "内置模版不允许%s"),
    TEMPLATE_DEFAULT_LIMIT("11103", "默认模版不允许%s"),

    // -----视频文件------
    VIDEO_MERGE_COUNT_LIMIT("20001", "请勾选至少2~50个视频文件（不包含合并中、已归档状态）"),
    VIDEO_MERGE_STATUS_LIMIT("20002", "合并中、合并失败、已归档的视频文件不允许合并"),
    VIDEO_IS_NOT_MERGE("20003", "不是合并而来的视频文件"),
    VIDEO_SPLIT_ARCHIVE_LIMIT("20004", "已归档的视频文件不允许拆分"),
    VIDEO_SPLIT_EMPTY("20005", "视频文件不可拆分"),


    ABILITY_ERROR("20000", "%s算法异常"),

    LEARN_FAILED("20001", "文件学习失败, 失败文件数量: %s"),
    ;
    private String code;
    private String message;


}
