package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AuthApproveEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IAuthApproveManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.ApproveMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 申请列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Service
public class AuthApproveManagerImpl extends ServiceImpl<ApproveMapper, AuthApproveEntity> implements IAuthApproveManager {

    @Override
    public AuthApproveEntity getByInstanceId(String instanceId) {
        return this.getOne(
                new LambdaQueryWrapper<AuthApproveEntity>()
                        .eq(AuthApproveEntity::getApproveInstanceId, instanceId)
                        .last("limit 1")
        );
    }

    @Override
    public boolean exist(String tenantId, String userCode, Integer sourceType, String sourceId) {
        return exists(
                new LambdaQueryWrapper<AuthApproveEntity>()
                        .eq(AuthApproveEntity::getTenantId, tenantId)
                        .eq(AuthApproveEntity::getCreatorId, userCode)
                        .eq(AuthApproveEntity::getSourceType, sourceType)
                        .eq(AuthApproveEntity::getSourceId, sourceId)
                        .eq(AuthApproveEntity::getDeleted, false)
                        .eq(AuthApproveEntity::getState, 0)
        );
    }

    @Override
    public List<Long> listAllIngIds(String tenantId, String userCode, Integer sourceType) {
        return list(
                new LambdaQueryWrapper<AuthApproveEntity>()
                        .eq(AuthApproveEntity::getTenantId, tenantId)
                        .eq(AuthApproveEntity::getCreatorId, userCode)
                        .eq(AuthApproveEntity::getSourceType, sourceType)
                        .eq(AuthApproveEntity::getDeleted, false)
                        .eq(AuthApproveEntity::getState, 0)
                        .select(AuthApproveEntity::getSourceId)
        ).stream().map(s -> Long.parseLong(s.getSourceId())).collect(Collectors.toList());
    }
}
