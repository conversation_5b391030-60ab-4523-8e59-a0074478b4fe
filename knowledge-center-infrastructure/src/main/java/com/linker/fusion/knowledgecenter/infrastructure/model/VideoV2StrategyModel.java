package com.linker.fusion.knowledgecenter.infrastructure.model;

import com.linker.fusion.knowledgecenter.infrastructure.dto.data.EventAnalyze;
import com.linker.fusion.knowledgecenter.infrastructure.dto.data.VideoExtract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视频策略
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VideoV2StrategyModel extends StrategyModelBasic {

    @ApiModelProperty("是否启用视频转码，默认开启 1，0为关闭 ")
    private Integer enableVideoTranscode = 1;
    @ApiModelProperty("分段识别")
    private Integer enableSegment = 0;
    @ApiModelProperty("信息抽取")
    private VideoExtract videoExtract = new VideoExtract();
    @ApiModelProperty("事件分析")
    private EventAnalyze eventAnalyze = new EventAnalyze();


}
