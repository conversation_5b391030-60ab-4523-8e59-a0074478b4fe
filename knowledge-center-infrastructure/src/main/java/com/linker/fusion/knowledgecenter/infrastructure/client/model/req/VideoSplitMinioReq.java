package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

//{
//        "taskId": "temp0",
//        "src": [
//        {
//        "srcType": "url",
//        "data": "https://test-om.linker.cc/webdav/CloudSystemCri/2022-10-08/2bdb7267-f7fc-4392-85c9-02619486c7b2/2bdb7267-f7fc-4392-85c9-02619486c7b2.mp4"
//        }
//        ],
//        "kwargs": {
//        "resourceId": "temp00001"
//        }
//        }

@NoArgsConstructor
@Data
public class VideoSplitMinioReq {

    @JsonProperty("taskId")
    private String taskId;
    @JsonProperty("src")
    private List<SrcDTO> src;
    @JsonProperty("kwargs")
    private KwargsDTO kwargs;

    @NoArgsConstructor
    @Data
    public static class KwargsDTO {
        @JsonProperty("resourceId")
        private String resourceId;
    }

    @NoArgsConstructor
    @Data
    public static class SrcDTO {
        @JsonProperty("srcType")
        private String srcType;
        @JsonProperty("data")
        private String data;
    }
}
