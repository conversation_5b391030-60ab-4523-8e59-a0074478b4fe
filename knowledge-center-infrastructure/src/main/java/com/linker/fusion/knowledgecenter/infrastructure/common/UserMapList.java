package com.linker.fusion.knowledgecenter.infrastructure.common;

import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.UserInfoResp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class UserMapList {

    public static LocalDateTime refreshTime = LocalDateTime.now();
    public static Boolean isTouch = true;
    private static List<UserInfoResp> list = new ArrayList<>();

    public static void setList(List<UserInfoResp> list) {
        refreshTime = LocalDateTime.now();
        isTouch = false;
        UserMapList.list = list;
    }

    public static UserInfoResp getRpcUserInfoRespResp(String userCode) {
        isTouch = true;
        return list.stream().filter(x -> x.getUserCode().equals(userCode)).findFirst().orElse(new UserInfoResp());
    }

    public static String getUserName(String userCode) {
        UserInfoResp rpcUserInfoResp = getRpcUserInfoRespResp(userCode);
        return rpcUserInfoResp == null ? "-" : rpcUserInfoResp.getNickName();
    }
}
