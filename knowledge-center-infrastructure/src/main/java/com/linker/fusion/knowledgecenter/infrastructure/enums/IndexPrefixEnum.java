package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ES索引枚举
 */
@Getter
@AllArgsConstructor
public enum IndexPrefixEnum {

    DOCUMENT(1, "文档", "knowledge_center_document_"),

    IMAGE(2, "图片", "knowledge_center_image_"),

    VIDEO(3, "视频", "knowledge_center_video_"),

    AUDIO(4, "音频", "knowledge_center_audio_"),

    FAQ(5, "FAQ", "knowledge_center_faq_"),

    TABLE(6, "表格", "knowledge_center_table_new_"),

    SENSITIVE(8, "敏感词", ""),

    PRO(9, "专业词", ""),

    FEEDBACK(10, "反馈学习", "knowledge_center_feedback_"),

    QUESTION_BANK(11, "题库", "knowledge_center_question_bank_"),

    ;

    private final Integer type;

    private final String name;

    private final String indexPrefix;
}
