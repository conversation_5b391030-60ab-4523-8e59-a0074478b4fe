package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FeedbackEntity;

import java.util.Collection;

/**
 * <p>
 * 反馈学习表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
public interface IFeedbackManager extends IService<FeedbackEntity> {

    Collection<String> getAllUserId();
}
