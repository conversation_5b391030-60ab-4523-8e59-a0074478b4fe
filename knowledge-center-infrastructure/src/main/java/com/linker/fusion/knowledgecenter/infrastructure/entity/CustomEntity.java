package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@TableName("t_knowledge_custom")
public class CustomEntity extends BaseEntity {

    /**
     * 知识类型 1-文档 2-图片 3-视频 4-音频 5-FAQ 6-表格
     */
    @TableField("manage_type")
    private Integer manageType;

    /**
     * 字段类型 0字符串 1数字 2时间 3标签 4单选 5多选 6附件
     */
    @TableField("field_type")
    private Integer fieldType;

    @TableField("name")
    private String name;

    /**
     * 字段ID
     */
    @TableField("field")
    private String field;

    /**
     * 多选信息
     */
    @TableField("options")
    private String options;

    @TableField("defaultValue")
    private String defaultValue;

    @TableField("required")
    private Boolean required;

    @TableField("sort")
    private Integer sort;

    /**
     * 是否生效 -> 0：否、1：是
     */
    @TableField("enable")
    private Boolean enable;

    @ApiModelProperty(value = "外部ID")
    @TableField("is_external")
    private Boolean isExternal;

    /**
     * 标签是否展示
     */
    @TableField("tab_display")
    private Boolean tabDisplay;
}