package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FaqQuestionTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * FAQ表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
@Data
@TableName(value = "t_faq_info", autoResultMap = true)
public class FaqInfoEntity extends BaseEntity {

    /**
     * ES文档uid
     */
    @TableField("uid")
    private String uid;

    /**
     * 组ID
     */
    @TableField("group_id")
    private Long groupId;

    /**
     * 分组路径
     */
    @TableField("group_path")
    private String groupPath;

    /**
     * 问题类型 0-标准问题 1-相似问题
     *
     * @see FaqQuestionTypeEnum
     */
    @TableField("type")
    private Integer type;

    /**
     * 相似问题关联标准问题ID
     */
    @TableField("standard_id")
    private Long standardId;

    /**
     * 问题内容
     */
    @TableField("question")
    private String question;

    /**
     * 是否开启大模型回答润色
     */
    @TableField("is_llm_enhanced")
    private Boolean isLlmEnhanced;

    /**
     * 0-纯文本 1-富文本
     */
    @TableField("answer_type")
    private Integer answerType;

    /**
     * 回答内容
     */
    @TableField(value = "answers", typeHandler = JacksonTypeHandler.class)
    private List<String> answers;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Boolean enable;
    /**
     * 关联的资源片段ID
     */
    @TableField("segment_info")
    private String segmentInfo;
    /**
     * 关联的资源ID
     */
    @TableField("resource_id")
    private String resourceId;
}
