package com.linker.fusion.knowledgecenter.infrastructure.client.person;

import lombok.Data;

import java.io.Serializable;

/**
 * OD 库返回参数
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Data
public class ODLibResp implements Serializable {

    /**
     * OD 库ID
     */
    private Long odLibId;

    /**
     * 唯一id
     */
    private String guid;

    /**
     * 名称
     */
    private String name;

    /**
     * 分类ID
     */
    private Long catalogId;

    /**
     * 分类名称
     */
    private String catalogName;

    /**
     * 特征描述
     */
    private String featureStr;

    /**
     * 置信度：0-1
     */
    private float threshold;

    /**
     * 状态：0停用 1启用
     */
    private Integer enable;

}
