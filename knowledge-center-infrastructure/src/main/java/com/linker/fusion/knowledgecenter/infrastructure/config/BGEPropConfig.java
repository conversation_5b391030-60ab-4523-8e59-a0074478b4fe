package com.linker.fusion.knowledgecenter.infrastructure.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
public class BGEPropConfig {

    public static String ImageModelId;

    public static String ImageTextId;

    public static String TextModelId;

    public static String RerankModelId;

    public static Integer TextBatch = 20;

    @Value("${bge.batch:20}")
    public Integer batch;

    @Value("${bge.version:normal}")
    private String bgeVersion;

    @Value("${bge.text:}")
    private String bgeText;

    @Value("${bge.image:}")
    private String bgeImage;

    @Value("${bge.image-text:}")
    private String bgeImageText;

    @Value("${bge.rerank:}")
    private String bgeRerank;

    @PostConstruct
    public void init() {
        if ("torch".equals(bgeVersion)) {
            ImageModelId = "bge_embedding";
            ImageTextId = "bge_embedding";
            TextModelId = "bge_embedding";
            RerankModelId = "bge-reranker-large";
        } else if ("trt".equals(bgeVersion)) {
            ImageModelId = "bge-visual_fp16_A10_trt";
            ImageTextId = "bge_image_text";
            TextModelId = "bge_embedding";
            RerankModelId = "bge-reranker-large";
        } else {
            ImageModelId = "visual";
            TextModelId = "bge";
            ImageTextId = "bge_image_text";
            RerankModelId = "rerank";
        }
        if (batch == null || batch == 0) {
            batch = 20;
        }

        if (StringUtils.isNotBlank(bgeText)) {
            TextModelId = bgeText;
        }

        if (StringUtils.isNotBlank(bgeImage)) {
            ImageModelId = bgeImage;
        }

        if (StringUtils.isNotBlank(bgeImageText)) {
            ImageTextId = bgeImageText;
        }

        if (StringUtils.isNotBlank(bgeRerank)) {
            RerankModelId = bgeRerank;
        }

        TextBatch = batch;
    }

}
