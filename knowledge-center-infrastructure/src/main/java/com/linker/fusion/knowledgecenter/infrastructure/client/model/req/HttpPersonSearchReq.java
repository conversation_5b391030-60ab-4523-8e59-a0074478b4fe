package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import lombok.Data;

import java.util.List;

/**
 * 人脸检索请求对象
 */
@Data
public class HttpPersonSearchReq {

    /**
     * 图片列表
     */
    private List<PersonSearchImagesReq> images;

    /**
     * 租户id
     */
    private String tenantId;


    @Data
    public static class PersonSearchImagesReq {
        /**
         * 资源数据
         */
        private String data;

        /**
         * 事件时间
         */
        private String eventTime;

        /**
         * ID
         */
        private String id;

        /**
         * 资源类型 URL 或 base64 目前仅支持URL
         */
        private String srcType = "URL";
    }
}

