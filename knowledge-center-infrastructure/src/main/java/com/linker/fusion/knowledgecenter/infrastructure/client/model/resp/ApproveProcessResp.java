package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import com.alibaba.fastjson.JSON;
import lombok.Data;

@Data
public class ApproveProcessResp {
    private String createBy;
    private String createid;
    private long createTime;
    /**
     * 审批描述
     */
    private String des;
    private long id;
    /**
     * 当前流程的实例url
     */
    private String instanceurl;
    /**
     * 流程模型定义JSON内容
     */
    private String modelContent;
    /**
     * 流程图标地址
     */
    private String processIcon;
    /**
     * 流程定义 key 唯一标识
     */
    private String processKey;
    /**
     * 流程定义名称
     */
    private String processName;
    /**
     * 流程状态，0，不可用 1，可用 2，历史版本
     */
    private long processState;
    /**
     * 流程定义类型（预留字段）
     */
    private String processType;
    /**
     * 流程版本
     */
    private long processVersion;
    /**
     * 备注说明
     */
    private String remark;
    /**
     * 排序
     */
    private long sort;
    private String tenantid;
    /**
     * 使用范围 ，0，全员 1，指定人员（业务关联） 2，均不可提交
     */
    private long useScope;

    public ApproveProcessConfig getConfig() {
        return JSON.parseObject(modelContent, ApproveProcessConfig.class);
    }
}
