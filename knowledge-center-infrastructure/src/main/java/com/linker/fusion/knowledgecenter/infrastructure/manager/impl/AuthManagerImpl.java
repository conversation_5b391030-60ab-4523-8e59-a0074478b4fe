package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AuthEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.SourceTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IAuthManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.AuthMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 库实现类
 */
@Service
public class AuthManagerImpl extends ServiceImpl<AuthMapper, AuthEntity> implements IAuthManager {
    @Override
    public void remove(String sourceId, Integer sourceType, String authId, Integer authType) {
        QueryWrapper<AuthEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AuthEntity::getSourceId, sourceId)
                .eq(AuthEntity::getSourceType, sourceType)
                .eq(AuthEntity::getAuthId, authId)
                .eq(AuthEntity::getAuthType, authType);
        remove(wrapper);
    }

    @Override
    public void removeBySourceId(String sourceId, Integer sourceType) {
        QueryWrapper<AuthEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AuthEntity::getSourceId, sourceId)
                .eq(AuthEntity::getSourceType, sourceType);
        remove(wrapper);
    }

    @Override
    public void update(Long id, Integer level) {
        UpdateWrapper<AuthEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(AuthEntity::getId, id)
                .set(AuthEntity::getAuthLevel, level);

        update(wrapper);
    }

    @Override
    public List<AuthEntity> listBySourceIds(Collection<String> sourceIds, Integer sourceType) {
        QueryWrapper<AuthEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(AuthEntity::getSourceId, sourceIds);
        wrapper.lambda().eq(AuthEntity::getSourceType, sourceType);
        wrapper.lambda().eq(AuthEntity::getDeleted, false);
        return list(wrapper);
    }

    @Override
    public List<AuthEntity> listByParentId(Long parentId, Integer sourceType) {
        QueryWrapper<AuthEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AuthEntity::getParentId, parentId);
        wrapper.lambda().eq(AuthEntity::getSourceType, sourceType);
        wrapper.lambda().eq(AuthEntity::getDeleted, false);
        return list(wrapper);
    }

    @Override
    public List<AuthEntity> listAuths(String tenantId, String userCode, List<String> departmentList, List<String> sourceIds, Integer sourceType) {
        QueryWrapper<AuthEntity> wrapper = new QueryWrapper<>();

        wrapper.lambda().eq(AuthEntity::getDeleted, false)
                .eq(AuthEntity::getSourceType, sourceType)
                .in(CollectionUtils.isNotEmpty(sourceIds), AuthEntity::getSourceId, sourceIds)
                .and(w ->
                        w.and(innerWrapper ->
                                        innerWrapper.eq(AuthEntity::getAuthId, userCode).eq(AuthEntity::getAuthType, AuthTypeEnum.User.getValue())
                                )
                                .or(innerWrapper ->
                                        innerWrapper.eq(AuthEntity::getAuthId, tenantId).eq(AuthEntity::getAuthType, AuthTypeEnum.Tenant.getValue()))
                                .or(CollectionUtils.isNotEmpty(departmentList), innerWrapper ->
                                        innerWrapper.in(AuthEntity::getAuthId, departmentList)
                                                .eq(AuthEntity::getAuthType, AuthTypeEnum.Department.getValue()))

                );
        return list(wrapper);
    }

    @Override
    public AuthEntity get(String sourceId, Integer sourceType, String authId, Integer authType) {
        QueryWrapper<AuthEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AuthEntity::getDeleted, false);
        wrapper.lambda()
                .eq(AuthEntity::getSourceId, sourceId)
                .eq(AuthEntity::getSourceType, sourceType)
                .eq(AuthEntity::getAuthId, authId)
                .eq(AuthEntity::getAuthType, authType);

        List<AuthEntity> list = list(wrapper);
        if (CollectionUtils.isNotEmpty(list)) return list.get(0);
        return null;
    }

    @Override
    public List<AuthEntity> list(List<String> sourceIds, Integer sourceType, String authId, Integer authType) {
        QueryWrapper<AuthEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AuthEntity::getDeleted, false);
        wrapper.lambda()
                .in(AuthEntity::getSourceId, sourceIds)
                .eq(AuthEntity::getSourceType, sourceType)
                .eq(AuthEntity::getAuthId, authId)
                .eq(AuthEntity::getAuthType, authType);

        return list(wrapper);
    }

    @Override
    public void update(String sourceId, Integer sourceType, String authId, Integer authType, Integer authLevel) {
        UpdateWrapper<AuthEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda()
                .eq(AuthEntity::getSourceId, sourceId)
                .eq(AuthEntity::getSourceType, sourceType)
                .eq(AuthEntity::getAuthId, authId)
                .eq(AuthEntity::getAuthType, authType)
                .eq(AuthEntity::getDeleted, false)
                .set(AuthEntity::getAuthLevel, authLevel);
        update(wrapper);
    }

    @Override
    public List<AuthEntity> listAuthsByParentIds(List<Long> parentIds) {
        QueryWrapper<AuthEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AuthEntity::getDeleted, false);
        wrapper.lambda()
                .eq(AuthEntity::getSourceType, SourceTypeEnum.File.getValue())
                .in(AuthEntity::getParentId, parentIds);
        return list(wrapper);
    }
}
