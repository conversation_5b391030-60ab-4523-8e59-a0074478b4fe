package com.linker.fusion.knowledgecenter.infrastructure.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GroupAction {
    @ApiModelProperty("添加目录")
    private boolean create = false;
    @ApiModelProperty("添加子目录")
    private boolean createSub = false;
    @ApiModelProperty("权限管理")
    private boolean auth = false;
    @ApiModelProperty("删除")
    private boolean delete = false;
    @ApiModelProperty("重命名")
    private boolean rename = false;
    @ApiModelProperty("移动")
    private boolean move = false;
    @ApiModelProperty("ai生成")
    private boolean aiGenerate=false;
}
