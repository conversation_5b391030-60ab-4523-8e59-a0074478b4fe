package com.linker.fusion.knowledgecenter.infrastructure.common;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SplitImageUrlUtil {

    private static final Pattern URL_PATTERN =
            Pattern.compile("(https?://[^\\s]*?\\.(?i:jpg|jpeg|png|gif|bmp))(?=[^\\w./]|$)");

    public static List<String> split(String text) {
        List<String> list = new ArrayList<>();
        Matcher m = URL_PATTERN.matcher(text);

        int last = 0;
        while (m.find()) {
            if (m.start() > last) {
                list.add(text.substring(last, m.start()));
            }
            list.add(m.group(1));
            last = m.end();
        }
        if (last < text.length()) {
            list.add(text.substring(last));
        }
        return list;
    }

    /**
     * 按裸图片 URL 拆成 Map<片段, 类型>
     */
    public static Map<String, String> splitToMap(String text) {
        Map<String, String> map = new LinkedHashMap<>(); // 保持顺序
        Matcher m = URL_PATTERN.matcher(text);

        int last = 0;
        while (m.find()) {
            // 1) URL 前面的文本
            if (m.start() > last) {
                map.put(text.substring(last, m.start()), "text");
            }
            // 2) URL 本身
            map.put(m.group(1), "image");
            last = m.end();
        }
        // 3) 末尾剩余文本
        if (last < text.length()) {
            map.put(text.substring(last), "text");
        }
        return map;
    }

    /**
     * 按裸图片 URL 拆成 Map<片段, 类型>
     */
    public static List<Map<String, String>> splitToMapToList(String text) {
        List<Map<String, String>> linkedList = new LinkedList<>();// 保持顺序
//        Map<String, String> map = new LinkedHashMap<>(); // 保持顺序
        Matcher m = URL_PATTERN.matcher(text);

        int last = 0;
        while (m.find()) {
            // 1) URL 前面的文本
            if (m.start() > last) {
                Map<String, String> map1 = new HashMap<>();
                map1.put(text.substring(last, m.start()), "text");
                linkedList.add(map1);
            }
            // 2) URL 本身
            Map<String, String> map2 = new HashMap<>();
            map2.put(m.group(1), "image");
            linkedList.add(map2);
            last = m.end();
        }
        // 3) 末尾剩余文本
        if (last < text.length()) {
            Map<String, String> map3 = new HashMap<>();
            map3.put(text.substring(last), "text");
            linkedList.add(map3);
        }
        return linkedList;
    }
}