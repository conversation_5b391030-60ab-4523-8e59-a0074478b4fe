package com.linker.fusion.knowledgecenter.infrastructure.config;

import cn.hutool.system.SystemUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * OpenBrowser
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-25 10:49
 */
@Component
public class OpenBrowser implements CommandLineRunner {
    @Value("${spring.profiles.active:local}")
    private String env;

    @Value("${server.port:8080}")
    private String port;

    @Value("${server.servlet.context-path:/}")
    private String path;

    /**
     * 运行
     */
    @Override
    public void run(String... args) throws Exception {
        if ("local".equals(env) && System.getProperty(SystemUtil.OS_NAME).startsWith("Windows")) {
            System.out.println("应用已经准备就绪 ... 启动浏览器并自动加载指定的页面 ... ");
            String url = String.format("http://localhost:%s%s%s", port, path, "/doc.html");
            System.out.println("访问地址：" + url);
            Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler " + url);
        }
    }
}