package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
public enum ResourceVisibleTypeEnum {
    PERSONAL(0, "个人"),
    PUBLIC(1, "公共");
    @Getter
    private final Integer value;
    @Getter
    private final String name;

    public static String getName(Integer visibleType) {
        return Arrays.stream(ResourceVisibleTypeEnum.values())
                .filter(c -> c.getValue().equals(visibleType)).findFirst().get().getName();
    }
}
