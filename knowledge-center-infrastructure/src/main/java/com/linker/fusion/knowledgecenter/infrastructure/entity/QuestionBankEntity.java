package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linker.fusion.knowledgecenter.infrastructure.model.QuestionBankOptionModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 选择题: {
 * "question": "以下哪个是正确的?",
 * "answer": "A,C",
 * "analysis": "选项A和C是正确的，因为...",
 * "options": [
 * {"key": "A", "value": "选项1"},
 * {"key": "B", "value": "选项2"},
 * {"key": "C", "value": "选项3"},
 * {"key": "D", "value": "选项4"}
 * ],
 * "questionType": "CHOICE"
 * }
 * 判断题:
 * {
 * "question": "地球是圆的",
 * "answer": "1",
 * "analysis": "地球确实是圆的，因为...",
 * "options": [
 * {"key": "1", "value": "正确"},
 * {"key": "0", "value": "错误"}
 * ],
 * "questionType": "TRUE_FALSE"
 * }
 */
@Data
@TableName("t_question_bank")
@EqualsAndHashCode(callSuper = true)
public class QuestionBankEntity extends BaseEntity {

    @TableField("group_id")
    private Long groupId;

    @TableField("question")
    private String question;
    /**
     * 问题类型, CHOICE("选择题"),MULTIPLE_CHOICE("多选题"),TRUE_FALSE("判断题"),FILL_IN_BLANK("填空题");
     *
     * @see com.linker.fusion.knowledgecenter.infrastructure.enums.QuestionTypeEnum
     */
    @TableField("question_type")
    private String questionType;

    @TableField("answer")
    // 选择题时存储正确答案的内容，判断题时存储 1/0
    private String answer;

    @TableField("analysis")
    private String analysis;

    @TableField("question_image")
    private String questionImage;

    @TableField("options")
    // JSON数组，选择题存储所有选项，判断题固定为["正确", "错误"]
    private String options;

    @TableField("enable")
    private Boolean enable;

    @TableField("uid")
    private String uid;

    @TableField(exist = false)
    private List<String> authCodes;

    @TableField(exist = false)
    private Integer authLevel;
    @TableField("faq_info")
    private String faqInfo;
    public List<String> getUrls() {
        List<String> urls = new ArrayList<>();
        if (StringUtils.isNotBlank(questionImage))
            urls.add(questionImage);
        List<QuestionBankOptionModel> opList = JSON.parseArray(options, QuestionBankOptionModel.class);
        opList.forEach(op -> {
            if (CollectionUtils.isEmpty(op.getImages()))
                return;
            urls.addAll(op.getImages());
        });
        return urls;
    }
}