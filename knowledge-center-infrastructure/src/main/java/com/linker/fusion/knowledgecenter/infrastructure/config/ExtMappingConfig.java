package com.linker.fusion.knowledgecenter.infrastructure.config;

import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "exts")
@RefreshScope
public class ExtMappingConfig {
    private List<String> table = Arrays.asList("xlsx", "xls");
    private List<String> image = new ArrayList<>();
    private List<String> doc = new ArrayList<>();
    private List<String> video = new ArrayList<>();
    private List<String> audio = new ArrayList<>();

    public FileTypeEnum getResourceType(String suffix) {
        if (StringUtils.isBlank(suffix) || suffix.length() <= 1) {
            return FileTypeEnum.Other;
        }
        //去除.
        String suffix2 = suffix.toLowerCase(); //.substring(1);
        if (doc.stream().filter(e -> e.equals(suffix2)).findAny().isPresent()) {
            return FileTypeEnum.DOCUMENT;
        } else if (audio.stream().filter(e -> e.equals(suffix2)).findAny().isPresent()) {
            return FileTypeEnum.AUDIO;
        } else if (video.stream().filter(e -> e.equals(suffix2)).findAny().isPresent()) {
            return FileTypeEnum.VIDEO;
        } else if (image.stream().filter(e -> e.equals(suffix2)).findAny().isPresent()) {
            return FileTypeEnum.IMAGE;
        }
        return FileTypeEnum.Other;
    }

    public static String getUrlSuffix(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }

        // 获取最后一个斜杠后的部分
        String fileName = url.substring(url.lastIndexOf('/') + 1);

        // 获取文件名中的最后一个点后的部分
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex == -1) {
            return null; // 没有后缀名
        }

        return fileName.substring(dotIndex + 1);
    }

    public FileTypeEnum getResourceTypeByUrl(String url) {
        if (StringUtils.isBlank(url) || url.length() <= 1) {
            return FileTypeEnum.Other;
        }
        return getResourceType(getUrlSuffix(url));
    }
}
