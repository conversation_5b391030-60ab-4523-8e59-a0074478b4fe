package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 人大视频搜索记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Getter
@Setter
@TableName("t_npc_search_log")
public class NpcSearchLogEntity extends BaseEntity {

    /**
     * 文件类型 1-文档 2-图片 3-视频
     */
    @TableField("file_type")
    private Integer fileType;

    /**
     * 按什么搜索 1-会议（标题） 2-人员（人脸库） 3-内容（ASR）
     */
    @TableField("search_type")
    private Integer searchType;

    /**
     * 搜索内容，输入或者勾选人物ID
     */
    @TableField("search_content")
    private String searchContent;

    @TableField("input")
    private String input;
}
