package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * BaseEntity
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-16 16:50
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public abstract class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private String creatorId;


    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill =  FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_id", fill =  FieldFill.UPDATE)
    private String updateId;

    /**
     * 租户或组织的唯一标识符
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    private Boolean deleted;

    public void init(String tenantId,String userCode){
        this.tenantId=tenantId;
        this.createTime=LocalDateTime.now();
        this.updateTime=LocalDateTime.now();
        this.creatorId=userCode;
        this.updateId=userCode;
        this.deleted=false;
    }
}