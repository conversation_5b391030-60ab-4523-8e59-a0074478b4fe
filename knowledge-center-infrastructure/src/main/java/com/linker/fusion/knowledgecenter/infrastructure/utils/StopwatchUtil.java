package com.linker.fusion.knowledgecenter.infrastructure.utils;

import cn.hutool.core.date.StopWatch;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StopwatchUtil {
    public static void preStart(StopWatch stopWatch, String a) {
        try {
            stopWatch.start(a);
        } catch (Exception e) {
            stop(stopWatch);
            log.error("stopWatch error:{}", e.getMessage());
        }

    }

    public static void stop(StopWatch stopWatch) {
        try {
            stopWatch.stop();
        } catch (Exception e) {
            log.error("stopWatch error:{}", e.getMessage());
        }
    }
}
