package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class ApproveStartFormModel {

    private Map<String, String> formData = new LinkedHashMap<>();

    private Schema schema = new Schema();

    @Data
    public static class Schema {
        private List<SchemaItem> schemas = new ArrayList<>();
        private String script;

        @Data
        public static class SchemaItem {
            private ComponentProps componentProps;
            private String id = "default";
            private String label;
            private String type;
            private List<Child> children = new ArrayList<>();

            @Data
            @Accessors(chain = true)
            public static class Child {
//                private ComponentProps componentProps;
                private String field;
                private Boolean input = true;
                private String label;
                private String type = "input";
                private String id;
            }
        }
    }



    @Data
    public static class ComponentProps {
        private Boolean colon = true;
        private String labelAlign = "right";
        private LabelCol labelCol = new LabelCol();
        private String labelLayout = "fixed";
        private String labelPlacement = "left";
        private Integer labelWidth = 120;
        private String layout = "horizontal";
        private String name = "default";
        private WrapperCol wrapperCol = new WrapperCol();
    }

    @Data
    public static class LabelCol {
        private Integer span = 5;
    }

    @Data
    public static class WrapperCol {
        private Integer span = 19;
    }

//    @Data
//    public static class ComponentProps {
//        private String defaultValue;
//        private String placeholder;
//        private String type;
//    }
}