package com.linker.fusion.knowledgecenter.infrastructure.es;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ImageChunkEntity extends BaseChunkEntity {

    /**
     * 图片向量化
     */
    @JsonProperty("image_vector")
    private List<Double> imageVector;

    /**
     * 人脸标签列表
     */
    @JsonProperty("persons_labels")
    private List<String> personsLabels;

    /**
     * 实体标签
     */
    @JsonProperty("object_labels")
    private List<String> objectLabels;

    /**
     * 专名标签
     */
    @JsonProperty("text_labels")
    private List<String> textLabels;

    /**
     * 排序
     */
    @JsonProperty("sort")
    private Double sort;




}
