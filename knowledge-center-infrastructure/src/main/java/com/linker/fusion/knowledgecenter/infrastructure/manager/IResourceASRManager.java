package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceASREntity;

import java.util.List;
import java.util.Set;


public interface IResourceASRManager extends IService<ResourceASREntity> {
    List<ResourceASREntity> list(String segmentId);

    void clear(String docId);

    List<ResourceASREntity> listByDocId(String docId);

    void clearByFileIds(List<String> fileIds);

    Set<String> getDocIdsByContent(String tenantId, String asrContent);

    List<ResourceASREntity> listByDocIdsAndContent(List<String> docId, String asrContent);

}
