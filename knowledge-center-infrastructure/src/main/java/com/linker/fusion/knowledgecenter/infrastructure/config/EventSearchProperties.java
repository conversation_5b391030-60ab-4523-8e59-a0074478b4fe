package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "event-search")
@RefreshScope
public class EventSearchProperties {

    private Integer maxResult = 100;

    private String model = "qwen2-72b-instruct";

    private String systemPrompt = "请根据用户输入问题，调用指定工具";

    private String toolJson = "[\n" +
            "    {\n" +
            "        \"type\": \"function\",\n" +
            "        \"name\": \"ai_advanced_search\",\n" +
            "        \"description\": \"根据用户输入的关键词，自动给出高级搜索的指令\",\n" +
            "        \"parameters\": {\n" +
            "            \"type\": \"object\",\n" +
            "            \"properties\": {\n" +
            "                \"proper_noun_info\": {\n" +
            "                    \"description\": \"专有名词信息\",\n" +
            "                    \"type\": \"object\",\n" +
            "                    \"properties\": {\n" +
            "                        \"person\": {\n" +
            "                            \"description\": \"人物信息，人名\",\n" +
            "                            \"type\": \"array\",\n" +
            "                            \"items\": {\n" +
            "                                \"type\": \"string\"\n" +
            "                            }\n" +
            "                        },\n" +
            "                        \"organization\": {\n" +
            "                            \"description\": \"组织信息，组织名称或带有指代组织的名称\",\n" +
            "                            \"type\": \"array\",\n" +
            "                            \"items\": {\n" +
            "                                \"type\": \"string\"\n" +
            "                            }\n" +
            "                        },\n" +
            "                        \"location\": {\n" +
            "                            \"description\": \"地点信息，地点名称或带有指代地点的名称\",\n" +
            "                            \"type\": \"array\",\n" +
            "                            \"items\": {\n" +
            "                                \"type\": \"string\"\n" +
            "                            }\n" +
            "                        }\n" +
            "                    }\n" +
            "                },\n" +
            "                \"object_info\": {\n" +
            "                    \"description\": \"物体名称如眼镜，杯子等，或者事件名称:打坐、朝拜、推行李箱、多人聚集、人在床上、使用电子产品、在床上看书、在床上躺卧、玩手机\",\n" +
            "                    \"type\": \"array\",\n" +
            "                    \"items\": {\n" +
            "                        \"type\": \"string\"\n" +
            "                    }\n" +
            "                },\n" +
            "                \"person_info\": {\n" +
            "                    \"description\": \"人物的姓名，如张三，李四\",\n" +
            "                    \"type\": \"array\",\n" +
            "                    \"items\": {\n" +
            "                        \"type\": \"string\"\n" +
            "                    }\n" +
            "                },\n" +
            "                \"related_filename_info\": {\n" +
            "                    \"type\": \"array\",\n" +
            "                    \"items\": {\n" +
            "                        \"type\": \"object\",\n" +
            "                        \"properties\": {\n" +
            "                            \"condition\": {\n" +
            "                                \"description\": \"条件\",\n" +
            "                                \"type\": \"string\",\n" +
            "                                \"enum\": [\n" +
            "                                    \"等于\",\n" +
            "                                    \"包含\",\n" +
            "                                    \"不包含\"\n" +
            "                                ]\n" +
            "                            },\n" +
            "                            \"filename\": {\n" +
            "                                \"description\": \"用户需要搜索文件名或标题\",\n" +
            "                                \"type\": \"string\"\n" +
            "                            }\n" +
            "                        }\n" +
            "                    }\n" +
            "                }\n" +
            "            },\n" +
            "            \"required\": [\n" +

            "            ]\n" +
            "        }\n" +
            "    }\n" +
            "]";

    public static void main(String[] args) {
        System.out.println(new EventSearchProperties().getToolJson());
    }
}
