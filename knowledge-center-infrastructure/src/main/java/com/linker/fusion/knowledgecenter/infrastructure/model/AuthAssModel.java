package com.linker.fusion.knowledgecenter.infrastructure.model;

import cn.hutool.core.collection.CollectionUtil;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AuthEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthLevelEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.SourceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * 授权关联类
 */
@Data
@NoArgsConstructor
public class AuthAssModel {
    @ApiModelProperty("分组")
    private KnowledgeGroupEntity group;
    @ApiModelProperty("授权类型")
    private SourceTypeEnum sourceType;
    @ApiModelProperty("授权Id")
    private String sourceId;
    @ApiModelProperty("授权信息")
    private AuthEntity auth;
    @ApiModelProperty("父级授权")
    private AuthEntity parentAuth;
    @ApiModelProperty("所有授权")
    private List<AuthEntity> allAuths;
    @ApiModelProperty("父级目录列表")
    private List<KnowledgeGroupEntity> parents;
    @ApiModelProperty("跟目录")
    private KnowledgeGroupEntity root;
    @ApiModelProperty("是否是管理员")
    private Boolean isManager;
    @ApiModelProperty("租户id")
    private String tenantId;
    @ApiModelProperty("用户id")
    private String userId;
    @ApiModelProperty("部门列表")
    private List<String> departmentCodes;

    public AuthAssModel(String tenantId, String userId, List<String> departmentCodes, String sourceId, SourceTypeEnum sourceType) {
        this.tenantId = tenantId;
        this.userId = userId;
        this.auth = AuthEntity.noAuth();
        this.sourceType = sourceType;
        this.departmentCodes = Objects.isNull(departmentCodes) ? Collections.emptyList() : departmentCodes;
        this.sourceId = sourceId;
        this.parents = new ArrayList<>();
    }

    public AuthAssModel(String tenantId, String userId, List<String> departmentCodes, KnowledgeGroupEntity group) {
        this(tenantId, userId, departmentCodes, group.getId().toString(), SourceTypeEnum.Group);
        this.group = group;
        if (group.getParentId().equals(0L)) {
            root = group;
        }
    }

    public AuthAssModel(AuthAssModel parent, KnowledgeGroupEntity group) {
        this(parent.getTenantId(), parent.getUserId(), null, group.getId().toString(), SourceTypeEnum.Group);
        this.group = group;
        this.departmentCodes = parent.getDepartmentCodes();
        this.allAuths = parent.getAllAuths();
        this.isManager = parent.isManager;
        this.root = parent.getRoot();
        this.parentAuth = parent.getAuth();
    }

    /**
     * 过滤 权限编码
     *
     * @param authMap   权限等级编码列表
     * @param syncCodes 支持同步目录的编码
     * @return 编码列表
     */
    public List<String> getAuthedCodes(Map<Integer, List<String>> authMap, List<String> syncCodes) {
        if (auth.getAuthLevel().equals(AuthLevelEnum.NoAuth.getValue())) {
            return Collections.emptyList();
        }
        List<String> authedCodes = new ArrayList<>(authMap.get(auth.getAuthLevel()));
        if (CollectionUtil.isNotEmpty(syncCodes)) {
            authedCodes.retainAll(syncCodes);
        }
        return authedCodes;

    }

}
