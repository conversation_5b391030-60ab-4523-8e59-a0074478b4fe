package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ExaminationPaperEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.PaperStatusEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IExaminationPaperManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.ExaminationPaperMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 试卷表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Service
public class ExaminationPaperManagerImpl extends ServiceImpl<ExaminationPaperMapper, ExaminationPaperEntity> implements IExaminationPaperManager {

    @Override
    public ExaminationPaperEntity getDetail(String tenantId, String paperUniqueId) {
        return this.getOne(new LambdaQueryWrapper<ExaminationPaperEntity>()
                .eq(ExaminationPaperEntity::getDeleted, 0)
                .eq(ExaminationPaperEntity::getTenantId, tenantId)
                .eq(ExaminationPaperEntity::getPaperUniqueId, paperUniqueId));
    }

    @Override
    public ExaminationPaperEntity create(String tenantId, String userCode, String questionBaseIdsJson, String generateQuestionTypesJson,
                                         Integer generateQuestionNumber, Integer generateRealNumber, String paperContentJson) {
        ExaminationPaperEntity paperEntity = new ExaminationPaperEntity();
        paperEntity.init(tenantId, userCode);
        paperEntity.setBindQuestionBaseIds(questionBaseIdsJson);
        paperEntity.setGenerateQuestionTypes(generateQuestionTypesJson);
        paperEntity.setPaperUniqueId(IdUtil.simpleUUID());
        paperEntity.setPaperContent(paperContentJson);
        paperEntity.setExpectQuestionNumber(generateQuestionNumber);
        paperEntity.setRealQuestionNumber(generateRealNumber);
        paperEntity.setPaperStatus(0);
        this.save(paperEntity);
        return paperEntity;
    }

    @Override
    public void updatePaper(String tenantId, String userCode, String paperAnswerContentJson, Integer paperStatus, String paperScoreRespJson, String paperUniqueId) {
        LambdaUpdateWrapper<ExaminationPaperEntity> updateWrapper = new LambdaUpdateWrapper<ExaminationPaperEntity>()
                .eq(ExaminationPaperEntity::getDeleted, 0)
                .eq(ExaminationPaperEntity::getTenantId, tenantId)
                .eq(ExaminationPaperEntity::getPaperUniqueId, paperUniqueId)
                .set(ExaminationPaperEntity::getUpdateTime, LocalDateTime.now())
                .set(ExaminationPaperEntity::getUpdateId, userCode)
                .set(ExaminationPaperEntity::getPaperAnswerContent, paperAnswerContentJson);

        if (Objects.equals(1, paperStatus)) {
            updateWrapper.set(ExaminationPaperEntity::getPaperStatus, 1).set(ExaminationPaperEntity::getPaperScore, paperScoreRespJson);
        }
        this.update(updateWrapper);
    }

    @Override
    public void finalSubmit(String tenantId, String userCode, String paperUniqueId, String paperScoreRespJson, String paperAnswerInfo) {
        this.update(new LambdaUpdateWrapper<ExaminationPaperEntity>()
                .eq(ExaminationPaperEntity::getDeleted, 0)
                .eq(ExaminationPaperEntity::getTenantId, tenantId)
                .eq(ExaminationPaperEntity::getPaperUniqueId, paperUniqueId)
                .set(ExaminationPaperEntity::getUpdateTime, LocalDateTime.now())
                .set(ExaminationPaperEntity::getPaperScore, paperScoreRespJson)
                .set(ExaminationPaperEntity::getPaperAnswerContent, paperAnswerInfo)
                .set(ExaminationPaperEntity::getPaperStatus, PaperStatusEnum.FINISHED.getCode())
                .set(ExaminationPaperEntity::getUpdateId, userCode));
    }

}
