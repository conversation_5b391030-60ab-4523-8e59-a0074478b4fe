package com.linker.fusion.knowledgecenter.infrastructure.es;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class VideoChunkEntity extends BaseChunkEntity {
    /**
     * 扩展数据
     */
    @JsonProperty("meta")
    private JSONObject meta;
    /**
     * 人脸标签列表
     */
    @JsonProperty("persons_labels")
    private List<String> personsLabels;
    /**
     * 实体标签
     */
    @JsonProperty("object_labels")
    private List<String> objectLabels;
    /**
     * 专名标签
     */
    @JsonProperty("text_labels")
    private List<String> textLabels;
    /**
     * 排序
     */
    @JsonProperty("sort")
    private Double sort;
    /**
     * 开始时间
     */
    @JsonProperty("start_timestamp")
    private Long startTimestamp;
    /**
     * 结束时间
     */
    @JsonProperty("end_timestamp")
    private Long endTimestamp;
    /**
     * 启用
     */
    @JsonProperty("enable")
    private Integer enable;

}
