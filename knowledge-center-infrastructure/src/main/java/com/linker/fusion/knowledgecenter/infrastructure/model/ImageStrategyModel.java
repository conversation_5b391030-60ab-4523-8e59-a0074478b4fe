package com.linker.fusion.knowledgecenter.infrastructure.model;

import com.linker.fusion.knowledgecenter.infrastructure.dto.ODModelInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 策略配置对象响应数据结构
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImageStrategyModel extends StrategyModelBasic {

    @ApiModelProperty("图片解析增强 默认值1 不可改变")
    private Integer imageEnhance = 1;

//    @Deprecated
//    @ApiModelProperty("图像描述生成 默认：1, 1 caption、 2 OCR+caption(禁用)")
//    @Range(min = 1, max = 2, message = "图像描述生成类型不合法")
//    private Integer captionType = 1;
@ApiModelProperty("语义检索支持 1 启用 0禁用")
private Integer enableVector = 1;
    @ApiModelProperty("实体识别 0-关闭 1-开启 默认:1")
    private Integer enableObjectDetection = 1;

    @ApiModelProperty("人脸识别 0-关闭 1-开启 默认:1")
    private Integer enableFaceRecognition = 1;

    @ApiModelProperty("OD模版信息列表")
    private List<ODModelInput> odList;
    @Override
    public void setDefault() {
        imageEnhance = 1;
        if (enableObjectDetection == null) {
            enableObjectDetection = 1;
        }
        if (enableFaceRecognition == null) {
            enableFaceRecognition = 1;
        }
    }
}
