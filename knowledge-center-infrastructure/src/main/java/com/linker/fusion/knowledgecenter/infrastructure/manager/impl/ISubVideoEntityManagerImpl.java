package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.SubVideoEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.ISubVideoEntityManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.SubVideoEntityMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Service
public class ISubVideoEntityManagerImpl extends ServiceImpl<SubVideoEntityMapper, SubVideoEntity> implements ISubVideoEntityManager {

    @Override
    public List<SubVideoEntity> listByDocId(String docId) {
        return list(
                new LambdaQueryWrapper<SubVideoEntity>()
                        .eq(SubVideoEntity::getDocId, docId)
                        .eq(SubVideoEntity::getDeleted, false)
                        .orderByAsc(SubVideoEntity::getSort)
                        .orderByAsc(SubVideoEntity::getTitle)
        );
    }

    @Override
    public void deleteByDocIds(List<String> docIds) {
        if (CollectionUtils.isEmpty(docIds)) {
            return;
        }
        update(
                new LambdaUpdateWrapper<SubVideoEntity>()
                        .in(SubVideoEntity::getDocId, docIds)
                        .set(SubVideoEntity::getDeleted, true)
        );
    }

    @Override
    public void deleteByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        update(
                new LambdaUpdateWrapper<SubVideoEntity>()
                        .in(SubVideoEntity::getId, ids)
                        .set(SubVideoEntity::getDeleted, true)
        );
    }
}
