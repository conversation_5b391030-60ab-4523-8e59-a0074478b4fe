package com.linker.fusion.knowledgecenter.infrastructure.enums;


import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.MenuKeyEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@AllArgsConstructor
public enum OperatorTypeEnum {

    LIB_CREATE(0, "新增知识库"),
    IMPORT(1, "导入"),
    PREVIEW(2, "文件预览"),
    EDIT(3, "编辑"),
    DOWNLOAD(4, "下载"),
    DELETE(5, "删除文件"),
    REDO(6, "重新学习"),
    MAINTENANCE(7, "分段维护"),
    COPY(8, "分享"),
    MOVE(9, "移动"),
    RENAME(10, "重命名"),
    ARCHIVE(11, "归档"),
    SAVE_PART(12, "保存视频片段"),
    DOWNLOAD_PART(13, "下载视频片段"),
    INSPECTION(14, "巡检详情"),
    ANALYZE_SETTING(15, "解析设置"),
    MANAGER(16, "管理"),
    CREATE(17, "新建"),
    AI_GENERATE(18, "AI生成"),
    EXPORT(19, "导出"),
    AUTH(20, "权限管理"),
    DOC_GENERATE(21, "文档生成"),
    ENABLE(22, "启用禁用"),
    LIB_EDIT(23, "修改目录库"),
    LIB_DELETE(24, "删除目录库"),
    VIDEO_MERGE_SPLIT(25, "视频合并拆分"),
    ;
    @Getter
    private final Integer value;
    @Getter
    private final String name;

    public List<MenuKeyEnum> getMenuKey(Integer knowledgeType, Integer visibleType) {
        OperatorTypeEnum opType = this;
        return Arrays.stream(MenuKeyEnum.values())
                .filter(k ->
                        Objects.nonNull(k.getOperatorType()) && k.getOperatorType().equals(opType) &&
                                (Objects.isNull(k.getType()) || k.getType().getType().equals(knowledgeType)) &&
                                (Objects.isNull(k.getVisibleType()) || k.getVisibleType().getValue().equals(visibleType))).collect(Collectors.toList());
    }


}
