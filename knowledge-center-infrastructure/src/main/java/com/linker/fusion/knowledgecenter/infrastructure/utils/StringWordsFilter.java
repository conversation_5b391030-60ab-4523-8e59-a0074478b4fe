package com.linker.fusion.knowledgecenter.infrastructure.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * AC自动机算法搜索字符串中的关键词
 *
 * <AUTHOR>
 * @Date 2023/11/30 上午8:47
 * @DESC:
 */
@Slf4j
public class StringWordsFilter {

    public final static char replaceTemp = '§';
    public final static String replaceMarkDown = "\\*";
    private static TrieNode root;

    public StringWordsFilter(Set<String> words) {
        root = buildTrieTree(words);
        buildFailurePointer(root);
    }

    public static void main(String[] args) {
        Set<String> words = new HashSet<>(Arrays.asList("敏感词1", "1和敏", "敏感词3"));
        StringWordsFilter filter = new StringWordsFilter(words);

        String text = "这是一条包含敏感词1和敏感词3的文本敏词1";

        // 查找所有敏感词
        List<StringWordsFilterResult> results = filter.findAllWords(text);
        results.forEach(result ->
                log.info("找到敏感词：{}，位置：{}", result.getWord(), result.getStartIndex())
        );

        // 替换敏感词
        String replacedText = filter.replaceAllWords(text, '*');
        log.info("替换后的文本：{}", replacedText);
    }

    private TrieNode buildTrieTree(Set<String> words) {
        TrieNode root = new TrieNode();
        for (String word : words) {
            if (word == null || word.isEmpty()) {
                continue;
            }

            TrieNode current = root;
            for (char c : word.toCharArray()) {
                current = current.children.computeIfAbsent(c, k -> new TrieNode());
            }
            current.isEndOfWord = true;
            current.pattern = word;
        }
        return root;
    }

    private void buildFailurePointer(TrieNode root) {
        Queue<TrieNode> queue = new LinkedList<>();

        // 第一层节点的失败指针都指向根节点
        for (TrieNode child : root.children.values()) {
            child.failurePointer = root;
            queue.offer(child);
        }

        while (!queue.isEmpty()) {
            TrieNode current = queue.poll();

            // 遍历当前节点的子节点
            for (Map.Entry<Character, TrieNode> entry : current.children.entrySet()) {
                char c = entry.getKey();
                TrieNode child = entry.getValue();
                queue.offer(child);

                TrieNode failurePointer = current.failurePointer;
                while (failurePointer != null && !failurePointer.children.containsKey(c)) {
                    failurePointer = failurePointer.failurePointer;
                }

                // 设置失败指针
                child.failurePointer = (failurePointer != null)
                        ? failurePointer.children.get(c)
                        : root;
            }
        }
    }

    /**
     * 在文本中搜索关键词
     *
     * @param text 待搜索文本
     */
    public List<StringWordsFilterResult> findAllWords(String text) {
        List<StringWordsFilterResult> results = new ArrayList<>();

        if (text == null || text.isEmpty()) {
            return results;
        }

        TrieNode current = root;
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);

            // 跳转失败指针
            while (current != null && !current.children.containsKey(c)) {
                current = current.failurePointer;
            }

            // 重置当前节点
            current = (current == null) ? root : current.children.getOrDefault(c, root);

            // 检查是否匹配敏感词
            TrieNode matchNode = current;
            while (matchNode != root) {
                if (matchNode.isEndOfWord) {
                    int startIndex = i - matchNode.pattern.length() + 1;
                    StringWordsFilterResult result = new StringWordsFilterResult(true, matchNode.pattern, startIndex);
                    results.add(result);
                }
                matchNode = matchNode.failurePointer;
            }
        }

        return results;
    }

    /**
     * 检查文本是否包含关键词
     *
     * @param text 待检测文本
     */
    public boolean containsWords(String text) {
        return !findAllWords(text).isEmpty();
    }

    /**
     * 替换文本中的敏感词
     *
     * @param text        待处理文本
     * @param replacement 替换字符
     * @return 替换后的文本
     */
    public String replaceAllWords(String text, char replacement) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        List<StringWordsFilterResult> sensitiveWords = findAllWords(text);
        if (sensitiveWords.isEmpty()) {
            return text;
        }

        // 按照匹配位置降序排序,避免替换索引错误
        sensitiveWords.sort((a, b) -> b.getStartIndex() - a.getStartIndex());

        StringBuilder result = new StringBuilder(text);
        for (StringWordsFilterResult word : sensitiveWords) {
            int startIndex = word.getStartIndex();
            result.replace(startIndex, startIndex + word.getWord().length(),
                    createReplacementString(replacement, word.getWord().length()));
        }

        return result.toString();
    }

    private String createReplacementString(char replacement, int length) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < length; i++) {
            result.append(replacement);
        }
        return result.toString();
    }
}

