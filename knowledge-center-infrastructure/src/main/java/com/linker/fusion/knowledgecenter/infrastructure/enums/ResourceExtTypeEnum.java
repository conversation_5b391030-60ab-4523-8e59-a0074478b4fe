package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件类型枚举
 */
@AllArgsConstructor
@Getter
public enum ResourceExtTypeEnum {

    K2Supplement("K2Supplement", "K2异常补充"),
    K2Result("K2Result", "巡检结果"),
    ConversationId("ConversationId", "会话Id"),
    Expired("Expire", "过期时间"),
    ImageRecognize("ImageRecognize", "图像识别"),
    ;
    private final String type;
    private final String description;
}
