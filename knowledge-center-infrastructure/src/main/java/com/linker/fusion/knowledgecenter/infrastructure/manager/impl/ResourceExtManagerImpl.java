package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceExtEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceExtTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IResourceExtManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.ResourceExtMapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 **/
@Repository
public class ResourceExtManagerImpl extends ServiceImpl<ResourceExtMapper, ResourceExtEntity> implements IResourceExtManager {

    @Override
    public List<ResourceExtEntity> list(List<String> resIds, ResourceExtTypeEnum resourceExtTypeEnum) {
        QueryWrapper<ResourceExtEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ResourceExtEntity::getDocId, resIds);
        wrapper.lambda().eq(ResourceExtEntity::getDeleted, false);
        if (Objects.nonNull(resourceExtTypeEnum)) {
            wrapper.lambda().eq(ResourceExtEntity::getType, resourceExtTypeEnum.getType());
        }
        return list(wrapper);
    }

    @Override
    public List<ResourceExtEntity> list(ResourceExtTypeEnum resourceExtTypeEnum, List<String> values) {
        QueryWrapper<ResourceExtEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ResourceExtEntity::getField1, values);
        wrapper.lambda().eq(ResourceExtEntity::getDeleted, false);
        wrapper.lambda().eq(ResourceExtEntity::getType, resourceExtTypeEnum.getType());
        return list(wrapper);
    }


    @Override
    public void removeByDocIdType(String docId, ResourceExtTypeEnum resourceExtTypeEnum) {
        QueryWrapper<ResourceExtEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceExtEntity::getDocId, docId);
        wrapper.lambda().eq(ResourceExtEntity::getType, resourceExtTypeEnum);
        remove(wrapper);
    }

    @Override
    public List<ResourceExtEntity> listLt(ResourceExtTypeEnum resourceExtTypeEnum, String value) {
        QueryWrapper<ResourceExtEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().lt(ResourceExtEntity::getField1, value);
        wrapper.lambda().eq(ResourceExtEntity::getDeleted, false);
        wrapper.lambda().eq(ResourceExtEntity::getType, resourceExtTypeEnum.getType());
        return list(wrapper);
    }

    @Override
    public void deleteByResIds(List<String> resIds) {
        UpdateWrapper<ResourceExtEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(ResourceExtEntity::getDeleted, true);
        wrapper.lambda().in(ResourceExtEntity::getDocId, resIds);
        update(wrapper);
    }
}
