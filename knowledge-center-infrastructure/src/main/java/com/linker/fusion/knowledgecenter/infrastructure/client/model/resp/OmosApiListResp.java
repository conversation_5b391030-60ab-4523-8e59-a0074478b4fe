package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;



@NoArgsConstructor
@Data
public class OmosApiListResp {
    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private List<DataDTO> data;
    @JsonProperty("total")
    private Integer total;
    @JsonProperty("totalPage")
    private Integer totalPage;
    @JsonProperty("success")
    private Boolean success;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("appName")
        private String appName;
        @JsonProperty("versionNum")
        private String versionNum;
        @JsonProperty("actionId")
        private String actionId;
        @JsonProperty("updateTime")
        private String updateTime;
        @JsonProperty("createTime")
        private String createTime;
        @JsonProperty("appStatus")
        private Integer appStatus;
        @JsonProperty("appStatusText")
        private String appStatusText;
        @JsonProperty("taskTypeCode")
        private String taskTypeCode;
        @JsonProperty("isMatchType")
        private Boolean isMatchType;
        @JsonProperty("abilityType")
        private Integer abilityType;
        @JsonProperty("abilityEnum")
        private Integer abilityEnum;
        @JsonProperty("abilityNewType")
        private String abilityNewType;
        @JsonProperty("servingId")
        private Integer servingId;
        @JsonProperty("isRegister")
        private Boolean isRegister;
    }
}
//
//{
//        "code": "0",
//        "message": "成功",
//        "data": [
//        {
//        "appName": "专名识别",
//        "versionNum": "V1.0.0",
//        "actionId": "hanner_tag",
//        "updateTime": "2025-05-19 17:42:59",
//        "createTime": "2025-05-19 17:42:59",
//        "appStatus": 0,
//        "appStatusText": "运行中",
//        "taskTypeCode": "TaskTypeeyrq5obhy9c",
//        "isMatchType": false,
//        "abilityType": 0,
//        "abilityEnum": 88,
//        "abilityNewType": "common",
//        "servingId": 174,
//        "isRegister": true
//        },
//        {
//        "appName": "ASR",
//        "versionNum": "V1.0.0",
//        "actionId": "transcriptions_url",
//        "updateTime": "2025-05-17 13:57:49",
//        "createTime": "2025-05-17 13:57:49",
//        "appStatus": 0,
//        "appStatusText": "运行中",
//        "taskTypeCode": "TaskType7f0cbux54lj",
//        "isMatchType": false,
//        "abilityType": 0,
//        "abilityEnum": 88,
//        "abilityNewType": "common",
//        "servingId": 173,
//        "isRegister": true
//        },
//        {
//        "appName": "omdet02_openapi",
//        "versionNum": "V1",
//        "actionId": "LH210_101_004253_001",
//        "updateTime": "2025-05-14 16:53:56",
//        "createTime": "2025-05-14 16:53:56",
//        "appStatus": 0,
//        "appStatusText": "运行中",
//        "taskTypeCode": "TaskTypehn9j9ixghez",
//        "isMatchType": false,
//        "abilityType": 4,
//        "abilityEnum": 3,
//        "abilityNewType": "ovd",
//        "servingId": -1,
//        "isRegister": false
//        },
//        {
//        "appName": "qwen2-72b-instruct",
//        "versionNum": "V1.0.0",
//        "actionId": "qwen2-72b-instruct",
//        "updateTime": "2025-05-14 14:01:20",
//        "createTime": "2025-05-14 14:01:20",
//        "appStatus": 0,
//        "appStatusText": "运行中",
//        "taskTypeCode": "TaskTypejr2ygn7oz5l",
//        "isMatchType": false,
//        "abilityType": 0,
//        "abilityEnum": 43,
//        "abilityNewType": "llm",
//        "servingId": 169,
//        "isRegister": true
//        },
//        {
//        "appName": "qwen2.5-vl-32b-instruct",
//        "versionNum": "V1.0.0",
//        "actionId": "qwen2.5-vl-32b-instruct",
//        "updateTime": "2025-05-14 13:58:52",
//        "createTime": "2025-05-14 13:58:52",
//        "appStatus": 0,
//        "appStatusText": "运行中",
//        "taskTypeCode": "TaskType7rt5if9arsc",
//        "isMatchType": false,
//        "abilityType": 0,
//        "abilityEnum": 44,
//        "abilityNewType": "chat",
//        "servingId": 168,
//        "isRegister": true
//        },
//        {
//        "appName": "qwen2.5-vl-7b-instruct",
//        "versionNum": "V1.0.0",
//        "actionId": "qwen2.5-vl-7b-instruct",
//        "updateTime": "2025-05-14 13:57:21",
//        "createTime": "2025-05-14 13:57:21",
//        "appStatus": 0,
//        "appStatusText": "运行中",
//        "taskTypeCode": "TaskType2yih21jahul",
//        "isMatchType": false,
//        "abilityType": 0,
//        "abilityEnum": 44,
//        "abilityNewType": "chat",
//        "servingId": 167,
//        "isRegister": true
//        },
//        {
//        "appName": "镜头识别",
//        "versionNum": "V1.0.0",
//        "actionId": "video_split_minio",
//        "updateTime": "2025-05-14 12:56:15",
//        "createTime": "2025-05-14 12:56:15",
//        "appStatus": 0,
//        "appStatusText": "运行中",
//        "taskTypeCode": "TaskType3s1vuzzrdjz",
//        "isMatchType": false,
//        "abilityType": 0,
//        "abilityEnum": 88,
//        "abilityNewType": "common",
//        "servingId": 164,
//        "isRegister": true
//        }
//        ],
//        "total": 7,
//        "totalPage": 1,
//        "success": true
//        }
