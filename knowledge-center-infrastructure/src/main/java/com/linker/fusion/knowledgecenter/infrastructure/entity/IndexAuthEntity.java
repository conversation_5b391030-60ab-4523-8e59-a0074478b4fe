package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 索引定时配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
@Getter
@Setter
@TableName("t_index_auth")
public class IndexAuthEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户或组织的唯一标识符
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 索引是否开启定时任务 1-开启 0-关闭
     */
    @TableField("index_time_schedule_open")
    private Boolean indexTimeScheduleOpen;

    /**
     * 索引定时任务配置
     */
    @TableField("index_time_config")
    private String indexTimeConfig;
}
