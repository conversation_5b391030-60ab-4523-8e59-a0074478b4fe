package com.linker.fusion.knowledgecenter.infrastructure.client.aas.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class WorkflowAsyncRunReq {
    /**
     * 回调地址
     */
    private String callback;
    /**
     * 工作流ID
     */
    private String id;
    /**
     * 工作流的输入参数
     */
    private Map<String, Object> input;
    /**
     * 用户的透传参数,异步回调会把参数带回去
     */
    @JsonProperty("passthrough")
    private String passThrough;
}