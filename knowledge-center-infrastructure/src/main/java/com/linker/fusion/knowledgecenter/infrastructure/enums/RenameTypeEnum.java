package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RenameTypeEnum {

    Cover(1, "覆盖"),
    <PERSON><PERSON>(2, "跳过"),
    Rename(3, "重命名");

    private final Integer value;

    private final String name;
    public Integer getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.name;
    }
}
