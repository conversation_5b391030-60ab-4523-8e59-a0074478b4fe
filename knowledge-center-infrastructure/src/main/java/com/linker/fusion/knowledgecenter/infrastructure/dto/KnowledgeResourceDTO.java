package com.linker.fusion.knowledgecenter.infrastructure.dto;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class KnowledgeResourceDTO implements Serializable {
    /**
     * docId
     */

    private String docId;

//    /**
//     * workFlowId
//     */
//    private String workflowId;

    /**
     * 知识类型 1-文档 2-图片 3-视频 4-音频 5-FAQ 6-表格
     */
    private Integer type;

    /**
     * 组ID
     */
    private Long groupId;


    /**
     * 资源名称
     */
    private String title;
    private String creatorId;
    /**
     * 副标题
     */
    private String subheading;

    /**
     * 文件后缀
     */
    private String suffix;

    /**
     * 描述
     */
    private String description;

    /**
     * 资源地址
     */
    private String url;

    /**
     * 预览地址
     */
    private String previewSrc;

    /**
     * 是否已去除水印 -> 0：否、1：是，清洗规则开启去除水印、且上传的文档类型是PDF、且成功去除了水印
     */
    private Boolean cleanWatermarkFlag;

    /**
     * 去除水印的url，仅切分和分段维护时使用
     */
    private String cleanWatermarkUrl;

    /**
     * 文件资源大小（byte）
     */
    private Long size;

    /**
     * 文档：页数；表格：行数
     */
    private Long count;

    /**
     * 是否生效 -> 0：否、1：是
     */
    private Boolean enable;

    /**
     * 扩展信息字段json
     */
    private String extInfo;

    private JSONObject extInfoJson;
    private String strategy;
    private JSONObject strategyJson;
    private Long templateId;

    private Long id;


    /**
     * 租户或组织的唯一标识符
     */
    private String tenantId;

    private String createTime;

    public String getFileName() {
        return title + "." + suffix;
    }


    public static List<KnowledgeResourceDTO> toDTO(List<KnowledgeResourceEntity> listByDocIds) {
        List<KnowledgeResourceDTO> knowledgeResourceDTOlist = Lists.newArrayList();
        for (KnowledgeResourceEntity knowledgeResourceEntity : listByDocIds) {
            knowledgeResourceDTOlist.add(convertFromKnowledgeResourceEntity(knowledgeResourceEntity));
        }
        return knowledgeResourceDTOlist;
    }

    public static KnowledgeResourceDTO convertFromKnowledgeResourceEntity(KnowledgeResourceEntity knowledgeResourceEntity) {
        KnowledgeResourceDTO knowledgeResourceDTO = new KnowledgeResourceDTO();
        knowledgeResourceDTO.setDocId(knowledgeResourceEntity.getDocId());
        knowledgeResourceDTO.setType(knowledgeResourceEntity.getType());
        knowledgeResourceDTO.setGroupId(knowledgeResourceEntity.getGroupId());
        knowledgeResourceDTO.setTitle(knowledgeResourceEntity.getTitle());
        knowledgeResourceDTO.setSubheading(knowledgeResourceEntity.getSubheading());
        knowledgeResourceDTO.setSuffix(knowledgeResourceEntity.getSuffix());
        knowledgeResourceDTO.setDescription(knowledgeResourceEntity.getDescription());
        knowledgeResourceDTO.setUrl(knowledgeResourceEntity.getUrl());
        knowledgeResourceDTO.setPreviewSrc(knowledgeResourceEntity.getPreviewSrc());
        knowledgeResourceDTO.setCleanWatermarkFlag(knowledgeResourceEntity.getCleanWatermarkFlag());
        knowledgeResourceDTO.setCleanWatermarkUrl(knowledgeResourceEntity.getCleanWatermarkUrl());
        knowledgeResourceDTO.setSize(knowledgeResourceEntity.getSize());
        knowledgeResourceDTO.setCount(knowledgeResourceEntity.getCount());
        knowledgeResourceDTO.setEnable(knowledgeResourceEntity.getEnable());
        knowledgeResourceDTO.setExtInfo(knowledgeResourceEntity.getExtInfo());
        knowledgeResourceDTO.setExtInfoJson(JSONObject.parseObject(knowledgeResourceEntity.getExtInfo()));
        knowledgeResourceDTO.setId(knowledgeResourceEntity.getId());
        knowledgeResourceDTO.setTenantId(knowledgeResourceEntity.getTenantId());
        knowledgeResourceDTO.setCreateTime(StringComUtils.convertStr(knowledgeResourceEntity.getCreateTime()));
        knowledgeResourceDTO.setStrategy(knowledgeResourceEntity.getStrategy());
        knowledgeResourceDTO.setStrategyJson(JSONObject.parseObject(knowledgeResourceEntity.getStrategy()));
        return knowledgeResourceDTO;
    }

}