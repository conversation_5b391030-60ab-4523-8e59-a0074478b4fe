package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OmosCommonReq<T> implements Serializable {

    @JsonProperty("model")
    private String model;

    @JsonProperty("requestJson")
    private String requestJson;

    @JsonIgnore  // Jackson库的忽略注解
    @JSONField(serialize = false)
    private T data;

    public OmosCommonReq(String model, T data) {
        this.model = model;
        this.requestJson = JSON.toJSONString(data);
    }
}
