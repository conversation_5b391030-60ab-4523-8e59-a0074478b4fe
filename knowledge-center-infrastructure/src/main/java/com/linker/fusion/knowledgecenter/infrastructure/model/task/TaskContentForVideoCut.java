package com.linker.fusion.knowledgecenter.infrastructure.model.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskContentForVideoCut {
    private String url;
    private Long start;
    private Long end;
    private String saveFileId;
    @ApiModelProperty("去除水印区域")
    private List<CutAreaItem> removeAreas;
    @ApiModelProperty("加水印区域")
    private List<CutWatermarkAreaItem> watermarkAreas;
}
