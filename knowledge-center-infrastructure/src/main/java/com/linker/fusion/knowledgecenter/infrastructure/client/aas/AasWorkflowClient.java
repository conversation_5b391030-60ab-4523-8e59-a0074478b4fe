package com.linker.fusion.knowledgecenter.infrastructure.client.aas;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.*;
import org.springframework.stereotype.Service;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;

@Service
@RetrofitClient(baseUrl = "${workflow.execute.endpoint}", readTimeoutMs = 120000, sourceOkHttpClient = "kernelOkHttpClient")
@Intercept(handler = AasApiSignInterceptor.class)
public interface AasWorkflowClient {

    @POST("api/workflow/v1/sync-run")
    BaseResp<WorkflowSyncRunResp> runSync(@Body WorkflowSyncRunReq req);

    @POST("api/workflow/v1/async-run")
    WorkflowAsyncRunResp runAsync(@Body WorkflowAsyncRunReq req);

    @GET("api/workflow/v1/{workflow_instance_id}/results")
    BaseResp<WorkflowResultResp> results(@Path("workflow_instance_id") String workflowInstanceId);

    @POST("api/workflow/v1/async-cancel")
    BaseResp<Object> cancelAsync(@Body WorkflowAsyncCancelReq req);

    @POST("api/workflow/v1/pause")
    BaseResp<Object> pause(@Body WorkflowAsyncCancelReq req);

    @POST("api/workflow/v1/resume")
    BaseResp<Object> resume(@Body WorkflowAsyncCancelReq req);
}
