package com.linker.fusion.knowledgecenter.infrastructure.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.person.PromptGetReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.person.PromptResp;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Service;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

import java.util.List;

@Service
@RetrofitClient(baseUrl = "${ide-server.endpoint:http://127.0.0.1:5001}")
public interface IdeServerClient {

    /**
     * 移除任务
     */
    @GET("prompt/getList")
    BaseResp<List<PromptResp>> promptGetList(@Query("status") Integer status,
                                             @Query("promptKeyList") List<String> promptKeyList);
}
