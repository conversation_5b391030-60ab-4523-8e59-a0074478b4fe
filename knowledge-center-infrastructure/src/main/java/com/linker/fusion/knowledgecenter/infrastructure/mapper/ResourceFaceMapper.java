package com.linker.fusion.knowledgecenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceFaceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ResourceFaceMapper extends BaseMapper<ResourceFaceEntity> {

    List<Map<String, Object>> listGroupBySourceIdOrderByCount(@Param("docIds") List<String> docIds);
}
