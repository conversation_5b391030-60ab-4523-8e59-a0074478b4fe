package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import lombok.Data;

import java.util.List;

@Data
public class OcrResp {

    private Long took;

    private List<List<SingleSentence>> objects;

    @Data
    public static class SingleSentence {
        private Double xmin;
        private Double ymin;
        private Double xmax;
        private Double ymax;
        private Double conf;
        private String label;
    }
}

/**
 * {
 *     "took": 338,
 *     "objects": [
 *         [
 *             {
 *                 "xmin": 132.0,
 *                 "ymin": 44.0,
 *                 "xmax": 161.0,
 *                 "ymax": 60.0,
 *                 "conf": 0.999671220779419,
 *                 "label": "备注"
 *             }
 *         ]
 *     ]
 * }
 */