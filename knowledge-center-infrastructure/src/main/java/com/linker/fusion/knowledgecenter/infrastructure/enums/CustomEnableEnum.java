package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 扩展字段生效枚举
 **/
@AllArgsConstructor
@Getter
public enum CustomEnableEnum {

    ENABLE(1, "启用"),
    UN_ENABLE(0, "禁用");

    /**
     * 代码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    public static CustomEnableEnum valueOf(Integer code) {
        for (CustomEnableEnum e : CustomEnableEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static CustomEnableEnum valueOfDesc(String desc) {
        for (CustomEnableEnum e : CustomEnableEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e;
            }
        }
        return null;
    }


}
