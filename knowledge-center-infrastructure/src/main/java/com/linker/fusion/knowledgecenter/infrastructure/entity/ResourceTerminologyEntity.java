package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_resource_terminology")
public class ResourceTerminologyEntity extends BaseEntity {
    /**
     * 分段id
     */
    @TableField("segment_id")
    private String segmentId;
    /**
     * 文档id
     */
    @TableField("doc_id")
    private String docId;
    /**
     * 标签类型
     */
    @TableField("`type`")
    private String type;
    /**
     * 标签名称
     */
    @TableField("`name`")
    private String name;
    /**
     * 位置
     */
    @TableField("position")
    private String position;
    /**
     * 位置
     */
    @TableField("time_point")
    private Long timePoint;
}
