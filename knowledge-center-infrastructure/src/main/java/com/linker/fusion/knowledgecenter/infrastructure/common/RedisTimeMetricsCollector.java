package com.linker.fusion.knowledgecenter.infrastructure.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class RedisTimeMetricsCollector {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${metrics:false}")
    private boolean metricsEnabled;
    private HashOperations<String, String, String> hashOps;

    public RedisTimeMetricsCollector(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.hashOps = redisTemplate.opsForHash();
    }

    // 记录开始时间
    public void recordStartTime(String taskId, String metric) {
        if (!metricsEnabled) {
            return;
        }
        String key = "task:" + metric;
        String field = "start:" + taskId;
        long startTime = System.currentTimeMillis();
        hashOps.put(key, field, String.valueOf(startTime));
        redisTemplate.expire(key, Duration.ofMinutes(120));
    }

    // 记录结束时间并计算耗时
    public void recordEndTime(String taskId, String metric) {
        if (!metricsEnabled) {
            return;
        }
        String key = "task:" + metric;
        String startField = "start:" + taskId;
        String durationField = "duration:" + taskId;

        String startTimeStr = hashOps.get(key, startField);
        if (startTimeStr != null) {
            long startTime = Long.parseLong(startTimeStr);
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            hashOps.put(key, durationField, String.valueOf(duration));
            // 清除开始时间
            hashOps.delete(key, startField);
        }
    }

    // 定时输出统计结果
//    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void reportStatistics() {
        redisTemplate.keys("task:*").forEach(key -> {
            Map<String, String> entries = hashOps.entries(key);
            String taskId = key.split(":")[1];

            entries.forEach((field, value) -> {
                if (field.startsWith("duration:")) {
                    String metric = field.split(":")[1];
                    System.out.println("Task ID: " + taskId + ", Metric: " + metric + ", Duration: " + value + " ms");

                    // 清除统计数据
                    hashOps.delete(key, field);
                }
            });

            // 如果任务没有剩余字段，删除整个哈希
            if (hashOps.entries(key).isEmpty()) {
                redisTemplate.delete(key);
            }
        });
    }
}
