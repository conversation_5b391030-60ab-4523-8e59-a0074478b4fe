package com.linker.fusion.knowledgecenter.infrastructure.client.aas.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class WorkflowResultResp {

    /**
     * 错误码
     */
    private String code;

    /**
     * 工作流输入参数
     */
    private Map<String, Object> input;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 工作流输出参数
     */
    private Map<String, Object> output;

    /**
     * 透传数据字段
     */
    private String passthrough;

    /**
     * 工作流执行的状态
     */
    private WorkflowStatus status;

    /**
     * 工作流的运行实例id
     */
    @JsonProperty("workflow_instance_id")
    private String workflowInstanceid;

}
