package com.linker.fusion.knowledgecenter.infrastructure.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DocAction {
    @ApiModelProperty("导入文档")
    private boolean upload = false;
    @ApiModelProperty("文档预览")
    private boolean view = false;
    @ApiModelProperty("编辑")
    private boolean edit = false;
    @ApiModelProperty("下载")
    private boolean download = false;
    @ApiModelProperty("删除")
    private boolean delete = false;
    @ApiModelProperty("失效")
    private boolean expire = false;
    @ApiModelProperty("重新学习")
    private boolean redo = false;
    @ApiModelProperty("分段维护")
    private boolean segmentEdit = false;
    @ApiModelProperty("文档分段/分块")
    private boolean chunkEdit = false;
    @ApiModelProperty("移动文档")
    private boolean move = false;
    @ApiModelProperty("重命名")
    private boolean rename = false;
    @ApiModelProperty("归档")
    private boolean archive=false;
    @ApiModelProperty("巡检")
    private boolean inspect=false;
}
