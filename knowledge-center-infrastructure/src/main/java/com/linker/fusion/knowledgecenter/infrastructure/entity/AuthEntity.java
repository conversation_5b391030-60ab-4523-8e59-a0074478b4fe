package com.linker.fusion.knowledgecenter.infrastructure.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthLevelEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("t_auth")
@NoArgsConstructor
public class AuthEntity extends BaseEntity{
    /**
     * 被授权资源id
     */
    private String sourceId;
    /**
     * 授权的父级id
     */
    private Long parentId;
    /**
     * 资源类型 1分组 2文件
     */
    private Integer sourceType;
    /**
     * 授权id
     */
    private String authId;
    /**
     * 授权类型 1 用户 2部门 3租户
     */
    private Integer authType;
    /**
     * 授权类型
     */
    private Integer authLevel;

    public AuthEntity(String tenantId, String userId, String sourceId, Long parentId, Integer sourceType, String authId, Integer authType, Integer authLevel) {
        this.authId = authId;
        this.authType = authType;
        this.parentId = parentId;
        this.sourceId = sourceId;
        this.sourceType = sourceType;
        this.authLevel = authLevel;
        this.setCreateTime(LocalDateTime.now());
        this.setDeleted(false);
        this.setCreateTime(LocalDateTime.now());
        this.setCreatorId(userId);
        this.setCreateTime(LocalDateTime.now());
        this.setUpdateId(userId);
        this.setUpdateTime(LocalDateTime.now());
        this.setTenantId(tenantId);
    }

    /**
     * 无权限
     *
     * @return
     */
    public static AuthEntity noAuth() {
        AuthEntity auth = new AuthEntity();
        auth.setAuthLevel(AuthLevelEnum.NoAuth.getValue());
        return auth;
    }

    /**
     * 管理权限
     *
     * @return
     */
    public static AuthEntity manager() {
        AuthEntity auth = new AuthEntity();
        auth.setAuthLevel(AuthLevelEnum.Manage.getValue());
        return auth;
    }

    /**
     * 查看权限
     *
     * @return
     */
    public static AuthEntity view() {
        AuthEntity auth = new AuthEntity();
        auth.setAuthLevel(AuthLevelEnum.View.getValue());
        return auth;
    }
}
