package com.linker.fusion.knowledgecenter.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@AllArgsConstructor
public enum OprationLogRecordTypeEnum {
    FILE_MANAGEMENT("文件管理"),
//    KNOWLEDGE_BASE_MANAGEMENT("知识库管理")
    ;

    @Getter
    private final String desc;

    public static String getByName(String name) {
        for (OprationLogRecordTypeEnum oprationLogRecordTypeEnum : values()) {
            if (oprationLogRecordTypeEnum.name().equals(name)) {
                return oprationLogRecordTypeEnum.getDesc();
            }
        }
        return null;
    }
}
