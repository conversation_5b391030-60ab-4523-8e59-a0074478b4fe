package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FaqInfoEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IFaqInfoManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.FaqInfoMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * FAQ表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
@Service
public class FaqInfoManagerImpl extends ServiceImpl<FaqInfoMapper, FaqInfoEntity> implements IFaqInfoManager {

    @Override
    public List<FaqInfoEntity> selectByQuestionAndGroupIds(String tenantId, String question, List<Long> groupIds, Integer limit) {
        return baseMapper.selectList(
                new LambdaQueryWrapper<FaqInfoEntity>()
                        .eq(FaqInfoEntity::getTenantId, tenantId)
                        .eq(FaqInfoEntity::getDeleted, false)
                        .eq(FaqInfoEntity::getEnable, true)
                        .in(CollectionUtils.isNotEmpty(groupIds), FaqInfoEntity::getGroupId, groupIds)
                        .like(StringUtils.isNotBlank(question), FaqInfoEntity::getQuestion, question)
                        .last(limit != null, "limit " + limit)
        );
    }

    @Override
    public List<FaqInfoEntity> getByIds(String tenantId, List<Long> ids) {
        if (tenantId == null || ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<FaqInfoEntity>()
                .eq(FaqInfoEntity::getTenantId, tenantId)
                .eq(FaqInfoEntity::getDeleted, false)
                .in(FaqInfoEntity::getId, ids)
                .orderByDesc(FaqInfoEntity::getCreateTime)
                .orderByDesc(FaqInfoEntity::getId)
        );
    }

    @Override
    public Long getCount(String tenantId) {
        return count(new LambdaQueryWrapper<FaqInfoEntity>()
                .eq(FaqInfoEntity::getTenantId, tenantId)
                .isNull(FaqInfoEntity::getStandardId)
                .eq(FaqInfoEntity::getDeleted, false)
        );
    }

    @Override
    public List<FaqInfoEntity> listByGroupIds(List<Long> groupIds, Integer type) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<FaqInfoEntity>()
                .eq(FaqInfoEntity::getDeleted, false)
                .eq(Objects.nonNull(type), FaqInfoEntity::getType, type)
                .in(FaqInfoEntity::getGroupId, groupIds)
        );
    }

    @Override
    public List<FaqInfoEntity> getByUids(String tenantId, Set<String> uIds) {
        return list(
                new LambdaQueryWrapper<>(FaqInfoEntity.class)
                        .eq(FaqInfoEntity::getTenantId, tenantId)
                        .in(FaqInfoEntity::getUid, uIds)
        );
    }
}
