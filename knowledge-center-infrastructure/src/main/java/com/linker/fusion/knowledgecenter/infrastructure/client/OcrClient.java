package com.linker.fusion.knowledgecenter.infrastructure.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.OcrReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.OcrResp;
import org.springframework.stereotype.Service;
import retrofit2.http.Body;
import retrofit2.http.POST;

@Service
@RetrofitClient(baseUrl = "${ocr.endpoint}")
public interface OcrClient {

//    @POST("omdetv2turbo/serving/omocr_ocr")
//    OcrResp ocr(@Body OcrReq req);

    @POST("omocr/v1/process/ocr_infer")
    OcrResp ocr(@Body OcrReq req);
}
