package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 反馈学习设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_feedback_setting")
public class FeedbackSettingEntity extends BaseEntity {

    /**
     * 智能体ID
     */
    @TableField("agent_id")
    private Long agentId;

    /**
     * 开启反馈学习，默认禁用
     */
    @TableField("learn_enable")
    private Boolean learnEnable;
}
