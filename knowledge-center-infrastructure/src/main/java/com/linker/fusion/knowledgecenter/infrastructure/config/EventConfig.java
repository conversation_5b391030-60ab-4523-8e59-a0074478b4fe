package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Data
@RefreshScope
@ConfigurationProperties(prefix = "event")
public class EventConfig {

    private String model;

    private List<Event> items;

    @Data
    public static class Event {

        private String name;

        private String desc;
    }
}
