package com.linker.fusion.knowledgecenter.infrastructure.client.person;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SceneMatchResp {

    @ApiModelProperty("输入的ID")
    private String id;

    @ApiModelProperty("输入的URL")
    private String url;

    @ApiModelProperty("命中的场景特征信息")
    private List<SceneInfo> sceneInfos = new ArrayList<>();

    @Data
    public static class SceneInfo {

        @ApiModelProperty("特征ID")
        private String id;

        @ApiModelProperty("名称")
        private String name;

        @ApiModelProperty("命中分数")
        private Double score;
    }

}