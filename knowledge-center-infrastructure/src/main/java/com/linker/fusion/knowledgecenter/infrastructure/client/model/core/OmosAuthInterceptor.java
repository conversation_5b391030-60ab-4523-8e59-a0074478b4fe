package com.linker.fusion.knowledgecenter.infrastructure.client.model.core;

import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import com.linker.omagent.starter.config.OmOsProperties;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class OmosAuthInterceptor extends BasePathMatchInterceptor {

    @Autowired
    OmOsProperties omOsProperties;

    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        Request request = chain.request();

        Request newRequest = request.newBuilder()
                .url(request.url())
                .method(request.method(), request.body())
                .header("Authorization", "Bearer " + omOsProperties.getApiKey())
                .build();
        return chain.proceed(newRequest);
    }
}
