package com.linker.fusion.knowledgecenter.infrastructure.validation;

import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.stereotype.Component;

import java.sql.Connection;

@Slf4j
@Component
@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})})
public class MybatisKeywordCompatibilityInterceptor implements Interceptor {

    @org.springframework.beans.factory.annotation.Value("${spring.datasource.driver-class-name}")
    private String driver;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        if (StringUtils.isNotEmpty(driver)) {
            if (driver.equals("com.gbasedbt.jdbc.Driver")) {
                StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
                BoundSql boundSql = statementHandler.getBoundSql();
                String sql_new = boundSql.getSql()
                        .replaceAll("`index`", "index")
                        .replaceAll("`name`", "name")
                        .replaceAll("`data`", "data")
                        .replaceAll("`desc`", "desc")
                        .replaceAll("`status`", "status")
                        .replaceAll("`key`", "key")
                        .replaceAll("`icon`", "icon")
                        .replaceAll("`sort`", "sort")
                        .replaceAll("`remark`", "remark")
                        .replaceAll("`type`", "type");

                PluginUtils.mpBoundSql(boundSql).sql(sql_new);
            }
        }
        return invocation.proceed();
    }
}