package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class PersonInfoDTO implements Serializable {

    private String id;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 是否重点人物
     */
    private Boolean isFocus;

    /**
     * 人物名称
     */
    private String name;

    @ApiModelProperty("一级分类ID")
    private Integer type = 1;

    @ApiModelProperty("一级分类名称")
    private String typeName;

    @ApiModelProperty("二级分类ID")
    private Long sensitiveId = 0L;

    @ApiModelProperty(value = "二级分类名称")
    private String sensitiveName;
    @ApiModelProperty("是否是已知人物")
    private Integer isKnown;
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 人脸库维护数组
     */
    private List<PhotoMapResp> photos = new ArrayList<>();

    /**
     * 人脸信息详情
     */
    @Data
    public static class PhotoMapResp {
        /**
         * 地址
         */
        private String url;

        /**
         * Checked
         */
        private Boolean checked = true;
    }

    public String getViewImage() {
        return photos.stream().filter(PhotoMapResp::getChecked).findFirst().map(PhotoMapResp::getUrl).orElse(null);
    }
}