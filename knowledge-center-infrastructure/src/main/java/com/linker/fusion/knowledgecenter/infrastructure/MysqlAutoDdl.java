package com.linker.fusion.knowledgecenter.infrastructure;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.ddl.IDdl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.DDLHistoryEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceImportantLevelEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceVisibleTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IDDLHistoryManager;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IKnowledgeGroupManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Component
public class MysqlAutoDdl implements IDdl {

    @Autowired
    private DataSource dataSource;
    @Resource
    private IDDLHistoryManager iddlHistoryManager;
    @Resource
    private IKnowledgeGroupManager knowledgeGroupManager;


    @Override
    public void runScript(Consumer<DataSource> consumer) {
        consumer.accept(dataSource);
        fixPath();
        fixGroupId();



    }

    /**
     * 修复分组数据
     */
    private void fixPath() {
        String dataInitSql = "db/dataInit__V1.7.5.1.sql";
        if (iddlHistoryManager.list(new QueryWrapper<DDLHistoryEntity>().lambda().eq(DDLHistoryEntity::getScript, dataInitSql)).stream().count() == 0) {
            List<KnowledgeGroupEntity> allGroups = knowledgeGroupManager.list(new QueryWrapper<KnowledgeGroupEntity>().lambda()
                    .eq(KnowledgeGroupEntity::getVisibleType, ResourceVisibleTypeEnum.PERSONAL.getValue())
                    .eq(KnowledgeGroupEntity::getDeleted, false));
            allGroups.stream().forEach(
                    group -> {
                        if (group.getBizType().equals(2))
                            return;
                        KnowledgeGroupEntity parent = getOrCreateByBiz(group.getTenantId(), group.getCreatorId(), 2);
                        if (group.getType().equals(KnowledgeTypeEnum.FILE.getType()) && group.getParentId().equals(0L)) {
                            group.setIsLibrary(false);
                            group.setPath(parent.getSearchPath());
                            group.setParentId(parent.getId());
                            knowledgeGroupManager.updateById(group);
                        } else if (!group.getType().equals(KnowledgeTypeEnum.FILE.getType())) {
                            if (group.getParentId().equals(parent.getId())) {
                                group.setParentId(0L);
                                group.setIsLibrary(true);
                                knowledgeGroupManager.updateById(group);
                            }
                        }

                    }
            );
            allGroups.stream().forEach(group -> {
                String path = getPath(allGroups, group);
                if (StringUtils.isNotBlank(path)) {
                    path = "/" + path + "/";
                }
                if (!group.getPath().equals(path)) {
                    group.setPath(path);
                    knowledgeGroupManager.updatePath(group.getId(), group.getPath());
                }
            });
            insertHistory(dataInitSql);
        }
    }

    private void fixGroupId() {
        String dataInitSql = "db/dataInit__V1.7.5.sql";
        if (iddlHistoryManager.list(new QueryWrapper<DDLHistoryEntity>().lambda().eq(DDLHistoryEntity::getScript, dataInitSql)).stream().count() == 0) {
            List<KnowledgeGroupEntity> allGroups = knowledgeGroupManager.list(new QueryWrapper<KnowledgeGroupEntity>().lambda()
                    .eq(KnowledgeGroupEntity::getDeleted, false));
            Map<Long, List<KnowledgeGroupEntity>> map = allGroups.stream().collect(Collectors.groupingBy(KnowledgeGroupEntity::getParentId));
            fixSubGroupId(map, 0L, "");
            insertHistory(dataInitSql);
        }
    }

    private void fixSubGroupId(Map<Long, List<KnowledgeGroupEntity>> map, Long parentId, String parentGuid) {
        List<KnowledgeGroupEntity> groups = map.get(parentId);
        if (Objects.isNull(groups)) {
            return;
        }
        groups.stream().forEach(group -> {
            group.setGuid(UUID.randomUUID().toString());
            group.setParentIdS(parentGuid);
            fixSubGroupId(map, group.getId(), group.getGuid());
        });
        knowledgeGroupManager.updateBatchById(groups);
    }

    private void insertHistory(String script) {
        DDLHistoryEntity ddlHistoryEntity = new DDLHistoryEntity();
        ddlHistoryEntity.setScript(script);
        ddlHistoryEntity.setType("init");
        ddlHistoryEntity.setVersion(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")));
        iddlHistoryManager.save(ddlHistoryEntity);
    }


    private KnowledgeGroupEntity getOrCreateByBiz(String tenantId, String userId, Integer bizType) {
        //如果文件夹不存在 则创建
        KnowledgeGroupEntity group = knowledgeGroupManager.getByBiz(tenantId, userId, bizType);
        if (Objects.isNull(group)) {
            group = new KnowledgeGroupEntity();
            group.init(tenantId, userId);
            group.setType(KnowledgeTypeEnum.FILE.getType());
            group.setImportantLevel(ResourceImportantLevelEnum.DEFAULT.getValue());
            group.setVisibleType(ResourceVisibleTypeEnum.PERSONAL.getValue());
            group.setDescription("自动生成的文件夹");
            group.setName(bizType == 1 ? "会话文件" : "我的文件");
            group.setIsLibrary(true);

            if (bizType == 1) {
                group.setParentId(getOrCreateByBiz(tenantId, userId, 1).getId());
            } else {
                group.setParentId(0L);
            }
            group.setIsSync(false);
            group.setSort(0d);
            group.setBizType(bizType);
            group.setPath("");
            group.setLogo("");
            group.setApproveProcessKey("");
            knowledgeGroupManager.save(group);
        }
        return group;
    }

    private String getPath(List<KnowledgeGroupEntity> allGroups, KnowledgeGroupEntity group) {
        if (group.getParentId().equals(0L))
            return "";
        else {
            Optional<KnowledgeGroupEntity> opParent = allGroups.stream().filter(a -> a.getId().equals(group.getParentId())).findFirst();
            if (opParent.isPresent()) {
                String path = getPath(allGroups, opParent.get());
                if (StringUtils.isNotBlank(path))
                    path = path + "/";
                return path + group.getParentId();
            } else {
                return "";
            }
        }


    }

    /**
     * 获取要执行的SQL脚本文件顺序列表
     */
    @Override
    public List<String> getSqlFiles() {
        return Arrays.asList(
                "db/V202409131810__v1.0.0.sql",
                "db/V202409201810__v1.0.1.sql",
                "db/V202409230931__v1.0.1.sql",
                "db/V202409230932__v1.0.1.sql",
                "db/V202409261532__v1.0.1.sql",
                "db/V202409262313__v1.0.1.sql",
                "db/V202409271532__v1.0.1.sql",
                "db/V202410101532__v1.0.1.sql",
                "db/V202410111532__v1.0.1.sql",
                "db/V202410161556__v1.0.1.sql",
                "db/V202410261556__v1.0.1.sql",
                "db/V202411061556__v1.0.1.sql",
                "db/V202411261335__v1.4.4.sql",
                "db/V202411271825__v1.4.4.sql",
                "db/V202412130915__v1.4.4.sql",
                "db/V202412161100__v1.4.4.sql",
                "db/V202412310900__v1.4.6.sql",
                "db/V202501061614__v1.4.6.sql",
                "db/V202501071629__v1.4.6.sql",
                "db/V202501131718__v1.4.6.sql",
                "db/V202501141418__v1.4.6.sql",
                "db/V202501161518__v1.4.6.sql",
                "db/V202501171738__v1.4.6.sql",
                "db/V202501201438__v1.4.6.sql",
                "db/V202501221308__v1.4.6.sql",
                "db/V202501221508__v1.4.6.sql",
                "db/V202502190000__v1.5.0.sql",
                "db/V202503170000__v1.5.0.sql",
                "db/V202503120000__v1.5.1.sql",
                "db/V202503190000__v1.5.1.sql",
                "db/V202503200000__v1.5.1.sql",
                "db/V202503200000__v1.6.1.sql",
                "db/V202504010000__v1.6.2.sql",
                "db/V202504020000__v1.6.3.sql",
                "db/V202504210000__v1.6.4.sql",
                "db/V202505120000__v1.6.6.sql",
                "db/V202505160000__v1.6.6.sql",
                "db/V202505190000__v1.6.6.sql",
                "db/V202505221331_sqloptimze_v1.6.6.sql",
                "db/V202506120000__v1.6.7.sql",
                "db/V202506190000__v1.6.8.sql",
                "db/V202506240000__v1.6.8.sql",
                "db/V202507080000__v1.6.8.sql",
                "db/V202507080000__v1.7.0.sql",
                "db/V202508190000__v1.7.4.sql",
                "db/V202508250000__v1.7.4.sql",
                "db/V202509090000__v1.7.4.sql",
                "db/V202509030000__v1.7.5.sql",
                "db/V20250911810__v1.7.5.sql"

        );
    }
}

