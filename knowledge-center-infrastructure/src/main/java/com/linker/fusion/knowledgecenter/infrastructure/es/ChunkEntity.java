package com.linker.fusion.knowledgecenter.infrastructure.es;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.linker.fusion.knowledgecenter.infrastructure.dto.KnowledgeResourceDTO;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceSegmentEntity;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Accessors(chain = true)
public class ChunkEntity extends BaseChunkEntity {




    /**
     * 路径
     */
    @JsonProperty("path")
    @Deprecated
    private String path;


    /**
     * 分页
     */
    @JsonProperty("page")
    private Integer page;
    /**
     * 排序
     */
    @JsonProperty("sort")
    private Double sort;
    /**
     * 位置
     */
    @JsonProperty("position")
    private String position;
    @JsonProperty("status")
    private Integer status;

    public static ChunkEntity create(KnowledgeResourceDTO resource, ResourceSegmentEntity segment) {
        String url = StringComUtils.getImgUrl(segment.getContent());
        return create(resource, segment, url);
    }

    public static ChunkEntity create(KnowledgeResourceDTO resource, ResourceSegmentEntity segment, String url) {
        ChunkEntity chunk = new ChunkEntity();
        chunk.setPage(0);
        chunk.setContent(StringUtils.isBlank(url) ? segment.getContent() : "");
        chunk.setTitle(resource.getTitle());
        chunk.setPosition("{}");
        chunk.setUrl(url);
        chunk.setSort(1d);
        chunk.setSrcType(StringUtils.isNotBlank(url) ? "image" : "text");
        chunk.setChunkId(UUID.randomUUID().toString());
        chunk.setMeta(JSON.parseObject(resource.getExtInfo()));
        chunk.setCreateTime(resource.getCreateTime());
        chunk.setCreateId(resource.getCreatorId());
        chunk.setTenantId(resource.getTenantId());
        chunk.setDocId(resource.getDocId());
        chunk.setSegmentId(segment.getSegmentId());
//        chunk.setPath(resource.getGroupPath());
        chunk.setEnable(Boolean.TRUE.equals(resource.getEnable()) ? 1 : 0);
        chunk.setGroupId(resource.getGroupId());
        return chunk;
    }

    public static ChunkEntity create(KnowledgeResourceEntity resource, ResourceSegmentEntity segment) {
        String url = StringComUtils.getImgUrl(segment.getContent());
        return create(resource, segment, url);
    }

    public static ChunkEntity create(KnowledgeResourceEntity resource, ResourceSegmentEntity segment, String url) {
        ChunkEntity chunk = new ChunkEntity();
        chunk.setPage(0);
        chunk.setContent(StringUtils.isBlank(url) ? segment.getContent() : "");
        chunk.setTitle(resource.getTitle());
        chunk.setPosition("{}");
        chunk.setUrl(url);
        chunk.setSort(1d);
        chunk.setSrcType(StringUtils.isNotBlank(url) ? "image" : "text");
        chunk.setChunkId(UUID.randomUUID().toString());
        chunk.setMeta(JSON.parseObject(resource.getExtInfo()));
        chunk.setCreateTime(StringComUtils.convertStr(resource.getCreateTime()));
        chunk.setCreateId(resource.getCreatorId());
        chunk.setTenantId(resource.getTenantId());
        chunk.setDocId(resource.getDocId());
        chunk.setSegmentId(segment.getSegmentId());
//        chunk.setPath(resource.getGroupPath());
        chunk.setEnable(Boolean.TRUE.equals(resource.getEnable()) ? 1 : 0);
        chunk.setGroupId(resource.getGroupId());
        return chunk;
    }
}
