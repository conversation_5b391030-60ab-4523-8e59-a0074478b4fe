package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AuthLevelEntity;

import java.util.List;

/**
 * <p>
 * AUTHLevel表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
public interface IAuthLevelManager extends IService<AuthLevelEntity> {
    AuthLevelEntity get(String tenantId, String name, Integer level);

    List<AuthLevelEntity> list(String tenantId);
}
