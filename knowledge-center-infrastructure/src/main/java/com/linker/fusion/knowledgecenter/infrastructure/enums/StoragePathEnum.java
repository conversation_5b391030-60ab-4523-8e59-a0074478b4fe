package com.linker.fusion.knowledgecenter.infrastructure.enums;

import cn.hutool.core.io.file.FileNameUtil;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum StoragePathEnum {
    File("{tenant_id}/file/{file_id}/{filename}", "文件"),
    Attach("{tenant_id}/file/{file_id}/attach/{filename}", "附件"),
    Segment("{tenant_id}/file/{file_id}/segment/{filename}", "分段"),
    Chunk("{tenant_id}/file/{file_id}/chunk/{filename}", "分段"),
    Frame("{tenant_id}/file/{file_id}/frame/{filename}", "抽帧"),
    QA("{tenant_id}/qa/{file_id}/{filename}", "问答对"),
    QB("{tenant_id}/qb/{file_id}/{filename}", "题库"),
    Template("{tenant_id}/template/{file_id}/{filename}", "模板库"),
    ;
    private final String value;
    private final String desc;

    public String getValue(String tenantId, String id, String name) {
        return this.value.replace("{tenant_id}", tenantId)
                //.replace("{date}", DateUtil.format(LocalDateTime.now(), "yyyyMMdd"))
                .replace("{file_id}", id)
                .replace("{filename}", name);
    }
    public String getValueByUrl(String tenantId, String id, String url) {
        return getValue(tenantId,id, FileNameUtil.getName(url));
    }


    public String getDesc() {
        return this.getDesc();
    }
}
