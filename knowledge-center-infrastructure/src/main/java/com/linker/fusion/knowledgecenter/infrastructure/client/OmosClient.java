package com.linker.fusion.knowledgecenter.infrastructure.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.core.OmosAuthInterceptor;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.HannerTagReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.OmosCommonReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.TranscriptionsUrlReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.VideoSplitMinioReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.HannerTagResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.OmosApiListResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.TranscriptionsUrlResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.VideoSplitMinioResp;
import org.springframework.stereotype.Service;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;

@Service
@RetrofitClient(baseUrl = "${omos.base-url}", sourceOkHttpClient = "kernelOkHttpClient")
@Intercept(handler = OmosAuthInterceptor.class)
public interface OmosClient {
    /**
     * ASR  transcriptions_url
     *
     * @return
     */
    @POST("common")
    TranscriptionsUrlResp transcriptions_url(@Body OmosCommonReq<TranscriptionsUrlReq> req);

    /**
     * 镜头识别  video_split_minio
     *
     * @return
     */
    @POST("common")
    VideoSplitMinioResp video_split_minio(@Body OmosCommonReq<VideoSplitMinioReq> req);

    /**
     * 专名识别  hanner_tag
     *
     * @return
     */
    @POST("common")
    HannerTagResp hanner_tag(@Body OmosCommonReq<HannerTagReq> req);

    /**
     * OMOS 大语言模型列表  app_list_llm
     *
     * @return
     */
    @GET("/service/api/app/list?abilityType=llm&page=1&pageSize=100&appStatus=0")
    OmosApiListResp app_list_llm();

    /**
     * OMOS 多模态模型列表  app_list_mllm
     *
     * @return
     */
    @GET("/service/api/app/list?abilityType=chat&page=1&pageSize=100&appStatus=0")
    OmosApiListResp app_list_mllm();

    /**
     * OMOS 开放目标识别OVD  app_list_ovd
     *
     * @return
     */
    @GET("/service/api/app/list?abilityType=ovd&page=1&pageSize=100&appStatus=0")
    OmosApiListResp app_list_ovd();
}
