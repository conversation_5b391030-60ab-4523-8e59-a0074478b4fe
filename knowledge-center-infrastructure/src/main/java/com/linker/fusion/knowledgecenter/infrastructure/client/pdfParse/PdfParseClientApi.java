package com.linker.fusion.knowledgecenter.infrastructure.client.pdfParse;


import com.alibaba.fastjson2.JSONObject;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import org.springframework.stereotype.Component;
import retrofit2.http.Body;
import retrofit2.http.POST;

@RetrofitClient(baseUrl = "${pdf-parse.url}")
@Component
public interface PdfParseClientApi {

    @POST("vql/v1/parse/parse/pdf")
    JSONObject parse(@Body PdfParseReq req);

    @POST("vql/v1/parse/parse/title_split")
    JSONObject titleSplit(@Body PdfParseReq req);
}