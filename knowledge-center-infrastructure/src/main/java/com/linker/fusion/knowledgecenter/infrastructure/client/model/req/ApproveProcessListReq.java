package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ApproveProcessListReq {

    private Integer page = 1;

    private Integer pageSize = 100;
    /**
     * 审批流
     */
    private String processName;
    /**
     * 流程状态，0，不可用 1，可用 2，历史版本
     */
    private Integer processState;

    private String tenantId;

    /**
     * 结束时间
     */
    private String updateTimeEnd;

    /**
     * 开始时间
     */
    private String updateTimeStart;
}