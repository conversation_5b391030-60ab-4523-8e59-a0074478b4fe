package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.QuestionBankEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 题库管理接口
 */
public interface IQuestionBankManager extends IService<QuestionBankEntity> {


    List<QuestionBankEntity> randList(String tenantId, List<Long> questionBaseIds, List<String> questionTypes, Integer generateQuestionNumber);

    List<QuestionBankEntity> listByGroupIds(Collection<Long> groupIds);

    void deleteByGroupIds( List<Long> groupIds);

    /**
     * 根据分组ID列表和题目类型统计题目数量
     * @param groupIds 分组ID列表
     * @param questionType 题目类型（可选）
     * @return Map<分组ID, 题目数量>
     */
    Map<Long, Integer> countByGroups(List<Long> groupIds, String questionType);

    QuestionBankEntity getByUid(String tenantId, String uid);
    
    /**
     * 批量更新题目启用状态
     * @param ids 题目ID列表
     * @param enable 启用状态
     * @param updateId 更新人ID
     * @return 更新是否成功
     */
    boolean updateEnableByIds(List<Long> ids, Boolean enable, String updateId);
    
    /**
     * 批量更新题目分组ID
     * @param ids 题目ID列表
     * @param groupId 新的分组ID
     * @param updateId 更新人ID
     * @return 更新是否成功
     */
    boolean updateGroupByIds(List<Long> ids, Long groupId, String updateId);
}