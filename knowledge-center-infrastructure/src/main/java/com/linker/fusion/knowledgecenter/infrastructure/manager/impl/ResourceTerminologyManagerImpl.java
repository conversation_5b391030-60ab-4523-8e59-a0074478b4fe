
package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceTerminologyEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IResourceTerminologyManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.ResourceTerminologyMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ResourceTerminologyManagerImpl extends ServiceImpl<ResourceTerminologyMapper, ResourceTerminologyEntity> implements IResourceTerminologyManager {

    @Override
    public List<ResourceTerminologyEntity> list(String docId, String segmentId) {
        return list(
                new LambdaQueryWrapper<ResourceTerminologyEntity>()
                        .eq(StringUtils.isNotBlank(docId), ResourceTerminologyEntity::getDocId, docId)
                        .eq(StringUtils.isNotBlank(segmentId), ResourceTerminologyEntity::getSegmentId, segmentId)
        );
    }

    @Override
    public void clearByDocId(String docId) {
        QueryWrapper<ResourceTerminologyEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceTerminologyEntity::getDocId, docId);
        remove(wrapper);
    }

    @Override
    public void clearByFileIds(List<String> fileIds) {
        if (CollectionUtils.isEmpty(fileIds))
            return;
        QueryWrapper<ResourceTerminologyEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ResourceTerminologyEntity::getDocId, fileIds);
        remove(wrapper);
    }
}
