package com.linker.fusion.knowledgecenter.infrastructure.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 文件类型枚举
 */
@AllArgsConstructor
@Getter
public enum FileTypeEnum implements IntArrayValuable {

    DOCUMENT(1, "文档", "document"),

    IMAGE(2, "图片", "image"),

    VIDEO(3, "视频", "video"),

    AUDIO(4, "音频", "audio"),
    FAQ(5, "FAQ", "FAQ"),
    TABLE(6,"表格", "table"),

    Other(7, "其他", "other"),
    ;

    private final Integer type;

    private final String name;

    private final String value;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(FileTypeEnum::getType).toArray();

    public static FileTypeEnum valueOf(Integer value) {
        return ArrayUtil.firstMatch(s -> s.getType().equals(value), FileTypeEnum.values());
    }

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
