package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoCutFrameReq {

    private Data data;

    @lombok.Data
    public static class Data {
        /**
         * 原视频文件路径，视频转码、抽帧使用
         */
        @NotBlank(message = "原视频文件路径不能为空")
        private String sourceUrl;

        /**
         * 截帧时间，为int类型毫秒值
         */
        @NotNull(message = "截帧时间不能为空")
        private Long cutTime;

        /**
         * 任务来源
         */
        @NotBlank(message = "任务来源不能为空")
        private String clientId;

        /**
         * 视频编码
         */
        @NotBlank(message = "视频编码不能为空")
        private String fragmentCode;

        /**
         * 输出图片类型如 jpg、png，默认是jpg格式
         */
        private String imgFormat = "jpg";

        /**
         * 截帧图片宽度
         */
        private Integer height = 0;

        /**
         * 截帧图片长度
         */
        private Integer width = 0;
    }

}
