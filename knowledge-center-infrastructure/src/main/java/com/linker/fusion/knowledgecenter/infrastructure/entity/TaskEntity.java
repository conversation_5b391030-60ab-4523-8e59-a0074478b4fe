package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("t_task")
public class TaskEntity extends BaseEntity {
    @ApiModelProperty("任务类型")
    @TableField("`type`")
    private Integer type;
    @ApiModelProperty("任务状态")
    private Integer status;
    @ApiModelProperty("已读状态")
    @TableField("read_status")
    private Integer readStatus;
    @ApiModelProperty("目录Id")
    @TableField("group_id")
    private Long groupId;
    @ApiModelProperty("任务内容")
    private String content;
    @ApiModelProperty("任务结果")
    private String result;
    @ApiModelProperty("唯一值,用于查询")
    @TableField("`key`")
    private String key;

}
