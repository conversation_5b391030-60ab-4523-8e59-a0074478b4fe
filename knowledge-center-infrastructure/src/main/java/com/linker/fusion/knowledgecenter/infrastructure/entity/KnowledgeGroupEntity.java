package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 知识分组表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
@Data
@TableName("t_knowledge_group")
@NoArgsConstructor
public class KnowledgeGroupEntity extends BaseEntity {

    public static final long ROOT_ID = 0L;

    /**
     * 是否是知识库,true-知识库 false-目录
     */
    @TableField("is_library")
    private Boolean isLibrary;

    /**
     * 知识库描述
     */
    @TableField("description")
    private String description;

    /**
     * 知识库logo
     */
    @TableField("logo")
    private String logo;

    /**
     * 父级ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 知识类型 1-文件库 5-FAQ库 6-数据表库 8-敏感词库 9-专用词库
     */
    @TableField("type")
    private Integer type;

    /**
     * 分组名
     */
    @TableField("name")
    private String name;

    /**
     * 排序
     */
    @TableField("sort")
    private Double sort;

    /**
     * 唯一id
     */
    @TableField("guid")
    private String guid;
    /**
     * 源目录id
     */
    @TableField("parent_id_s")
    private String parentIdS;

    /**
     * true-是扫盘任务
     */
    @TableField("is_sync")
    private Boolean isSync;
    /**
     * 路径
     */
    private String path;
    /**
     * 重要等级
     */
    private Integer importantLevel;
    /**
     * 可见类型
     */
    private Integer visibleType;

    /**
     * 审批流程key
     */
    @TableField("approve_process_key")
    private String approveProcessKey;
    /**
     * 业务类型 1 会话目录
     */
    private Integer bizType;


    public KnowledgeGroupEntity(Long id, String creatorId, Long parentId) {
        this.setId(id);
        this.setCreatorId(creatorId);
        this.parentId = parentId;
    }

    /**
     * 获取根目录id
     *
     * @return
     */
    public Long getRootId() {
        if (parentId.equals(0L))
            return getId();
        if (StringUtils.isBlank(path))
            return null;
        return Long.parseLong(path.split("/")[1]);
    }
    public List<Long> getParentIdList(){
        if(StringUtils.isBlank(path)){
            return Collections.emptyList();
        }
        return Arrays.stream(path.split("/")).filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
    }
    /**
     * 获取搜索路径
     *
     * @return
     */
    public String getSearchPath() {
        String ret;
        if (StringUtils.isNotBlank(path))
            ret = path + getId();
        else
            ret = "/" + getId();
        ret = ret + "/";
        return ret;
    }
}
