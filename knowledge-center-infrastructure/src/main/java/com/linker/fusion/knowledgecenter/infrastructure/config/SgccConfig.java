package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "sgcc")
@Data
public class SgccConfig {
    private OperateLog operateLog = new OperateLog();

    @Data
    public static class OperateLog {
        private boolean enabled = false;
        private String baseUrl;
    }
}
