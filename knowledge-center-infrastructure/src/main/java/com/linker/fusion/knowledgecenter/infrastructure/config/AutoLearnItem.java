package com.linker.fusion.knowledgecenter.infrastructure.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AutoLearnItem {

    /**
     * 下发工作流ID
     */
    private String workflowId;

    /**
     * 是否启用
     */
    private Boolean enable = true;

    /**
     * 名称
     */
    private String name;

    /**
     * 跳过的msg信息
     */
    private List<String> skipMsgs = new ArrayList<>();

    /**
     * 单次下发数量
     */
    private Integer size = 5;

    /**
     * 处理资源类型
     */
    private Integer type = 3;


}
