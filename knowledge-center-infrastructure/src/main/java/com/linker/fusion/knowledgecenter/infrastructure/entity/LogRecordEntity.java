package com.linker.fusion.knowledgecenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.linker.logapi.beans.LogRecord;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 日志记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName("t_log_record")
public class LogRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * ip
     */
    @TableField("ip")
    private String ip;

    /**
     * 业务id
     */
    @TableField("biz_id")
    private String bizId;

    /**
     * 操作详情
     */
    @TableField("action")
    private String action;

    /**
     * 额外数据
     */
    @TableField("extra")
    private String extra;

    /**
     * 操作类型，
     */
    @TableField("type")
    private String type;

    /**
     * 操作事件等级
     */
    @TableField("level")
    private String level;

    /**
     * 操作子类型
     */
    @TableField("sub_type")
    private String subType;

    /**
     * 状态 1成功，0失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 创建人ID
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     * 账号
     */
    private String username;
    /**
     * 操作人
     */
    private String operator;
    public static LogRecordEntity build(LogRecord logRecord) {
        LogRecordEntity logRecordEntity = new LogRecordEntity();
        logRecordEntity.setTenantId(logRecord.getTenant());
        logRecordEntity.setBizId(logRecord.getBizId());
        logRecordEntity.setAction(logRecord.getAction());
        logRecordEntity.setExtra(logRecord.getExtra());
        logRecordEntity.setType(logRecord.getType());
        logRecordEntity.setLevel(logRecord.getLevel());
        logRecordEntity.setSubType(logRecord.getSubType());
        logRecordEntity.setStatus(logRecord.isFail() ? 0 : 1);
        logRecordEntity.setDeleted(false);
        logRecordEntity.setUpdateId(logRecord.getOperator());
        logRecordEntity.setUpdateTime(logRecord.getCreateTime());
        logRecordEntity.setCreatorId(logRecord.getOperator());
        logRecordEntity.setCreateTime(logRecord.getCreateTime());
        return logRecordEntity;
    }
}
