package com.linker.fusion.knowledgecenter.infrastructure.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linker.fusion.knowledgecenter.infrastructure.entity.NpcSearchLogEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 人大视频搜索记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface INpcSearchLogManager extends IService<NpcSearchLogEntity> {

    List<NpcSearchLogEntity> list(String tenantId, String userCode, Integer searchType, LocalDateTime startTime, Integer limit);

}
