package com.linker.fusion.knowledgecenter.infrastructure.client.aas.model;

import lombok.Getter;

/**
 * 工作流执行的状态
 * <p>
 * Workflow Status
 */
@Getter
public enum WorkflowStatus {

    /**
     * 完成
     */
    COMPLETED,

    /**
     * 失败
     */
    FAILED,

    /**
     * 暂停
     */
    PAUSED,

    /**
     * 运行中
     */
    RUNNING,

    /**
     * 终止
     */
    TERMINATED,

    /**
     * 超时
     */
    TIMED_OUT;
}