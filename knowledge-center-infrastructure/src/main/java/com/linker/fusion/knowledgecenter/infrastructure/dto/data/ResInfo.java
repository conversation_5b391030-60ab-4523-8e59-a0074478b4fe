package com.linker.fusion.knowledgecenter.infrastructure.dto.data;

import lombok.Data;

@Data
public class ResInfo {
    /**
     * 文件大小
     */
    private long size;
    /**
     * 文件id
     */
    private String docId;
    /**
     * 文件名
     */
    private String name;
    /**
     * 文件类型，标识上传文件的类别。
     * 可能的值：1（文档）、2（图片）、3（视频）、4（音频）、6（表格）、7（其他）。
     *
     * @see com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum
     */
    private int uploadType;
    /**
     * 子类型，文件后缀
     */
    private String subType;
    /**
     * 是否是最新上传
     */
    private boolean isNew;
    /**
     * 文件地址
     */
    private String url;
}