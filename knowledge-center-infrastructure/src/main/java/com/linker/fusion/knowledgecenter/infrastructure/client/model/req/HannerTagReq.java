package com.linker.fusion.knowledgecenter.infrastructure.client.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

//{
//        "taskId": "31d7151f-ba95-436f-b3f4-3502efb93ca9",
//        "src": [
//        {
//        "data": "新闻联播在晚上八点开始了，习近平会见了特朗普",
//        "srcType": "text",
//        "kwargs": {
//        "timestamp": 0.0
//        }
//        }
//        ]
//        }


@NoArgsConstructor
@Data
public class HannerTagReq {

    @JsonProperty("taskId")
    private String taskId;
    @JsonProperty("src")
    private List<SrcDTO> src;

    @NoArgsConstructor
    @Data
    public static class SrcDTO {
        @JsonProperty("data")
        private String data;
        @JsonProperty("srcType")
        private String srcType;
        @JsonProperty("kwargs")
        private KwargsDTO kwargs;

        @NoArgsConstructor
        @Data
        public static class KwargsDTO {
            @JsonProperty("timestamp")
            private Double timestamp;
        }
    }
}
