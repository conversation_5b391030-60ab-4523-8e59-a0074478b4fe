package com.linker.fusion.knowledgecenter.infrastructure.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class VideoExtract {
    @ApiModelProperty(value = "是否启用信息抽取,1 开启，0 关闭", example = "0")
    private Integer enableVideoExtract = 0;
    @ApiModelProperty("抽帧数量")
    private Integer frameNum = 1;
    @ApiModelProperty("识别提示词参数")
    private List<PromptInfoDTO> promptInfo;
}
