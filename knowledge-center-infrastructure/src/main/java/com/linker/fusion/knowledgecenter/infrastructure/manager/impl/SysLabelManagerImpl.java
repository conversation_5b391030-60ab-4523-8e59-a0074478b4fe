package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.SysLabelEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.ISysLabelManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.SysLabelMapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description
 **/
@Repository
public class SysLabelManagerImpl extends ServiceImpl<SysLabelMapper, SysLabelEntity> implements ISysLabelManager {
}
