package com.linker.fusion.knowledgecenter.infrastructure.client.usercenter;

import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.MenuItem;
import com.linker.user.api.dto.UserInfo;

import java.util.List;

public interface IUserCenterService {

    /**
     * 菜单列表
     *
     * @param token
     * @param tenantId
     * @param userCode
     * @return
     */
    List<MenuItem> menus(String token, String tenantId, String userCode);

    /**
     * 验证菜单权限
     *
     * @param token token
     * @param tenantId 租户
     * @param userCode 用户
     * @param menuKeys 菜单列表
     * @param throwError 是否抛出异常
     * @return true/false
     */
    boolean hasAuth(String token, String tenantId, String userCode, List<MenuKeyEnum> menuKeys, boolean throwError);

    /**
     * 验证菜单权限
     * @param token
     * @param tenantId
     * @param userCode
     * @param menuKey
     * @param throwError
     * @return
     */
    boolean hasAuth(String token, String tenantId, String userCode, MenuKeyEnum menuKey, boolean throwError);
    /**
     * 是否是管理员
     */
    boolean isManager(String token, String tenantId, String userCode);

    /**
     * 获取部门Code列表
     *
     * @param departments 当前部门列表
     * @param token       登录信息
     * @param tenantId    token
     * @return 当前部门和父级部门code列表
     */
    List<String> getDepartmentCodes(List<UserInfo.Department> departments, String token, String tenantId);
    /**
     * 获取iknow权限
     * @param tenantId 租户id
     */
    IKnowPowerResp getIknowPower(String tenantId);
}
