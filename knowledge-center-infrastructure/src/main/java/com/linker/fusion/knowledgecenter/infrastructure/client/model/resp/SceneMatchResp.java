package com.linker.fusion.knowledgecenter.infrastructure.client.model.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SceneMatchResp {

    @ApiModelProperty("输入的URL")
    private String url;

    @ApiModelProperty("命中的场景特征信息")
    private List<SceneInfo> sceneInfos;

    @Data
    public static class SceneInfo {

        @ApiModelProperty("id")
        private String id;

        @ApiModelProperty("名称")
        private String name;

        @ApiModelProperty("命中分数")
        private Double score;
    }

}
