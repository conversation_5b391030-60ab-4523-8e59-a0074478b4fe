package com.linker.fusion.knowledgecenter.infrastructure.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.linker.core.base.baseclass.BaseResp;
import org.springframework.stereotype.Service;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

@Service
@RetrofitClient(baseUrl = "${file-scan.endpoint:http://127.0.0.1:5001}")
public interface FileScanClient {

    /**
     * 移除任务
     *
     * @param collectionIds 样本集id
     */
    @POST("rpc/v1/task/collection")
    BaseResp<Void> removeTask(@Body List<Long> collectionIds);
}
