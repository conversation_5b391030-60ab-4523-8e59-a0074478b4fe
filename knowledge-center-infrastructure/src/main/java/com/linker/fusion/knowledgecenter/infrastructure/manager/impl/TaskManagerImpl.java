package com.linker.fusion.knowledgecenter.infrastructure.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.ITaskManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.ITaskMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 解析策略表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@Service
public class TaskManagerImpl extends ServiceImpl<ITaskMapper, TaskEntity> implements ITaskManager {

    @Override
    public void updateReadStatus(Long groupId, String tenantId, String creatorId) {
        UpdateWrapper<TaskEntity> wrapper = new UpdateWrapper<>();

        wrapper.lambda().eq(TaskEntity::getGroupId, groupId)
                .eq(TaskEntity::getTenantId, tenantId)
                .eq(TaskEntity::getCreatorId, creatorId)
                .eq(TaskEntity::getReadStatus, 0)
                .eq(TaskEntity::getDeleted, 0)
                .set(TaskEntity::getReadStatus, 1);

        update(wrapper);
    }

    @Override
    public void updateStatusToFail(String tenantId, String creatorId, String key) {
        UpdateWrapper<TaskEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(TaskEntity::getKey, key)
                .eq(TaskEntity::getTenantId, tenantId)
                .eq(TaskEntity::getCreatorId, creatorId)
                .eq(TaskEntity::getStatus, ProcessEnum.Executing.getValue())
                .eq(TaskEntity::getDeleted, 0)
                .set(TaskEntity::getStatus, ProcessEnum.Fail.getValue());
        update(wrapper);
    }

    @Override
    public List<TaskEntity> listUnread(String tenantId, String creatorId) {
        QueryWrapper<TaskEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(TaskEntity::getTenantId, tenantId)
                .eq(TaskEntity::getCreatorId, creatorId)
                .eq(TaskEntity::getReadStatus, 0)
                .eq(TaskEntity::getDeleted, 0);
        return list(wrapper);
    }

    @Override
    public TaskEntity getLastRun(String tenantId, String userCode, Long groupId) {
        List<TaskEntity> list = list(new LambdaQueryWrapper<TaskEntity>()
                .eq(TaskEntity::getGroupId, groupId)
                .eq(TaskEntity::getTenantId, tenantId)
                .eq(TaskEntity::getCreatorId, userCode)
                .eq(TaskEntity::getDeleted, false)
                .eq(TaskEntity::getStatus, ProcessEnum.Executing.getValue())
                .orderByDesc(TaskEntity::getId)
        );
        if (list.size() > 0)
            return list.get(0);
        return null;
    }

    @Override
    public TaskEntity getByKey(String key) {
        List<TaskEntity> list = list(new LambdaQueryWrapper<TaskEntity>()
                .eq(TaskEntity::getKey, key)
                .eq(TaskEntity::getDeleted, false)
                .eq(TaskEntity::getStatus, ProcessEnum.Executing.getValue())
                .orderByDesc(TaskEntity::getId)
        );
        if (list.size() > 0)
            return list.get(0);
        return null;
    }

    @Override
    public List<TaskEntity> listBykeys(List<String> keys) {
        List<TaskEntity> list = list(new LambdaQueryWrapper<TaskEntity>()
                .in(TaskEntity::getKey, keys)
                .eq(TaskEntity::getDeleted, false)
                .eq(TaskEntity::getStatus, ProcessEnum.Executing.getValue())
                .orderByDesc(TaskEntity::getId)
        );
        return list;
    }
}
