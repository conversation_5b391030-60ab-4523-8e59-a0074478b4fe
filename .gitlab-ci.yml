include:
  - remote: 'https://gitlab-ci.linker.cc/ymls/template_default-pipeline.yml' # 不用修改

variables:

  ## 作业控制
  RUN_PIPELINE_BUILD: "yes"     #是否运行java构建 yes/no,dev分支可以改成no
  RUN_PIPELINE_GO_BUILD: "no"      #是否运行go程序构建 yes/no
  RUN_PIPELINE_C_BUILD: "no"       #是否运行c++构建 yes/no
  RUN_CODE_ANALYSIS: "no"      #是否代码扫描（暂不支持） yes/no
  RUN_PIPELINE_TEST: "no"      #是否运行测试（暂不支持） yes/no
  RUN_BUILD_HBT_IMAGE: "yes"     #是否生成hbt镜像 yes/no，dev分支可以改成no
  RUN_BUILD_LINKER_IMAGE: "yes"      #是否生成linkercc镜像 yes/no
  RUN_ARM64_BUILD: "yes"
  ## 依赖容器镜像
  BUILD_IMAGE: "hbt.linker.cc/citools/maven:3.9.9-eclipse-temurin-8-self"
  ## 构建测试参数
  BUILD_SHELL: 'mvn clean install  -DskipTests  '   #构建命令
    #  ## 依赖容器镜像
    #  BUILD_IMAGE: "hbt.linker.cc/citools/mvnd:1.0.2-eclipse-temurin-8-self-3"
    #  ## 构建测试参数  mvnd
    #  BUILD_SHELL: 'mvnd clean install  -Dmaven.javadoc.skip -DskipTests -Dmaven.test.skip=true -Dquickly '   #构建命令

    ## 单元测试参数
  TEST_SHELL : 'mvn test  --settings=./settings.xml'       #测试命令
    #JUNIT_REPORT_PATH: 'target/surefire-reports/TEST-*.xml'   #单元测试报告

    ## 代码扫描
    ## SONAR_SOURCE_DIR : "ilink-manage-service/src"                                          #项目源码目录
    ## SONAR_SERVER_LOGIN: '${SONAR_TOKEN}'    #Sonar Token最好在项目中定义。
    ## SONAR_SCAN_ARGS: "-Dsonar.sources=${SONAR_SOURCE_DIR}
    ## -Dsonar.java.binaries=ilink-manage-service/target/classes
  ## -Dsonar.java.test.binaries=ilink-manage-service/target/test-classes
  ## -Dsonar.java.surefire.report=ilink-manage-service/target/surefire-reports "                                     #项目扫描参数