<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>knowledge-center</artifactId>
        <groupId>com.linker.fusion</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>knowledge-center-server</artifactId>

    <properties>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
            <version>4.6.1</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>bson</artifactId>
            <version>4.6.1</version>
        </dependency>
        <dependency>
            <groupId>com.linker.fusion</groupId>
            <artifactId>knowledge-center-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linker.fusion</groupId>
            <artifactId>knowledge-center-service</artifactId>
        </dependency>
        <!-- 认证接口依赖 -->
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>auth-starter</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.flywaydb</groupId>-->
<!--            <artifactId>flyway-core</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.github.lianjiatech</groupId>
            <artifactId>retrofit-spring-boot-starter</artifactId>
            <version>2.3.11</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-server</artifactId>
            <version>2.34</version>
        </dependency>
        <dependency>
            <groupId>com.supalle</groupId>
            <artifactId>auto-trim</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>log-metric-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linker.omos</groupId>
            <artifactId>omos-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.5.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.5.5.Final</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>0.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.gbase.jdbc.Driver</groupId>
            <artifactId>gbase</artifactId>
            <version>3.5.1</version>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

    <build>
        <defaultGoal>compile</defaultGoal>
        <finalName>knowledge-center</finalName>
        <plugins>
            <!-- 跳过deploy -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>

                        <!-- 将configuration移动到execution内部 -->
                        <configuration>
                            <!-- 添加classifier避免原始文件被覆盖 -->
                            <classifier/>
                            <!-- 使用绝对路径更可靠的写法 -->
                            <outputDirectory>../target</outputDirectory>
                            <!-- 确保finalName与主pom一致 -->
                            <finalName>${project.build.finalName}</finalName>
                            <!-- 指定改成启动类对应地址 -->
                            <mainClass>com.linker.fusion.knowledgecenter.server.Application</mainClass>

                        </configuration>

                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.5.5.Final</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
