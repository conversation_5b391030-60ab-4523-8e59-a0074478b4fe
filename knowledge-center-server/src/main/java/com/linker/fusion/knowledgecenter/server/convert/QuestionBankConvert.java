package com.linker.fusion.knowledgecenter.server.convert;

import com.alibaba.fastjson.JSON;
import com.linker.fusion.knowledgecenter.infrastructure.entity.QuestionBankEntity;
import com.linker.fusion.knowledgecenter.infrastructure.model.QuestionBankOptionModel;
import com.linker.fusion.knowledgecenter.server.dto.req.question.QuestionBankCreateReq;
import com.linker.fusion.knowledgecenter.server.dto.req.question.QuestionBankPageReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.QuestionBankResp;
import com.linker.fusion.knowledgecenter.service.domain.question.dto.QuestionBankPageDTO;
import com.linker.user.api.dto.UserInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface QuestionBankConvert {

    QuestionBankConvert INSTANCE = Mappers.getMapper(QuestionBankConvert.class);

    @Mapping(target = "options", source = "options", qualifiedByName = "optionsToJson")
    QuestionBankEntity toEntity(QuestionBankCreateReq req);

    @Mapping(target = "options", source = "options", qualifiedByName = "jsonToOptions")
    QuestionBankResp toResp(QuestionBankEntity entity);

    @Named("optionsToJson")
    default String optionsToJson(List<QuestionBankOptionModel> options) {
        return options != null ? JSON.toJSONString(options) : null;
    }

    @Named("jsonToOptions")
    default List<QuestionBankOptionModel> jsonToOptions(String options) {
        return options != null ? JSON.parseArray(options, QuestionBankOptionModel.class) : null;
    }

    @Mapping(target = "tenantId", source = "userInfo.tenantInfoDTO.tenantId")
    @Mapping(target = "page", source = "req.page")
    @Mapping(target = "pageSize", source = "req.pageSize")
    @Mapping(target = "keyword", source = "req.keyword")
    @Mapping(target = "enable", source = "req.enable")
    @Mapping(target = "questionType", source = "req.questionType")
    QuestionBankPageDTO toDto(QuestionBankPageReq req, UserInfo userInfo);
} 