package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description: 类说明
 * @Date: Created in 2025/4/2
 */
@Data
public class PaperSubmitResp {

    /**
     * 试卷状态, 0:回答中, 1:完成
     */
    private Integer paperStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime finishTime;
}
