package com.linker.fusion.knowledgecenter.server;

import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.linker.logapi.starter.annotation.EnableLogRecord;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @desc Boot应用的入口类
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.linker.fusion.knowledgecenter", "com.linker.core"})
@MapperScan("com.linker.fusion.knowledgecenter.infrastructure.mapper")
@EnableScheduling
@EnableAsync
@EnableMethodCache(basePackages = {"com.linker.fusion.knowledgecenter"})
@EnableLogRecord
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}