package com.linker.fusion.knowledgecenter.server.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
public class ExampleItem {

    @ApiModelProperty("名称")
    @NotBlank
    private String name;

    @ApiModelProperty("公文标题")
    @NotBlank
    @Length(max = 50, message = "公文标题最多50个字")
    private String title;

    @ApiModelProperty("主要内容")
    @NotBlank
    @Length(max = 500, message = "主要内容最多500个字")
    private String content;

    private String prompt;

}
