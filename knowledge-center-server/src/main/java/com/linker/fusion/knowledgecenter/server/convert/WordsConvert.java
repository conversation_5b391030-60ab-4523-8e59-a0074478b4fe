package com.linker.fusion.knowledgecenter.server.convert;

import com.linker.fusion.knowledgecenter.server.dto.req.word.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.WordsPageResp;
import com.linker.fusion.knowledgecenter.service.domain.word.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description 词条转换器
 **/
@Mapper
public interface WordsConvert {
    WordsConvert INSTANCE = Mappers.getMapper(WordsConvert.class);


    WordsDTO toDto(WordsCreateReq cmd);

    BatchWordsDeleteDTO toDto(WordsDeletedReq cmd);

    @Mappings({
            @Mapping(source = "cmd.wordsId", target = "id")
    })
    WordsDTO toDto(WordsModifyReq cmd);

    BatchWordsUpdateDTO toDto(WordsChangeStateReq cmd);

    WordsSearchDTO toDto(WordsSearchReq query);

    List<WordsPageResp> toResp(List<WordsListDTO> page);

    WordsSearchDTO toDto(WordsFindListReq wordsFindListReq);
}
