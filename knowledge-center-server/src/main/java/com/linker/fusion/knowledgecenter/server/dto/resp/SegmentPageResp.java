package com.linker.fusion.knowledgecenter.server.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件分段响应体
 */
@Data
public class SegmentPageResp {
    @ApiModelProperty("分段Id")
    private String segmentId;
    @ApiModelProperty("学习状态")
    private Integer status;
    @ApiModelProperty("分段标题")
    private String title;
    @ApiModelProperty("分段内容")
    private String content;
    @ApiModelProperty("排序")
    private Double sort;
    @ApiModelProperty("用户定义排序")
    private Integer number;
    @ApiModelProperty("位置信息")
    private String position;
    @ApiModelProperty("所在页面")
    private Integer page;
    @ApiModelProperty("开始时间")
    private Long startTimestamp;
    @ApiModelProperty("结束时间")
    private Long endTimestamp;
    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("时长 毫秒")
    private Long duration;

    @ApiModelProperty("封面图")
    private String thumbnail;

}
