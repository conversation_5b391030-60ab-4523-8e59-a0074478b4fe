package com.linker.fusion.knowledgecenter.server.biz;

import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceSegmentEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.TaskTypeEnum;
import com.linker.fusion.knowledgecenter.server.dto.req.word.ReadTaskReq;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.StudyStatusDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceSegmentService;
import com.linker.fusion.knowledgecenter.service.domain.task.ITaskService;
import com.linker.omagent.starter.manager.SseEmitterUtf8;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;


@Component
@Slf4j
public class TaskBiz {
    @Resource
    private ITaskService iTaskService;
    @Resource
    private IResourceSegmentService iResourceSegmentService;
    /**
     * 任务设为已读
     *
     * @param req
     */
    public void readTask(ReadTaskReq req) {
        TaskEntity task = iTaskService.getNotNull(req.getTaskId());
        if (task.getStatus().equals(ProcessEnum.Executing.getValue()))
            return;
        task.setReadStatus(1);
        iTaskService.update(task);
    }

    public SseEmitter statusListener(List<Long> ids) {
        SseEmitter emitter = new SseEmitterUtf8(3600000L);
        emitter.onTimeout(emitter::complete);
        emitter.onCompletion(emitter::complete);
        emitter.onError(throwable -> {
            emitter.completeWithError(throwable);
            log.warn("SseEmitter 断开");
        });
        CompletableFuture.runAsync(() -> {
            try {
                List<TaskEntity> taskEntityList = iTaskService.ListByIds(ids);
                while (true) {
                    for (TaskEntity task : taskEntityList) {
                        if (!task.getStatus().equals(ProcessEnum.Executing.getValue())) {
                            StudyStatusDTO studyStatusDTO=   new StudyStatusDTO(task.getId().toString(), task.getStatus(), 0, "keep-alive", 0L,"");
                            if(TaskTypeEnum.SegRetry.getValue().equals(task.getType())){
                                ResourceSegmentEntity resourceSegment=iResourceSegmentService.getNotNull(task.getKey());
                                if(Objects.nonNull(resourceSegment)){
                                    studyStatusDTO.setMessage(resourceSegment.getContent());
                                    studyStatusDTO.setHandleStatus(resourceSegment.getStatus());
                                }else{
                                    studyStatusDTO.setMessage("");
                                }
                            }
                            emitter.send(studyStatusDTO);
                            ids.remove(task.getId());
                        } else {
                            //60分超时
                            if (task.getCreateTime().plusHours(1).isBefore(LocalDateTime.now())) {
                                task.setStatus(ProcessEnum.Fail.getValue());
                                task.setUpdateTime(LocalDateTime.now());
                                iTaskService.update(task);
                            }
                        }
                    }
                    if (ids.isEmpty()) {
                        emitter.complete();
                        break;
                    }
                    taskEntityList = iTaskService.ListByIds(ids);
                    Thread.sleep(1000);
                    emitter.send(new StudyStatusDTO(null, null, 0, "keep-alive", 0L,""));
                }
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });
        return emitter;
    }
}
