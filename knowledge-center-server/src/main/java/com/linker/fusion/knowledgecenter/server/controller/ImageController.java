package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthNodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OperatorTypeEnum;
import com.linker.fusion.knowledgecenter.server.biz.ImageBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.file.image.ImageChunkAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.image.ImageChunkDeleteReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.image.ImageChunkListReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.image.ImageChunkUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.ImageChunkResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.MenuAndResNodeAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.linker.core.auth.enums.InterfaceType.TOC;

@Api(tags = "图片管理")
@RestController
@RequestMapping("file/image")
public class ImageController {

    @Resource
    private ImageBiz imageBiz;

    @PostMapping("chunk/list")
    @ApiOperation(value = "分块列表", tags = "v1.5.0")
    @FunctionPoint(interfaceType = TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<List<ImageChunkResp>> chunkList(@RequestBody @Valid ImageChunkListReq req) {
        return new BaseResp<>(imageBiz.chunkList(req));
    }

    @PostMapping("chunk/update")
    @ApiOperation(value = "分块更新", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<Void> updateChunk(@RequestBody @Valid ImageChunkUpdateReq req) {
        imageBiz.updateChunk(req);
        return new BaseResp<>();
    }

    @PostMapping("chunk/add")
    @ApiOperation(value = "分块添加", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<Void> addChunk(@RequestBody @Valid ImageChunkAddReq req) {
        imageBiz.addChunk(req);
        return new BaseResp<>();
    }

    @PostMapping("chunk/delete")
    @ApiOperation(value = "分块删除", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, chunkIds = "{#req.chunkIds}")
    public BaseResp<Void> deleteChunk(@RequestBody ImageChunkDeleteReq req) {
        imageBiz.deleteChunk(req);
        return new BaseResp<>();
    }

}
