package com.linker.fusion.knowledgecenter.server.biz;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.HttpPersonClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.HttpPersonInfoReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.PersonInfoDTO;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.NpcSearchLogEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceASREntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceFaceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.RenDaSearchTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.ChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IKnowledgeResourceManager;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IResourceFaceManager;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.server.convert.DocConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.npc.RenDaSearchReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.ChunkItemResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.DocInfoEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.npc.NpcFileInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.npc.RenDaPersonStatsResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.npc.RenDaSearchSuggestionResp;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import com.linker.fusion.knowledgecenter.service.domain.group.model.SearchPreChekDto;
import com.linker.fusion.knowledgecenter.service.domain.npc.INpcSearchLogService;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IDocChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceASRService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceFaceService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.common.Constants.RESOURCE_EXT_PREFIX;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RenDaSearchGBaseBiz extends BaseBiz {

    private final ExecutorService fixedThreadPool = new ThreadPoolExecutor(30, Integer.MAX_VALUE, 300L, TimeUnit.SECONDS, new SynchronousQueue<Runnable>());

    @Resource
    private IKnowledgeResourceManager iKnowledgeResourceManager;

    @Resource
    private IResourceFaceService iResourceFaceService;

    @Resource
    private IResourceFaceManager faceManager;

    @Resource
    private IResourceASRService asrService;

    @Resource
    private KnowledgeGroupService knowledgeGroupService;

    @Resource
    private KnowledgeGroupFactory knowledgeGroupFactory;

    @Resource
    private INpcSearchLogService iNpcSearchLogService;

    @Resource
    private HttpPersonClient personClient;

    @Resource
    private IDocChunkIBaseServiceResource iDocChunkIBaseServiceResource;

    @Resource
    private CacheManager cacheManager;

    private static final QuickConfig quickConfig = QuickConfig.newBuilder("npc_search")
            .expire(Duration.ofMinutes(5))
            .cacheType(CacheType.LOCAL)
            .localLimit(100)
            .build();

    public BasePaginResp<NpcFileInfoResp> searchVideo(RenDaSearchReq req) {
        RenDaSearchTypeEnum renDaSearchTypeEnum = RenDaSearchTypeEnum.valueOf(req.getSearchType());
        // 查询出所有符合条件的数据
        MatchDTO matchDTO = getMatchDTO(req);
        List<KnowledgeResourceEntity> allMatches = matchDTO.getAllData();
        if (CollectionUtils.isEmpty(allMatches)) {
            return new BasePaginResp<>(0L, Collections.emptyList());
        }
        // 内存分页
        PageUtil.setFirstPageNo(1);
        List<KnowledgeResourceEntity> currentPageList = ListUtil.page((int) req.getPage(), (int) req.getPageSize(), allMatches);
        List<DocInfoEntityResp> respList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(currentPageList)) {
            List<Long> currentPageResourceIds = currentPageList.stream().map(KnowledgeResourceEntity::getId).collect(Collectors.toList());
            List<KnowledgeResourceEntity> currentPageResources = iKnowledgeResourceManager.listByIds(currentPageResourceIds);
            Map<Long, KnowledgeResourceEntity> currentPageResourceMap = currentPageResources.stream().collect(Collectors.toMap(KnowledgeResourceEntity::getId, Function.identity()));
            for (Long id : currentPageResourceIds) {
                respList.add(DocConvert.INSTANCE.toResp(currentPageResourceMap.get(id)));
            }
        }
        List<NpcFileInfoResp> npcFileInfoRespList = null;
        if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.TITLE)) {
            npcFileInfoRespList = respList.stream().map(NpcFileInfoResp::init).collect(Collectors.toList());
        } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.PERSON)) {
            List<ResourceFaceEntity> faceEntityList = iResourceFaceService.listByDocIdAndFaceId(respList.stream().map(DocInfoEntityResp::getDocId).collect(Collectors.toList()), req.getSearchContent());
            Map<String, List<ResourceFaceEntity>> faceMap = faceEntityList.stream().collect(Collectors.groupingBy(ResourceFaceEntity::getDocId));
            npcFileInfoRespList = respList.stream().map(s -> {
                NpcFileInfoResp npcFileInfoResp = NpcFileInfoResp.init(s);
                List<NpcFileInfoResp.SceneItem> personScenes = faceMap.get(s.getDocId()).stream().map(resourceFaceEntity -> new NpcFileInfoResp.SceneItem(resourceFaceEntity.getTimePoint(), resourceFaceEntity.getTimePoint())).collect(Collectors.toList());
                npcFileInfoResp.setPersonScenes(personScenes);
                npcFileInfoResp.setMatchDurationMs(personScenes.size() * 1000L);
                return npcFileInfoResp;
            }).collect(Collectors.toList());
        } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.CONTENT)) {
            List<ResourceASREntity> asrList = asrService.listByDocIdsAndContent(respList.stream().map(DocInfoEntityResp::getDocId).collect(Collectors.toList()), req.getSearchContent());
            Map<String, List<ResourceASREntity>> asrMap = asrList.stream().collect(Collectors.groupingBy(ResourceASREntity::getDocId));
            Map<String, ChunkEntity> finalDocIdContentMap = matchDTO.getDocIdContentMap();
            npcFileInfoRespList = respList.stream().map(s -> {
                NpcFileInfoResp npcFileInfoResp = NpcFileInfoResp.init(s);
                // 视频类型
                if (asrMap.containsKey(s.getDocId())) {
                    List<NpcFileInfoResp.SceneItem> contentScenes = asrMap.get(s.getDocId()).stream().map(resourceFaceEntity -> new NpcFileInfoResp.SceneItem(resourceFaceEntity.getStartTimestamp(), resourceFaceEntity.getEndTimestamp())).collect(Collectors.toList());
                    npcFileInfoResp.setContentScenes(contentScenes);
                    long matchDurationMs = contentScenes.stream().map(sceneItem -> sceneItem.getEndTimestamp() - sceneItem.getStartTimestamp()).mapToLong(Long::longValue).sum();
                    npcFileInfoResp.setMatchDurationMs(matchDurationMs);
                }
                // 文档类型
                if (finalDocIdContentMap.containsKey(s.getDocId())) {
                    ChunkItemResp documentChunk = new ChunkItemResp(finalDocIdContentMap.get(s.getDocId()));
                    documentChunk.setContent(HtmlUtil.cleanHtmlTag(documentChunk.getContent()));
                    npcFileInfoResp.setDocumentChunk(documentChunk);
                }
                return npcFileInfoResp;
            }).collect(Collectors.toList());
        }
//        List<SubVideoEntity> subVideoEntityList = subVideoService.listByDocIds(videoInfoList.stream().map(VideoInfoResp::getDocId).collect(Collectors.toList()));
//        for (VideoInfoResp videoInfoResp : videoInfoList) {
//            Optional<SubVideoEntity> optional = subVideoEntityList.stream().filter(subVideoEntity -> subVideoEntity.getDocId().equals(videoInfoResp.getDocId())).findFirst();
//            optional.ifPresent(subVideoEntity -> videoInfoResp.setThumbnail(subVideoEntity.getThumbnail()));
//            if (StringUtils.isBlank(videoInfoResp.getThumbnail())) {
//                VideoFrameEntity firstFrame = iVideoFrameIBaseService.getFirst(getTenantId(), videoInfoResp.getDocId());
//                if (firstFrame != null) {
//                    videoInfoResp.setThumbnail(firstFrame.getUrl());
//                }
//            }
//        }
        return new BasePaginResp<>((long) allMatches.size(), npcFileInfoRespList);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MatchDTO implements Serializable {
        List<KnowledgeResourceEntity> allData = new ArrayList<>();
        Map<String, ChunkEntity> docIdContentMap = new HashMap<>();
    }

    private MatchDTO getMatchDTO(RenDaSearchReq req) {
        String cacheKey = req.generateCacheKey();
        cacheKey += "_" + getTenantId() + "_" + getUserCode();
        Cache<String, MatchDTO> cache = cacheManager.getOrCreateCache(quickConfig);
        return cache.computeIfAbsent(cacheKey, k -> {
            MatchDTO matchDTO = new MatchDTO();
            String reqId = UUID.randomUUID().toString();
            RenDaSearchTypeEnum renDaSearchTypeEnum = RenDaSearchTypeEnum.valueOf(req.getSearchType());
            if (renDaSearchTypeEnum == null) {
                return matchDTO;
            }
            long t2 = System.currentTimeMillis();
            // 预筛选
            Set<String> docIdsByFaceIds = null;
            if (CollectionUtils.isNotEmpty(req.getPersonIds())) {
                docIdsByFaceIds = iResourceFaceService.getDocIdsByFaceIds(req.getPersonIds());
                // 通过AI人员搜索无结果
                if (CollectionUtils.isEmpty(docIdsByFaceIds)) {
                    return matchDTO;
                }
            }
            long t3 = System.currentTimeMillis();
            log.info("{}获取人脸ID耗时:{}ms", reqId, t3 - t2);
            SearchPreChekDto preChekDto = knowledgeGroupFactory.searchPreCheckAllPublic(getTenantId(), getUserCode(), getDepartmentCodes(), KnowledgeTypeEnum.FILE);
            if (preChekDto == null) {
                return matchDTO;
            }
            Set<Long> authedGroupIds = new HashSet<>(preChekDto.getAuthedGroupIds());
            Set<Long> notIds = new HashSet<>(preChekDto.getNotIds());
            long t4 = System.currentTimeMillis();
            log.info("{}分组鉴权耗时:{}ms", reqId, t4 - t3);
            Set<String> docIdsByContent = null;
            if (StringUtils.isNotBlank(req.getAsrContent())) {
                if (FileTypeEnum.VIDEO.getType().equals(req.getFileType())) {
                    docIdsByContent = asrService.getDocIdsByContent(getTenantId(), req.getAsrContent());
                    // 通过ASR内容搜索无结果
                    if (CollectionUtils.isEmpty(docIdsByContent)) {
                        return matchDTO;
                    }
                } else if (FileTypeEnum.DOCUMENT.getType().equals(req.getSearchType())) {
                    docIdsByContent = iDocChunkIBaseServiceResource.queryByContentGroupByDocId(getTenantId(), req.getAsrContent(), authedGroupIds).keySet();
                    if (CollectionUtils.isEmpty(docIdsByContent)) {
                        return matchDTO;
                    }
                }
            }
            long t5 = System.currentTimeMillis();
            log.info("{}获取ASR耗时:{}ms", reqId, t5 - t4);
            LambdaQueryWrapper<KnowledgeResourceEntity> queryWrapper = initQuery(req, docIdsByFaceIds, docIdsByContent);

            Map<String, ChunkEntity> docIdContentMap = new HashMap<>();
            if (CollectionUtils.isEmpty(authedGroupIds)) {
                return matchDTO;
            }
            queryWrapper.notIn(CollectionUtils.isNotEmpty(notIds), KnowledgeResourceEntity::getId, notIds);
            if (authedGroupIds.size() <= 200) {
                queryWrapper.in(KnowledgeResourceEntity::getGroupId, authedGroupIds);
            }

            if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.TITLE)) {
                queryWrapper.like(KnowledgeResourceEntity::getTitle, StringComUtils.replaceSqlEsc(req.getSearchContent()));
            } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.PERSON)) {
                Set<String> docIds = iResourceFaceService.getDocIdsByFaceIds(Collections.singletonList(req.getSearchContent()));
                if (CollectionUtils.isEmpty(docIds)) {
                    return matchDTO;
                }
                queryWrapper.in(KnowledgeResourceEntity::getDocId, docIds);
            } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.CONTENT) && FileTypeEnum.VIDEO.getType().equals(req.getFileType())) {
                Set<String> docIds = asrService.getDocIdsByContent(getTenantId(), req.getSearchContent());
                if (CollectionUtils.isEmpty(docIds)) {
                    return matchDTO;
                }
                queryWrapper.in(KnowledgeResourceEntity::getDocId, docIds);
            } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.CONTENT) && FileTypeEnum.DOCUMENT.getType().equals(req.getFileType())) {
                docIdContentMap = iDocChunkIBaseServiceResource.queryByContentGroupByDocId(getTenantId(), req.getSearchContent(), authedGroupIds);
                if (org.springframework.util.CollectionUtils.isEmpty(docIdContentMap)) {
                    return matchDTO;
                }
                matchDTO.setDocIdContentMap(docIdContentMap);
                queryWrapper.in(KnowledgeResourceEntity::getDocId, docIdContentMap.keySet());
            }
            long t6 = System.currentTimeMillis();
            log.info("{}主查询条件过滤耗时:{}ms", reqId, t6 - t5);
            // 第一次仅查询id和扩展字段
            queryWrapper.select(KnowledgeResourceEntity::getId, KnowledgeResourceEntity::getGroupId, KnowledgeResourceEntity::getExtInfo);
            List<KnowledgeResourceEntity> allMatches = iKnowledgeResourceManager.list(queryWrapper);
            long t7 = System.currentTimeMillis();
            log.info("{}查询文件库耗时:{}ms", reqId, t7 - t6);
            // 二次过滤，根据扩展字段
            allMatches = filter(allMatches, req, authedGroupIds.size() <= 200 ? null : authedGroupIds);
            log.info("{}扩展字段二次过滤并排序耗时:{}ms", reqId, System.currentTimeMillis() - t7);
            matchDTO.setAllData(allMatches);
            return matchDTO;
        });
    }

    private LambdaQueryWrapper<KnowledgeResourceEntity> initQuery(RenDaSearchReq req, Collection<String> docIdsByFaceIds, Collection<String> docIdsByAsrContent) {
        return new LambdaQueryWrapper<KnowledgeResourceEntity>()
                .eq(KnowledgeResourceEntity::getDeleted, false)
                .eq(KnowledgeResourceEntity::getTenantId, getTenantId())
                .eq(KnowledgeResourceEntity::getType, req.getFileType())
                .eq(KnowledgeResourceEntity::getEnable, true)
                .in(CollectionUtils.isNotEmpty(req.getHandleStatus()), KnowledgeResourceEntity::getHandleStatus, req.getHandleStatus())
                .like(StringUtils.isNotBlank(req.getMeetingTitle()), KnowledgeResourceEntity::getTitle, StringComUtils.replaceSqlEsc(req.getMeetingTitle()))
                .in(CollectionUtils.isNotEmpty(docIdsByFaceIds), KnowledgeResourceEntity::getDocId, docIdsByFaceIds)
                .in(CollectionUtils.isNotEmpty(docIdsByAsrContent), KnowledgeResourceEntity::getDocId, docIdsByAsrContent);
    }

    /**
     * 根据扩展字段二次过滤并排序
     *
     * @param list     从数据库查出来的列表
     * @param req      扩展字段筛选条件
     * @param groupIds 分组id列表，如果不为空，过滤分组id不匹配的结果
     */
    private List<KnowledgeResourceEntity> filter(List<KnowledgeResourceEntity> list, RenDaSearchReq req, Set<Long> groupIds) {
        return list.stream().filter(entity -> {
                    if (groupIds != null && !groupIds.contains(entity.getGroupId())) {
                        return false;
                    }
                    JSONObject jsonObject = JSON.parseObject(entity.getExtInfo());
                    if (StringUtils.isNotBlank(req.getMeetingLocation())
                            && !StringUtils.containsIgnoreCase(jsonObject.getString("customer_meeting_location"), req.getMeetingLocation())) {
                        return false;
                    }
                    Long meetingDateGt = StringUtils.isNotBlank(req.getMeetingDateGt()) ? StringComUtils.convertToTimestamp(req.getMeetingDateGt()) : null;
                    if (meetingDateGt != null) {
                        long meetingDate = StringComUtils.convertToTimestamp(jsonObject.getString(RESOURCE_EXT_PREFIX + "meeting_date"));
                        if (meetingDate < meetingDateGt) {
                            return false;
                        }
                    }
                    Long meetingDateLt = StringUtils.isNotBlank(req.getMeetingDateLt()) ? StringComUtils.convertToTimestamp(req.getMeetingDateLt()) : null;
                    if (meetingDateLt != null) {
                        long meetingDate = StringComUtils.convertToTimestamp(jsonObject.getString(RESOURCE_EXT_PREFIX + "meeting_date"));
                        if (meetingDate > meetingDateLt) {
                            return false;
                        }
                    }
                    if (StringUtils.isNotBlank(req.getMeetingModerator())
                            && !StringUtils.containsIgnoreCase(jsonObject.getString("customer_meeting_moderator"), req.getMeetingModerator())) {
                        return false;
                    }
                    if (StringUtils.isNotBlank(req.getMeetingParticipant())
                            && !StringUtils.containsIgnoreCase(jsonObject.getString("customer_meeting_participant"), req.getMeetingParticipant())) {
                        return false;
                    }
                    if (StringUtils.isNotBlank(req.getMeetingPhotographer())
                            && !StringUtils.containsIgnoreCase(jsonObject.getString("customer_meeting_photographer"), req.getMeetingPhotographer())) {
                        return false;
                    }
                    if (StringUtils.isNotBlank(req.getMeetingHost())
                            && !StringUtils.containsIgnoreCase(jsonObject.getString("customer_meeting_host"), req.getMeetingHost())) {
                        return false;
                    }

                    return true;
                })
                .sorted((e1, e2) -> {
                    long d1 = StringComUtils.convertToTimestamp(e1.getJsonExtInfo().getString(RESOURCE_EXT_PREFIX + "meeting_date"));
                    long d2 = StringComUtils.convertToTimestamp(e2.getJsonExtInfo().getString(RESOURCE_EXT_PREFIX + "meeting_date"));
                    return Long.compare(d2, d1);
                })
                .collect(Collectors.toList());
    }

    public List<PersonInfoDTO> personRelated(RenDaSearchReq req) {
        try {
            // 给文档资料搜索时，保存搜索记录用 finally执行
            if (FileTypeEnum.DOCUMENT.getType().equals(req.getFileType())) {
                return Collections.emptyList();
            }
            RenDaSearchTypeEnum renDaSearchTypeEnum = RenDaSearchTypeEnum.valueOf(req.getSearchType());
            if (renDaSearchTypeEnum == null) {
                return Collections.emptyList();
            }
            // 预筛选
            Set<String> docIdsByFaceIds = null;
            if (CollectionUtils.isNotEmpty(req.getPersonIds())) {
                docIdsByFaceIds = iResourceFaceService.getDocIdsByFaceIds(req.getPersonIds());
                // 通过AI人员搜索无结果
                if (CollectionUtils.isEmpty(docIdsByFaceIds)) {
                    return Collections.emptyList();
                }
            }
            Set<String> docIdsByAsrContent = null;
            if (StringUtils.isNotBlank(req.getAsrContent())) {
                docIdsByAsrContent = asrService.getDocIdsByContent(getTenantId(), req.getAsrContent());
                // 通过ASR内容搜索无结果
                if (CollectionUtils.isEmpty(docIdsByAsrContent)) {
                    return Collections.emptyList();
                }
            }
            LambdaQueryWrapper<KnowledgeResourceEntity> queryWrapper = initQuery(req, docIdsByFaceIds, docIdsByAsrContent);
            SearchPreChekDto preChekDto = knowledgeGroupFactory.searchPreCheckAllPublic(getTenantId(), getUserCode(), getDepartmentCodes(), KnowledgeTypeEnum.FILE);
            if (preChekDto == null) {
                return Collections.emptyList();
            }
            Set<Long> authedGroupIds = new HashSet<>(preChekDto.getAuthedGroupIds());
            Set<Long> notIds = new HashSet<>(preChekDto.getNotIds());
            if (CollectionUtils.isEmpty(authedGroupIds)) {
                return Collections.emptyList();
            }
            queryWrapper.notIn(CollectionUtils.isNotEmpty(notIds), KnowledgeResourceEntity::getId, notIds);
            if (authedGroupIds.size() <= 200) {
                queryWrapper.in(KnowledgeResourceEntity::getGroupId, authedGroupIds);
            }
            if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.TITLE)) {
                queryWrapper.like(KnowledgeResourceEntity::getTitle, StringComUtils.replaceSqlEsc(req.getSearchContent()));
            } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.CONTENT)) {
                Set<String> docIds = asrService.getDocIdsByContent(getTenantId(), req.getSearchContent());
                if (CollectionUtils.isEmpty(docIds)) {
                    return Collections.emptyList();
                }
                queryWrapper.in(KnowledgeResourceEntity::getDocId, docIds);
            }
            queryWrapper.select(KnowledgeResourceEntity::getDocId, KnowledgeResourceEntity::getExtInfo);
            List<KnowledgeResourceEntity> list = iKnowledgeResourceManager.list(queryWrapper);
            list = filter(list, req, authedGroupIds.size() <= 200 ? null : authedGroupIds);
            List<String> docIds = list.stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(docIds)) {
                return Collections.emptyList();
            }

            List<Map<String, Object>> maps = faceManager.listGroupBySourceIdOrderByCount(docIds);
            List<PersonInfoDTO> personInfoDTOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(maps)) {
                for (Map<String, Object> map : maps) {
                    String sourceId = map.get("source_id").toString();
//                    long count = (long) map.get("count");
                    List<PersonInfoDTO> data = personClient.personInfo(new HttpPersonInfoReq().setTenantId(getTenantId()).setIds(Collections.singletonList(sourceId))).getData();
                    if (CollectionUtils.isNotEmpty(data)) {
                        personInfoDTOList.add(data.get(0));
                    }
                    if (personInfoDTOList.size() == 12) {
                        break;
                    }
                }
            }
            return personInfoDTOList;
        } finally {
            fixedThreadPool.submit(() -> {
                try {
                    NpcSearchLogEntity npcSearchLogEntity = new NpcSearchLogEntity();
                    npcSearchLogEntity.init(getTenantId(), getUserCode());
                    npcSearchLogEntity.setFileType(req.getFileType());
                    npcSearchLogEntity.setSearchType(req.getSearchType());
                    npcSearchLogEntity.setSearchContent(req.getSearchContent());
                    npcSearchLogEntity.setInput(JSON.toJSONString(req));
                    iNpcSearchLogService.save(npcSearchLogEntity);
                } catch (Exception e) {
                    log.error("异步保存日志失败", e);
                }
            });
        }
    }

    public RenDaPersonStatsResp personStats(RenDaSearchReq req) {
        RenDaPersonStatsResp renDaPersonStatsResp = new RenDaPersonStatsResp();
        RenDaSearchTypeEnum renDaSearchTypeEnum = RenDaSearchTypeEnum.valueOf(req.getSearchType());
        if (!RenDaSearchTypeEnum.PERSON.equals(renDaSearchTypeEnum)) {
            return renDaPersonStatsResp;
        }
        try {
            List<PersonInfoDTO> data = personClient.personInfo(new HttpPersonInfoReq().setTenantId(getTenantId()).setIds(Collections.singletonList(req.getSearchContent()))).getData();
            if (CollectionUtils.isNotEmpty(data)) {
                renDaPersonStatsResp.setPersonInfo(data.get(0));
            }
            Set<String> coreDocIds = iResourceFaceService.getDocIdsByFaceIds(Collections.singletonList(req.getSearchContent()));
            log.info("coreDocIds count:{}", coreDocIds.size());
            if (CollectionUtils.isEmpty(coreDocIds)) {
                return renDaPersonStatsResp;
            }
            // 预筛选
            Set<String> docIdsByFaceIds = null;
            if (CollectionUtils.isNotEmpty(req.getPersonIds())) {
                docIdsByFaceIds = iResourceFaceService.getDocIdsByFaceIds(req.getPersonIds());
                log.info("docIdsByFaceIds count:{}", docIdsByFaceIds.size());
                // 通过AI人员搜索无结果
                if (CollectionUtils.isEmpty(docIdsByFaceIds)) {
                    return renDaPersonStatsResp;
                }
            }
            Set<String> docIdsByAsrContent = null;
            if (StringUtils.isNotBlank(req.getAsrContent())) {
                docIdsByAsrContent = asrService.getDocIdsByContent(getTenantId(), req.getAsrContent());
                log.info("docIdsByAsrContent count:{}", docIdsByAsrContent.size());
                // 通过ASR内容搜索无结果
                if (CollectionUtils.isEmpty(docIdsByAsrContent)) {
                    return renDaPersonStatsResp;
                }
            }
            LambdaQueryWrapper<KnowledgeResourceEntity> queryWrapper = initQuery(req, docIdsByFaceIds, docIdsByAsrContent);
            SearchPreChekDto preChekDto = knowledgeGroupFactory.searchPreCheckAllPublic(getTenantId(), getUserCode(), getDepartmentCodes(), KnowledgeTypeEnum.FILE);
            if (preChekDto == null) {
                log.warn("preChekDto is null");
                return renDaPersonStatsResp;
            }
            Set<Long> authedGroupIds = new HashSet<>(preChekDto.getAuthedGroupIds());
            log.info("authedGroupIds: {}", authedGroupIds);
            Set<Long> notIds = new HashSet<>(preChekDto.getNotIds());
            log.info("notIds: {}", notIds);
            if (CollectionUtils.isEmpty(authedGroupIds)) {
                log.warn("authedGroupIds is empty");
                return renDaPersonStatsResp;
            }
            queryWrapper.notIn(CollectionUtils.isNotEmpty(notIds), KnowledgeResourceEntity::getId, notIds);
            if (authedGroupIds.size() <= 200) {
                queryWrapper.in(KnowledgeResourceEntity::getGroupId, authedGroupIds);
            }
            queryWrapper.in(KnowledgeResourceEntity::getDocId, coreDocIds);
            queryWrapper.select(KnowledgeResourceEntity::getDocId, KnowledgeResourceEntity::getGroupId, KnowledgeResourceEntity::getExtInfo);

            List<KnowledgeResourceEntity> list = iKnowledgeResourceManager.list(queryWrapper);
            log.info("list count:{}", list.size());
            list = filter(list, req, authedGroupIds.size() <= 200 ? null : authedGroupIds);
            log.info("list count:{}", list.size());
            List<String> docIds = list.stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList());
            log.info("docIds count:{}", docIds.size());
            if (CollectionUtils.isEmpty(docIds)) {
                log.warn("docIds is empty");
                return renDaPersonStatsResp;
            }

            List<ResourceFaceEntity> faceEntityList = faceManager.list(
                    new LambdaQueryWrapper<ResourceFaceEntity>()
                            .eq(ResourceFaceEntity::getDeleted, false)
                            .eq(ResourceFaceEntity::getTenantId, getTenantId())
                            .eq(ResourceFaceEntity::getSourceId, req.getSearchContent())
                            .in(ResourceFaceEntity::getDocId, docIds)
                            .select(ResourceFaceEntity::getDocId)
            );
            log.info("faceEntityList count:{}", faceEntityList.size());
            renDaPersonStatsResp.setVideoCount(faceEntityList.stream().map(ResourceFaceEntity::getDocId).distinct().count());
            renDaPersonStatsResp.setDurationSecond(faceEntityList.size());
            return renDaPersonStatsResp;
        } finally {
            NpcSearchLogEntity npcSearchLogEntity = new NpcSearchLogEntity();
            npcSearchLogEntity.init(getTenantId(), getUserCode());
            npcSearchLogEntity.setFileType(req.getFileType());
            npcSearchLogEntity.setSearchType(req.getSearchType());
            npcSearchLogEntity.setSearchContent(req.getSearchContent());
            npcSearchLogEntity.setInput(JSON.toJSONString(req));
            fixedThreadPool.submit(() -> {
                try {
                    iNpcSearchLogService.save(npcSearchLogEntity);
                } catch (Exception e) {
                    log.error("异步保存日志失败", e);
                }
            });
        }
    }

    public RenDaSearchSuggestionResp suggestion(Integer fileType, Integer searchType) {
        RenDaSearchSuggestionResp renDaSearchSuggestionResp = new RenDaSearchSuggestionResp();
        List<NpcSearchLogEntity> userLatestSearchList = iNpcSearchLogService.listByUser(getTenantId(), getUserCode(), fileType, searchType);
        List<RenDaSearchSuggestionResp.SearchParam> userLatestList = userLatestSearchList.stream().map(NpcSearchLogEntity::getSearchContent).distinct().limit(5).map(searchContent -> {
            RenDaSearchSuggestionResp.SearchParam searchParam = new RenDaSearchSuggestionResp.SearchParam();
            searchParam.setSearchType(searchType);
            searchParam.setSearchContent(searchContent);
            searchParam.setDisplayContent(searchContent);
            return searchParam;
        }).collect(Collectors.toList());
        renDaSearchSuggestionResp.setUserLatestList(userLatestList);

        LocalDateTime startTime = LocalDateTimeUtil.offset(LocalDateTime.now(), -1, ChronoUnit.MONTHS);
        List<NpcSearchLogEntity> tenantLatestSearchList = iNpcSearchLogService.listByTenant(getTenantId(), fileType, searchType, startTime);
        List<Map.Entry<String, Long>> sortedSearchContent = tenantLatestSearchList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getSearchContent()))
                .collect(Collectors.groupingBy(
                        s -> s.getSearchContent().trim(),
                        Collectors.counting()
                ))
                .entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .collect(Collectors.toList());

        List<RenDaSearchSuggestionResp.SearchParam> tenantHotList = sortedSearchContent.stream().map(e -> {
            RenDaSearchSuggestionResp.SearchParam searchParam = new RenDaSearchSuggestionResp.SearchParam();
            searchParam.setSearchType(searchType);
            searchParam.setSearchContent(e.getKey());
            searchParam.setDisplayContent(e.getKey());
            searchParam.setCount(e.getValue());
            return searchParam;
        }).collect(Collectors.toList());
        renDaSearchSuggestionResp.setTenantHotList(tenantHotList);

        if (searchType == 2) {
            faceInfo(renDaSearchSuggestionResp);
        }
        return renDaSearchSuggestionResp;
    }

    private void faceInfo(RenDaSearchSuggestionResp renDaSearchSuggestionResp) {

        List<String> list1 = renDaSearchSuggestionResp.getUserLatestList().stream().map(RenDaSearchSuggestionResp.SearchParam::getSearchContent).collect(Collectors.toList());
        List<String> list2 = renDaSearchSuggestionResp.getTenantHotList().stream().map(RenDaSearchSuggestionResp.SearchParam::getSearchContent).collect(Collectors.toList());

        List<PersonInfoDTO> personInfoDTOList = new ArrayList<>();
        List personIds = ListUtils.union(list1, list2);
        if (CollectionUtils.isNotEmpty(personIds)) {
            personInfoDTOList = personClient.personInfo(new HttpPersonInfoReq().setTenantId(getTenantId()).setIds(personIds)).getData();
        }
        Map<String, PersonInfoDTO> personInfoMap = personInfoDTOList.stream().collect(Collectors.toMap(PersonInfoDTO::getId, person -> person));

        renDaSearchSuggestionResp.getUserLatestList().forEach(e -> {
            PersonInfoDTO personInfoDTO = personInfoMap.get(e.getSearchContent());
            if (personInfoDTO != null) {
                e.setDisplayContent(personInfoDTO.getName());
            }
            e.setPersonInfo(personInfoDTO);
        });
        renDaSearchSuggestionResp.getTenantHotList().forEach(e -> {
            PersonInfoDTO personInfoDTO = personInfoMap.get(e.getSearchContent());
            if (personInfoDTO != null) {
                e.setDisplayContent(personInfoDTO.getName());
            }
            e.setPersonInfo(personInfoDTO);
        });
    }

    public void suggestionClear(Integer fileType, Integer searchType) {
        iNpcSearchLogService.deleteByUser(getTenantId(), getUserCode(), fileType, searchType);
    }
}
