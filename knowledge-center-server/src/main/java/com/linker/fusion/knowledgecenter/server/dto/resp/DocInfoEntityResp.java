package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.alibaba.fastjson.JSON;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.BaseEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.KnowledgeGroupParentResp;
import com.linker.fusion.knowledgecenter.service.domain.common.FileSizeFormatter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Slf4j
@Data
public class DocInfoEntityResp extends BaseEntityResp {

    @ApiModelProperty("编号")
    private Long id;

    @ApiModelProperty("docId")
    private String docId;

    @ApiModelProperty("workflowId")
    private String workflowId;

    @ApiModelProperty("知识类型 1-文档 2-图片 3-视频 4-音频 5-FAQ 6-表格")
    private String type;

    @ApiModelProperty("组ID")
    private String groupId;

    @ApiModelProperty("资源名称")
    private String title;

    @ApiModelProperty("副标题")
    private String subheading;

    @ApiModelProperty("文件后缀")
    private String suffix;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("资源地址")
    private String url;

    @ApiModelProperty("预览地址")
    private String previewSrc;

    @ApiModelProperty("是否清除水印")
    private Boolean cleanWatermarkFlag;

    @ApiModelProperty("清除水印的url，为空的时候取previewSrc")
    private String cleanWatermarkUrl;

    @ApiModelProperty("文件资源大小（byte）")
    private Long size;

    @ApiModelProperty("定义了知识资源在处理过程中的各种状态：0-排队中 1-学习中, 2-学习成功, 3-学习失败, 4-归档, 5-合并中, 6-合并失败, 7-预处理中（转码、抽帧）, 8-暂停学习,9-取消学习")
    private Integer handleStatus;
    public String getFileSize() {
        return FileSizeFormatter.formatFileSize(size);
    }

    @ApiModelProperty("文档：页数；表格：行数")
    private Long count;

    @ApiModelProperty("学习失败时错误码")
    private Integer code;

    @ApiModelProperty("学习失败时错误信息")
    private String message;

    @ApiModelProperty("enable")
    private Boolean enable;

    @ApiModelProperty("扩展信息字段json")
    private String extInfo;

    @ApiModelProperty("类目信息")
    private KnowledgeGroupParentResp groupParentResp;

    @ApiModelProperty("策略信息")
    private String strategy;

    @ApiModelProperty("模版ID")
    private Long templateId;
    @ApiModelProperty("文件授权")
    private List<String> authCodes;
    @ApiModelProperty("重要等级")
    private Integer importantLevel;
    @ApiModelProperty("可见范围类型")
    private Integer visibleType;
    @ApiModelProperty("授权信息")
    private AuthActionModel authActions;
    @ApiModelProperty("权限等级")
    private Integer authLevel;
    @ApiModelProperty("申请中")
    private Boolean applying;
    @ApiModelProperty("来源")
    private Integer source;
    @ApiModelProperty("封面地址")
    private String thumbnail;
    public void setSetUpDefault() {
        if (StringUtils.isBlank(strategy) || "null".equals(strategy)) {
            strategy = JSON.toJSONString(getDefaultStrategy());
        } else {
            StrategyModelBasic strategyModelBasic = formatStrategyStr(strategy);
            if (strategyModelBasic != null) {
                strategyModelBasic.setDefault();
            }
            strategy = JSON.toJSONString(strategyModelBasic);
        }
    }

    private StrategyModelBasic getDefaultStrategy() {
        switch (FileTypeEnum.valueOf(Integer.valueOf(type))) {
            case DOCUMENT:
                return new DocStrategyModel();
            case IMAGE:
                return new ImageStrategyModel();
            case VIDEO:
                return new VideoStrategyModel();
            case AUDIO:
                return new AudioStrategyModel();
            default:
                return null;
        }
    }

    private StrategyModelBasic formatStrategyStr(String strategy) {
        switch (FileTypeEnum.valueOf(Integer.valueOf(type))) {
            case DOCUMENT:
                strategy = StringUtils.removeStart(strategy, "\"");
                strategy = StringUtils.removeEnd(strategy, "\"");
                return JSON.parseObject(StringEscapeUtils.unescapeJava(strategy), DocStrategyModel.class);
            case IMAGE:
                return JSON.parseObject(strategy, ImageStrategyModel.class);
            case VIDEO:
                return JSON.parseObject(strategy, VideoStrategyModel.class);
            case AUDIO:
                return JSON.parseObject(strategy, AudioStrategyModel.class);
            default:
                return null;
        }
    }
}
