package com.linker.fusion.knowledgecenter.server.dto.resp.operationlog;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(description = "private String tenantId;")
@Data
public class LogRecordPageResp {

//    /**
//     * 主键ID
//     */
//    private Long id;

//    /**
//     * 租户ID
//     */
//    private String tenantId;

    /**
     * ip
     */
    @ApiModelProperty("ip")
    private String ip;

    /**
     * 业务id
     */
    @ApiModelProperty("业务id")
    private String bizId;

    /**
     * 操作详情
     */
    @ApiModelProperty("操作详情")
    private String action;


    /**
     * 操作类型，
     */
    @ApiModelProperty("操作类型，")
    private String type;

    /**
     * 操作事件等级
     */
    @ApiModelProperty("操作事件等级")
    private String level;

    /**
     * 操作子类型
     */
    @ApiModelProperty("操作子类型")
    private String subType;

    /**
     * 状态 1成功，0失败
     */
    @ApiModelProperty("状态 1成功，0失败")
    private Integer status;


    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    private String creatorId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


    /**
     * 账号
     */
    @ApiModelProperty("账号")
    private String username;
    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String operator;
}
