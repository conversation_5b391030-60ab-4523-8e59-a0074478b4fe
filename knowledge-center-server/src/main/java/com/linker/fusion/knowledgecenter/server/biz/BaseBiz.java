package com.linker.fusion.knowledgecenter.server.biz;

import com.linker.core.auth.utils.UserContext;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.core.BaseClientResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.DeptCodeListReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IUserCenter;
import com.linker.fusion.knowledgecenter.infrastructure.config.ConductorConfigProperties;
import com.linker.user.api.dto.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BaseBiz {

    @Autowired
    ConductorConfigProperties conductorConfigProperties;
    @Value("${conductor.task.extWorker:knowledge_center_file_convert_task}")
    String extWorkers;
    @Resource
    private IUserCenter iUserCenter;

    public String getToken() {
        return UserContext.getToken();
    }

    public String getTenantId() {
        return UserContext.getUser().getTenantInfoDTO().getTenantId();
    }

    public String getUserCode() {
        return UserContext.getUser().getUser().getUserCode();
    }

    public List<String> getDepartmentCodes() {
        List<UserInfo.Department> departments = UserContext.getUser().getDeptList();
        if (CollectionUtils.isEmpty(departments)) {
            return Collections.emptyList();
        }
        List<String> departmentCodes = departments.stream().map(UserInfo.Department::getDepartmentCode).collect(Collectors.toList());
        BaseClientResp<List<String>> allCodeResp = iUserCenter.getDeptCodeList(getToken(), new DeptCodeListReq(getTenantId(), departmentCodes));
        if (Objects.nonNull(allCodeResp) && Objects.nonNull(allCodeResp.getData())) {
            departmentCodes.addAll(allCodeResp.getData());
        }
        return departmentCodes;
    }

}

