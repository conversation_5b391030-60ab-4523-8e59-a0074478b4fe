package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.server.biz.TemplateBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.core.IdBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.template.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.TemplateResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = {"解析策略模版", "v1.6.1"})
@RestController
@RequestMapping("/template")
public class TemplateController {

    @Resource
    private TemplateBiz templateBiz;

    @PostMapping("/create")
    @ApiOperation(value = "创建模版")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> create(@RequestBody @Valid TemplateCreateReq req) {
        templateBiz.create(req);
        return new BaseResp<>();
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<TemplateResp> page(@RequestBody @Valid TemplatePageReq req) {
        return templateBiz.page(req);
    }

    @GetMapping("/available")
    @ApiOperation(value = "查询可用模版")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<TemplateResp>> available(@ApiParam(name = "type", value = "知识类型 1-文档 2-图片 3-视频 4-音频", required = false)
                                                  @RequestParam(value = "type", required = false) Integer type) {
        return new BaseResp<>(templateBiz.available(type));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新模版")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> update(@RequestBody @Valid TemplateUpdateReq req) {
        templateBiz.update(req);
        return new BaseResp<>();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除模版")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> delete(@RequestBody @Valid IdBaseReq<Long> req) {
        templateBiz.delete(req);
        return new BaseResp<>();
    }

    @PostMapping("/enable")
    @ApiOperation(value = "启用、禁用模版")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> enable(@RequestBody @Valid TemplateEnableReq req) {
        templateBiz.enable(req);
        return new BaseResp<>();
    }

    @PostMapping("/setDefault")
    @ApiOperation(value = "设置默认")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> setDefault(@RequestBody @Valid IdBaseReq<Long> req) {
        templateBiz.setDefault(req);
        return new BaseResp<>();
    }

    @PostMapping("/setSort")
    @ApiOperation(value = "设置排序")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> setSort(@RequestBody @Valid TemplateSortReq req) {
        templateBiz.setSort(req);
        return new BaseResp<>();
    }
}
