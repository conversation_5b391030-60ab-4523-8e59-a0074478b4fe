package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthNodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.AuthMenuModel;
import com.linker.fusion.knowledgecenter.server.biz.AuthBiz;
import com.linker.fusion.knowledgecenter.server.dto.auth.*;
import com.linker.fusion.knowledgecenter.server.dto.req.group.RecoveryAuthReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/auth")
@Api(tags = "授权")
public class AuthController {

    @Resource
    AuthBiz authBiz;
    @PostMapping("/add")
    @ApiOperation("添加")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> add(@RequestBody @Valid AddReq req) {
        authBiz.checkAuth(req.getId(), req.getType());
        authBiz.add(req);
        return new BaseResp<>();
    }

    @PostMapping("/remove")
    @ApiOperation("移除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> remove(@RequestBody @Valid RemoveReq req) {
        authBiz.checkAuth(req.getId());
        authBiz.remove(req);
        return new BaseResp<>();
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> update(@RequestBody @Valid UpdateReq req) {
        if (Objects.isNull(req.getId())) {
            authBiz.checkAuth(req.getSourceId(), req.getSourceType());
        } else {
            authBiz.checkAuth(req.getId());
        }
        authBiz.update(UserContext.tenantId(), UserContext.userCode(), req);
        return new BaseResp<>();
    }

    @PostMapping("/recovery")
    @ApiOperation("恢复授权")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> recoveryAuth(@RequestBody @Valid RecoveryAuthReq req) {
        authBiz.checkAuth(req.getSourceId(), req.getSourceType());
        authBiz.recoveryAuth(req);
        return new BaseResp<>();
    }

    @PostMapping("/transfer/owner")
    @ApiOperation("转让所有者")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> transferOwner(@RequestBody @Valid TransferOwnerReq req) {
        authBiz.transferOwer(req);
        return new BaseResp<>();
    }

    @GetMapping("/list")
    @ApiOperation("获取授权列表")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<AuthResp> list(@RequestParam("sourceId") Long sourceId, @RequestParam("sourceType") Integer sourceType) {
        authBiz.checkAuth(sourceId, sourceType);
        AuthResp resp = authBiz.auths(sourceId, sourceType);
        return new BaseResp<>(resp);
    }

    @GetMapping("/node/list")
    @ApiOperation(value = "获取受权节点列表", tags = "v1.6.6")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<AuthMenuModel>> listNodes() {
        return new BaseResp<>(AuthNodeEnum.getList());
    }

    @GetMapping("/level/list")
    @ApiOperation(value = "权限等级列表", tags = "v1.6.6")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<AuthLevelResp>> listLevels() {
        return new BaseResp<>(authBiz.listLevels());
    }

    @PostMapping("/level/update")
    @ApiOperation(value = "修改权限等级", tags = "v1.6.6")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> updateAuthLevel(@RequestBody @Valid UpdateAuthLevelReq req) {
        authBiz.updateAuthLevel(req);
        return new BaseResp<>();
    }

    @PostMapping("/addOrUpdateIndexConf")
    @ApiOperation("添加或更新索引配置")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<AuthIndexConfigAddOrCreateResp> addOrUpdateIndexConf(@RequestBody @Valid AuthIndexConfigAddOrCreateReq req) {
        AuthIndexConfigAddOrCreateResp resp = authBiz.addOrUpdateIndexConf(req);
        return new BaseResp<>(resp);
    }
}
