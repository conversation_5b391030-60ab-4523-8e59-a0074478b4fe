package com.linker.fusion.knowledgecenter.server.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("题目数量统计响应")
public class QuestionCountResp {
    @ApiModelProperty("分组ID")
    private Long groupId;
    
    @ApiModelProperty("题目数量")
    private Integer count;
} 