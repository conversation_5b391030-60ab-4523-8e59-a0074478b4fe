package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.base.exception.ServiceException;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.infrastructure.config.ExtMappingConfig;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthNodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OperatorTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.server.biz.DocBiz;
import com.linker.fusion.knowledgecenter.server.biz.TableBiz;
import com.linker.fusion.knowledgecenter.server.biz.TableSliceBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.core.BatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.EnableReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.IdBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.DocPageReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.MoveReq;
import com.linker.fusion.knowledgecenter.server.dto.req.table.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.TableInfoEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.TableSliceResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.TableUploadResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.MenuAndResNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.TableSearchInput;
import com.linker.log.annotation.LogMetric;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.*;

@Api(tags = "表格管理")
@RestController
@RequestMapping("/table")
public class TableController {

    @Autowired
    private TableBiz tableBiz;

    @Autowired
    private TableSliceBiz tableSliceBiz;

    @Autowired
    private ExtMappingConfig extMappingConfig;
    @Resource
    private DocBiz docBiz;
    @Autowired
    private KnowledgeGroupFactory knowledgeGroupFactory;

    @PostMapping("/add")
    @ApiOperation(value = "添加")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.TABLE, operatorType = OperatorTypeEnum.IMPORT, node = AuthNodeEnum.TABLE_IMPORT, groupIds = "{#req.groupId}")
    public BaseResp<Void> add(@RequestBody @Validated TableAddReq req) throws IOException {
        tableBiz.add(req);
        return new BaseResp<>();
    }

    @PostMapping("/upload")
    @ApiOperation(value = "上传")
    @LogFilter
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<TableUploadResp> upload(@RequestParam("file") MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            throw new ServiceException(TABLE_FILE_NULL);
        }
        String fileExtension = StringComUtils.getFileExtension(file.getOriginalFilename());
        // 判断文件后缀名是否在允许的列表中
        if (!extMappingConfig.getTable().contains(fileExtension.toLowerCase())) {
            throw new ServiceException(TABLE_TYPE_Forbid);
        }
        // 文件大小判断
        long maxSize = 20 * 1024 * 1024; // 20MB
        if (file.getSize() > maxSize) {
            throw new ServiceException(TABLE_FILE_MAX);
        }
        return new BaseResp<>(tableBiz.upload(file.getBytes(), fileExtension));
    }

    @PostMapping("/ibase/add")
    @ApiOperation(value = "ibaseAdd")
//    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> ibaseAdd(@RequestBody @Validated BatchBaseReq req) {
        req.getIds().forEach(x -> {
            tableBiz.ibaseAdd(x);
        });
        return new BaseResp<>();
    }

    @PostMapping("/ibase/text/search")
    @ApiOperation(value = "ibaseTextSearch")
    public BaseResp ibaseTextSearch(@RequestBody @Validated TableSearchInput req) {
        return new BaseResp<>(tableBiz.ibaseTextSearch(req));
    }

    @PostMapping("/update")
    @ApiOperation(value = "编辑")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.TABLE, operatorType = OperatorTypeEnum.EDIT, node = AuthNodeEnum.TABLE_EDIT, ids = "{#req.id}")
    public BaseResp<Void> update(@RequestBody @Validated TableUpdateReq req) {
        tableBiz.update(req);
        return new BaseResp<>();
    }

    @PostMapping("/relearn")
    @ApiOperation(value = "重新学习")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.TABLE, operatorType = OperatorTypeEnum.REDO, node = AuthNodeEnum.TABLE_REDO, ids = "{#req.ids}")
    public BaseResp<Void> relearn(@RequestBody @Validated BatchBaseReq req) {
        tableBiz.relearn(req);
        return new BaseResp<>();
    }

    @PostMapping("/page")
    @ApiOperation(value = "列表分页", tags = {"v1.5.1"})
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public BasePaginResp<TableInfoEntityResp> page(@RequestBody @Valid DocPageReq req) {
        return tableBiz.page(req);
    }

    @PostMapping("/enable")
    @ApiOperation(value = "启用/禁用")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.TABLE, operatorType = OperatorTypeEnum.ENABLE, node = AuthNodeEnum.TABLE_ENABLE, ids = "{#req.ids}")
    public BaseResp<Void> enable(@RequestBody @Validated EnableReq req) {
        tableBiz.enable(req);
        return new BaseResp<>();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.TABLE, operatorType = OperatorTypeEnum.DELETE, node = AuthNodeEnum.TABLE_DELETE, ids = "{#req.ids}")
    public BaseResp<Void> delete(@RequestBody @Validated BatchBaseReq req) {
        tableBiz.delete(req);
        return new BaseResp<>();
    }

    @PostMapping("/move")
    @ApiOperation(value = "移动")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.TABLE, operatorType = OperatorTypeEnum.MOVE, node = AuthNodeEnum.TABLE_MOVE, ids = "{#req.ids}")
    public BaseResp<Void> move(@RequestBody @Validated MoveReq req) {
        docBiz.move(req);
        return new BaseResp<>();
    }

    @PostMapping("/download")
    @ApiOperation(value = "下载", produces = "application/octet-stream")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    @LogMetric
    // @MenuAndResNodeAuth(type = KnowledgeTypeEnum.TABLE, operatorType = OperatorTypeEnum.DOWNLOAD, node = AuthNodeEnum.TABLE_DOWNLOAD, ids = "{#req.id}")
    public void download(@RequestBody @Validated IdBaseReq<Long> req, HttpServletResponse response) {
        //存在response 的时候 测试环境会出现 表达式解析失败  本地环境正常
        knowledgeGroupFactory.checkMenuAndResNodeAuth(Collections.singletonList(req.getId()), KnowledgeTypeEnum.TABLE, OperatorTypeEnum.DOWNLOAD, AuthNodeEnum.TABLE_DOWNLOAD);
        tableBiz.download(req, response);
    }

    @PostMapping("/details")
    @ApiOperation(value = "详情接口", notes = "会话判断文件状态也是用的这个接口")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<TableInfoEntityResp>> details(@RequestBody @Validated BatchBaseReq req) {
        return new BaseResp<>(tableBiz.details(req));
    }

    @PostMapping("/slice/page")
    @ApiOperation("切片分页")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<TableSliceResp> slicePage(@RequestBody @Valid TableSlicePageReq req) {
        return tableSliceBiz.page(req);
    }

    @PostMapping("/slice/update")
    @ApiOperation("切片修改")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.TABLE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.TABLE_EDIT_SEG, ids = "{#req.tableId}")
    public BaseResp<TableSliceResp> sliceUpdate(@RequestBody @Valid TableSliceResp req) {
        if (req.getRow().stream().filter(x -> x.toString().length() > 2000).count() > 0) {
            throw new ServiceException(TABLE_COl_MAX_2000);
        }
        return new BaseResp<>(tableSliceBiz.update(req));
    }

    @PostMapping("/slice/delete")
    @ApiOperation("切片删除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.TABLE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.TABLE_EDIT_SEG, ids = "{#req.tableId}")
    public BaseResp<Void> sliceDelete(@RequestBody @Valid TableSliceDeleteBatchReq req) {
        tableSliceBiz.sliceDelete(req.getTableId(), req.getUids());
        return new BaseResp<>();
    }

    @PostMapping("/slice/localtion")
    @ApiOperation("切片定位")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Integer> sliceLocaltion(@RequestBody @Valid TableLocationReq req) {
        Integer page = tableSliceBiz.TableLocationReq(req);
//        if (page == 0) {
//            throw new ServiceException(TABLE_COl_NULL);
//        }
        return new BaseResp<>(page);
    }

}
