package com.linker.fusion.knowledgecenter.server.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.exception.BusinessException;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ExaminationPaperEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.PaperStatusEnum;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.server.dto.req.paper.PaperCheckReq;
import com.linker.fusion.knowledgecenter.server.dto.req.paper.PaperGenerateReq;
import com.linker.fusion.knowledgecenter.server.dto.req.paper.PaperSubmitReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.PaperCheckResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.PaperInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.PaperScoreResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.PaperSubmitResp;
import com.linker.fusion.knowledgecenter.service.domain.paper.ExaminationPaperService;
import com.linker.fusion.knowledgecenter.service.domain.paper.dto.PaperAnswerInfoDTO;
import com.linker.fusion.knowledgecenter.service.domain.paper.dto.PaperInfoDTO;
import com.linker.fusion.knowledgecenter.service.domain.question.handler.QuestionBankHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.ALREADY_ANSWER;

/**
 * @Author: YangQiyan
 * @Description: 类说明
 * @Date: Created in 2025/4/2
 */
@Component
public class PaperBiz {

    @Autowired
    private ExaminationPaperService examinationPaperService;

    @Autowired
    private QuestionBankHolder questionBankHolder;

    public BaseResp<String> generate(PaperGenerateReq req) {
        String tenantId = UserContext.tenantId();
        String userCode = UserContext.userCode();
        String paperUniqueId = examinationPaperService.generate(tenantId, userCode, req.getQuestionBaseIds(),
                req.getGenerateQuestionTypes(), req.getGenerateQuestionNumber());
        return new BaseResp<>(paperUniqueId);
    }

    public BaseResp<PaperInfoResp> detail(String paperUniqueId) {
        String tenantId = UserContext.tenantId();
        ExaminationPaperEntity paperEntity = examinationPaperService.getDetail(tenantId, paperUniqueId);
        if (Objects.isNull(paperEntity)) {
            throw new BusinessException("试卷信息不存在");
        }
        PaperInfoResp infoResp = new PaperInfoResp();
        infoResp.setPagerGenerateTime(paperEntity.getCreateTime());
        infoResp.setPaperStatus(paperEntity.getPaperStatus());
        int maxNumber = 0;
        int currentNumber = 0;
        if (StringUtils.isNotBlank(paperEntity.getPaperContent())) {
            List<PaperInfoDTO> paperInfo = JSONArray.parseArray(paperEntity.getPaperContent(), PaperInfoDTO.class);
            LinkedList<PaperInfoResp.PaperContentDTO> paperContentList = new LinkedList<>();
            for (PaperInfoDTO paperInfoDTO : paperInfo) {
                paperContentList.add(BeanUtil.copyProperties(paperInfoDTO, PaperInfoResp.PaperContentDTO.class));
            }
            infoResp.setPaperContentList(paperContentList);
            maxNumber = paperInfo.size();
        }
        if (StringUtils.isNotBlank(paperEntity.getPaperAnswerContent())) {
            List<PaperAnswerInfoDTO> paperAnswerInfoDTOS = JSONArray.parseArray(paperEntity.getPaperAnswerContent(), PaperAnswerInfoDTO.class);
            LinkedList<PaperInfoResp.PaperAnswerRecordDTO> paperAnswerRecordList = new LinkedList<>();
            for (PaperAnswerInfoDTO paperAnswerInfoDTO : paperAnswerInfoDTOS) {
                paperAnswerRecordList.add(BeanUtil.copyProperties(paperAnswerInfoDTO, PaperInfoResp.PaperAnswerRecordDTO.class));
            }
            infoResp.setPaperAnswerRecordList(paperAnswerRecordList);
            currentNumber = paperAnswerInfoDTOS.size();
        }
        infoResp.setCurrentQuestionNumber(maxNumber > currentNumber ? currentNumber + 1 : maxNumber);
        infoResp.setPaperSchedule(currentNumber + "/" + maxNumber);
        return new BaseResp<>(infoResp);
    }

    public BaseResp<PaperCheckResp> check(PaperCheckReq req) {
        String tenantId = UserContext.tenantId();
        String userCode = UserContext.userCode();
        String paperUniqueId = req.getPaperUniqueId();
        ExaminationPaperEntity paperEntity = examinationPaperService.getDetail(tenantId, paperUniqueId);
        if (Objects.isNull(paperEntity)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "试卷");
        }

        if (StringUtils.isBlank(paperEntity.getPaperContent())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "试卷");
        }
        if (paperEntity.getPaperStatus().equals(PaperStatusEnum.FINISHED.getCode())) {
            throw new ServiceException(ALREADY_ANSWER);
        }
        String paperContent = paperEntity.getPaperContent();
        List<PaperInfoDTO> paperInfoDTOS = JSONArray.parseArray(paperContent, PaperInfoDTO.class);
        Map<Integer, PaperInfoDTO> numberAndItemMap = paperInfoDTOS.stream().collect(Collectors.toMap(PaperInfoDTO::getQuestionNumber, item -> item, (k1, k2) -> k1));
        PaperInfoDTO paperInfoDTO = numberAndItemMap.get(req.getQuestionNumber());
        if (Objects.isNull(paperInfoDTO)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "题目");
        }
        //校验是否正确
        int isCorrect = questionBankHolder.route(paperInfoDTO.getQuestionType())
                .checkCorrect(paperInfoDTO.getCorrectAnswer(), req.getFillAnswer());
//         isCorrect = Objects.equals(paperInfoDTO.getCorrectAnswer(), req.getFillAnswer()) ? 1 : 0;
        PaperCheckResp paperCheckResp = new PaperCheckResp();
        paperCheckResp.setTotalQuestionNumber(paperInfoDTOS.size());

        // Create answer info for current question
        PaperAnswerInfoDTO answerInfoDTO = new PaperAnswerInfoDTO();
        answerInfoDTO.setQuestionBankId(paperInfoDTO.getQuestionBankId());
        answerInfoDTO.setQuestionNumber(paperInfoDTO.getQuestionNumber());
        answerInfoDTO.setQuestionContent(paperInfoDTO.getQuestionContent());
        answerInfoDTO.setQuestionImages(paperInfoDTO.getQuestionImages());
        answerInfoDTO.setQuestionType(paperInfoDTO.getQuestionType());
        answerInfoDTO.setAnswerList(paperInfoDTO.getAnswerList());
        answerInfoDTO.setFillAnswer(req.getFillAnswer());
        answerInfoDTO.setCorrectAnswer(paperInfoDTO.getCorrectAnswer());
        answerInfoDTO.setAnswerResult(isCorrect);
        answerInfoDTO.setAnswerAnalysis(paperInfoDTO.getAnswerAnalysis());

        List<PaperAnswerInfoDTO> paperAnswerInfo;
        if (StringUtils.isNotBlank(paperEntity.getPaperAnswerContent())) {
            paperAnswerInfo = JSONArray.parseArray(paperEntity.getPaperAnswerContent(), PaperAnswerInfoDTO.class);

            // Find existing answer for this question number
            boolean updated = false;
            for (int i = 0; i < paperAnswerInfo.size(); i++) {
                if (Objects.equals(paperAnswerInfo.get(i).getQuestionNumber(), answerInfoDTO.getQuestionNumber())) {
                    paperAnswerInfo.set(i, answerInfoDTO); // Update existing answer
                    updated = true;
                    break;
                }
            }

            // Add as new answer if not found
            if (!updated) {
                paperAnswerInfo.add(answerInfoDTO);
            }
        } else {
            paperAnswerInfo = new LinkedList<>();
            paperAnswerInfo.add(answerInfoDTO);
        }
        paperCheckResp.setPaperStatus(PaperStatusEnum.UNFINISHED.getCode());

        String paperScoreRespJson = null;
//        if (Objects.equals(PaperStatusEnum.FINISHED.getCode(), paperCheckResp.getPaperStatus())) {
//            PaperScoreResp paperScoreResp = new PaperScoreResp();
//            paperScoreResp.setTotalNumber(paperEntity.getRealQuestionNumber());
//            paperScoreResp.setCompleteTotalNumber(paperAnswerInfo.size());
//            long realCount = paperAnswerInfo.stream().filter(item -> Objects.equals(item.getAnswerResult(), 1)).count();
//            paperScoreResp.setRealTotalNumber(Convert.toInt(realCount));
//            paperScoreResp.setErrorTotalNumber(paperScoreResp.getCompleteTotalNumber() - paperScoreResp.getRealTotalNumber());
//            paperScoreResp.setRealRate(new BigDecimal(paperScoreResp.getRealTotalNumber())
//                    .divide(new BigDecimal(paperScoreResp.getCompleteTotalNumber()), 4, RoundingMode.HALF_UP));
//            paperScoreRespJson = JSON.toJSONString(paperScoreResp);
//        }

        examinationPaperService.updatePaper(tenantId, userCode, paperAnswerInfo, paperCheckResp.getPaperStatus(), paperScoreRespJson, paperUniqueId);
        return new BaseResp<>(paperCheckResp);
    }

    public BaseResp<PaperScoreResp> result(String paperUniqueId) {
        String tenantId = UserContext.getUser().getTenantInfoDTO().getTenantId();

        // 获取试卷详情
        ExaminationPaperEntity paperEntity = examinationPaperService.getDetail(tenantId, paperUniqueId);
        if (Objects.isNull(paperEntity)) {
            throw new BusinessException("试卷信息不存在");
        }

        // 校验试卷状态
        Integer paperStatus = paperEntity.getPaperStatus();
        if (Objects.equals(paperStatus, PaperStatusEnum.UNFINISHED.getCode())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.PAPER_UNFINiSH);
        }

        // 初始化响应对象
        PaperScoreResp resp = new PaperScoreResp();
        resp.setTotalNumber(paperEntity.getRealQuestionNumber());
        resp.setPaperStatus(paperStatus);

        // 处理试卷得分信息
        processPaperScore(paperEntity, resp);

        // 处理答题记录
        if (StringUtils.isNotBlank(paperEntity.getPaperAnswerContent())) {
            List<PaperAnswerInfoDTO> answerInfoList = JSONArray.parseArray(
                    paperEntity.getPaperAnswerContent(),
                    PaperAnswerInfoDTO.class
            );

            // 计算得分统计
            calculateScoreStatistics(resp, answerInfoList);

            // 转换答题记录
            convertAnswerRecords(resp, answerInfoList);
        } else {
            initializeEmptyScores(resp);
        }

        return new BaseResp<>(resp);
    }

    private void processPaperScore(ExaminationPaperEntity paperEntity, PaperScoreResp resp) {
        String paperScore = paperEntity.getPaperScore();
        if (StringUtils.isNotBlank(paperScore)) {
            PaperScoreResp paperScoreResp = JSON.parseObject(paperScore, PaperScoreResp.class);
            paperScoreResp.setPaperStatus(1);
            resp.setFinishTime(paperScoreResp.getFinishTime());
        }
    }

    private void calculateScoreStatistics(PaperScoreResp resp, List<PaperAnswerInfoDTO> answerInfoList) {
        resp.setCompleteTotalNumber(answerInfoList.size());

        long correctAnswers = answerInfoList.stream()
                .filter(item -> Objects.equals(item.getAnswerResult(), 1))
                .count();

        resp.setRealTotalNumber(Convert.toInt(correctAnswers));
        resp.setErrorTotalNumber(resp.getCompleteTotalNumber() - resp.getRealTotalNumber());

        resp.setRealRate(new BigDecimal(resp.getRealTotalNumber())
                .divide(new BigDecimal(resp.getCompleteTotalNumber()), 4, RoundingMode.HALF_UP));
    }

    private void convertAnswerRecords(PaperScoreResp resp, List<PaperAnswerInfoDTO> answerInfoList) {
        LinkedList<PaperScoreResp.PaperAnswerRecordDTO> paperAnswerRecordList = new LinkedList<>();

        for (PaperAnswerInfoDTO answerInfo : answerInfoList) {
            paperAnswerRecordList.add(
                    BeanUtil.copyProperties(answerInfo, PaperScoreResp.PaperAnswerRecordDTO.class)
            );
        }

        resp.setPaperAnswerRecordList(paperAnswerRecordList);
    }

    private void initializeEmptyScores(PaperScoreResp resp) {
        resp.setCompleteTotalNumber(0);
        resp.setRealTotalNumber(0);
        resp.setErrorTotalNumber(0);
    }

    /**
     * 用户最终提交答题
     * @param req
     * @return
     */
    public BaseResp<PaperSubmitResp> submit(PaperSubmitReq req) {
        String tenantId = UserContext.tenantId();
        String userCode = UserContext.userCode();
        String paperUniqueId = req.getPaperUniqueId();

        // 获取试卷信息
        ExaminationPaperEntity paperEntity = examinationPaperService.getDetail(tenantId, paperUniqueId);
        if (Objects.isNull(paperEntity)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "试卷");
        }

        String paperContent = paperEntity.getPaperContent();
        if (StringUtils.isBlank(paperContent)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "试卷");
        }

        // 检查试卷状态
        if (paperEntity.getPaperStatus().equals(PaperStatusEnum.FINISHED.getCode())) {
            throw new ServiceException(ALREADY_ANSWER);
        }

        // 获取用户已答题信息
        List<PaperAnswerInfoDTO> paperAnswerInfo = new ArrayList<>();
        if (StringUtils.isNotBlank(paperEntity.getPaperAnswerContent())) {
            paperAnswerInfo = JSONArray.parseArray(paperEntity.getPaperAnswerContent(), PaperAnswerInfoDTO.class);
        }

        // 获取试卷题目信息
        List<PaperInfoDTO> paperInfoContent = JSONArray.parseArray(paperContent, PaperInfoDTO.class);

        // 创建已答题目的映射，用于快速查找
        Map<Integer, PaperAnswerInfoDTO> answeredQuestions = paperAnswerInfo.stream()
                .collect(Collectors.toMap(PaperAnswerInfoDTO::getQuestionNumber, item -> item));

        // 处理未答题目
        for (PaperInfoDTO question : paperInfoContent) {
            if (!answeredQuestions.containsKey(question.getQuestionNumber())) {
                // 创建未答题记录
                PaperAnswerInfoDTO unansweredQuestion = new PaperAnswerInfoDTO();
                unansweredQuestion.setQuestionBankId(question.getQuestionBankId());
                unansweredQuestion.setQuestionNumber(question.getQuestionNumber());
                unansweredQuestion.setQuestionContent(question.getQuestionContent());
                unansweredQuestion.setQuestionImages(question.getQuestionImages());
                unansweredQuestion.setQuestionType(question.getQuestionType());
                unansweredQuestion.setAnswerList(question.getAnswerList());
                // 空答案
                unansweredQuestion.setFillAnswer(null);
                unansweredQuestion.setCorrectAnswer(question.getCorrectAnswer());
                // 未答题算错误
                unansweredQuestion.setAnswerResult(0);
                unansweredQuestion.setAnswerAnalysis(question.getAnswerAnalysis());

                paperAnswerInfo.add(unansweredQuestion);
            }
        }

        // 生成提交响应
        PaperSubmitResp paperSubmitResp = new PaperSubmitResp();
        paperSubmitResp.setPaperStatus(PaperStatusEnum.FINISHED.getCode());
        paperSubmitResp.setFinishTime(LocalDateTime.now());

        // 计算得分情况
        PaperScoreResp paperScoreResp = new PaperScoreResp();
        paperScoreResp.setTotalNumber(paperEntity.getRealQuestionNumber());
        paperScoreResp.setCompleteTotalNumber(paperAnswerInfo.size());
        paperScoreResp.setFinishTime(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        long realCount = paperAnswerInfo.stream()
                .filter(item -> Objects.equals(item.getAnswerResult(), 1))
                .count();
        paperScoreResp.setRealTotalNumber(Convert.toInt(realCount));
        paperScoreResp.setErrorTotalNumber(paperScoreResp.getCompleteTotalNumber() - paperScoreResp.getRealTotalNumber());

        // 计算正确率
        if (paperScoreResp.getCompleteTotalNumber() > 0) {
            paperScoreResp.setRealRate(new BigDecimal(paperScoreResp.getRealTotalNumber())
                    .divide(new BigDecimal(paperScoreResp.getCompleteTotalNumber()), 4, RoundingMode.HALF_UP));
        } else {
            paperScoreResp.setRealRate(BigDecimal.ZERO);
        }

        // 更新试卷状态
        String paperScoreRespJson = JSON.toJSONString(paperScoreResp);
        examinationPaperService.finalSubmit(tenantId, userCode, paperUniqueId, paperScoreRespJson, JSON.toJSONString(paperAnswerInfo));

        return new BaseResp<>(paperSubmitResp);
    }
}
