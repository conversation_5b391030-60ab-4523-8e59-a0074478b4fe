package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.server.biz.CustomBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.core.BatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.EnableReq;
import com.linker.fusion.knowledgecenter.server.dto.req.custom.CustomAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.custom.CustomPageReq;
import com.linker.fusion.knowledgecenter.server.dto.req.custom.CustomUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.CustomFillInResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.CustomInfoEntityResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "扩展字段")
@RestController
@RequestMapping("/custom")
public class CustomController {

    @Resource
    private CustomBiz customBiz;

    @PostMapping("/add")
    @ApiOperation(value = "添加")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> add(@RequestBody @Validated CustomAddReq req) {
        customBiz.add(req);
        return new BaseResp<>();
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<CustomInfoEntityResp> page(@RequestBody @Validated  CustomPageReq req) {
        return customBiz.page(req);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> update(@RequestBody @Validated CustomUpdateReq req) {
        customBiz.update(req);
        return new BaseResp<>();
    }

    @PostMapping("/enable")
    @ApiOperation(value = "启用/禁用")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> enable(@RequestBody @Validated EnableReq req) {
        customBiz.enable(req);
        return new BaseResp<>();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> delete(@RequestBody @Validated BatchBaseReq req) {
        customBiz.delete(req);
        return new BaseResp<>();
    }

    @GetMapping("/queryFillInfo")
    @ApiOperation(value = "查询扩展字段填充信息", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<CustomFillInResp>> queryFillInfo(@ApiParam(name = "manageType", value = "知识类型 1-文档 2-图片 3-视频 4-音频", required = false)
                                                          @RequestParam(required = false) Integer manageType) {
        return customBiz.queryFillInfo(manageType);
    }

}
