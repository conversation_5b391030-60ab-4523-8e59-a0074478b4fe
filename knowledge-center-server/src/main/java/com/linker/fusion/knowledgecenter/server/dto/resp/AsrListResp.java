package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AsrListResp {

    @ApiModelProperty("es唯一id,用于更新")
    private String uid;

    @ApiModelProperty("开始时间")
    private Integer startTimestamp;

    @ApiModelProperty("结束时间")
    private Integer endTimestamp;

    @ApiModelProperty("内容")
    private String text;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("发言人列表")
    private String speakers;

    @ApiModelProperty("图片地址")
    private String url;

    @ApiModelProperty("文档类型  image text")
    private String srcType;

    public List<String> getSpeakers() {
        return JSON.parseArray(speakers, String.class);
    }

    public String getContent() {
        return text;
    }
}
