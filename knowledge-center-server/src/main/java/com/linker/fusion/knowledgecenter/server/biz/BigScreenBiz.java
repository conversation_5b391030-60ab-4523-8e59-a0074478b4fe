package com.linker.fusion.knowledgecenter.server.biz;

import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.GroupExtModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.ResourceStaModel;
import com.linker.fusion.knowledgecenter.server.dto.resp.bigScreen.BigScreenFileStaResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.bigScreen.BigScreenSpaceStaResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.bigScreen.BigScreenStatusResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.bigScreen.BigScreenYearStaResp;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BigScreenBiz extends BaseBiz {

    @Resource
    IResourceService iResourceService;
    @Resource
    KnowledgeGroupService knowledgeGroupService;

    public BaseResp<BigScreenStatusResp> statusSta() {
        BigScreenStatusResp resp = new BigScreenStatusResp();
        resp.setSuccess(iResourceService.getCount(getTenantId(), Arrays.asList(FileTypeEnum.VIDEO, FileTypeEnum.DOCUMENT, FileTypeEnum.IMAGE), Arrays.asList(ProcessEnum.Success, ProcessEnum.ARCHIVE),null));
        resp.setProcessing(iResourceService.getCount(getTenantId(), Arrays.asList(FileTypeEnum.VIDEO, FileTypeEnum.DOCUMENT, FileTypeEnum.IMAGE), Arrays.asList(ProcessEnum.Executing, ProcessEnum.MERGING),null));
        resp.setFail(iResourceService.getCount(getTenantId(), Arrays.asList(FileTypeEnum.VIDEO, FileTypeEnum.DOCUMENT, FileTypeEnum.IMAGE), Arrays.asList(ProcessEnum.Fail, ProcessEnum.MERGE_FAILED),null));
        return new BaseResp<>(resp);
    }

    public BaseResp<BigScreenFileStaResp> fileSta() {
        BigScreenFileStaResp resp = new BigScreenFileStaResp();
        resp.setVideo(iResourceService.getCount(getTenantId(), Collections.singletonList(FileTypeEnum.VIDEO), null,null));
        resp.setAudio(iResourceService.getCount(getTenantId(), Collections.singletonList(FileTypeEnum.AUDIO), null,null));
        resp.setPic(iResourceService.getCount(getTenantId(), Collections.singletonList(FileTypeEnum.IMAGE), null,null));
        resp.setDoc(iResourceService.getCount(getTenantId(), Collections.singletonList(FileTypeEnum.DOCUMENT), null,null));
        return new BaseResp<>(resp);
    }

    public BaseResp<BigScreenSpaceStaResp> spaceSta() {
        BigScreenSpaceStaResp resp = new BigScreenSpaceStaResp();
        resp.setVideo(iResourceService.sumSize(getTenantId(), FileTypeEnum.VIDEO));
        resp.setAudio(iResourceService.sumSize(getTenantId(), FileTypeEnum.AUDIO));
        resp.setPic(iResourceService.sumSize(getTenantId(), FileTypeEnum.IMAGE));
        resp.setDoc(iResourceService.sumSize(getTenantId(), FileTypeEnum.DOCUMENT));
        return new BaseResp<>(resp);
    }

    public BaseResp<List<BigScreenYearStaResp>> yearSta() {
        List<BigScreenYearStaResp> respList = new ArrayList<>();
        List<Long> libIds = knowledgeGroupService.listByTypeAndParentId(getTenantId(), KnowledgeTypeEnum.FILE.getType(), Collections.singletonList(KnowledgeGroupEntity.ROOT_ID), null)
                .stream().map(KnowledgeGroupEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(libIds)) {
            return new BaseResp<>(respList);
        }
        List<KnowledgeGroupEntity> secondLevelGroups = knowledgeGroupService.listByTypeAndParentId(getTenantId(), KnowledgeTypeEnum.FILE.getType(), libIds, null);
        if (CollectionUtils.isEmpty(secondLevelGroups)) {
            return new BaseResp<>(respList);
        }
        List<Long> secondLibIds = secondLevelGroups.stream().map(KnowledgeGroupEntity::getId).collect(Collectors.toList());
//        List<Long> groupIds = Arrays.asList(bigScreenConfig.getVideoGroupId(), bigScreenConfig.getImageGroupId(), bigScreenConfig.getDocGroupId());
        GroupExtModel groupExtModel = knowledgeGroupService.getMaxAndMin(secondLibIds);
//        List<KnowledgeGroupEntity> groups=knowledgeGroupService.listByIds(secondLibIds);
        List<String> pathList = secondLevelGroups.stream().map(r -> r.getSearchPath() + "%").collect(Collectors.toList());
        List<ResourceStaModel> videoModelList = iResourceService.staByLib(pathList, FileTypeEnum.VIDEO.getType());
        List<ResourceStaModel> imageModelList = iResourceService.staByLib(pathList, FileTypeEnum.IMAGE.getType());
        List<ResourceStaModel> docModelList = iResourceService.staByLib(pathList, FileTypeEnum.DOCUMENT.getType());
        for (int i = groupExtModel.getMin(); i <=groupExtModel.getMax(); i++) {
            BigScreenYearStaResp resp = new BigScreenYearStaResp();
            String name = i + "";
            resp.setName(name);
            List<ResourceStaModel> collectVideo = videoModelList.stream().filter(s -> s.getPrefix().endsWith(name)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collectVideo)) {
                resp.setVideo(collectVideo.stream().mapToLong(ResourceStaModel::getCount).sum());
                resp.setVideoSize(collectVideo.stream().mapToLong(ResourceStaModel::getSum).sum());
            }
            List<ResourceStaModel> collectPic = imageModelList.stream().filter(s -> s.getPrefix().endsWith(name)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collectPic)) {
                resp.setPic(collectPic.stream().mapToLong(ResourceStaModel::getCount).sum());
                resp.setPicSize(collectPic.stream().mapToLong(ResourceStaModel::getSum).sum());
            }
            List<ResourceStaModel> collectDoc = docModelList.stream().filter(s -> s.getPrefix().endsWith(name)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collectDoc)) {
                resp.setDoc(collectDoc.stream().mapToLong(ResourceStaModel::getCount).sum());
                resp.setDocSize(collectDoc.stream().mapToLong(ResourceStaModel::getSum).sum());
            }
            respList.add(resp);
        }
        return new BaseResp<>(respList);
    }
}
