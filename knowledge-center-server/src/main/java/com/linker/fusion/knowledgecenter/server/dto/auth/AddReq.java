package com.linker.fusion.knowledgecenter.server.dto.auth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AddReq {

    @ApiModelProperty("分组/文件id")
    private Long id;
    @ApiModelProperty("类型 1 分组 2文件")
    private Integer type;
    @ApiModelProperty("授权列表")
    @NotNull
    private List<AddItem> items;
}
