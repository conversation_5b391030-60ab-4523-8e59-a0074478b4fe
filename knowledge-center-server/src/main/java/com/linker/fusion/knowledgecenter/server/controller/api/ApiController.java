package com.linker.fusion.knowledgecenter.server.controller.api;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.cos.model.req.UploadReq;
import com.linker.core.cos.model.resp.UploadResp;
import com.linker.core.cos.property.CosConfigProperties;
import com.linker.core.cos.property.CosPlatformProperties;
import com.linker.core.cos.provider.CosHelper;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceExtEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceODTagEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.RenameTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.VideoStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.server.biz.DocBiz;
import com.linker.fusion.knowledgecenter.server.biz.FileBiz;
import com.linker.fusion.knowledgecenter.server.dto.api.*;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.DocUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.ImportReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.RedoBatchReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.DocInfoEntityResp;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.TagExtInfoModel;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.VideoExtractInfoModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.linker.core.auth.enums.InterfaceType.TOC;

@Api(tags = "知识中心对外接口")
@RestController
@RequestMapping("api/v1")
public class ApiController {
    @Resource
    DocBiz docBiz;
    @Resource
    FileBiz fileBiz;


    @Resource
    private CosHelper cosHelper;
    @Autowired
    CosConfigProperties cosConfigProperties;

    @ApiOperation(value = "文件后端上传", tags = "api")
    @PostMapping(value = "/upload")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
//    @ResponseBody
    @LogFilter
    public BaseResp<UploadResp> upload(@RequestPart("file") MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);

        UploadReq uploadReq = new UploadReq(file);
        List<CosPlatformProperties.BucketConfig> configBuckets = cosHelper.getSelectProvider().getConfigBuckets();
        uploadReq.bucketName(configBuckets.stream()
                .filter(c -> c.getExpire() == null).findFirst().orElse(cosHelper.getSelectProvider().getDefaultBucket()).getName());
        uploadReq.fileKey("/api/" + DateUtil.format(DateUtil.date(), "yyyyMMdd") + "/" + UUID.randomUUID() + "." + fileExtension);

        UploadResp upload = cosHelper.getSelectProvider().upload(uploadReq);
        return new BaseResp<>(upload);
    }

    @PostMapping("resource/import")
    @ApiOperation(value = "导入文件", tags = "api")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<ApiDocInfoEntityResp> importDoc(@RequestBody @Validated ApiImportReq req) {
        ImportReq importReq = new ImportReq();
        importReq.setCustomFields(req.getCustomFields());
        importReq.setRelativePath(req.getRelativePath());
        importReq.setFileName(req.getFileName());
        importReq.setRenameType(RenameTypeEnum.Rename.getValue());
        importReq.setUrl(req.getFileUrl());
        VideoStrategyModel videoStrategyModel = new VideoStrategyModel();
        videoStrategyModel.setEnableSegment(0);
        videoStrategyModel.setEnableVideoTranscode(req.getVideoStrategy().getEnableVideoTranscode());
        videoStrategyModel.setEnableEvent(req.getVideoStrategy().getEventAnalyze().getEnableEvent());
        videoStrategyModel.setPromptInfos(req.getVideoStrategy().getEventAnalyze().getPromptInfo());
        videoStrategyModel.setVideoExtract(req.getVideoStrategy().getVideoExtract());
        importReq.setStrategyVideo(videoStrategyModel);
        DocInfoEntityResp resp = docBiz.importDoc(importReq);
        ApiDocInfoEntityResp apiDocInfoEntityResp = new ApiDocInfoEntityResp(resp);
        return new BaseResp<>(apiDocInfoEntityResp);
    }


    @PostMapping("/resource/task/batch-redo")
    @ApiOperation(value = "批量重新学习", tags = "api")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> redoBatch(@RequestBody @Validated ApiReDoReq req) {
        RedoBatchReq redoBatchReq = new RedoBatchReq();
        redoBatchReq.setResIds(req.getResourceIds());
        VideoStrategyModel videoStrategyModel = new VideoStrategyModel();
        videoStrategyModel.setEnableSegment(req.getStrategyVideo().getEnableSegment());
        videoStrategyModel.setEventSetting(new VideoStrategyModel.EventSetting());
        videoStrategyModel.getEventSetting().setType(1);
        videoStrategyModel.getEventSetting().setEnable(req.getStrategyVideo().getEventAnalyze().getEnableEvent());
        videoStrategyModel.getEventSetting().setInterval(req.getStrategyVideo().getEventAnalyze().getInterval());
        videoStrategyModel.setEnableVideoTranscode(req.getStrategyVideo().getEnableVideoTranscode());
        videoStrategyModel.getEventSetting().setPromptInfos(req.getStrategyVideo().getEventAnalyze().getPromptInfo());
        videoStrategyModel.setEnableEvent(req.getStrategyVideo().getEventAnalyze().getEnableEvent());
        videoStrategyModel.setVideoExtract(req.getStrategyVideo().getVideoExtract());
        redoBatchReq.setStrategy(JSON.toJSONString(videoStrategyModel));
        docBiz.batchRedo(redoBatchReq);
        return new BaseResp<>();
    }


    @PostMapping("/resource/task/control")
    @ApiOperation(value = "文件分析任务控制，启动/停止/取消", tags = "api")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> controlTask(@RequestBody @Validated ApiBatchTaskControlReq req) {
        fileBiz.controlTask(req.getResourceIds(), req.getControlParam());
        return new BaseResp<>();
    }

    @PostMapping("/resource/list")
    @ApiOperation(value = "文件信息列表", tags = "api")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<ApiResourceInfoResp>> resourceList(@RequestBody @Validated ApiBatchResReq req) {
        if (CollectionUtils.isEmpty(req.getResourceIds())) {
            return new BaseResp<>(Collections.emptyList());
        }
        List<KnowledgeResourceEntity> resourceList = fileBiz.list(req.getResourceIds());
        List<ApiResourceInfoResp> apiResourceInfoRespList = resourceList.stream().map(ApiResourceInfoResp::new).collect(Collectors.toList());
        List<ResourceODTagEntity> tagList = fileBiz.listEvents(req.getResourceIds());
        List<ResourceExtEntity> resourceExtList = fileBiz.listExt(req.getResourceIds());

        apiResourceInfoRespList.forEach(resp -> {
            List<ResourceODTagEntity> curTagList = tagList.stream().filter(t -> t.getDocId().equals(resp.getResourceId())).collect(Collectors.toList());
            resp.getEvents().setTotalCount(curTagList.size());
            resp.getEvents().setItems(new ArrayList<>());
            curTagList.stream().collect(Collectors.groupingBy(ResourceODTagEntity::getName)).forEach((name, list) -> {
                List<ApiEventItemResp> sameNamelist = new ArrayList<>();
                list.stream().sorted(Comparator.comparingLong(ResourceODTagEntity::getTimePoint)).forEach(curTag -> {
                    TagExtInfoModel tagExtInfo = new TagExtInfoModel();
                    if (StringUtils.isNotBlank(curTag.getExtInfo())) {
                        tagExtInfo = JSON.parseObject(curTag.getExtInfo(), TagExtInfoModel.class);
                    }
                    ApiEventItemResp apiEventItemResp;
                    if (CollectionUtils.isEmpty(sameNamelist) || curTag.getTimePoint() > (sameNamelist.get(sameNamelist.size() - 1).getEndTimePoint() + 1900)) {
                        apiEventItemResp = new ApiEventItemResp();
                        apiEventItemResp.setImages(new ArrayList<>());
                        apiEventItemResp.setActionName(curTag.getName());
                        apiEventItemResp.setDeviceName(tagExtInfo.getDeviceName());
                        apiEventItemResp.setTimePoint(curTag.getTimePoint());
                        apiEventItemResp.setEndTimePoint(Objects.isNull(tagExtInfo.getEndPoint()) ? (curTag.getTimePoint() + 1000) : tagExtInfo.getEndPoint());
                        if (StringUtils.isNotBlank(tagExtInfo.getRecordingTime())) {
                            apiEventItemResp.setStartTime(tagExtInfo.getRecordingTime());
                            apiEventItemResp.setEndTime(StringComUtils.convertStr(StringComUtils.convertDate(tagExtInfo.getRecordingTime()).plus(apiEventItemResp.getEndTimePoint() - apiEventItemResp.getTimePoint(), ChronoUnit.MILLIS)));
                        } else {
                            apiEventItemResp.setStartTime(StringComUtils.formatSeconds(apiEventItemResp.getTimePoint() / 1000));
                            apiEventItemResp.setEndTime(StringComUtils.formatSeconds(apiEventItemResp.getEndTimePoint() / 1000));
                        }
                        apiEventItemResp.setEventTime(apiEventItemResp.getStartTime() + "-" + apiEventItemResp.getEndTime());
                        sameNamelist.add(apiEventItemResp);
                    } else {
                        apiEventItemResp = sameNamelist.get(sameNamelist.size() - 1);
                        apiEventItemResp.setEndTimePoint(Objects.isNull(tagExtInfo.getEndPoint()) ? (curTag.getTimePoint() + 1000) : tagExtInfo.getEndPoint());
                        if (StringUtils.isNotBlank(tagExtInfo.getRecordingTime())) {
                            apiEventItemResp.setEndTime(StringComUtils.convertStr(StringComUtils.convertDate(tagExtInfo.getRecordingTime()).plus(apiEventItemResp.getEndTimePoint() - apiEventItemResp.getTimePoint(), ChronoUnit.MILLIS)));
                        } else {
                            apiEventItemResp.setEndTime(StringComUtils.formatSeconds(apiEventItemResp.getEndTimePoint() / 1000));
                        }
                        apiEventItemResp.setEventTime(apiEventItemResp.getStartTime() + "-" + apiEventItemResp.getEndTime());
                    }
                    apiEventItemResp.setCount(apiEventItemResp.getCount() + 1);
                    if (StringUtils.isNotBlank(tagExtInfo.getImage())) {
                        apiEventItemResp.getImages().add(new ApiEventImageItemResp(tagExtInfo.getImage(), curTag.getTimePoint(), tagExtInfo.getRecordingTime()));
                    }
                });
                resp.getEvents().getItems().addAll(sameNamelist);
            });

            resp.getEvents().setItems(resp.getEvents().getItems().stream().sorted(Comparator.comparingLong(ApiEventItemResp::getTimePoint)).collect(Collectors.toList()));
            resourceExtList.stream().filter(e -> e.getDocId().equals(resp.getResourceId())).findFirst().ifPresent(e -> {
                if (StringUtils.isNotBlank(e.getField1())) {
                    resp.setVideoExtractInfo(JSON.parseObject(e.getField1(), VideoExtractInfoModel.class));
                    for (VideoExtractInfoModel.VideoExtractInfoItem item : resp.getVideoExtractInfo().getItems()) {
                        if (Objects.isNull(item.getMeta())) {
                            item.setMeta(new JSONObject());
                        }
//                        item.getMeta().put("recordingTime",StringComUtils.convertStr(LocalDateTime.now()));
//                        if (!item.getMeta().containsKey("deviceName")) {
//                            item.getMeta().put("deviceName", resp.getTitle());
//                        }
//                        item.getMeta().put("ssdx","受审对象");
//                        item.getMeta().put("ftry","访谈人员");
//                        item.getMeta().put("jcxw","检查行为");
                    }

                }
            });
        });
        return new BaseResp<>(apiResourceInfoRespList);
    }

    @PostMapping("/resource/update")
    @ApiOperation(value = "更新文件信息", tags = "api")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> updateResource(@RequestBody @Validated ApiUpdateResourceReq req) {
        KnowledgeResourceEntity resource = fileBiz.get(req.getResourceId());
        DocUpdateReq docUpdateReq = new DocUpdateReq();
        docUpdateReq.setId(resource.getId());
        docUpdateReq.setTitle(req.getFileName());
        docUpdateReq.setExtInfo(JSON.toJSONString(req.getCustomFields()));
        docBiz.update(docUpdateReq);
        return new BaseResp<>();
    }
}
