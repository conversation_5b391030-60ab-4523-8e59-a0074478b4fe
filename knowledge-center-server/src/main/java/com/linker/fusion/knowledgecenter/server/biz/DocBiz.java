package com.linker.fusion.knowledgecenter.server.biz;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.base.enums.GlobalErrorCodeEnum;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.AasWorkflowClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowResultResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowStatus;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IKnowPowerResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IUserCenterService;
import com.linker.fusion.knowledgecenter.infrastructure.config.AasWorkflowProperties;
import com.linker.fusion.knowledgecenter.infrastructure.config.AppInfoConfig;
import com.linker.fusion.knowledgecenter.infrastructure.config.ExtMappingConfig;
import com.linker.fusion.knowledgecenter.infrastructure.entity.*;
import com.linker.fusion.knowledgecenter.infrastructure.enums.*;
import com.linker.fusion.knowledgecenter.infrastructure.es.ChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.*;
import com.linker.fusion.knowledgecenter.infrastructure.model.task.TaskContentForDocReChunk;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.server.convert.DocConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.core.BatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.EnableReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.*;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.rpc.FileDeleteRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.rpc.FileImportRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.rpc.SegmentReChunkReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.rpc.VideoCleanRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.ChunkItemResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.DocInfoEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.KnowledgeGroupParentResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthApproveService;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthLevelService;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthService;
import com.linker.fusion.knowledgecenter.service.domain.common.IUtilService;
import com.linker.fusion.knowledgecenter.service.domain.custom.CustomService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import com.linker.fusion.knowledgecenter.service.domain.group.model.SearchPreChekDto;
import com.linker.fusion.knowledgecenter.service.domain.img.ImgService;
import com.linker.fusion.knowledgecenter.service.domain.oprationlog.OperationLogProxyService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IAudioChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IDocChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IImageChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IVideoChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.AddChunkDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.NodeExceptionDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.StudyStatusDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TableEmbeddingData;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceExtService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceSegmentService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IVideoFrameIBaseService;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ChunkServiceFactory;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ResourceUtilService;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.TableIBaseService;
import com.linker.fusion.knowledgecenter.service.domain.strategy.TemplateService;
import com.linker.fusion.knowledgecenter.service.domain.task.ITaskService;
import com.linker.fusion.knowledgecenter.service.domain.task.dto.UpdateResourceChunkEnableDTO;
import com.linker.fusion.knowledgecenter.service.domain.video.merge.SubVideoService;
import com.linker.omagent.starter.manager.SseEmitterUtf8;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.*;

@Slf4j
@Component
public class DocBiz extends BaseBiz {
    private final ExecutorService fixedThreadPool = new ThreadPoolExecutor(8, Integer.MAX_VALUE,
            300L, TimeUnit.SECONDS,
            new SynchronousQueue<Runnable>());
    @Resource
    private IResourceService iResourceService;
    @Resource
    private ResourceUtilService resourceUtilService;
    @Resource
    private KnowledgeGroupService knowledgeGroupService;
    @Autowired
    private ExtMappingConfig extMappingConfig;
    @Resource
    private IDocChunkIBaseServiceResource iChunkIBaseService;
    @Resource
    private IResourceSegmentService iResourceSegmentService;
    @Resource
    private IVideoChunkIBaseServiceResource iVideoChunkIBaseService;
    @Resource
    private IVideoFrameIBaseService iVideoFrameIBaseService;
    @Resource
    private IAudioChunkIBaseServiceResource iAudioChunkIBaseService;
    @Resource
    private IImageChunkIBaseServiceResource iImageChunkIBaseService;
    @Resource
    private TableIBaseService tableIBaseService;
    @Autowired
    private ImgService imgService;
    @Autowired
    private CustomService customService;
    @Resource
    private AppInfoConfig appInfoConfig;
    @Resource
    private TemplateService templateService;
    @Resource
    private IUtilService utilService;
    @Value("${document.image.encode:false}")
    private Boolean imageEncode;
    @Resource
    private ChunkServiceFactory chunkServiceFactory;
    @Resource
    private SubVideoService subVideoService;
    @Resource
    private KnowledgeGroupFactory knowledgeGroupFactory;
    @Resource
    private AasWorkflowProperties aasWorkflowProperties;
    @Resource
    private AasWorkflowClient aaasWorkflowClient;
    @Autowired
    private IAuthService iAuthService;
    @Autowired
    private IAuthLevelService iAuthLevelService;
    @Autowired
    private IUserCenterService iUserCenterService;
    @Resource
    private IAuthApproveService iAuthApproveService;
    @Autowired
    private ITaskService iTaskService;
    @Autowired
    private IUtilService iUtilService;
    @Resource
    private IResourceExtService iResourceExtService;
    @Resource
    private OperationLogProxyService operationLogProxyService;

    public BasePaginResp<DocInfoEntityResp> page(@Valid DocPageReq req) {
        List<String> inResIds = req.getResIds();
        if (StringUtils.isNotBlank(req.getConversationId())) {
            KnowledgeGroupEntity group = knowledgeGroupService.getOrCreateByBiz(getTenantId(), getUserCode(), 1);
            if (Objects.isNull(group)) {
                return new BasePaginResp<>(0L, Collections.EMPTY_LIST);
            }
            req.setGroupId(group.getId());
            if (!req.getConversationId().equalsIgnoreCase("all")) {
                List<ResourceExtEntity> resourceExtList = iResourceExtService.list(ResourceExtTypeEnum.ConversationId, Collections.singletonList(req.getConversationId()));
                inResIds = resourceExtList.stream().map(ResourceExtEntity::getDocId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(inResIds)) {
                    return new BasePaginResp<>(0L, Collections.EMPTY_LIST);
                }
            }
        }


        SearchPreChekDto dto = knowledgeGroupFactory.searchPreCheck(req);
        Page<KnowledgeResourceEntity> ret = iResourceService.page(req.getPage(), req.getPageSize(), dto.getTenantId(), req.getTypes(), dto.getAuthedGroupIds(), req.getEnable(), req.getHandleStatus(), dto.getNotIds(), inResIds, req.getKeyword(), req.getOrder());
        List<KnowledgeGroupEntity> groupList = knowledgeGroupService.listByIds(ret.getRecords().stream().map(KnowledgeResourceEntity::getGroupId).collect(Collectors.toList()));
        List<Long> applyIds = iAuthApproveService.listAllIngIds(getTenantId(), getUserCode(), SourceTypeEnum.File.getValue());
        List<DocInfoEntityResp> respList = ret.getRecords().stream().map(entity -> {
            DocInfoEntityResp resp = DocConvert.INSTANCE.toResp(entity);
            resp.setAuthCodes(dto.getAuthedCodes(entity.getId(), entity.getGroupId()));
            resp.setAuthLevel(dto.getAuthedLevel(entity.getId(), entity.getGroupId()));
            groupList.stream().filter(g -> g.getId().equals(entity.getGroupId())).findFirst().ifPresent(g ->
                    {
                        resp.setImportantLevel(g.getImportantLevel());
                        resp.setVisibleType(g.getVisibleType());
                    }
            );
            resp.setSetUpDefault();
            resp.setApplying(applyIds.contains(resp.getId()));
            resp.setUrl(iUtilService.getSignedUrl(resp.getUrl()));
            resp.setPreviewSrc(iUtilService.getSignedUrl(resp.getPreviewSrc()));
            return resp;
        }).collect(Collectors.toList());
        return new BasePaginResp<>(ret.getTotal(), respList);
    }

    public SseEmitter statusListener(DocIdsReq req) {
        SseEmitter emitter = new SseEmitterUtf8(3600000L);
        emitter.onTimeout(emitter::complete);
        emitter.onCompletion(emitter::complete);
        emitter.onError(throwable -> {
            emitter.completeWithError(throwable);
            log.warn("SseEmitter 断开");
        });
        CompletableFuture.runAsync(() -> {
            try {
                Map<String, Integer> docStatusMap = new HashMap<>();
                List<String> docIds = new ArrayList<>(req.getDocIds());
                while (true) {
                    List<KnowledgeResourceEntity> resources = iResourceService.listByResIds(docIds);
                    for (KnowledgeResourceEntity resource : resources) {
                        Integer status = docStatusMap.get(resource.getDocId());
                        // 状态初始化或者变更
                        if (status == null || !status.equals(resource.getHandleStatus())) {
                            KnowledgeResourceEntity.HandleFailReason handleFailReason = resource.getHandleFailReason();
                            Integer code = handleFailReason == null ? null : handleFailReason.getCode();
                            String message = handleFailReason == null ? null : handleFailReason.getMessage();
                            StudyStatusDTO studyStatusDTO = new StudyStatusDTO(resource.getDocId(), resource.getHandleStatus(), code, message, resource.getSize(), resource.getThumbnail());
                            emitter.send(studyStatusDTO);
                            docStatusMap.put(resource.getDocId(), resource.getHandleStatus());
                        }
                        // 中止状态，停止循环
                        if (ProcessEnum.Success.getValue().equals(resource.getHandleStatus()) ||
                                ProcessEnum.Fail.getValue().equals(resource.getHandleStatus()) ||
                                ProcessEnum.ARCHIVE.getValue().equals(resource.getHandleStatus()) ||
                                ProcessEnum.MERGE_FAILED.getValue().equals(resource.getHandleStatus())) {
                            docIds.remove(resource.getDocId());
                        }
                    }
                    if (docIds.isEmpty()) {
                        emitter.complete();
                        break;
                    }
                    Thread.sleep(1000);
                    emitter.send(new StudyStatusDTO(null, null, 0, "keep-alive", 0L, ""));
                }
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });
        return emitter;
    }

    public SseEmitter statusListener(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        List<String> docIds = iResourceService.listByIds(ids).stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList());
        return statusListener(new DocIdsReq(docIds));
    }

    /**
     * 生效/失效
     */
    public BaseResp<Void> enable(EnableReq req) {
        iResourceService.updateEnable(req.getIds(), req.getEnable());
        UpdateResourceChunkEnableDTO updateResourceChunkEnableDTO = new UpdateResourceChunkEnableDTO(req.getIds(), req.getEnable());
        iTaskService.send(getTenantId(), getUserCode(), 0L, updateResourceChunkEnableDTO, UUID.randomUUID().toString(), TaskTypeEnum.UpdateResourceChunk);
        return new BaseResp<>();
    }

    /**
     * 删除
     */
    public BaseResp<Void> delete(BatchBaseReq req) {
        List<KnowledgeResourceEntity> resources = iResourceService.listByIds(req.getIds());
        iTaskService.send(getTenantId(), getUserCode(), 0L, resources.stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList()), UUID.randomUUID().toString(), TaskTypeEnum.DeleteRes);
        // resourceUtilService.delete(resources);
        iResourceService.deleteByIds(req.getIds());
        return new BaseResp<>();
    }

    /**
     * 导入文档
     */
    public DocInfoEntityResp importDoc(ImportReq req) {
        req.joinUrl();
        Long start = System.currentTimeMillis();
        log.debug("importDoc_checkAuth:" + (System.currentTimeMillis() - start));
        KnowledgeResourceEntity resource = new KnowledgeResourceEntity();
        resource.init(getTenantId(), getUserCode());
        resource.setDocId(UUID.randomUUID().toString());
        resource.setCount(0L);
        resource.setDescription("");
        resource.setEnable(true);
        resource.setSize(Objects.isNull(req.getFileSize()) ? 0L : req.getFileSize());
        JSONObject extInfo = new JSONObject();
        // 查询fields字段
        List<CustomerFieldDto> customFields = req.getCustomFields();
        List<Long> customerIdList = customFields.stream().map(CustomerFieldDto::getCustomerId).collect(Collectors.toList());
        Map<Long, CustomEntity> fieldMap = customService.getListToMap(customerIdList);
        // 设置扩展字段
        if (CollectionUtils.isNotEmpty(customFields)) {
            for (CustomerFieldDto dto : customFields) {
                if (StringUtils.isBlank(dto.getField()) || StringUtils.isBlank(dto.getValue()))
                    continue;
                CustomEntity custom = fieldMap.get(dto.getCustomerId());
                if (Objects.isNull(custom))
                    continue;
                customService.fillSearchExtInfo(custom.getFieldType(), dto.getField(), extInfo, dto.getValue());
            }

        }
        extInfo.put("sys_file_time", req.getFileTime());
        resource.setExtInfo(JSON.toJSONString(extInfo));
        resource.setSuffix(FileUtil.getSuffix(req.getFileName()).toLowerCase());
        resource.setType(extMappingConfig.getResourceType(resource.getSuffix()).getType());
        // 数据库可以用同一个字段
        resource.setTemplateId(req.getTemplateId());
        resource.setStrategy(formatStrategy(resource.getType(), req.getStrategyDoc(), req.getStrategyImage(), req.getStrategyVideo(), req.getStrategyAudio()));
        resource.setGroupId(req.getGroupId());
        resource.setHandleStatus(3);
        resource.setTitle(FileUtil.getPrefix(req.getFileName()));
        resource.setWorkflowId("");
        resource.setPreviewSrc("");
        resource.setUrl(req.getUrl());
        //目录上传
        if (StringUtils.isNotBlank(req.getRelativePath())) {
            AtomicReference<Long> groupId = new AtomicReference<>(Objects.isNull(resource.getGroupId()) ? 0L : resource.getGroupId());
            Arrays.stream(req.getRelativePath().split("/")).forEach(name -> {
                if (StringUtils.isBlank(name))
                    return;
                KnowledgeGroupEntity group = knowledgeGroupService.get(resource.getTenantId(), resource.getCreatorId(), groupId.get(), name);
                if (Objects.isNull(group)) {
                    group = new KnowledgeGroupEntity();
                    group.init(getTenantId(), getUserCode());
                    group.setType(KnowledgeTypeEnum.FILE.getType());

                    group.setDescription("根据路径自动创建的目录");
                    group.setName(name);
                    group.setIsLibrary(false);

                    group.setIsSync(false);
                    group.setSort(0d);
                    group.setBizType(0);

                    group.setLogo("");
                    group.setApproveProcessKey("");
                    if (groupId.get().equals(0L)) {
                        group.setImportantLevel(ResourceImportantLevelEnum.DEFAULT.getValue());
                        group.setVisibleType(ResourceVisibleTypeEnum.PUBLIC.getValue());
                        group.setParentId(0L);
                        group.setPath("");
                    } else {
                        KnowledgeGroupEntity parent = knowledgeGroupService.get(groupId.get());
                        group.setImportantLevel(parent.getImportantLevel());
                        group.setVisibleType(parent.getVisibleType());
                        group.setParentId(parent.getId());
                        group.setPath(parent.getSearchPath());
                    }


                    knowledgeGroupService.save(group);
                    operationLogProxyService.createFolderLogRecord(group.getId(), group.getName(), ResourceVisibleTypeEnum.getName(group.getVisibleType()));
                }
                groupId.set(group.getId());
            });
            resource.setGroupId(groupId.get());
        }

        KnowledgeGroupEntity group = null;
        //对话文件处理
        if (StringUtils.isNotBlank(req.getConversationId())) {
            group = knowledgeGroupService.getOrCreateByBiz(getTenantId(), getUserCode(), 1);
            resource.setGroupId(group.getId());
        } else if (Objects.nonNull(req.getGroupId()) && req.getGroupId().equals(-2L)) {
            group = knowledgeGroupService.getOrCreateByBiz(getTenantId(), getUserCode(), 2);
            resource.setGroupId(group.getId());
        }
        if (!resource.getGroupId().equals(-1L)) {
            if (knowledgeGroupService.isSync(req.getGroupId())) {
                throw new ServiceException(GROUP_SYNC_LIMIT, "导入文件");
            }
            KnowledgeResourceEntity existsRes = iResourceService.getByGroupId_Title_Suffix(resource.getGroupId(), resource.getTitle(), resource.getSuffix());
            if (Objects.nonNull(existsRes)) {
                if (req.getRenameType().equals(RenameTypeEnum.Skip.getValue())) {
                    return DocConvert.INSTANCE.toResp(existsRes);
                }
                if (req.getRenameType().equals(RenameTypeEnum.Cover.getValue())) {
                    resourceUtilService.delete(Collections.singletonList(existsRes));
                    resource.setId(existsRes.getId());
                    resource.setDocId(existsRes.getDocId());
                }
                if (req.getRenameType().equals(RenameTypeEnum.Rename.getValue())) {
                    String title = iResourceService.getNewTitle(resource.getGroupId(), resource.getTitle(), resource.getSuffix());
                    resource.setTitle(title);
                }
            }
            if (Objects.isNull(group)) {
                group = knowledgeGroupService.getNotNull(resource.getGroupId());
            }
        }

        limitCheck(resource.getGroupId(), Objects.isNull(group) ? null : group.getVisibleType(), resource.getSize());
        List<String> movedUrls = new ArrayList<>();
        try {
            String url = utilService.moveTempToDefault(resource.getUrl(), StoragePathEnum.File.getValueByUrl(resource.getTenantId(), resource.getDocId(), resource.getUrl()));
            movedUrls.add(url);
            resource.setUrl(url);
            moveAttach(resource.getTenantId(), resource.getDocId(), extInfo, movedUrls);
            resource.setExtInfo(JSON.toJSONString(extInfo));
        } catch (Exception ex) {
            movedUrls.forEach(url -> utilService.deleteFile(url));
            throw ex;
        }
        if (StringUtils.isBlank(resource.getUrl())) {
            resource.setUrl(req.getUrl());
        }


        iResourceService.saveOrUpdate(resource);

        if (StringUtils.isNotBlank(req.getConversationId())) {
            iResourceExtService.save(resource, ResourceExtTypeEnum.ConversationId, req.getConversationId());
            iResourceExtService.save(resource, ResourceExtTypeEnum.Expired, StringComUtils.convertStr(resource.getCreateTime().plusDays(30)));
        }
        resourceUtilService.handResource(resource, req.getTemplateId(), resource.getStrategy());

        if (resource.getGroupId().equals(-1L) || Objects.isNull(group)) {
            return DocConvert.INSTANCE.toResp(resource);
        }

        operationLogProxyService.importDowComplete(resource.getTitle(), group.getId(), resource.getDocId());
        List<KnowledgeGroupEntity> parents = knowledgeGroupService.getParents(group);
        parents.add(group);
        DocInfoEntityResp resp = DocConvert.INSTANCE.toResp(resource);
        resp.setGroupParentResp(new KnowledgeGroupParentResp(group.getId(), group.getType(), group.getVisibleType(), parents));
        resp.setSetUpDefault();
        return resp;
    }

    /**
     * 校验文件限制
     *
     * @param groupId 目录Id
     */
    private void limitCheck(Long groupId, Integer visibleType, Long size) {
        //todo 看情况是否需要加上redis锁 多并发的情况
        if (groupId != -1) {
            if (Objects.nonNull(appInfoConfig.getFileLimit()) && appInfoConfig.getFileLimit() > 0) {
                Long count = iResourceService.getCount(getTenantId());
                if (count >= appInfoConfig.getFileLimit()) {
                    throw new ServiceException(RESOURCE_COUNT_LIMIT, String.valueOf(Math.max(0, appInfoConfig.getFileLimit() - count)));
                }
            }
            IKnowPowerResp iKnowPowerResp = iUserCenterService.getIknowPower(getTenantId());
            if (ResourceVisibleTypeEnum.PERSONAL.getValue().equals(visibleType)) {
                if (Objects.nonNull(iKnowPowerResp.getMySpace()) && iKnowPowerResp.getMySpace() > 0) {
                    Long used = iResourceService.sumSizeByVisibleType(getTenantId(), getUserCode(), ResourceVisibleTypeEnum.PERSONAL);
                    if (used + size > iKnowPowerResp.getMySpace()) {
                        throw new ServiceException(RESOURCE_SIZE_LIMIT);
                    }
                }
            } else {
                if (Objects.nonNull(iKnowPowerResp.getOrgSpace()) && iKnowPowerResp.getOrgSpace() > 0) {
                    Long used = iResourceService.sumSizeByVisibleType(getTenantId(), null, ResourceVisibleTypeEnum.PUBLIC);
                    if (used + size > iKnowPowerResp.getOrgSpace()) {
                        throw new ServiceException(RESOURCE_SIZE_LIMIT);
                    }
                }
            }
        } else {
            if (Objects.nonNull(appInfoConfig.getTempLimit())) {
                Long count = iResourceService.getTempCount(getTenantId());
                if (count >= appInfoConfig.getTempLimit()) {
                    throw new ServiceException(RESOURCE_COUNT_LIMIT, String.valueOf(Math.max(0, appInfoConfig.getTempLimit() - count)));
                }
            }
        }
    }

    private String formatStrategy(Integer type, DocStrategyModel docStrategyModel, ImageStrategyModel imageStrategyModel, VideoStrategyModel videoStrategyModel, AudioStrategyModel audioStrategyModel) {
        switch (FileTypeEnum.valueOf(type)) {
            case DOCUMENT:
                return Objects.nonNull(docStrategyModel) ? JSON.toJSONString(docStrategyModel) : "{}";
            case IMAGE:
                return Objects.nonNull(imageStrategyModel) ? JSON.toJSONString(imageStrategyModel) : "{}";
            case VIDEO:
                return Objects.nonNull(videoStrategyModel) ? JSON.toJSONString(videoStrategyModel) : "{}";
            case AUDIO:
                return Objects.nonNull(audioStrategyModel) ? JSON.toJSONString(audioStrategyModel) : "{}";
        }
        return "{}";
    }


    public void importCallback(String docId, WorkflowResultResp workflowResultResp) {
        KnowledgeResourceEntity resourceEntity = iResourceService.getNotNull(docId);
        String workflowInstanceId = resourceEntity.getWorkflowId();
        log.info("导入结果回调 docId:{}", docId);
//        WorkflowResultResp workflowResultResp = aaasWorkflowClient.results(workflowInstanceId).getData();
        log.info("导入结果 {}", JSON.toJSONString(workflowResultResp));
        long startTime = Long.valueOf(workflowResultResp.getInput().getOrDefault("startTimestamp", System.currentTimeMillis()).toString());
        long l = System.currentTimeMillis() - startTime;
        KnowledgeResourceEntity resource = new KnowledgeResourceEntity();
        resource.setId(resourceEntity.getId());
        resource.setUpdateTime(LocalDateTime.now());
        String message = "";
        Integer code = 0;
        if (workflowResultResp == null) {
            code = 500;
            message = "工作流执行异常";
            resource.setHandleStatus(ProcessEnum.Fail.getValue());
        } else if (WorkflowStatus.COMPLETED.equals(workflowResultResp.getStatus())) {
            resource.setHandleStatus(ProcessEnum.Success.getValue());
        } else {
            message = workflowResultResp.getMessage();
            log.info("导入失败 {}", message);
            NodeExceptionDTO nodeExceptionDTO = format(message);
            code = nodeExceptionDTO.getCode();
            message = nodeExceptionDTO.getMessage();
            if (ProcessEnum.MERGING.getValue().equals(resourceEntity.getHandleStatus())) {
                resource.setHandleStatus(ProcessEnum.MERGE_FAILED.getValue());
            } else {
                resource.setHandleStatus(ProcessEnum.Fail.getValue());
            }
        }
        resource.setHandleFailReason(new KnowledgeResourceEntity.HandleFailReason(code, message, l));
        iResourceService.updateById(resource);
    }

    private NodeExceptionDTO format(String message) {
        NodeExceptionDTO nodeExceptionDTO = null;
        if (StringUtils.isNotBlank(message)) {
            String extractContent = extractContent(message);
            if (StringUtils.isNotBlank(extractContent)) {
                try {
                    nodeExceptionDTO = JSON.parseObject(extractContent, NodeExceptionDTO.class);
                } catch (Exception e) {
                    log.warn("格式化错误信息失败 {}", message);
                }
            }
        }
        if (nodeExceptionDTO == null) {
            nodeExceptionDTO = new NodeExceptionDTO(500, "未知错误，请联系平台处理");
        }
        return nodeExceptionDTO;
    }

    private String extractContent(String input) {
        if (input == null || input.isEmpty()) {
            return null;
        }
        Pattern pattern = Pattern.compile("'(.*?)'");
        Matcher matcher = pattern.matcher(input);
        return matcher.find() ? matcher.group(1) : null;
    }

    public void archive(BatchBaseReq req) {
        List<KnowledgeResourceEntity> resources;
        if (CollectionUtils.isNotEmpty(req.getIds())) {
            resources = iResourceService.listByIds(req.getIds());
        } else {
            resources = iResourceService.listByResIds(req.getDocIds());
        }
        if (resources.stream().anyMatch(s -> !ProcessEnum.Success.getValue().equals(s.getHandleStatus()))) {
            throw new ServiceException(GlobalErrorCodeEnum.RESOURCE_LIMIT_REACHED, "仅学习成功的文件可以归档");
        }
        iResourceService.updateStatus(resources.stream().map(BaseEntity::getId).collect(Collectors.toList()), ProcessEnum.ARCHIVE);
    }

    /**
     * 判断名称是否存在
     *
     * @param req
     * @return
     */
    public BaseResp<Boolean> nameExists(NameExistsReq req) {
        KnowledgeResourceEntity existsRes = iResourceService.getByGroupId_Title_Suffix(req.getGroupId(), FileUtil.getPrefix(req.getFileName()).trim(), FileUtil.getSuffix(req.getFileName()).trim());
        return new BaseResp<>(Objects.nonNull(existsRes));
    }

    /**
     * 重新学习
     */
    public void redo(RedoReq req) {
        List<KnowledgeResourceEntity> docs = iResourceService.listByIds(req.getIds());
        if (docs.stream().anyMatch(s -> ProcessEnum.ARCHIVE.getValue().equals(s.getHandleStatus()))) {
            throw new ServiceException(RESOURCE_ARCHIVE_LIMIT, "不支持重新学习");
        }
        docs.forEach(doc -> {
            if (!doc.getHandleStatus().equals(ProcessEnum.Executing.getValue())) {
                resourceUtilService.handResource(doc, req.getTemplateId(), formatStrategy(doc.getType(), req.getStrategyDoc(), req.getStrategyImage(), req.getStrategyVideo(), req.getStrategyAudio()));
            }
        });
    }

    /**
     * 批量重新学习
     */
    public void batchRedo(RedoBatchReq req) {
        List<KnowledgeResourceEntity> docs;
        if (CollectionUtils.isNotEmpty(req.getIds())) {
            docs = iResourceService.listByIds(req.getIds());
        } else if (CollectionUtils.isNotEmpty(req.getResIds())) {
            docs = iResourceService.listByResIds(req.getResIds());
        } else {
            throw new ServiceException(GlobalErrorCodeEnum.MISSING_PARAMETER);
        }
        if (docs.stream().anyMatch(s -> ProcessEnum.ARCHIVE.getValue().equals(s.getHandleStatus()))) {
            throw new ServiceException(RESOURCE_ARCHIVE_LIMIT, "不支持重新学习");
        }
        docs.forEach(doc -> {
            if (!doc.getHandleStatus().equals(ProcessEnum.Executing.getValue())) {
                resourceUtilService.handResource(doc, StringUtils.isBlank(req.getStrategy()) ? doc.getTemplateId() : null, StringUtils.isBlank(req.getStrategy()) ? doc.getStrategy() : req.getStrategy());
            }
        });
    }

    /**
     * 租户重新学习
     */
    public List<KnowledgeResourceEntity> telRedo(RedoReq req) {
        if (StringUtils.isNotBlank(req.getTenantId())) {
            List<KnowledgeResourceEntity> listErrorIds = iResourceService.top(req.getTenantId(), true, ProcessEnum.Fail, FileTypeEnum.DOCUMENT, req.getSize());
            List<KnowledgeGroupEntity> groups = knowledgeGroupService.listByIds(listErrorIds.stream().map(KnowledgeResourceEntity::getGroupId).collect(Collectors.toList()));
//            groups.stream().forEach(g -> {
//                iAuthService.checkEditAuth(token, g);
//            });

            listErrorIds.forEach(x -> {
                resourceUtilService.handResource(x, req.getTemplateId(), formatStrategy(x.getType(), req.getStrategyDoc(), req.getStrategyImage(), req.getStrategyVideo(), req.getStrategyAudio()));
            });
            return listErrorIds;
        }
        return new ArrayList<>();
    }


    public BaseResp<TaskEntity> reChunk(SegmentReChunkReq req) {
        ResourceSegmentEntity segment = iResourceSegmentService.getNotNull(req.getSegmentId());
        KnowledgeResourceEntity doc = iResourceService.getNotNull(segment.getDocId());
        TaskContentForDocReChunk taskContent = new TaskContentForDocReChunk();
        taskContent.setSegmentId(req.getSegmentId());
        segment.setStatus(ProcessEnum.Executing.getValue());
        iResourceSegmentService.updateById(segment);
        try {
            chunkServiceFactory.deleteBySegment(segment);
            DocStrategyModel docStrategyModel = JSON.parseObject(doc.getStrategy(), DocStrategyModel.class);
            List<ChunkEntity> chunkEntityList = iResourceSegmentService.reChunk(doc, docStrategyModel, segment);
            iChunkIBaseService.add(doc.getTenantId(), chunkEntityList, true);
            segment.setStatus(ProcessEnum.Success.getValue());
        } catch (Exception ex) {
            segment.setStatus(ProcessEnum.Fail.getValue());
            log.error("重新分块失败：{}", ex.getMessage(), ex);
        }
        iResourceSegmentService.updateById(segment);
        // TaskEntity task = iTaskService.send(getTenantId(), getUserCode(), 0L, taskContent, req.getSegmentId(), TaskTypeEnum.ReChunk);
        return new BaseResp<>(null);
    }


    /**
     * 分片列表
     *
     * @param req
     * @return
     */
    public BaseResp<List<ChunkItemResp>> chunk(ChunkReq req) {
        List<ChunkEntity> chunks = iChunkIBaseService.list(getTenantId(), req.getSegmentId(), req.getTitle(), req.getContent());
        List<ChunkItemResp> resp = chunks.stream().map(ChunkItemResp::new).collect(Collectors.toList());
        return new BaseResp<>(resp);
    }

    /**
     * 更新分片
     */
    public void updateChunk(UpdateChunkReq req) {
        chunkServiceFactory.update(FileTypeEnum.DOCUMENT, getTenantId(), req.getUid(), req.getContent(), req.getUrl());
    }

    /**
     * 添加分片
     *
     * @param req
     * @return
     */
    public BaseResp<Void> addChunk(AddChunkReq req) {
        StopWatch stopWatch = new StopWatch("addChunk");
        AddChunkDTO dto = chunkServiceFactory.addPre(req.getDocId(), req.getContent(), req.getUrl(), imageEncode);
        KnowledgeResourceEntity resource = dto.getFile();
        ChunkEntity chunk = new ChunkEntity();
        chunk.setEnable(Boolean.TRUE.equals(resource.getEnable()) ? 1 : 0);
        chunk.setGroupId(resource.getGroupId());
        chunk.setChunkId(UUID.randomUUID().toString());
        chunk.setSegmentId(req.getSegmentId());
        chunk.setSort(Objects.nonNull(req.getSort()) ? req.getSort() : 0);
        chunk.setPosition(req.getPosition());
        chunk.setTitle(resource.getTitle());
        chunk.setContent(req.getContent());
        chunk.setDocId(req.getDocId());
        chunk.setContentVector(dto.getContentVector());
        chunk.setCreateTime(StringComUtils.convertStr(LocalDateTime.now()));
        chunk.setCreateId(UserContext.getUser().getUser().getUserCode());
        chunk.setTenantId(UserContext.getUser().getTenantInfoDTO().getTenantId());
        chunk.setPage(req.getPage());
        chunk.setMeta(JSON.parseObject(resource.getExtInfo()));
        chunk.setSrcType(req.getSrcType());
        chunk.setUrl(dto.getNewUrl());
        stopWatch.start("ibaseAdd");
        iChunkIBaseService.add(UserContext.getUser().getTenantInfoDTO().getTenantId(), Collections.singletonList(chunk), true);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return new BaseResp<>();
    }

    /**
     * 删除分片
     *
     * @param req
     * @return
     */
    public BaseResp<Void> deleteChunk(DeleteChunkReq req) {
        chunkServiceFactory.delete(FileTypeEnum.DOCUMENT, getTenantId(), req.getChunkIds());
        return new BaseResp<>();
    }

    /**
     * 预览
     *
     * @param id    文件id
     * @param docId 文件uuId
     * @return 详情
     */
    public DocInfoEntityResp preview(Long id, String docId) {
        KnowledgeResourceEntity resource = null;
        if (id != null && id > 0) {
            resource = iResourceService.getNotNull(id);
        } else {
            resource = iResourceService.getNotNull(docId);
        }
        if (resource.getDeleted()) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "资源");
        }
        DocInfoEntityResp resp = DocConvert.INSTANCE.toResp(resource);
        resp.setSetUpDefault();
        if (resource.getGroupId().equals(-1L)) {
            return resp;
        }
        KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(resource.getGroupId());
        resp.setSource(group.getIsSync() ? 1 : 0);
        List<KnowledgeGroupEntity> parents = knowledgeGroupService.getParents(group);
        parents.add(group);
        resp.setGroupParentResp(new KnowledgeGroupParentResp(group.getId(), group.getType(), group.getVisibleType(), parents));
        resp.setUrl(iUtilService.getSignedUrl(resource.getUrl()));
        resp.setPreviewSrc(iUtilService.getSignedUrl(resource.getPreviewSrc()));
        resp.setVisibleType(group.getVisibleType());
        resp.setImportantLevel(group.getImportantLevel());
        if (ResourceVisibleTypeEnum.PERSONAL.getValue().equals(group.getVisibleType())) {
            resp.setAuthCodes(Arrays.stream(AuthNodeEnum.values()).map(Enum::toString).collect(Collectors.toList()));
        } else {
            AuthAssModel authAssModel = new AuthAssModel(getTenantId(), getUserCode(), getDepartmentCodes(), resource.getId().toString(), SourceTypeEnum.File);
            authAssModel.setGroup(group);
            authAssModel.setIsManager(iUserCenterService.isManager(getToken(), getTenantId(), getUserCode()));
            authAssModel.setParents(parents);
            authAssModel.setRoot(parents.get(0));
            iAuthService.getAuthNew(authAssModel);
            if (authAssModel.getAuth().getAuthLevel() > 0) {
                resp.setAuthCodes(authAssModel.getAuthedCodes(iAuthLevelService.mapCodes(getTenantId()), group.getIsSync() ? AuthNodeEnum.syncCodes() : null));
            }
        }
        return resp;
    }


    /**
     * 更新
     *
     * @param req
     * @return
     */
    public BaseResp<Void> update(DocUpdateReq req) {
        KnowledgeResourceEntity resource = iResourceService.getNotNull(req.getId());
        if (ProcessEnum.ARCHIVE.getValue().equals(resource.getHandleStatus())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_SAVE, "已归档的文件");
        }
        List<String> oldAttachUrls = iResourceService.getAttachUrls(resource);
        resource.setSubheading(req.getSubheading());
        resource.setUpdateTime(LocalDateTime.now());
        resource.setUpdateId(getUserCode());
        boolean titleChanged = false;
        if (!resource.getTitle().equals(req.getTitle())) {
            KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(resource.getGroupId());
            if (group.getIsSync()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_RENAME, "同步文件");
            }
            KnowledgeResourceEntity exists = iResourceService.getByGroupId_Title_Suffix(group.getId(), req.getTitle(), resource.getSuffix());
            if (Objects.nonNull(exists) && !exists.getId().equals(resource.getId())) {
                throw new ServiceException(DUPLICATE_NAME);
            }
            resource.setTitle(req.getTitle());
            titleChanged = true;
        }

        if (StringUtils.isNotBlank(req.getExtInfo())) {
            JSONObject existExtInfo = JSON.parseObject(resource.getExtInfo());
            existExtInfo = existExtInfo == null ? new JSONObject() : existExtInfo;
            // 获取入参扩展信息，获取租户下所有的扩展信息
            JSONObject updateExtInfo = JSONObject.parseObject(req.getExtInfo());
            List<CustomEntity> customEntities = customService.queryFillInfo(getTenantId(), resource.getType());
            Map<String, CustomEntity> customCacheMap = new HashMap<>();

            // 校验必填
            for (CustomEntity custom : customEntities) {
                // 存缓存
                String filed = "customer_" + custom.getField();
                customCacheMap.put(filed, custom);
                // 校验
                if (!custom.getRequired()) {
                    continue;
                }
                if (!updateExtInfo.containsKey(filed) || Objects.isNull(updateExtInfo.get(filed)) || StringUtils.isBlank(updateExtInfo.get(filed).toString())) {
                    throw new ServiceException(CUSTOM_NOT_EMPTY, custom.getName());
                }
            }
            // extInfo取出来，增加查询字段
            JSONObject addCache = new JSONObject();
            for (Map.Entry<String, Object> entry : updateExtInfo.entrySet()) {
                String field = entry.getKey();
                String value = entry.getValue() == null ? null : entry.getValue().toString();

                // 取扩展字段缓存
                CustomEntity custom = customCacheMap.get(field);
                // 系统字段不处理,且extInfo存的字段是完整字段，需要特殊处理一下
                if (field.startsWith("customer_") && custom != null) {
                    customService.fillSearchExtInfo(custom.getFieldType(), custom.getField(), addCache, value);
                }
            }
            existExtInfo.putAll(addCache);

            List<String> movedUrls = new ArrayList<>();
            try {
                moveAttach(resource.getTenantId(), resource.getDocId(), existExtInfo, movedUrls);
            } catch (Exception ex) {
                movedUrls.forEach(url -> utilService.deleteFile(url));
                throw ex;
            }

            resource.setExtInfo(JSONObject.toJSONString(existExtInfo));
        }
        iResourceService.saveOrUpdate(resource);
        List<String> newAttachUrls = iResourceService.getAttachUrls(resource);
        List<String> deletedUrls = ListUtils.subtract(oldAttachUrls, newAttachUrls);
        deletedUrls.forEach(url -> utilService.deleteFile(url));
        // 更新es
        updateIBaseChunkMeta(resource);
        if (titleChanged) {
            updateIBaseChunkTitle(resource);
        }
        return new BaseResp<>();
    }

    /**
     * 移动附件文件
     *
     * @param docId
     * @param extInfo
     * @param movedUrls
     */
    public void moveAttach(String teantId, String docId, JSONObject extInfo, List<String> movedUrls) {
        Set<String> keys = extInfo.keySet();
        for (String key : keys) {
            Object value = extInfo.get(key);
            if (Objects.isNull(value))
                continue;
            if (value instanceof String) {
                String strValue = value.toString();
                if (strValue.startsWith("[") && strValue.endsWith("]")) {
                    JSONArray arr = JSONArray.parseArray(strValue);
                    for (Object item : arr) {
                        if (!(item instanceof JSONObject)) {
                            continue;
                        }
                        JSONObject obj = (JSONObject) item;
                        if (obj.containsKey("url") && StringUtils.isNotBlank(obj.getString("url"))) {
                            String movedUrl = utilService.moveTempToDefault(obj.getString("url"), StoragePathEnum.Attach.getValueByUrl(teantId, docId, obj.getString("url")));
                            if (StringUtils.isNotBlank(movedUrl)) {
                                strValue = strValue.replace(obj.getString("url"), movedUrl);
                                movedUrls.add(movedUrl);
                            }
                        }
                    }
                    extInfo.put(key, strValue);
                }
                ;

            }
        }
    }

    /**
     * 获取图片caption
     *
     * @param url
     * @return
     */
    public String imageCaption(String url) {
        return imgService.odMllmCaption(url);
    }


    /**
     * 移动
     */
    public void move(MoveReq req) {
        //校验目标分组权限
        KnowledgeGroupEntity target = knowledgeGroupService.getNotNull(req.getGroupId());
        //校验文件权限
        List<KnowledgeResourceEntity> resources = iResourceService.listByIds(req.getIds());
        if (CollectionUtils.isEmpty(resources)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "资源");
        }
        //校验文件所在分组权限
        List<KnowledgeGroupEntity> groups = knowledgeGroupService.listByIds(resources.stream().map(KnowledgeResourceEntity::getGroupId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(groups)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "资源所在分组");
        }

        for (KnowledgeGroupEntity source : groups) {
            if (source.getVisibleType() > target.getVisibleType()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录可见等级不足,");
            }
            if (source.getImportantLevel() > target.getImportantLevel()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录重要等级不足,");
            }
        }
        //如果存在从个人空间移动到分组空间，需要校验分组空间是否已满
        if (ResourceVisibleTypeEnum.PUBLIC.getValue().equals(target.getVisibleType())) {
            List<KnowledgeGroupEntity> personGroups = groups.stream().filter(s -> ResourceVisibleTypeEnum.PERSONAL.getValue().equals(s.getVisibleType())).collect(Collectors.toList());
            List<Long> personGroupIds = personGroups.stream().map(KnowledgeGroupEntity::getId).collect(Collectors.toList());
            Long size = resources.stream().filter(s -> personGroupIds.contains(s.getGroupId())).mapToLong(KnowledgeResourceEntity::getSize).sum();
            if (size > 0) {
                IKnowPowerResp iKnowPowerResp = iUserCenterService.getIknowPower(getTenantId());
                if (iKnowPowerResp.getOrgSpace() > 0) {
                    Long used = iResourceService.sumSizeByVisibleType(getTenantId(), null, ResourceVisibleTypeEnum.PUBLIC);
                    if (used + size > iKnowPowerResp.getOrgSpace()) {
                        throw new ServiceException(RESOURCE_SIZE_LIMIT);
                    }
                }
            }
        }


        resources.forEach(resource -> {
            KnowledgeResourceEntity exists = iResourceService.getByGroupId_Title_Suffix(req.getGroupId(), resource.getTitle(), resource.getSuffix());
            if (Objects.nonNull(exists) && !exists.getDocId().equals(resource.getDocId())) {
                String title = iResourceService.getNewTitle(req.getGroupId(), resource.getTitle(), resource.getSuffix());
                resource.setTitle(title);
                updateIBaseChunkTitle(resource);
            }
            resource.setGroupId(req.getGroupId());
            iResourceService.saveOrUpdate(resource);
        });
        iResourceService.updateGroupIdByDocIds(resources.stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList()), req.getGroupId());
        String tenantId = getTenantId();
        CompletableFuture.allOf(resources.stream()
                .map(resource -> CompletableFuture.runAsync(() -> {
                    FileTypeEnum fileTypeEnum = FileTypeEnum.valueOf(resource.getType());
                    switch (fileTypeEnum) {
                        case DOCUMENT:
                            iChunkIBaseService.updateGroupId(tenantId, resource.getDocId(), req.getGroupId());
                            break;
                        case TABLE:
                            List<String> uIds = tableIBaseService.getAllUIds(tenantId, resources.stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList()));
                            List<TableEmbeddingData> docs = new ArrayList<>(uIds.size());
                            uIds.forEach(uid -> {
                                TableEmbeddingData doc = new TableEmbeddingData();
                                doc.setUid(uid);
                                doc.setGroupId(target.getId());
                                docs.add(doc);
                            });
                            tableIBaseService.batchUpdate(tenantId, docs);
                        case VIDEO:
                            iVideoChunkIBaseService.updateGroupId(tenantId, resource.getDocId(), req.getGroupId());
                            iAudioChunkIBaseService.updateGroupId(tenantId, resource.getDocId(), req.getGroupId());
                            iVideoFrameIBaseService.updateGroupId(tenantId, resource.getDocId(), req.getGroupId());
                            break;
                        case AUDIO:
                            iAudioChunkIBaseService.updateGroupId(tenantId, resource.getDocId(), req.getGroupId());
                            break;
                        case IMAGE:
                            iImageChunkIBaseService.updateGroupId(tenantId, resource.getDocId(), req.getGroupId());
                            break;
                    }
                }, fixedThreadPool)).toArray(CompletableFuture[]::new)).join();

    }

    /**
     * 重命名
     */
    public void rename(RenameReq req) {

        StopWatch stopWatch = new StopWatch("rename");
        stopWatch.start("getResource");
        KnowledgeResourceEntity resource = iResourceService.getNotNull(req.getId());
        stopWatch.stop();
        if (ProcessEnum.ARCHIVE.getValue().equals(resource.getHandleStatus())) {
            throw new ServiceException(RESOURCE_ARCHIVE_LIMIT, "不支持重命名");
        }
        stopWatch.start("getGroup");
        KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(resource.getGroupId());
        stopWatch.stop();
        if (group.getIsSync()) {
            throw new ServiceException(RESOURCE_SYNC_LIMIT, "不支持重命名");
        }
        stopWatch.start("getByGroupId_Title_Suffix");
        KnowledgeResourceEntity exists = iResourceService.getByGroupId_Title_Suffix(group.getId(), req.getName(), resource.getSuffix());
        stopWatch.stop();
        if (Objects.nonNull(exists) && !exists.getId().equals(resource.getId())) {
            throw new ServiceException(DUPLICATE_NAME);
        }
        resource.setTitle(req.getName());
        stopWatch.start("updateTitle");
        iResourceService.updateTitle(resource.getId(), resource.getTitle());
        stopWatch.stop();
        stopWatch.start("updateIBaseChunkTitle");
        updateIBaseChunkTitle(resource);
        stopWatch.stop();

        log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));

    }

    private void updateIBaseChunkTitle(KnowledgeResourceEntity resource) {
        FileTypeEnum fileTypeEnum = FileTypeEnum.valueOf(resource.getType());
        switch (fileTypeEnum) {
            case DOCUMENT:
                iChunkIBaseService.updateTitle(getTenantId(), resource.getDocId(), resource.getTitle());
                break;
            case VIDEO:
                iVideoChunkIBaseService.updateTitle(getTenantId(), resource.getDocId(), resource.getTitle());
                iAudioChunkIBaseService.updateTitle(getTenantId(), resource.getDocId(), resource.getTitle());
                iVideoFrameIBaseService.updateTitle(getTenantId(), resource.getDocId(), resource.getTitle());
                break;
            case AUDIO:
                iAudioChunkIBaseService.updateTitle(getTenantId(), resource.getDocId(), resource.getTitle());
                break;
            case IMAGE:
                iImageChunkIBaseService.updateTitle(getTenantId(), resource.getDocId(), resource.getTitle());
                break;
        }
    }

    private void updateIBaseChunkMeta(KnowledgeResourceEntity resource) {
        FileTypeEnum fileTypeEnum = FileTypeEnum.valueOf(resource.getType());
        switch (fileTypeEnum) {
            case DOCUMENT:
                iChunkIBaseService.updateMeta(resource.getTenantId(), resource.getDocId(), JSON.parseObject(resource.getExtInfo()));
                break;
            case VIDEO:
                iVideoChunkIBaseService.updateMeta(resource.getTenantId(), resource.getDocId(), resource.getJsonExtInfo());
                iAudioChunkIBaseService.updateMeta(resource.getTenantId(), resource.getDocId(), JSON.parseObject(resource.getExtInfo()));
                iVideoFrameIBaseService.updateMeta(resource.getTenantId(), resource.getDocId(), resource.getJsonExtInfo());
                break;
            case AUDIO:
                iAudioChunkIBaseService.updateMeta(resource.getTenantId(), resource.getDocId(), JSON.parseObject(resource.getExtInfo()));
                break;
            case IMAGE:
                iImageChunkIBaseService.updateMeta(resource.getTenantId(), resource.getDocId(), JSON.parseObject(resource.getExtInfo()));
                break;
        }
    }

    public String importFile(FileImportRpcReq req) {
        KnowledgeGroupEntity groupEntity = knowledgeGroupService.get(req.getGroupId());
        if (Objects.isNull(groupEntity)) {
            throw new ServiceException(NOT_EXIST, "分组");
        }
        FileTypeEnum fileTypeEnum = extMappingConfig.getResourceType(FileUtil.getSuffix(req.getFileName()));
        // 人大历史数据兼容 处理流程不为空 且第一次入库，查询是否已存在，存在则跳过，直接返回现有的docId
        if (req.getProcessFlow() != null && BooleanUtils.isFalse(req.getChange())) {
            // groupId + 文件名 + 后缀 = 唯一
            KnowledgeResourceEntity one = iResourceService.getByGroupId_Title_Suffix(req.getGroupId(), FileUtil.getPrefix(req.getFileName()), FileUtil.getSuffix(req.getFileName()), null);
            if (one != null) {
                // 视频文件，需要创建子视频
                if (FileTypeEnum.VIDEO.equals(fileTypeEnum)) {
                    SubVideoEntity subVideoEntity = buildSubVideo(req, req.getGroupId());
                    // 子视频设置成已合成
                    subVideoEntity.setOperation(0);
                    // 设置子视频管理的主视频docId
                    KnowledgeResourceEntity mainVideoEntity = iResourceService.getSingleVideoByGroupId(req.getGroupId());
                    subVideoEntity.setDocId(mainVideoEntity == null ? null : mainVideoEntity.getDocId());
                    subVideoService.save(subVideoEntity);
                    // 返回子视频的fileId
                    return subVideoEntity.getFileId();
                } else {
                    // 非视频文件，直接返回docId
                    return one.getDocId();
                }
            }
        }
        String fileId;
        if (FileTypeEnum.VIDEO.equals(fileTypeEnum) && Objects.equals(req.getProcessFlow(), 1)) {
            if (BooleanUtils.isTrue(req.getChange())) {
                subVideoService.deleteByGroupIdAndFileName(req.getGroupId(), req.getFileName());
            }
            SubVideoEntity subVideoEntity = buildSubVideo(req, req.getGroupId());
            fileId = subVideoEntity.getFileId();
            subVideoService.save(subVideoEntity);
        } else {
            KnowledgeResourceEntity resourceEntity = buildEntity(req, groupEntity);
            fileId = resourceEntity.getDocId();
            // 删除旧文件根据文件中心给的参数判断
            deleteRepeat(req, resourceEntity.getGroupId());
            if (Objects.equals(req.getProcessFlow(), 2)) {
                JSONObject npcExtInfo = getNpcExtInfo(groupEntity, FileTypeEnum.valueOf(resourceEntity.getType()));
                JSONObject jsonExtInfo = resourceEntity.getJsonExtInfo();
                jsonExtInfo.putAll(npcExtInfo);
                resourceEntity.setExtInfo(JSON.toJSONString(jsonExtInfo));
                resourceEntity.setTitle(npcExtInfo.getString("sys_group_name") + "/" + resourceEntity.getTitle());
                resourceEntity.setSubheading(npcExtInfo.getString("sys_subheading"));
            }
            iResourceService.saveOrUpdate(resourceEntity);
            resourceUtilService.handResource(resourceEntity, resourceEntity.getTemplateId(), resourceEntity.getStrategy());
        }

        return fileId;
    }

    private void deleteRepeat(FileImportRpcReq req, Long groupId) {
        if (BooleanUtils.isTrue(req.getChange())) {
            KnowledgeResourceEntity knowledgeResourceEntity = iResourceService.getByGroupId_Title_Suffix(groupId, FileUtil.getPrefix(req.getFileName()), FileUtil.getSuffix(req.getFileName()));
            resourceUtilService.delete(Lists.newArrayList(knowledgeResourceEntity));
        }
    }

    private SubVideoEntity buildSubVideo(FileImportRpcReq req, Long groupId) {
        SubVideoEntity subVideoEntity = new SubVideoEntity();
        subVideoEntity.setFileId(UUID.randomUUID().toString());
        subVideoEntity.setTitle(FileUtil.getPrefix(req.getFileName()));
        subVideoEntity.setSize(req.getFileSize());
        subVideoEntity.setSuffix(FileUtil.getSuffix(req.getFileName()));
        subVideoEntity.setUrl(req.getUrl());
        subVideoEntity.setTenantId(req.getTenantId());
        subVideoEntity.setCreatorId(req.getUserCode());
        subVideoEntity.setUpdateId(req.getUserCode());
        subVideoEntity.setCreateTime(LocalDateTime.now());
        subVideoEntity.setUpdateTime(LocalDateTime.now());
        subVideoEntity.setGroupId(groupId);
        subVideoEntity.setOperation(1);
        return subVideoEntity;
    }

    private KnowledgeResourceEntity buildEntity(FileImportRpcReq req, KnowledgeGroupEntity parent) {
        KnowledgeResourceEntity resourceEntity = new KnowledgeResourceEntity();
        resourceEntity.setDocId(UUID.randomUUID().toString());
        resourceEntity.setTitle(FileUtil.getPrefix(req.getFileName()));
        resourceEntity.setSize(req.getFileSize());
        resourceEntity.setCount(0L);
        resourceEntity.setSuffix(FileUtil.getSuffix(req.getFileName()));
        resourceEntity.setType(extMappingConfig.getResourceType(resourceEntity.getSuffix()).getType());
        resourceEntity.setEnable(true);
        resourceEntity.setDescription("FileScan");
        resourceEntity.setUrl(req.getUrl());
        resourceEntity.setTenantId(req.getTenantId());
        resourceEntity.setCreatorId(req.getUserCode());
        resourceEntity.setUpdateId(req.getUserCode());
        resourceEntity.setCreateTime(LocalDateTime.now());
        resourceEntity.setUpdateTime(LocalDateTime.now());
        resourceEntity.setHandleStatus(ProcessEnum.Fail.getValue());
        resourceEntity.setGroupId(parent.getId());
        resourceEntity.setGroupPath(knowledgeGroupService.getGroupPath(req.getTenantId(), parent.getId()));
//        resourceEntity.setSubheading(req.getSecondTitle());
        JSONObject extInfo = new JSONObject();
        extInfo.put("sys_file_time", StringComUtils.convertStr(resourceEntity.getUpdateTime()));
        if (CollectionUtils.isNotEmpty(req.getCustomFields())) {
            for (CustomerFieldDto dto : req.getCustomFields()) {
                if (StringUtils.isNotBlank(dto.getField())) {
                    extInfo.put("customer_" + dto.getField(), dto.getValue());
                }
            }
        }
        resourceEntity.setExtInfo(JSON.toJSONString(extInfo));
        resourceEntity.setPreviewSrc("");
        TemplateEntity defaultTemplate = templateService.getDefault(resourceEntity.getTenantId(), resourceEntity.getType());
        resourceEntity.setTemplateId(defaultTemplate == null ? null : defaultTemplate.getId());
        resourceEntity.setStrategy("{}");
        return resourceEntity;
    }

    public List<KnowledgeResourceEntity> delete(FileDeleteRpcReq req) {
        if (req.getProcessFlow() != null) {
            subVideoService.setOperation(req.getDocIds(), 2);
            return new ArrayList<>();
        } else {
            List<KnowledgeResourceEntity> fileList = iResourceService.listByResIds(req.getDocIds());
            resourceUtilService.delete(fileList);
            return fileList;
        }
    }

    public void videoMerge(List<Long> groupIds) {
        List<KnowledgeGroupEntity> groupEntityList = knowledgeGroupService.listByIds(groupIds);
        if (CollectionUtils.isEmpty(groupEntityList)) {
            return;
        }
        for (KnowledgeGroupEntity groupEntity : groupEntityList) {
            Long groupId = groupEntity.getId();
            // 分组下的视频文件有变动：新增、删除，需要重新合成
            if (subVideoService.groupNeedReMerge(groupId)) {
                JSONObject npcExtInfo = getNpcExtInfo(groupEntity, FileTypeEnum.VIDEO);
                List<SubVideoEntity> subVideoEntityList = subVideoService.listByGroupId(Collections.singletonList(groupId));
                if (CollectionUtils.isEmpty(subVideoEntityList)) {
                    continue;
                }
                // 名称中仅有英文和数字的视频才需要合成
                List<SubVideoEntity> mergeSubVideoList = subVideoEntityList.stream().filter(s -> !Validator.hasChinese(s.getTitle())).collect(Collectors.toList());
                List<SubVideoEntity> skipSubVideoList = subVideoEntityList.stream().filter(s -> Validator.hasChinese(s.getTitle())).collect(Collectors.toList());
                // mergeSubVideoList中需要合成的文件数量大于1个，才合成
                if (mergeSubVideoList.stream().filter(s -> s.getOperation() == 0 || s.getOperation() == 1).count() > 1) {
                    // 删除旧的视频文件
                    List<KnowledgeResourceEntity> list = iResourceService.list(FileTypeEnum.VIDEO, groupId);
                    list.stream().filter(KnowledgeResourceEntity::isMerge).map(KnowledgeResourceEntity::getId).findFirst().ifPresent(
                            id -> {
                                iResourceService.deleteByIds(Collections.singletonList(id));
                                iTaskService.send(groupEntity.getTenantId(), groupEntity.getCreatorId(), 0L, Collections.singletonList(id), UUID.randomUUID().toString(), TaskTypeEnum.DeleteRes);
                            }
                    );
                    subVideoMerge(mergeSubVideoList, groupEntity, new JSONObject(npcExtInfo));
                } else {
                    singleVideo(mergeSubVideoList, groupEntity, new JSONObject(npcExtInfo));
                }
                if (CollectionUtils.isNotEmpty(skipSubVideoList)) {
                    singleVideo(skipSubVideoList, groupEntity, new JSONObject(npcExtInfo));
                }
            }
        }
    }

    private JSONObject getNpcExtInfo(KnowledgeGroupEntity groupEntity, FileTypeEnum fileTypeEnum) {
        // 从当前目录，往父级寻找符合人大目录规则的目录，可能没有三、四级目录
        // \1视频\2022\2022.03-1200-大会工作****- - - \sub1\sub2\11.mp4
        //一级目录：2022
        //二级目录：2022.03-1200-大会工作****- - -
        //三级目录：sub1
        //四类目录：sub2
        // 副标题使用三四级目录名称：sub1-sub2
        String subheading = "";
        String groupName = groupEntity.getName();
        if (!isValidFolderName(groupName)) {
            subheading = groupName;
            KnowledgeGroupEntity parentGroup = knowledgeGroupService.get(groupEntity.getParentId());
            if (parentGroup != null) {
                groupName = parentGroup.getName();

                if (!isValidFolderName(groupName)) {
                    subheading = groupName + "-" + subheading;

                    parentGroup = knowledgeGroupService.get(parentGroup.getParentId());

                    if (parentGroup != null) {
                        groupName = parentGroup.getName();
                    }
                }
            }
        }
        // 从groupName拆分获取结构化字段值
        String[] split = groupName.split("-");
        String date = "";
        String time = "";
        String title = "";
        String location = "";
        String host = "";
        String photographer = "";
        String moderator = "";
        String participant = "";
        for (int i = 0; i < split.length; i++) {
            if (i == 0) {
                date = split[i];
            } else if (i == 1) {
                time = split[i];
                if (StringUtils.isBlank(time)) {
                    time = "0000";
                }
            } else if (i == 2) {
                title = split[i];
            } else if (i == 3) {
                location = split[i];
            } else if (i == 4) {
                host = split[i];
            } else if (i == 5) {
                photographer = split[i];
            } else if (i == 6) {
                moderator = split[i];
            } else if (i == 7) {
                participant = split[i];
            }
        }
        LocalDateTime dateTime = null;
        String datetimeStr = date + " " + time;
        try {
            dateTime = LocalDateTime.parse(datetimeStr, DateTimeFormatter.ofPattern("yyyy.MM.dd HHmm"));
            datetimeStr = StringComUtils.convertStr(dateTime);
        } catch (Exception e) {
            log.warn("时间转换失败：{}", datetimeStr);
        }
        if (StringUtils.isBlank(title)) {
            title = groupName;
        }
        JSONObject extInfo = new JSONObject();
        extInfo.put("sys_group_name", groupName);
        extInfo.put("sys_title", title);
        extInfo.put("sys_subheading", subheading);
        extInfo.put("sys_file_time", datetimeStr);
        extInfo.put("customer_meeting_date", StringComUtils.convertStr(dateTime));
        extInfo.put("customer_meeting_location", location);
        extInfo.put("customer_meeting_host", host);
        extInfo.put("customer_meeting_photographer", photographer);
        extInfo.put("customer_meeting_moderator", moderator);
        extInfo.put("customer_meeting_participant", participant);

        List<CustomEntity> customEntities = customService.queryFillInfo(groupEntity.getTenantId(), fileTypeEnum.getType());
        customService.fillSearchExtInfo(extInfo, customEntities);
        return extInfo;
    }

    private void subVideoMerge(List<SubVideoEntity> subVideoEntityList, KnowledgeGroupEntity groupEntity, JSONObject npcExtInfo) {
        KnowledgeResourceEntity resourceEntity = new KnowledgeResourceEntity();
        resourceEntity.setDocId(UUID.randomUUID().toString());
        resourceEntity.setTitle(npcExtInfo.getString("sys_title"));
        resourceEntity.setSize(-1L);
        resourceEntity.setCount(0L);
        resourceEntity.setSuffix("mp4");
        resourceEntity.setType(FileTypeEnum.VIDEO.getType());
        resourceEntity.setEnable(true);
        resourceEntity.setDescription("FileScan");
        resourceEntity.setUrl("");
        resourceEntity.setTenantId(groupEntity.getTenantId());
        resourceEntity.setCreatorId(groupEntity.getCreatorId());
        resourceEntity.setUpdateId(groupEntity.getUpdateId());
        resourceEntity.setCreateTime(LocalDateTime.now());
        resourceEntity.setUpdateTime(LocalDateTime.now());
        resourceEntity.setHandleStatus(ProcessEnum.Fail.getValue());
        resourceEntity.setGroupId(groupEntity.getId());
        resourceEntity.setGroupPath("");
        resourceEntity.setSubheading(npcExtInfo.getString("sys_subheading"));
        npcExtInfo.put("system_merge", 1);
        resourceEntity.setExtInfo(JSON.toJSONString(npcExtInfo));
        resourceEntity.setPreviewSrc("");
        TemplateEntity defaultTemplate = templateService.getDefault(resourceEntity.getTenantId(), resourceEntity.getType());
        resourceEntity.setTemplateId(defaultTemplate.getId());
        resourceEntity.setStrategy(defaultTemplate.getStrategy());
        iResourceService.saveOrUpdate(resourceEntity);

        Map<String, Object> inputs = new HashMap<>();

        List<String> subVideoUrlList = subVideoEntityList.stream().filter(s -> s.getOperation() == 0 || s.getOperation() == 1).map(SubVideoEntity::getUrl).collect(Collectors.toList());
        for (SubVideoEntity subVideoEntity : subVideoEntityList) {
            subVideoEntity.setDocId(resourceEntity.getDocId());
            if (subVideoEntity.getOperation() == 1) {
                subVideoEntity.setOperation(0);
            } else if (subVideoEntity.getOperation() == 2) {
                subVideoEntity.setDeleted(true);
            }
        }
        subVideoService.updateBatch(subVideoEntityList);
        inputs.put("subVideoUrlList", subVideoUrlList);
        inputs.put("docId", resourceEntity.getDocId());

        WorkflowAsyncRunReq workflowAsyncRunReq = new WorkflowAsyncRunReq()
                .setId(aasWorkflowProperties.getWorkflowId().getVideoMerge())
                .setInput(inputs)
                .setCallback(aasWorkflowProperties.getWorkflowId().getImportCallback() + "/" + resourceEntity.getDocId());
        log.info("workflowAsyncRunReq:{}", JSON.toJSONString(workflowAsyncRunReq));
        WorkflowAsyncRunResp workflowAsyncRunResp = aaasWorkflowClient.runAsync(workflowAsyncRunReq);
        if (workflowAsyncRunResp == null || !Objects.equals("0", workflowAsyncRunResp.getCode())) {
            log.error("下发工作流任务失败{}", JSON.toJSONString(workflowAsyncRunResp));
            throw new RuntimeException("下发工作流任务失败");
        }
        String workflowInstanceId = workflowAsyncRunResp.getData();
        log.info("文件导入下发工作流任务成功：docId:{}, workflowInstanceId:{}", resourceEntity.getDocId(), workflowInstanceId);
        resourceEntity.setWorkflowId(workflowInstanceId);
        resourceEntity.setHandleStatus(ProcessEnum.MERGING.getValue());
        iResourceService.saveOrUpdate(resourceEntity);
    }

    private void singleVideo(List<SubVideoEntity> subVideoEntityList, KnowledgeGroupEntity groupEntity, JSONObject npcExtInfo) {
        for (SubVideoEntity subVideoEntity : subVideoEntityList) {
            if (subVideoEntity.getOperation() == 1) {

                KnowledgeResourceEntity resourceEntity = new KnowledgeResourceEntity();
                resourceEntity.setDocId(UUID.randomUUID().toString());
                String title = subVideoEntity.getTitle();
                if (npcExtInfo.containsKey("sys_title")) {
                    title = npcExtInfo.getString("sys_title");
                    KnowledgeResourceEntity exists = iResourceService.getByGroupId_Title_Suffix(groupEntity.getId(), title, subVideoEntity.getSuffix());
                    if (Objects.nonNull(exists)) {
                        title = iResourceService.getNewTitle(groupEntity.getId(), title, subVideoEntity.getSuffix());
                    }
                }
                resourceEntity.setTitle(title);
                resourceEntity.setSize(subVideoEntity.getSize());
                resourceEntity.setCount(0L);
                resourceEntity.setSuffix(subVideoEntity.getSuffix());
                resourceEntity.setType(FileTypeEnum.VIDEO.getType());
                resourceEntity.setEnable(true);
                resourceEntity.setDescription("FileScan");
                resourceEntity.setUrl(subVideoEntity.getUrl());
                resourceEntity.setTenantId(groupEntity.getTenantId());
                resourceEntity.setCreatorId(groupEntity.getCreatorId());
                resourceEntity.setUpdateId(groupEntity.getUpdateId());
                resourceEntity.setCreateTime(LocalDateTime.now());
                resourceEntity.setUpdateTime(LocalDateTime.now());
                resourceEntity.setHandleStatus(ProcessEnum.Fail.getValue());
                resourceEntity.setGroupId(groupEntity.getId());
                resourceEntity.setGroupPath("");
                String subheading = npcExtInfo.getString("sys_subheading");
                resourceEntity.setSubheading(StringUtils.isBlank(subheading) ? subVideoEntity.getTitle() : subheading);
                npcExtInfo.put("sys_original_filename", subVideoEntity.getTitle() + "." + subVideoEntity.getSuffix());
                resourceEntity.setExtInfo(JSON.toJSONString(npcExtInfo));
                resourceEntity.setPreviewSrc("");
                TemplateEntity defaultTemplate = templateService.getDefault(resourceEntity.getTenantId(), resourceEntity.getType());
                resourceEntity.setTemplateId(defaultTemplate.getId());
                resourceEntity.setStrategy(defaultTemplate.getStrategy());
                iResourceService.saveOrUpdate(resourceEntity);
                resourceUtilService.handResource(resourceEntity, resourceEntity.getTemplateId(), resourceEntity.getStrategy());
                subVideoEntity.setDocId(resourceEntity.getDocId());
                subVideoEntity.setOperation(0);
            } else if (subVideoEntity.getOperation() == 2) {
                subVideoEntity.setDeleted(true);
            }
        }
        subVideoService.updateBatch(subVideoEntityList);
    }

    /**
     * 判断目录名称是否符合人大结构化文件夹命名规则
     * 日期-时间-名称-地点-主办-拍摄（注：字段没有的填写“空格”）
     * <p> 示例
     * <p> 2023.03.05-0900-十四届全国人民代表大会第一次全体会议-大礼堂-全国人大-中央台
     * <p> 2023.01.04-1500-第93次常委会党组会议-天津厅- -
     * <p> 1987.07.10- -机关书法绘画、摄影书法展览- - -
     *
     * @param name 目录名称
     */
    public static boolean isValidFolderName(String name) {
        // 拆分字段
        String[] parts = name.split("-", -1);
        if (parts.length < 3) {
            return false;
        }

        String date = parts[0];
        String time = parts[1];
//        String title = parts[2];

        // 日期必须存在且格式为 yyyy.MM.dd
        if (!isValidDate(date, "yyyy.MM.dd") && !isValidDate(date, "yyyy.MM") && !isValidDate(date, "yyyy")) {
            return false;
        }

        // 名称不能为空或全是空格
//        if (title == null || title.trim().isEmpty()) {
//            return false;
//        }

        // 时间可以是空格，但如果有内容，必须是 4 位数字（HHmm）
        if (!time.trim().isEmpty() && !time.matches("\\d{4}")) {
            return false;
        }

        return true;
    }

    private static boolean isValidDate(String date, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        sdf.setLenient(false);
        try {
            sdf.parse(date);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    public static void main(String[] args) {
        String[] testCases = {
                "2023.03.05-0900-十四届全国人民代表大会第一次全体会议-大礼堂-全国人大-中央台",
                "2023.01.04-1500-第93次常委会党组会议-天津厅- - ",
                "1987.07.10- -机关书法绘画、摄影书法展览- - -",

                // 月份不对
                "2023.13.01-1200-测试错误日期- - -",

                // 时间格式不对
                "2023.03.05-abc-名称- - -",

                // 名称为空
                "2023.03.05-0900- - - -",
                "1990.03- -王厚德副秘书长接见泰国议会代表团2- - -姜仁鹏"
        };

        for (String test : testCases) {
            System.out.println(test + " => " + isValidFolderName(test));
        }

        String[] testTitles = {
                "D01",
                "D02",
                "测试01",
                "测试02",
                "S_01",
        };

        for (String test : testTitles) {
            System.out.println(test + " => " + Validator.isGeneral(test));
        }
    }

    public List<String> videoClean(VideoCleanRpcReq req) {
        List<KnowledgeGroupEntity> groupEntityList = knowledgeGroupService.listByPath("/" + req.getLibraryId() + "/");
        if (CollectionUtils.isEmpty(groupEntityList)) {
            return Collections.emptyList();
        }
        List<String> fileIds = new ArrayList<>();
        List<Long> groupIds = groupEntityList.stream().map(KnowledgeGroupEntity::getId).collect(Collectors.toList());
        groupIds.add(req.getLibraryId());
        List<SubVideoEntity> subVideoEntityList = subVideoService.listByGroupId(groupIds);
        if (CollectionUtils.isNotEmpty(subVideoEntityList)) {
            fileIds.addAll(subVideoEntityList.stream().map(SubVideoEntity::getFileId).collect(Collectors.toList()));
            subVideoService.deleteByGroupIds(groupIds);
        }

        List<KnowledgeResourceEntity> resources = iResourceService.listByGroupIds(groupIds);
        List<KnowledgeResourceEntity> videoList = resources.stream().filter(s -> FileTypeEnum.VIDEO.getType().equals(s.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(videoList)) {
            resourceUtilService.delete(videoList);
            fileIds.addAll(videoList.stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList()));
        }
        return fileIds;
    }


}
