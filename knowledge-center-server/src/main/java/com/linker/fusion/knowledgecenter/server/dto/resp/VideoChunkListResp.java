package com.linker.fusion.knowledgecenter.server.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VideoChunkListResp {

    @ApiModelProperty("es唯一id,用于更新")
    private String uid;

    @ApiModelProperty("分段id")
    private String segmentId;

    @ApiModelProperty("分块id")
    private String chunkId;

    @ApiModelProperty("分块内容")
    private String content;

    @ApiModelProperty("排序")
    private Double sort;

    @ApiModelProperty("图片地址")
    private String url;

    @ApiModelProperty("文档类型  image text")
    private String srcType;
}
