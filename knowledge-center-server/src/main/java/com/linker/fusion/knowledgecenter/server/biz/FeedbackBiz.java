package com.linker.fusion.knowledgecenter.server.biz;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FaqInfoEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FeedbackEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FeedbackSettingEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FeedbackLearnStatusEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FeedbackOperationTagEnum;
import com.linker.fusion.knowledgecenter.server.convert.FaqConvert;
import com.linker.fusion.knowledgecenter.server.convert.FeedbackConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.core.IdBatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.feedback.FeedbackAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.feedback.FeedbackImportReq;
import com.linker.fusion.knowledgecenter.server.dto.req.feedback.FeedbackPageReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.FaqInfoEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.FeedbackResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.FeedbackSettingResp;
import com.linker.fusion.knowledgecenter.service.domain.faq.FaqService;
import com.linker.fusion.knowledgecenter.service.domain.feedback.FeedbackService;
import com.linker.fusion.knowledgecenter.service.domain.feedback.model.FeedbackDTO;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class FeedbackBiz extends BaseBiz {

    @Resource
    private FeedbackService feedbackService;

    @Resource
    private FaqService faqService;

    @Resource
    private KnowledgeGroupService knowledgeGroupService;

    public FeedbackSettingResp getSetting(Long agentId) {
        FeedbackSettingEntity feedbackSettingEntity = feedbackService.getSetting(getTenantId(), agentId);
        return FeedbackConvert.INSTANCE.toResp(feedbackSettingEntity);
    }

    public void updateSetting(FeedbackSettingResp req) {
        feedbackService.updateSetting(req.getAgentId(), req.getLearnEnable());
    }

    public Long add(FeedbackAddReq req) {
        FeedbackEntity feedbackEntity = FeedbackConvert.INSTANCE.toEntity(req);
        feedbackEntity.setLearnStatus(FeedbackLearnStatusEnum.INIT.getCode());
        feedbackEntity.setOperationTag(FeedbackOperationTagEnum.INIT.getCode());
        feedbackEntity.setTenantId(getTenantId());
        feedbackService.saveOrUpdate(feedbackEntity);
        return feedbackEntity.getId();
    }

    public BasePaginResp<FeedbackResp> page(FeedbackPageReq req) {
        Page<FeedbackEntity> page = feedbackService.page(
                getTenantId(), req.getPage(), req.getPageSize(), req.getAgentId(), req.getKeyword(), req.getOperationTag(), req.getLearnStatus());

        List<FeedbackResp> feedbackRespList = page.getRecords().stream()
                .map(FeedbackConvert.INSTANCE::toResp).collect(Collectors.toList());

        Map<Long, FaqInfoEntity> faqMap = new HashMap<>();
        List<Long> faqIds = feedbackRespList.stream().map(FeedbackResp::getFaqId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(faqIds)) {
            List<FaqInfoEntity> faqInfoEntityList = faqService.listByIds(getTenantId(), faqIds);
            faqInfoEntityList.forEach(faqInfoEntity -> faqMap.put(faqInfoEntity.getId(), faqInfoEntity));
        }
        for (FeedbackResp feedbackResp : feedbackRespList) {
            if (feedbackResp.getFaqId() != null) {
                FaqInfoEntity faqInfoEntity = faqMap.get(feedbackResp.getFaqId());
                if (faqInfoEntity != null) {
                    FaqInfoEntityResp faqInfoResp = FaqConvert.INSTANCE.toDto(faqInfoEntity);
                    List<KnowledgeGroupEntity> groupParents = knowledgeGroupService.getParents(faqInfoEntity.getGroupId());
                    faqInfoResp.setGroupNamePath(groupParents.stream().map(KnowledgeGroupEntity::getName).collect(Collectors.joining("/")));

                    feedbackResp.setFaq(faqInfoResp);
                }
            }
        }

        return new BasePaginResp<>(page.getTotal(), feedbackRespList);
    }

    public void accept(Long faqId, Long feedbackId) {
        feedbackService.accept(getTenantId(), getUserCode(), feedbackId, faqId);
    }

    public void ignore(IdBatchBaseReq req) {
        feedbackService.ignore(getTenantId(), getUserCode(), req.getIds());
    }

    public FeedbackDTO retrieve(String tenantId, Long agentId, String question, double threshold) {
        return feedbackService.retrieve(tenantId, agentId, question, threshold);
    }

    public void rpcImport(FeedbackImportReq req) {
        List<FeedbackImportReq.Item> items = req.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<FeedbackEntity> feedbackEntityList = FeedbackConvert.INSTANCE.toEntity(items);
        for (FeedbackEntity feedbackEntity : feedbackEntityList) {
            feedbackEntity.setLearnStatus(FeedbackLearnStatusEnum.INIT.getCode());
            feedbackEntity.setOperationTag(FeedbackOperationTagEnum.INIT.getCode());
        }
        feedbackService.saveOrUpdateBatch(feedbackEntityList);
    }
}
