package com.linker.fusion.knowledgecenter.server.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.base.exception.BusinessException;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.config.AppInfoConfig;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.QuestionBankEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.QuestionTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.StoragePathEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.TaskTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.QuestionBankOptionModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.task.TaskContentForAIGenerateFromDocQBModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.task.TaskContentForAIGenerateQBModel;
import com.linker.fusion.knowledgecenter.server.convert.QuestionBankConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.question.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.QuestionBankResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.QuestionCountResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.KnowledgeGroupParentResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthService;
import com.linker.fusion.knowledgecenter.service.domain.common.IUtilService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import com.linker.fusion.knowledgecenter.service.domain.group.model.SearchPreChekDto;
import com.linker.fusion.knowledgecenter.service.domain.question.QuestionBankIBaseService;
import com.linker.fusion.knowledgecenter.service.domain.question.QuestionBankService;
import com.linker.fusion.knowledgecenter.service.domain.question.dto.QuestionBankPageDTO;
import com.linker.fusion.knowledgecenter.service.domain.question.model.QuestionBankEmbeddingData;
import com.linker.fusion.knowledgecenter.service.domain.task.ITaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class QuestionBankBiz extends BaseBiz {
    // 选择题选项常量
    private static final List<String> CHOICE_OPTION_KEYS = Arrays.asList("A", "B", "C", "D");
    // 判断题选项常量
    private static final List<String> TRUE_FALSE_OPTION_KEYS = Arrays.asList("1", "0");

    @Resource
    private QuestionBankService questionBankService;


    @Resource
    private QuestionBankIBaseService questionBankIBaseService;

    @Resource
    private KnowledgeGroupService knowledgeGroupService;
    @Resource
    private KnowledgeGroupFactory knowledgeGroupFactory;

    @Resource
    private IAuthService iAuthService;

    @Resource
    private AppInfoConfig appInfoConfig;
    @Resource
    private ITaskService iTaskService;
    @Value("${pdf-parse.storage.type}")
    private String storageType;
    @Value("${pdf-parse.storage.username}")
    private String storageUserName;
    @Value("${pdf-parse.storage.password}")
    private String storagePassword;
    @Value("${pdf-parse.storage.endpoint}")
    private String storageEndpoint;
    @Value("${pdf-parse.storage.bucket}")
    private String storageBucket;
    @Value("${pdf-parse.storage.secure}")
    private Integer storageSecure;
    @Resource
    private FileBiz fileBiz;
    @Resource
    private IUtilService iUtilService;

    /**
     * 分页查询题目
     */
    public BasePaginResp<QuestionBankResp> page(QuestionBankPageReq req) {
        SearchPreChekDto preChekDto = knowledgeGroupFactory.searchPreCheck(req);
        QuestionBankPageDTO dtoPage = QuestionBankConvert.INSTANCE.toDto(req, UserContext.getUser());
        dtoPage.setGroupIds(preChekDto.getAuthedGroupIds());
        Page<QuestionBankEntity> ret = questionBankService.page(dtoPage);
        ret.getRecords().forEach(entity -> {
            entity.setAuthCodes(preChekDto.getAuthedCodes(entity.getId(), entity.getGroupId()));
            entity.setAuthLevel(preChekDto.getAuthedLevel(entity.getId(), entity.getGroupId()));
        });
        // 转换结果
        List<QuestionBankResp> records = ret.getRecords().stream()
                .map(this::entityToResp)
                .collect(Collectors.toList());
        return new BasePaginResp<>(ret.getTotal(), records);
    }

    /**
     * 验证选项格式
     *
     * @param options      选项列表
     * @param questionType 题目类型
     */
    private void validateOptions(List<QuestionBankOptionModel> options, String questionType) {
        if (QuestionTypeEnum.FILL_IN_BLANK.name().equals(questionType)) {
            // 填空题不需要验证选项，因为选项是从答案中提取的
            return;
        }

        // 非填空题，选项不能为空
        if (CollectionUtils.isEmpty(options)) {
            throw new BusinessException("选项不能为空");
        }

        if (QuestionTypeEnum.CHOICE.name().equals(questionType)) {
            // 单选题必须有2-4个选项，且选项标识必须是A、B、C、D
            if (options.size() < 2 || options.size() > 4) {
                throw new BusinessException("单选题必须包含2-4个选项");
            }
            List<String> keys = options.stream()
                    .map(QuestionBankOptionModel::getKey)
                    .collect(Collectors.toList());
            // 检查所有key都在有效范围内
            if (!CHOICE_OPTION_KEYS.containsAll(keys)) {
                throw new BusinessException("单选题选项标识必须为A、B、C、D");
            }
            // 检查key是否连续
            for (int i = 0; i < keys.size(); i++) {
                if (!keys.contains(CHOICE_OPTION_KEYS.get(i))) {
                    throw new BusinessException("单选题选项必须从A开始连续");
                }
            }
        } else if (QuestionTypeEnum.MULTIPLE_CHOICE.name().equals(questionType)) {
            // 多选题必须有2-8个选项，且选项标识必须是A、B、C、D等
            if (options.size() < 2 || options.size() > 8) {
                throw new BusinessException("多选题必须包含2-8个选项");
            }
            List<String> keys = options.stream()
                    .map(QuestionBankOptionModel::getKey)
                    .collect(Collectors.toList());
            // 检查所有key都在有效范围内 (多选题可以有更多选项)
            List<String> validKeys = Arrays.asList("A", "B", "C", "D", "E", "F", "G", "H");
            if (!validKeys.containsAll(keys)) {
                throw new BusinessException("多选题选项标识必须为A-H");
            }
            // 检查key是否连续
            for (int i = 0; i < keys.size(); i++) {
                if (!keys.contains(validKeys.get(i))) {
                    throw new BusinessException("多选题选项必须从A开始连续");
                }
            }
        } else if (QuestionTypeEnum.TRUE_FALSE.name().equals(questionType)) {
            // 判断题必须有2个选项，且选项标识必须是1、0
            if (options.size() != 2) {
                throw new BusinessException("判断题必须包含2个选项");
            }
            List<String> keys = options.stream()
                    .map(QuestionBankOptionModel::getKey)
                    .collect(Collectors.toList());
            if (!new HashSet<>(keys).containsAll(TRUE_FALSE_OPTION_KEYS)) {
                throw new BusinessException("判断题选项标识必须为1和0");
            }
            // 判断题不允许包含图片
            for (QuestionBankOptionModel option : options) {
                if (CollectionUtils.isNotEmpty(option.getImages())) {
                    throw new BusinessException("判断题选项不支持图片");
                }
            }
        } else {
            throw new BusinessException("不支持的题目类型");
        }

        // 验证选项内容
        for (QuestionBankOptionModel option : options) {
            String value = option.getValue();
            List<String> images = option.getImages();

            // 选项内容和选项图片至少填写一项
            if (StringUtils.isBlank(value) && CollectionUtils.isEmpty(images)) {
                throw new BusinessException("选项内容和选项图片不能同时为空");
            }

            // 验证图片URL长度
            if (CollectionUtils.isNotEmpty(images)) {
                for (String image : images) {
                    if (StringUtils.isNotBlank(image) && image.length() > 1024) {
                        throw new BusinessException("选项图片URL长度不能超过1024字符");
                    }
                }
            }

            // 验证选项内容长度
            if (StringUtils.isNotBlank(value) && value.length() > 200) {
                throw new BusinessException("选项内容长度不能超过200字符");
            }
        }
    }

    /**
     * 验证答案是否合法
     *
     * @param answer       答案
     * @param options      选项列表
     * @param questionType 题目类型
     */
    private void validateAnswer(String answer, List<QuestionBankOptionModel> options, String questionType) {
        if (StringUtils.isBlank(answer)) {
            throw new BusinessException("答案不能为空");
        }

        if (QuestionTypeEnum.FILL_IN_BLANK.name().equals(questionType)) {
            // 填空题答案必须是JSON格式的数组，包含多个答案项
            try {
                List<Map<String, String>> answerList = JSON.parseObject(answer, new TypeReference<List<Map<String, String>>>() {
                });
                if (answerList.isEmpty()) {
                    throw new BusinessException("填空题答案不能为空");
                }

                // 验证每个答案项的格式
                for (Map<String, String> answerItem : answerList) {
                    if (!answerItem.containsKey("key") || !answerItem.containsKey("value")) {
                        throw new BusinessException("填空题答案格式错误，必须包含key和value字段");
                    }

                    if (StringUtils.isBlank(answerItem.get("key")) || StringUtils.isBlank(answerItem.get("value"))) {
                        throw new BusinessException("填空题答案的key和value不能为空");
                    }
                }
            } catch (Exception e) {
                throw new BusinessException("填空题答案必须是有效的JSON格式：[{\"key\":\"1\",\"value\":\"答案1\"},{\"key\":\"2\",\"value\":\"答案2\"}]");
            }
            return; // 填空题不需要验证选项
        }

        // 以下选项验证只针对非填空题
        if (CollectionUtils.isEmpty(options)) {
            throw new BusinessException("选项不能为空");
        }

        if (QuestionTypeEnum.CHOICE.name().equals(questionType)) {
            // 单选题答案必须是单个选项
            if (answer.contains(",")) {
                throw new BusinessException("单选题答案只能选择一个选项");
            }
            boolean validAnswer = options.stream()
                    .anyMatch(option -> answer.equals(option.getKey()));
            if (!validAnswer) {
                throw new BusinessException("答案必须是已有选项中的一个");
            }
        } else if (QuestionTypeEnum.MULTIPLE_CHOICE.name().equals(questionType)) {
            // 多选题答案必须是多个选项，用逗号分隔
            String[] answerKeys = answer.split(",");
            if (answerKeys.length < 2) {
                throw new BusinessException("多选题答案至少需要选择2个选项");
            }

            // 检查每个答案选项是否有效
            List<String> optionKeys = options.stream()
                    .map(QuestionBankOptionModel::getKey)
                    .collect(Collectors.toList());

            for (String key : answerKeys) {
                if (!optionKeys.contains(key)) {
                    throw new BusinessException("答案选项 '" + key + "' 不存在");
                }
            }

            // 确保答案按字母顺序排序
            List<String> orderedAnswerKeys = Arrays.asList(answerKeys);
            Collections.sort(orderedAnswerKeys);
            String orderedAnswer = String.join(",", orderedAnswerKeys);
            if (!orderedAnswer.equals(answer)) {
                throw new BusinessException("多选题答案必须按字母顺序排序，例如：A,B,C");
            }
        } else if (QuestionTypeEnum.TRUE_FALSE.name().equals(questionType)) {
            // 判断题答案必须是1或0
            if (!TRUE_FALSE_OPTION_KEYS.contains(answer)) {
                throw new BusinessException("判断题答案必须为1或0");
            }
        } else {
            throw new BusinessException("不支持的题目类型");
        }
    }

    /**
     * 验证问题内容
     *
     * @param question      问题内容
     * @param questionImage 问题图片
     */
    private void validateQuestion(String question, String questionImage) {
        if (StringUtils.isBlank(question) && StringUtils.isBlank(questionImage)) {
            throw new BusinessException("问题内容和问题图片不能同时为空");
        }
    }

    /**
     * 处理题目选项
     *
     * @param questionType 题目类型
     * @param answer       答案
     * @param options      选项列表（非填空题必填）
     * @return 处理后的选项列表
     */
    private List<QuestionBankOptionModel> processQuestionOptions(String questionType, String answer, List<QuestionBankOptionModel> options) {
        if (QuestionTypeEnum.FILL_IN_BLANK.name().equals(questionType)) {
            // 填空题从答案中提取key作为选项
            try {
                List<Map<String, String>> answerList = JSON.parseObject(answer, new TypeReference<List<Map<String, String>>>() {
                });
                return answerList.stream()
                        .map(answerItem -> {
                            QuestionBankOptionModel option = new QuestionBankOptionModel();
                            option.setKey(answerItem.get("key"));
                            return option;
                        })
                        .collect(Collectors.toList());
            } catch (Exception e) {
                throw new BusinessException("填空题答案格式错误，无法提取选项");
            }
        } else {
            // 非填空题必须提供选项
            if (options == null) {
                throw new BusinessException("非填空题的选项不能为空");
            }
            return options;
        }
    }

    /**
     * 新增题目
     */
    public BaseResp<QuestionBankResp> create(QuestionBankCreateReq req) {
        // 验证问题内容和图片
        validateQuestion(req.getQuestion(), req.getQuestionImage());
        // 处理选项
        List<QuestionBankOptionModel> options = processQuestionOptions(req.getQuestionType(), req.getAnswer(), req.getOptions());
        // 校验选项格式
        validateOptions(options, req.getQuestionType());
        // 校验答案
        validateAnswer(req.getAnswer(), options, req.getQuestionType());
        QuestionBankEntity entity = QuestionBankConvert.INSTANCE.toEntity(req);
        entity.init(getTenantId(), getUserCode());
        entity.setEnable(true);
        entity.setOptions(JSON.toJSONString(options));
        questionBankService.create(entity);

        if (StringUtils.isNotBlank(entity.getQuestionImage())) {
            entity.setQuestionImage(iUtilService.moveTempToDefault(req.getQuestionImage(), StoragePathEnum.QB.getValueByUrl(entity.getTenantId(), entity.getId().toString(), req.getQuestionImage())));
        }
        options.forEach(option -> {
            if (CollectionUtils.isNotEmpty(option.getImages())) {
                List<String> newUrls = new ArrayList<>();
                option.getImages().forEach(url -> {
                    if (StringUtils.isBlank(url))
                        return;
                    String newUrl = iUtilService.moveTempToDefault(url, StoragePathEnum.QB.getValueByUrl(entity.getTenantId(), entity.getId().toString(), url));
                    if (StringUtils.isNotBlank(newUrl)) {
                        newUrls.add(newUrl);
                    } else {
                        newUrls.add(url);
                    }

                });
                option.setImages(newUrls);
            }
        });
        entity.setOptions(JSON.toJSONString(options));
        questionBankService.updateById(entity);

        return new BaseResp<>(entityToResp(entity));
    }

    /**
     * 更新题目
     */
    public BaseResp<Void> update(QuestionBankUpdateReq req) {
        QuestionBankEntity entity = questionBankService.getNotNull(req.getId());
        List<String> oldUrls = entity.getUrls();

        // 验证问题内容和图片
        validateQuestion(req.getQuestion(), req.getQuestionImage());

        // 处理选项
        List<QuestionBankOptionModel> options = processQuestionOptions(req.getQuestionType(), req.getAnswer(), req.getOptions());

        // 校验选项格式
        validateOptions(options, req.getQuestionType());
        // 校验答案
        validateAnswer(req.getAnswer(), options, req.getQuestionType());

        if (StringUtils.isNotBlank(req.getQuestionImage())) {
            String newUrl = iUtilService.moveTempToDefault(req.getQuestionImage(), StoragePathEnum.QB.getValueByUrl(entity.getTenantId(), entity.getId().toString(), req.getQuestionImage()));
            if (StringUtils.isNotBlank(newUrl)) {
                req.setQuestionImage(newUrl);
            }
        }
        if (StringUtils.isNotBlank(entity.getQuestionImage()) && !entity.getQuestionImage().equals(req.getQuestionImage())) {
            iUtilService.deleteFile(entity.getQuestionImage());
        }
        entity.setQuestionImage(req.getQuestionImage());
        options.forEach(option -> {
            if (CollectionUtils.isNotEmpty(option.getImages())) {
                List<String> newUrls = new ArrayList<>();
                option.getImages().forEach(url -> {
                    if (StringUtils.isBlank(url))
                        return;
                    String newUrl = iUtilService.moveTempToDefault(url, StoragePathEnum.QB.getValueByUrl(entity.getTenantId(), entity.getId().toString(), url));
                    if (StringUtils.isNotBlank(newUrl))
                        newUrls.add(newUrl);
                    else {
                        newUrls.add(url);
                    }
                });
                option.setImages(newUrls);
            }
        });
        entity.setQuestion(req.getQuestion() != null ? req.getQuestion().trim() : null);
        entity.setQuestionType(req.getQuestionType());
        entity.setAnswer(req.getAnswer());
        entity.setAnalysis(req.getAnalysis());
        entity.setOptions(JSON.toJSONString(options));
        entity.setUpdateId(getUserCode());
        entity.setUpdateTime(LocalDateTime.now());
        questionBankService.updateById(entity);


        List<String> diffUrls = ListUtils.subtract(oldUrls, entity.getUrls());
        diffUrls.forEach(url -> iUtilService.deleteFile(url));


        // 更新向量数据
        QuestionBankEmbeddingData questionBankEmbeddingData = questionBankService.buildEmbeddingData(getTenantId(), entity);
        questionBankIBaseService.batchUpdate(getTenantId(), Collections.singletonList(questionBankEmbeddingData));
        return new BaseResp<>();
    }

    /**
     * 删除题目
     */
    public BaseResp<Void> delete(BatchQuestionDeleteReq req) {
        List<QuestionBankEntity> questions = questionBankService.listByIds(req.getIds());
        if (CollectionUtils.isEmpty(questions)) {
            throw new BusinessException("指定题目不存在或已删除");
        }

        // 删除问题的图片
        questions.forEach(q -> {
            q.getUrls().forEach(url -> iUtilService.deleteFile(url));
        });
        // 逻辑删除
        questions.forEach(q -> {
            q.setDeleted(true);
            q.setUpdateId(getUserCode());
            q.setUpdateTime(LocalDateTime.now());
        });
        questionBankService.updateBatchById(questions);
        // 删除向量数据
        questionBankIBaseService.delete(getTenantId(), questions.stream()
                .map(QuestionBankEntity::getId)
                .map(String::valueOf)
                .collect(Collectors.toList()));

        return new BaseResp<>();
    }

    /**
     * 启用/禁用题目
     */
    public BaseResp<Void> enable(QuestionEnableReq req) {
        QuestionBankEntity entity = questionBankService.getNotNull(req.getId());

        entity.setEnable(req.getEnable());
        entity.setUpdateId(getUserCode());
        entity.setUpdateTime(LocalDateTime.now());

        questionBankService.updateById(entity);

        // 更新向量数据的启用状态
        QuestionBankEmbeddingData questionBankEmbeddingData = questionBankService.buildEmbeddingData(getTenantId(), entity);
        questionBankIBaseService.batchUpdate(getTenantId(), Collections.singletonList(questionBankEmbeddingData));
        return new BaseResp<>();
    }

    /**
     * 批量启用/禁用题目
     */
    public BaseResp<Void> batchEnable(BatchQuestionEnableReq req) {
        List<QuestionBankEntity> questions = questionBankService.listByIds(req.getIds());
        if (CollectionUtils.isEmpty(questions)) {
            throw new BusinessException("指定题目不存在或已删除");
        }
        // 批量更新启用状态 - 使用单一SQL语句更新所有记录
        questionBankService.updateEnableByIds(req.getIds(), req.getEnable(), getUserCode());

        // 更新向量数据的启用状态
        // 更新内存中的实体，以便正确构建嵌入数据
        questions.forEach(q -> {
            q.setEnable(req.getEnable());
            q.setUpdateId(getUserCode());
            q.setUpdateTime(LocalDateTime.now());
        });

        // 使用批量处理嵌入数据
        List<QuestionBankEmbeddingData> embeddingDataList = questionBankService.batchBuildEmbeddingData(
                getTenantId(), questions);

        questionBankIBaseService.batchUpdate(getTenantId(), embeddingDataList);

        return new BaseResp<>();
    }

    /**
     * 移动题目
     */
    public BaseResp<Void> move(QuestionMoveReq req) {
        // 检查源题目权限
        List<QuestionBankEntity> questions = questionBankService.listByIds(req.getIds());
        if (CollectionUtils.isEmpty(questions)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "题目");
        }
        // 使用批量SQL更新分组ID
        questionBankService.updateGroupByIds(req.getIds(), req.getTargetGroupId(), getUserCode());

        // 更新内存中的实体，用于向量数据更新
        questions.forEach(q -> {
            q.setGroupId(req.getTargetGroupId());
            q.setUpdateId(getUserCode());
            q.setUpdateTime(LocalDateTime.now());
        });
        // 使用批量处理嵌入数据
        List<QuestionBankEmbeddingData> embeddingDataList = questionBankService.batchBuildEmbeddingData(
                getTenantId(), questions);
        questionBankIBaseService.batchUpdate(getTenantId(), embeddingDataList);
        return new BaseResp<>();
    }


    private QuestionBankResp entityToResp(QuestionBankEntity entity) {
        if (entity.getGroupId().equals(-1L)) {
            return QuestionBankConvert.INSTANCE.toResp(entity);
        }
        KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(entity.getGroupId());
        List<KnowledgeGroupEntity> parents = knowledgeGroupService.getParents(group);
        parents.add(group);
        QuestionBankResp resp = QuestionBankConvert.INSTANCE.toResp(entity);

        resp.setGroupParentResp(new KnowledgeGroupParentResp(group.getId(), group.getType(), group.getVisibleType(), parents));
        return resp;
    }

    /**
     * 题库生成
     *
     * @param req
     * @return
     */
    public TaskEntity aiGenerate(AIGenerateQBReq req) {
        Long count = questionBankService.getTotalCount(getTenantId());
        if (appInfoConfig.getQuestionLimit() <= count) {
            throw new BusinessException("题目超出剩余容量，当前剩余可新建容量0个");
        }
        TaskContentForAIGenerateQBModel aiGenerateContent = new TaskContentForAIGenerateQBModel();
        aiGenerateContent.setGroupIds(req.getSelectGroupIds());
        aiGenerateContent.setType(req.getType());
        iTaskService.checkHasRun(getTenantId(), getUserCode(), req.getTargetGroupId());
        return iTaskService.send(getTenantId(), getUserCode(), req.getTargetGroupId(), aiGenerateContent, null, TaskTypeEnum.QB);
    }

    /**
     * 根据文档生成题目
     *
     * @param req 请求参数
     * @return 任务实体
     */
    public TaskEntity aiGenerateFromDoc(AIGenerateFromDocReq req) {
        Long count = questionBankService.getTotalCount(getTenantId());
        if (appInfoConfig.getQuestionLimit() <= count) {
            throw new BusinessException("题目超出剩余容量，当前剩余可新建容量0个");
        }
        TaskContentForAIGenerateFromDocQBModel aiGenerateContent = new TaskContentForAIGenerateFromDocQBModel();
        aiGenerateContent.setGroupId(req.getTargetGroupId());
        aiGenerateContent.setType(req.getType());
        aiGenerateContent.setFile(req.getFile());

        Map<String, Object> inputs = new HashedMap<>();
        inputs.put("storageType", storageType);
        JSONObject linkage = new JSONObject();
        linkage.put("url", storageEndpoint);
        linkage.put("username", storageUserName);
        linkage.put("password", storagePassword);
        linkage.put("bucketName", storageBucket);
        linkage.put("secure", storageSecure);
        inputs.put("linkage", linkage);
        inputs.put("file", req.getFile());
        inputs.put("region", "{'vertical': [0, 100], 'horizontal': [0, 100]}");
        iTaskService.checkHasRun(getTenantId(), getUserCode(), req.getTargetGroupId());
        return iTaskService.send(getTenantId(), getUserCode(), req.getTargetGroupId(), aiGenerateContent, null, TaskTypeEnum.DocumentToQB, inputs);
    }

    public List<QuestionCountResp> countByGroups(QuestionCountReq req) {
        Map<Long, Integer> countMap = questionBankService.countByGroups(req.getGroupIds(), req.getQuestionType());
        List<QuestionCountResp> result = countMap.entrySet().stream()
                .map(entry -> new QuestionCountResp(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        return result;
    }
} 