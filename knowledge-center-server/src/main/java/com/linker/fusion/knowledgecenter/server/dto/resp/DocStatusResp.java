package com.linker.fusion.knowledgecenter.server.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocStatusResp {

    @ApiModelProperty("docId")
    private String docId;

    @ApiModelProperty("定义了知识资源在处理过程中的各种状态：0-排队中 1-学习中, 2-学习成功, 3-学习失败, 4-归档, 5-合并中, 6-合并失败, 7-预处理中（转码、抽帧）, 8-暂停学习,9-取消学习")
    private Integer handleStatus;

}
