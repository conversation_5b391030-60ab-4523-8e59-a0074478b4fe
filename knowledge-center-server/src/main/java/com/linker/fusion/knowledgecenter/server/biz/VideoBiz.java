package com.linker.fusion.knowledgecenter.server.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.enums.GlobalErrorCodeEnum;
import com.linker.core.base.exception.ServerException;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.AasWorkflowClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunResp;
import com.linker.fusion.knowledgecenter.infrastructure.config.AasWorkflowProperties;
import com.linker.fusion.knowledgecenter.infrastructure.entity.*;
import com.linker.fusion.knowledgecenter.infrastructure.enums.*;
import com.linker.fusion.knowledgecenter.infrastructure.es.AudioChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoFrameEntity;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.task.TaskContentForVideoCut;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.infrastructure.utils.UrlCheckUtil;
import com.linker.fusion.knowledgecenter.server.convert.FileConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.core.IdBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.CustomerFieldDto;
import com.linker.fusion.knowledgecenter.server.dto.req.file.FaceListReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.FaceListResp;
import com.linker.fusion.knowledgecenter.server.dto.req.file.video.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.*;
import com.linker.fusion.knowledgecenter.service.domain.common.CustomSheetWriteHandler;
import com.linker.fusion.knowledgecenter.service.domain.common.ExcelMerger;
import com.linker.fusion.knowledgecenter.service.domain.common.MinioStorage;
import com.linker.fusion.knowledgecenter.service.domain.custom.CustomService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourcePersonService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IAudioChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IVideoChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.AddChunkDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.*;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ChunkServiceFactory;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ResourceUtilService;
import com.linker.fusion.knowledgecenter.service.domain.strategy.TemplateService;
import com.linker.fusion.knowledgecenter.service.domain.task.ITaskService;
import com.linker.fusion.knowledgecenter.service.domain.task.TaskService;
import com.linker.fusion.knowledgecenter.service.domain.task.dto.UpdateResourceChunkEnableDTO;
import com.linker.fusion.knowledgecenter.service.domain.video.merge.SubVideoService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.*;

@Slf4j
@Component
public class VideoBiz extends BaseBiz {
    @Resource
    KnowledgeGroupService knowledgeGroupService;
    @Resource
    IVideoChunkIBaseServiceResource iVideoChunkIBaseService;
    @Resource
    IAudioChunkIBaseServiceResource iAudioChunkIBaseService;

    @Value("${document.image.encode:false}")
    private Boolean imageEncode;

    @Resource
    private IResourceFaceService iResourceFaceService;

    @Resource
    private IResourceODTagService iResourceODTagService;

    @Resource
    private IResourceTerminologyService iResourceTerminologyService;

    @Resource
    private IVideoFrameIBaseService iVideoFrameIBaseService;
    @Resource
    private IResourceExtService iResourceExtService;

    @Resource
    private IResourceService iResourceService;
    @Resource
    private ResourceUtilService resourceUtilService;
    @Resource
    private ChunkServiceFactory chunkServiceFactory;
    @Resource
    private ITaskService iTaskService;

    @Resource
    private TemplateService templateService;
    @Autowired
    private SubVideoService subVideoService;

    @Resource
    private MinioStorage minioStorage;
    @Resource
    private IResourcePersonService iResourcePersonService;
    @Resource
    private FileBiz fileBiz;

    @Resource
    private AasWorkflowProperties aasWorkflowProperties;

    @Resource
    private AasWorkflowClient aaasWorkflowClient;
    @Autowired
    private CustomService customService;
    @Autowired
    private TaskService taskService;

    /**
     * 查询分块列表
     *
     * @param req
     * @return
     */
    public BaseResp<List<VideoChunkListResp>> chunkList(VideoChunkListReq req) {
        List<VideoChunkEntity> segments = iVideoChunkIBaseService.list(getTenantId(), req.getSegmentId(), req.getContent());
        List<VideoChunkListResp> videoChunkResp = FileConvert.INSTANCE.toVideoChunkResp(segments);
        return new BaseResp<>(videoChunkResp.stream().filter(c -> StringUtils.isNotBlank(c.getContent()) || StringUtils.isNotBlank(c.getUrl())).collect(Collectors.toList()));
    }

    /**
     * 语音分块列表
     *
     * @param req
     * @return
     */
    public BaseResp<List<AudioChunkResp>> asrChunkList(VideoSegAsrListReq req) {
        if (StringUtils.isNotBlank(req.getSegmentId())) {
            List<AudioChunkEntity> audioChunks = iAudioChunkIBaseService.list(getTenantId(), req.getSegmentId(), req.getContent());
            Collections.sort(audioChunks, Comparator.comparingLong(AudioChunkEntity::getStartTimestamp));
            return new BaseResp<>(FileConvert.INSTANCE.toAudioChunkResp(audioChunks));
        } else if (StringUtils.isNotBlank(req.getDocId())) {
            List<AudioChunkEntity> audioChunks = iAudioChunkIBaseService.listByDocId(getTenantId(), req.getDocId(), req.getContent());
            Collections.sort(audioChunks, Comparator.comparingLong(AudioChunkEntity::getStartTimestamp));
            return new BaseResp<>(FileConvert.INSTANCE.toAudioChunkResp(audioChunks));
        } else {
            throw new ServiceException(GlobalErrorCodeEnum.MISSING_PARAMETER);
        }
    }


    public void updateChunk(VideoChunkUpdateReq req) {
        chunkServiceFactory.update(FileTypeEnum.VIDEO, getTenantId(), req.getUid(), req.getContent(), req.getUrl());
    }

    public void addChunk(VideoChunkAddReq req) {

        AddChunkDTO dto = chunkServiceFactory.addPre(req.getDocId(), req.getContent(), req.getUrl(), imageEncode);
        KnowledgeResourceEntity resource = dto.getFile();
        VideoChunkEntity chunk = new VideoChunkEntity();
        chunk.setEnable(Boolean.TRUE.equals(resource.getEnable()) ? 1 : 0);
        chunk.setGroupId(resource.getGroupId());
        chunk.setChunkId(UUID.randomUUID().toString());
        chunk.setSegmentId(req.getSegmentId());
        chunk.setSort(Objects.nonNull(req.getSort()) ? req.getSort() : 0);
        chunk.setTitle(resource.getTitle());
        chunk.setSrcType(req.getSrcType());
        chunk.setContent(req.getContent());
        chunk.setContentVector(dto.getContentVector());
        chunk.setDocId(req.getDocId());
        chunk.setCreateTime(StringComUtils.convertStr(LocalDateTime.now()));
        chunk.setCreateId(getUserCode());
        chunk.setTenantId(getTenantId());
        chunk.setMeta(JSON.parseObject(resource.getExtInfo()));
        chunk.setUrl(dto.getNewUrl());
        chunk.setPersonsLabels(iResourceFaceService.getLabelsBySegmentId(resource.getDocId(), req.getSegmentId()));
        chunk.setObjectLabels(iResourceODTagService.getLabelsBySegmentId(req.getSegmentId()));
        chunk.setTextLabels(iResourceTerminologyService.getLabelsBySegmentId(req.getSegmentId()));
        iVideoChunkIBaseService.add(getTenantId(), Collections.singletonList(chunk), true);
    }

    public void deleteChunk(VideoChunkDeleteReq req) {
        chunkServiceFactory.delete(FileTypeEnum.VIDEO, getTenantId(), req.getChunkIds());
    }


    /**
     * 标签列表
     *
     * @param req
     * @return
     */
    public BaseResp<TagFrameResp> tagFrame(VideoTagReq req) {
        TagFrameResp tagFrameResp = new TagFrameResp();
        KnowledgeResourceEntity resource = iResourceService.getNotNull(req.getDocId());
        List<ResourceODTagEntity> tagList = iResourceODTagService.listByResId(req.getDocId(), 1, req.getTagName());
        List<VideoFrameEntity> frameList = iVideoFrameIBaseService.list(resource.getTenantId(), Arrays.asList(req.getDocId())).stream().map(h -> h.getDocument()).collect(Collectors.toList());
        List<FrameResp> frameResps = FileConvert.INSTANCE.toFrameResp(frameList);
        tagFrameResp.setTags(new ArrayList<>());
        tagFrameResp.setFrames(new ArrayList<>());
        DecimalFormat df = new DecimalFormat("#0.00");
        tagList.stream().collect(Collectors.groupingBy(ResourceODTagEntity::getName)).forEach((name, list) -> {
            TagResp tagResp = new TagResp();
            tagResp.setName(name);
            tagResp.setTimes(list.size());
            tagResp.setPer(df.format(list.size() * 100 / (float) tagList.size()));
            List<String> frameIds = list.stream().map(l -> l.getFrameId()).distinct().collect(Collectors.toList());
            tagResp.setFrames(frameResps.stream().filter(f -> frameIds.contains(f.getFrameId())).collect(Collectors.toList()));

            tagResp.getFrames().forEach(f -> {
                f.getTagNames().add(name);
            });
            tagFrameResp.getFrames().addAll(tagResp.getFrames());
            tagFrameResp.getTags().add(tagResp);
        });
        return new BaseResp<>(tagFrameResp);

    }

    public BaseResp<Void> tagFrameDelete(VideoTagFrameDeleteReq req) {
        iResourceODTagService.deleteByDocId_FrameId_Name(req.getDocId(), req.getFrameId(), req.getTagNames());
        return new BaseResp<>();
    }

    public BaseResp<List<SupplementResp>> supplementList(SupplementListReq req) {
        List<ResourceExtEntity> extEntityList = iResourceExtService.list(Collections.singletonList(req.getDocId()), ResourceExtTypeEnum.K2Supplement);
        List<SupplementResp> respList = extEntityList.stream().map(e -> {
            SupplementResp resp = new SupplementResp();
            resp.setId(e.getId());
            resp.setDesc(e.getField1());
            resp.setUrls(JSON.parseArray(e.getField2(), String.class));
            return resp;
        }).collect(Collectors.toList());
        return new BaseResp<>(respList);
    }

    public BaseResp<Void> supplementAdd(SupplementAddReq req) {
        ResourceExtEntity resourceExtEntity = new ResourceExtEntity();
        resourceExtEntity.setDocId(req.getDocId());
        resourceExtEntity.setType(ResourceExtTypeEnum.K2Supplement.getType());
        resourceExtEntity.setField1(req.getDesc());
        resourceExtEntity.setField2(JSON.toJSONString(req.getUrls()));
        resourceExtEntity.setCreateTime(LocalDateTime.now());
        resourceExtEntity.setCreatorId(getUserCode());
        resourceExtEntity.setTenantId(getTenantId());
        resourceExtEntity.setUpdateTime(LocalDateTime.now());
        resourceExtEntity.setUpdateId(getUserCode());
        iResourceExtService.save(resourceExtEntity);
        return new BaseResp<>();
    }

    public BaseResp<Void> supplementUpdate(SupplementUpdateReq req) {
        ResourceExtEntity resourceExtEntity = iResourceExtService.getNotNull(req.getId());
        resourceExtEntity.setUpdateTime(LocalDateTime.now());
        resourceExtEntity.setUpdateId(getUserCode());
        resourceExtEntity.setField1(req.getDesc());
        resourceExtEntity.setField2(JSON.toJSONString(req.getUrls()));
        iResourceExtService.update(resourceExtEntity);
        return new BaseResp<>();
    }

    public BaseResp<Void> supplementDelete(IdBaseReq<Long> req) {
        ResourceExtEntity resourceExtEntity = iResourceExtService.getNotNull(req.getId());
        resourceExtEntity.setUpdateTime(LocalDateTime.now());
        resourceExtEntity.setUpdateId(getUserCode());
        resourceExtEntity.setDeleted(true);
        iResourceExtService.update(resourceExtEntity);
        return new BaseResp<>();
    }

    public BaseResp<InspectionConclusionResp> getInspectionConclusion(InspectionConclusionReq req) {
        KnowledgeResourceEntity resource = iResourceService.getNotNull(req.getDocId());
        InspectionConclusionResp resp = new InspectionConclusionResp();
        List<ResourceExtEntity> resourceExtEntities = iResourceExtService.list(Collections.singletonList(req.getDocId()), ResourceExtTypeEnum.K2Result);
        resp.setRet(resourceExtEntities.size() > 0 ? resourceExtEntities.get(0).getField1() : "");
        resp.setStatus(0);
        if (resource.getJsonExtInfo().containsKey(ResourceExtInfoTypeEnum.K2Status.getType())) {
            resp.setStatus(resource.getJsonExtInfo().getInteger(ResourceExtInfoTypeEnum.K2Status.getType()));
        }
        return new BaseResp<>(resp);
    }

    public BaseResp<Void> setInspectionConclusionStatus(InspectionConclusionStatusSetReq req) {
        KnowledgeResourceEntity resource = iResourceService.getNotNull(req.getDocId());
        JSONObject extInfo = resource.getJsonExtInfo();
        extInfo.put(ResourceExtInfoTypeEnum.K2Status.getType(), req.getStatus());
        resource.setExtInfo(JSON.toJSONString(extInfo));
        iResourceService.updateById(resource);
        iVideoChunkIBaseService.updateMeta(resource.getTenantId(), resource.getDocId(), resource.getJsonExtInfo());
        iVideoFrameIBaseService.updateMeta(resource.getTenantId(), resource.getDocId(), resource.getJsonExtInfo());
        return new BaseResp<>();
    }


    public BaseResp<Void> rerunInspectionConclusion(InspectionConclusionReq req) {
         return new BaseResp<>();
    }

    public BaseResp<Void> editInspectionConclusion(InspectionConclusionEditReq req) {
        KnowledgeResourceEntity resource = iResourceService.getNotNull(req.getDocId());
        iResourceExtService.save(resource, ResourceExtTypeEnum.K2Result, req.getContent());
        return new BaseResp<>();
    }


    public CompletableFuture<Void> cut(Long taskId, OutputStream outputStream) {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(5, TimeUnit.MINUTES)
                .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
                .build();

        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        CompletableFuture<String> resultFuture = new CompletableFuture<>();
        // 定时任务检查状态
        ScheduledFuture<?> taskFuture = scheduler.scheduleAtFixedRate(() -> {
            TaskEntity task = iTaskService.get(taskId);
            if (task.getStatus().equals(ProcessEnum.Success.getValue())) {
                JSONObject result = JSONObject.parseObject(task.getResult());
                resultFuture.complete(result.getString("ret"));
                scheduler.shutdown(); // 任务完成关闭调度
            } else if (LocalDateTime.now().isAfter(task.getCreateTime().plusMinutes(50))) {
                resultFuture.completeExceptionally(new TimeoutException("任务超时"));
                scheduler.shutdown();
            }
        }, 0, 1, TimeUnit.SECONDS);
        scheduler.schedule(() -> {
            if (!resultFuture.isDone()) {
                resultFuture.completeExceptionally(new TimeoutException("轮询超时"));
                taskFuture.cancel(true);
            }
        }, 50, TimeUnit.MINUTES);

        CompletableFuture<Void> downloadFuture = resultFuture.thenCompose(url -> {
            CompletableFuture<Void> future = new CompletableFuture<>();
            Request request = new Request.Builder()
                    .url(url)
                    .header("Accept", "application/octet-stream")
                    .build();

            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try (ResponseBody body = response.body();
                         InputStream inputStream = body.byteStream()) {

                        byte[] buffer = new byte[8192]; // 增大缓冲区提升吞吐量[3](@ref)
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }
                        future.complete(null);
                    } catch (IOException e) {
                        future.completeExceptionally(e);
                    }
                }

                @Override
                public void onFailure(Call call, IOException e) {
                    future.completeExceptionally(e);
                }
            });
            return future;

        });
        return downloadFuture;
    }

    public BaseResp<String> videoCut(@Valid VideoCutSavaReq req) {
        KnowledgeResourceEntity file = iResourceService.getNotNull(req.getDocId());
        if (!StringComUtils.isUrlFileCorrupted(file.getUrl(), 1)) {
            throw new ServerException(NOT_EXIST, "源文件");
        }
        TaskEntity task = iTaskService.send(file.getTenantId(), file.getCreatorId(), 0L, new TaskContentForVideoCut(file.getUrl(), req.getStart(), req.getEnd(), "", req.getRemoveAreas(), req.getWatermarkAreas()), UUID.randomUUID().toString(), TaskTypeEnum.FileCut);
        return new BaseResp<>("/file/video/cut/download?taskId=" + task.getId() + "&docId=" + file.getDocId());
    }

    public BaseResp<Void> videoCutSave(VideoCutSavaReq req) {
        KnowledgeResourceEntity file = iResourceService.getNotNull(req.getDocId());
        if (!StringComUtils.isUrlFileCorrupted(file.getUrl(), 1)) {
            throw new ServerException(NOT_EXIST, "源文件");
        }
        KnowledgeGroupEntity sourceGroup = knowledgeGroupService.getNotNull(file.getGroupId());
        KnowledgeGroupEntity targetGroup = knowledgeGroupService.getNotNull(req.getGroupId());
        // 校验保存视频片段权限
        checkSaveSubVideoPermission(sourceGroup, targetGroup);
        KnowledgeResourceEntity exist = iResourceService.getByGroupId_Title_Suffix(req.getGroupId(), req.getFileName(), file.getSuffix());
        if (Objects.nonNull(exist)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.DUPLICATE_NAME);
        }
        KnowledgeResourceEntity newFile = new KnowledgeResourceEntity();
        newFile.setDocId(UUID.randomUUID().toString());
        newFile.setTitle(req.getFileName());
        newFile.setSize(0L);
        newFile.setCount(0L);
        newFile.setSuffix(file.getSuffix());
        newFile.setType(file.getType());
        newFile.setEnable(true);
        newFile.setDescription("VideoCut");
        newFile.setUrl("");
        newFile.setTenantId(getTenantId());
        newFile.setCreatorId(getUserCode());
        newFile.setUpdateId(getUserCode());
        newFile.setCreateTime(LocalDateTime.now());
        newFile.setUpdateTime(LocalDateTime.now());
        newFile.setHandleStatus(ProcessEnum.Executing.getValue());
        newFile.setGroupId(targetGroup.getId());
        newFile.setGroupPath("");
        newFile.setSubheading("");
        JSONObject extInfo = new JSONObject();
        extInfo.put("sys_file_time", StringComUtils.convertStr(newFile.getUpdateTime()));
        newFile.setExtInfo(JSON.toJSONString(extInfo));
        newFile.setPreviewSrc("");
        TemplateEntity defaultTemplate = templateService.getDefault(newFile.getTenantId(), newFile.getType());
        newFile.setTemplateId(defaultTemplate == null ? null : defaultTemplate.getId());
        newFile.setStrategy("{}");
        iResourceService.saveOrUpdate(newFile);
        iTaskService.send(file.getTenantId(), file.getCreatorId(), 0L, new TaskContentForVideoCut(file.getUrl(), req.getStart(), req.getEnd(), newFile.getDocId(), req.getRemoveAreas(), req.getWatermarkAreas()), UUID.randomUUID().toString(), TaskTypeEnum.FileCut);
        return new BaseResp<>();

    }

    public List<VideoSubResp> subList(String docId) {
        List<SubVideoEntity> subVideoEntityList = subVideoService.findAllLeafNodes(docId);
        // sub video只有一个，说明不是合并的视频，直接返回空
        if (CollectionUtils.isEmpty(subVideoEntityList) || subVideoEntityList.size() == 1) {
            return Collections.emptyList();
        }
        List<VideoSubResp> videoSubRespList = subVideoEntityList.stream().map(VideoSubResp::new).collect(Collectors.toList());

        long startPoint = 0L;
        for (VideoSubResp videoSubResp : videoSubRespList) {
            if (videoSubResp.getDuration() != null) {
                videoSubResp.setStartTimestamp(startPoint);
                videoSubResp.setEndTimestamp(startPoint + videoSubResp.getDuration());
                startPoint += videoSubResp.getDuration();
            }
        }
        return videoSubRespList;
    }

    private void checkSaveSubVideoPermission(KnowledgeGroupEntity sourceGroup, KnowledgeGroupEntity targetGroup) {
        if (knowledgeGroupService.isSync(targetGroup)) {
            throw new ServiceException(SYNC_GROUP_LIMIT, "导入文件");
        }
        if (ResourceVisibleTypeEnum.PERSONAL.getValue().equals(sourceGroup.getVisibleType())) {
            return;
        }
        if (ResourceVisibleTypeEnum.PUBLIC.getValue().equals(sourceGroup.getVisibleType()) && !ResourceVisibleTypeEnum.PUBLIC.getValue().equals(targetGroup.getVisibleType())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.SAVE_PART_LIMIT, "公共空间的视频片段仅可保存到公共空间");
        }
        if (ResourceImportantLevelEnum.IMPORTANT.getValue().equals(sourceGroup.getImportantLevel()) && !ResourceImportantLevelEnum.IMPORTANT.getValue().equals(targetGroup.getImportantLevel())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.SAVE_PART_LIMIT, "重要库的视频片段仅可保存到重要库");
        }

    }

    public String subSave(VideoSubSavaReq req) {
        KnowledgeResourceEntity resource = iResourceService.getNotNull(req.getDocId());
        KnowledgeGroupEntity sourceGroup = knowledgeGroupService.getNotNull(resource.getGroupId());
        KnowledgeGroupEntity targetGroup = knowledgeGroupService.getNotNull(req.getGroupId());
        // 校验保存视频片段权限
        checkSaveSubVideoPermission(sourceGroup, targetGroup);
        KnowledgeResourceEntity exist = iResourceService.getByGroupId_Title_Suffix(req.getGroupId(), req.getFileName(), resource.getSuffix());
        if (Objects.nonNull(exist)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.DUPLICATE_NAME);
        }
        SubVideoEntity subVideoEntity = subVideoService.getByFileId(req.getSubFileId());
        if (Objects.isNull(subVideoEntity)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "视频分段");
        }
        String url = subVideoEntity.getUrl();
        if (!UrlCheckUtil.isUrlAccessible(url)) {
            url = URLUtil.encode(url);
            if (!UrlCheckUtil.isUrlAccessible(url)) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.FILE_LOST, "视频分段");
            }
        }
        String newDocId = UUID.randomUUID().toString();
        String newUrl = minioStorage.upload(url, StoragePathEnum.File.getValueByUrl(getTenantId(), newDocId, UUID.randomUUID() + "." + subVideoEntity.getSuffix()));
        KnowledgeResourceEntity newFile = new KnowledgeResourceEntity();
        newFile.setDocId(newDocId);
        newFile.setTitle(req.getFileName());
        newFile.setSize(0L);
        newFile.setCount(0L);
        newFile.setSuffix(subVideoEntity.getSuffix());
        newFile.setType(FileTypeEnum.VIDEO.getType());
        newFile.setEnable(true);
        newFile.setDescription("VideoSub");
        newFile.setUrl(newUrl);
        newFile.setTenantId(getTenantId());
        newFile.setCreatorId(getUserCode());
        newFile.setUpdateId(getUserCode());
        newFile.setCreateTime(LocalDateTime.now());
        newFile.setUpdateTime(LocalDateTime.now());
        newFile.setHandleStatus(ProcessEnum.Executing.getValue());
        newFile.setGroupId(targetGroup.getId());
        newFile.setGroupPath("");
        newFile.setSubheading("");
        JSONObject extInfo = new JSONObject();
        extInfo.put("sys_file_time", StringComUtils.convertStr(newFile.getUpdateTime()));
        newFile.setExtInfo(JSON.toJSONString(extInfo));
        newFile.setPreviewSrc("");
        TemplateEntity defaultTemplate = templateService.getDefault(newFile.getTenantId(), newFile.getType());
        newFile.setTemplateId(defaultTemplate == null ? null : defaultTemplate.getId());
        newFile.setStrategy("{}");
        iResourceService.saveOrUpdate(newFile);
        resourceUtilService.handResource(newFile, newFile.getTemplateId(), newFile.getStrategy());
        return newFile.getDocId();
    }

    public void merge(VideoMergeReq req) {
        List<KnowledgeResourceEntity> resourceEntityList = iResourceService.listByResIds(req.getDocIds());
        // 校验
        if (resourceEntityList.stream().anyMatch(BaseEntity::getDeleted)) {
            throw new ServiceException(NOT_EXIST, "选择的视频");
        }
        if (resourceEntityList.size() < 2 || resourceEntityList.size() > 50) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.VIDEO_MERGE_COUNT_LIMIT);
        }
        if (resourceEntityList.stream().anyMatch(
                s -> ProcessEnum.ARCHIVE.getValue().equals(s.getHandleStatus()) ||
                        ProcessEnum.MERGING.getValue().equals(s.getHandleStatus()) ||
                        ProcessEnum.MERGE_FAILED.getValue().equals(s.getHandleStatus()))) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.VIDEO_MERGE_STATUS_LIMIT);
        }
        // 按传入顺序重新排序
        Map<String, KnowledgeResourceEntity> entityMap = resourceEntityList.stream()
                .collect(Collectors.toMap(KnowledgeResourceEntity::getDocId, entity -> entity));
        resourceEntityList = req.getDocIds().stream()
                .map(entityMap::get)
                .filter(s -> s != null && FileTypeEnum.VIDEO.getType().equals(s.getType()))
                .collect(Collectors.toList());

        KnowledgeGroupEntity groupEntity = knowledgeGroupService.getNotNull(req.getGroupId());
        // 新增合并视频文件
        KnowledgeResourceEntity resourceEntity = buildEntity(req, groupEntity);
        List<SubVideoEntity> subVideoEntityList = new ArrayList<>();
        int i = 0;
        for (KnowledgeResourceEntity subResource : resourceEntityList) {
            subVideoEntityList.add(buildSubVideo(subResource, resourceEntity.getDocId(), i++));
        }
        subVideoService.saveBatch(subVideoEntityList);
        iResourceService.save(resourceEntity);
        for (KnowledgeResourceEntity resource : resourceEntityList) {
            resourceEntityList.forEach(s -> resourceUtilService.cancelLearning(s));
            if (ProcessEnum.Executing.getValue().equals(resource.getHandleStatus()) || ProcessEnum.MERGING.getValue().equals(resource.getHandleStatus())) {
                resource.setHandleStatus(ProcessEnum.Fail.getValue());
            }
            resource.setUpdateId(getUserCode());
            resource.setUpdateTime(LocalDateTime.now());
            resource.setDeleted(true);
        }
        List<Long> ids = resourceEntityList.stream().map(KnowledgeResourceEntity::getId).collect(Collectors.toList());
        UpdateResourceChunkEnableDTO updateResourceChunkEnableDTO = new UpdateResourceChunkEnableDTO(ids, false);
        iTaskService.send(getTenantId(), getUserCode(), 0L, updateResourceChunkEnableDTO, UUID.randomUUID().toString(), TaskTypeEnum.UpdateResourceChunk);
        iResourceService.updateBatchById(resourceEntityList);

        List<String> subVideoUrlList = subVideoEntityList.stream().map(SubVideoEntity::getUrl).collect(Collectors.toList());

        Map<String, Object> inputs = new HashMap<>();

        inputs.put("subVideoUrlList", subVideoUrlList);
        inputs.put("docId", resourceEntity.getDocId());

        WorkflowAsyncRunReq workflowAsyncRunReq = new WorkflowAsyncRunReq()
                .setId(aasWorkflowProperties.getWorkflowId().getVideoMerge())
                .setInput(inputs)
                .setCallback(aasWorkflowProperties.getWorkflowId().getImportCallback() + "/" + resourceEntity.getDocId());
        log.info("workflowAsyncRunReq:{}", JSON.toJSONString(workflowAsyncRunReq));
        WorkflowAsyncRunResp workflowAsyncRunResp = aaasWorkflowClient.runAsync(workflowAsyncRunReq);
        if (workflowAsyncRunResp == null || !Objects.equals("0", workflowAsyncRunResp.getCode())) {
            log.error("下发工作流任务失败{}", JSON.toJSONString(workflowAsyncRunResp));
            throw new RuntimeException("下发工作流任务失败");
        }
        String workflowInstanceId = workflowAsyncRunResp.getData();
        log.info("文件导入下发工作流任务成功：docId:{}, workflowInstanceId:{}", resourceEntity.getDocId(), workflowInstanceId);
        resourceEntity.setWorkflowId(workflowInstanceId);
        resourceEntity.setHandleStatus(ProcessEnum.MERGING.getValue());
        iResourceService.updateById(resourceEntity);
    }

    private KnowledgeResourceEntity buildEntity(VideoMergeReq req, KnowledgeGroupEntity groupEntity) {
        KnowledgeResourceEntity resourceEntity = new KnowledgeResourceEntity();
        resourceEntity.setDocId(UUID.randomUUID().toString());
        resourceEntity.setTitle(req.getTitle());
        resourceEntity.setSize(-1L);
        resourceEntity.setCount(0L);
        resourceEntity.setSuffix("mp4");
        resourceEntity.setType(FileTypeEnum.VIDEO.getType());
        resourceEntity.setEnable(true);
        resourceEntity.setDescription("视频合并");
        resourceEntity.setUrl("");
        resourceEntity.setTenantId(getTenantId());
        resourceEntity.setCreatorId(getUserCode());
        resourceEntity.setUpdateId(getUserCode());
        resourceEntity.setCreateTime(LocalDateTime.now());
        resourceEntity.setUpdateTime(LocalDateTime.now());
        resourceEntity.setHandleStatus(ProcessEnum.Fail.getValue());
        resourceEntity.setGroupId(groupEntity.getId());
        resourceEntity.setGroupPath("");
        JSONObject extInfo = new JSONObject();
        extInfo.put("sys_file_time", StringComUtils.convertStr(resourceEntity.getUpdateTime()));
        extInfo.put("system_merge", 1);
        List<CustomerFieldDto> customFields = req.getCustomFields();
        List<CustomEntity> customEntities = customService.queryFillInfo(getTenantId(), FileTypeEnum.VIDEO.getType());
        // 校验必填项
        for (CustomEntity customEntity : customEntities) {
            if (customEntity.getRequired()) {
                if (customFields.stream().noneMatch(s -> s.getField().equalsIgnoreCase(customEntity.getField()) && StringUtils.isNotBlank(s.getValue()))) {
                    throw new ServiceException(CUSTOM_NOT_EMPTY, customEntity.getName());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(customFields)) {
            for (CustomerFieldDto dto : customFields) {
                if (StringUtils.isNotBlank(dto.getField())) {
                    extInfo.put("customer_" + dto.getField(), dto.getValue());
                }
            }
        }
        // 填充搜索扩展字段
        customService.fillSearchExtInfo(extInfo, customEntities);

        resourceEntity.setExtInfo(JSON.toJSONString(extInfo));
        resourceEntity.setPreviewSrc("");
        TemplateEntity templateEntity = Objects.isNull(req.getTemplateId()) ? templateService.getDefault(getTenantId(), FileTypeEnum.VIDEO.getType()) : templateService.getNotNull(getTenantId(), req.getTemplateId());
        resourceEntity.setTemplateId(templateEntity.getId());
        resourceEntity.setStrategy(templateEntity.getStrategy());
        return resourceEntity;
    }

    private SubVideoEntity buildSubVideo(KnowledgeResourceEntity resource, String newDocId, Integer sort) {
        SubVideoEntity subVideoEntity = new SubVideoEntity();
        subVideoEntity.setDocId(newDocId);
        subVideoEntity.setFileId(resource.getDocId());
        subVideoEntity.setGroupId(resource.getGroupId());
        subVideoEntity.setTitle(resource.getTitle());
        subVideoEntity.setSuffix(resource.getSuffix());
        subVideoEntity.setUrl(resource.getUrl());
        subVideoEntity.setSize(resource.getSize());
//        subVideoEntity.setDuration();
//        subVideoEntity.setThumbnail();
        subVideoEntity.setOperation(0);
        subVideoEntity.setSort(sort);
        subVideoEntity.setTenantId(resource.getTenantId());
        subVideoEntity.setCreatorId(resource.getCreatorId());
        subVideoEntity.setUpdateId(resource.getUpdateId());
        subVideoEntity.setCreateTime(resource.getCreateTime());
        subVideoEntity.setUpdateTime(LocalDateTime.now());
        return subVideoEntity;
    }

    public void split(String docId) {

        // 校验
        KnowledgeResourceEntity resource = iResourceService.getNotNull(docId);
        if (!resource.isMerge()) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.VIDEO_IS_NOT_MERGE);
        }
        if (ProcessEnum.ARCHIVE.getValue().equals(resource.getHandleStatus())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.VIDEO_SPLIT_ARCHIVE_LIMIT);
        }
        // 查询所有叶子节点视频，多重合并时一拆到底，中间合并的不恢复
        List<SubVideoEntity> allDescendants = subVideoService.findAllLeafNodes(docId);
        if (CollectionUtils.isEmpty(allDescendants)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.VIDEO_SPLIT_EMPTY);
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("恢复chunk工作流");
        List<String> docIds = allDescendants.stream().map(SubVideoEntity::getFileId).collect(Collectors.toList());
        List<KnowledgeResourceEntity> resourceEntityList = iResourceService.listByResIds(docIds);
        List<Long> idsNeedEnable = resourceEntityList.stream().filter(s -> Boolean.TRUE.equals(s.getEnable())).map(KnowledgeResourceEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(idsNeedEnable)) {
            UpdateResourceChunkEnableDTO updateResourceChunkEnableDTO = new UpdateResourceChunkEnableDTO(idsNeedEnable, true);
            iTaskService.send(getTenantId(), getUserCode(), 0L, updateResourceChunkEnableDTO, UUID.randomUUID().toString(), TaskTypeEnum.UpdateResourceChunk);
        }
        stopWatch.stop();
        stopWatch.start("恢复视频记录，失败的重新学习");
        for (KnowledgeResourceEntity resourceEntity : resourceEntityList) {
            // 检查名称重复、重新命名
            KnowledgeResourceEntity existsRes = iResourceService.getByGroupId_Title_Suffix(resourceEntity.getGroupId(), resourceEntity.getTitle(), resourceEntity.getSuffix());
            if (Objects.nonNull(existsRes)) {
                String newTitle = iResourceService.getNewTitle(resourceEntity.getGroupId(), resourceEntity.getTitle(), resourceEntity.getSuffix());
                resourceEntity.setTitle(newTitle);
            }
            // 恢复
            resourceEntity.setDeleted(false);
            iResourceService.updateById(resourceEntity);
            // 失败的文件重新学习
            if (ProcessEnum.Fail.getValue().equals(resourceEntity.getHandleStatus())) {
                resourceUtilService.handResource(resourceEntity, resourceEntity.getTemplateId(), resourceEntity.getStrategy());
            }
        }
        stopWatch.stop();
        stopWatch.start("删除子视频记录，解除关联");
        subVideoService.deleteByRootDocId(docId);
        stopWatch.stop();
        stopWatch.start("删除合并视频和相关索引");

        iResourceService.deleteByIds(Collections.singletonList(resource.getId()));
        // 异步删除资源
        iTaskService.send(getTenantId(), getUserCode(), 0L, Collections.singletonList(resource.getDocId()), UUID.randomUUID().toString(), TaskTypeEnum.DeleteRes);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
    }

    public BaseResp<PersonExtractionSubResp> getPersonExtraction(String docId) {
        KnowledgeResourceEntity entity = iResourceService.get(docId);
        TemplateEntity templateEntity = templateService.getById(entity.getTemplateId());
        if (templateEntity.getType().equals(3)) {
            List<ResourcePersonEntity> list = iResourcePersonService.list(docId);
            if (CollectionUtils.isNotEmpty(list)) {
                JSONArray sub = this.getPersonExtractionSub(list);
                PersonExtractionSubResp resp = new PersonExtractionSubResp().setDocId(docId).setSubResult(sub);
                return new BaseResp<>(resp);
            }
        }
        return new BaseResp<>();
    }

    private JSONArray getPersonExtractionSub(List<ResourcePersonEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new JSONArray();
        }

        // 按时间点排序
        list = list.stream()
                .sorted(Comparator.comparing(ResourcePersonEntity::getTimePoint))
                .collect(Collectors.toList());

        JSONArray result = new JSONArray();
        JSONArray timeRange = new JSONArray();
        boolean isAlarm = list.get(0).getIsAlarm();

        // 只处理告警开始的时间点
        if (isAlarm) {
            timeRange.add(list.get(0).getTimePoint());
        }

        for (int i = 1; i < list.size(); i++) {
            ResourcePersonEntity current = list.get(i);
            if (!current.getIsAlarm().equals(isAlarm)) {
                if (isAlarm) {
                    // 添加结束时间点
                    timeRange.add(list.get(i - 1).getTimePoint());
                    result.add(timeRange);
                    timeRange = new JSONArray();
                }
                isAlarm = current.getIsAlarm();

                // 只处理告警开始的时间点
                if (isAlarm) {
                    timeRange.add(current.getTimePoint());
                }
            }
        }

        // 处理最后一个时间段
        if (isAlarm && timeRange.size() == 1) {
            timeRange.add(list.get(list.size() - 1).getTimePoint());
            result.add(timeRange);
        }

        return result;
    }

    public void inspectionExport(List<String> docIds, HttpServletResponse response) {
        try {
            if (docIds == null || docIds.isEmpty()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EMPTY, "资源");
            }

            //文件名
            String fileName = "视频巡检详情导出.xlsx";
            //创建输出流
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            // 读取资源文件（这样打成jar模板文件也在里面，防止模板文件被修改导致功能不可用，文件是在src/main/resources/excelTemplates下的）
            ClassPathResource resource = new ClassPathResource("template" + File.separator + "视频巡检详情导出模板.xlsx");

            if (docIds.size() == 1) {
                // 文件名
                KnowledgeResourceEntity knowledgeResourceEntity = iResourceService.get(docIds.get(0));
                fileName = knowledgeResourceEntity.getTitle() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
                // 组织并填充模板数据
                byteArrayOutputStream = this.compositeFill(docIds.get(0), knowledgeResourceEntity.getTitle(), resource.getInputStream());
            } else {
                List<ByteArrayOutputStream> byteArrayOutputStreams = new ArrayList<>();
                for (String docId : docIds) {
                    // 文件名
                    KnowledgeResourceEntity knowledgeResourceEntity = iResourceService.get(docId);
                    // 组织并填充模板数据
                    byteArrayOutputStream = this.compositeFill(docId, knowledgeResourceEntity.getTitle(), resource.getInputStream());
                    byteArrayOutputStreams.add(byteArrayOutputStream);
                }
                byteArrayOutputStream = ExcelMerger.mergeExcelInput(byteArrayOutputStreams);
            }


            // 设置响应头，告诉浏览器这是一个下载的文件，这里文件名
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", String.format("attachment;filename=%s", URLEncoder.encode(fileName, "UTF-8")));

            // 将文件内容写入响应输出流，浏览器可以直接触发下载
            response.getOutputStream().write(byteArrayOutputStream.toByteArray());
            response.getOutputStream().flush();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.ERROR);
        }
    }

    private ByteArrayOutputStream compositeFill(String docId, String sheetName, InputStream templateInputStream) {
        //人脸
        FaceListReq faceListReq = new FaceListReq().setDocId(docId).setShowUnKnowFace(false);
        List<FaceListResp> faceListResps = fileBiz.faceList(faceListReq).getData();
        //告警事件
        VideoTagReq videoTagReq = new VideoTagReq().setDocId(docId);
        TagFrameResp tagFrameResp = this.tagFrame(videoTagReq).getData();
        List<TagResp> tagResps = tagFrameResp.getTags();
        //异常补充
        SupplementListReq supplementListReq = new SupplementListReq().setDocId(docId);
        List<SupplementResp> supplementResps = this.supplementList(supplementListReq).getData();
        //巡检结论
        InspectionConclusionReq inspectionConclusionReq = new InspectionConclusionReq().setDocId(docId);
        InspectionConclusionResp inspectionConclusionResp = this.getInspectionConclusion(inspectionConclusionReq).getData();
        //归档状态
        KnowledgeResourceEntity knowledgeResourceEntity = iResourceService.get(docId);

        //处理人脸
        if (CollUtil.isNotEmpty(faceListResps)) {
            for (FaceListResp faceListResp : faceListResps) {
                faceListResp.setIsFocusStr(faceListResp.getIsFocus() ? "重点" : "非重点");
            }
        }
        //处理异常补充图片
        if (CollUtil.isNotEmpty(supplementResps)) {
            for (SupplementResp supplementResp : supplementResps) {
                WriteCellData<Void> writeCellData = new WriteCellData<>();
                try {
                    for (String url : supplementResp.getUrls()) {
                        // 可以放入多个图片
                        List<ImageData> imageDataList = new ArrayList<>();

                        ImageData imageData = new ImageData();
                        // 使用URL读取网络资源
                        URL imageUrl = new URL(url);
                        byte[] data = IOUtils.toByteArray(imageUrl.openStream());

                        imageData.setImage(data);
                        //这四个属性就是在上面的范围的前提下 图片的边距
                        imageData.setTop(5);
                        imageData.setBottom(5);
                        imageData.setLeft(5);
                        imageData.setRight(5);

                        imageDataList.add(imageData);
                        writeCellData.setImageDataList(imageDataList);
                    }
                } catch (Exception e) {
                    //获取图片出现异常
                    e.printStackTrace();
                }
                supplementResp.setWriteCellData(writeCellData);
            }
        }


        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 使用EasyExcel的模板填充功能，在这里指定合并单元格，这里应该是easyExcel的bug，第一列无法合并，其他列都可以，所以第一列单独用原生poi进行合并
        sheetName = sheetName.substring(0, Math.min(sheetName.length(), 30));
        try (ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream).withTemplate(templateInputStream).registerWriteHandler(new CustomSheetWriteHandler(sheetName)).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            // 防止上面两个表格覆盖下面两个表格，每一行都采用新增一行的方式
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            // 使用模板填充，必须使用FillWrapper，这是官方要求，并且每行两个表格只能有一个表格设置增行，否则会存在一个表格有空行，这里是造的测试数据
            excelWriter.fill(new FillWrapper("FaceListResp", faceListResps), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper("TagResp", tagResps), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper("SupplementResp", supplementResps), fillConfig, writeSheet);

            String handleStatus;
            switch (knowledgeResourceEntity.getHandleStatus()) {
                case 1:
                    handleStatus = "学习中";
                    break;
                case 2:
                    handleStatus = "学习成功";
                    break;
                case 3:
                    handleStatus = "学习失败";
                    break;
                case 4:
                    handleStatus = "已归档";
                    break;
                default:
                    handleStatus = "";
                    break;
            }

            // 设置表格外的填充数据
            HashMap<String, Object> map = new HashMap<>();
            map.put("title", knowledgeResourceEntity.getTitle());
            map.put("ret", inspectionConclusionResp.getRet());
            map.put("status", inspectionConclusionResp.getStatus() == 1 ? "异常" : "正常");
            map.put("handleStatus", handleStatus);
            excelWriter.fill(map, writeSheet);
        }
        return byteArrayOutputStream;
    }


}
