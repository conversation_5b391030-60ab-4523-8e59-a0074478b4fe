package com.linker.fusion.knowledgecenter.server.biz;

import com.linker.fusion.knowledgecenter.infrastructure.enums.OprationLogRecordSubTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OprationLogRecordTypeEnum;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class LogRecordMenuConverter {
    public static void main(String[] args) {
        List<LogRecordTypeMenuResp> logRecordTypeMenuResps = LogRecordMenuConverter.convertToMenuStructure();
        System.out.println(logRecordTypeMenuResps);
    }

    public static List<LogRecordTypeMenuResp> convertToMenuStructure() {
        List<LogRecordTypeMenuResp> result = new ArrayList<>();

        // Group LogRecordSubTypeEnum by parentCode
        Map<String, List<OprationLogRecordSubTypeEnum>> groupedByParent = Arrays.stream(OprationLogRecordSubTypeEnum.values())
                .collect(Collectors.groupingBy(OprationLogRecordSubTypeEnum::getParentCode));

        // Process each LogRecordTypeEnum
        for (OprationLogRecordTypeEnum type : OprationLogRecordTypeEnum.values()) {
            String typeKey = convertToCamelCase(type.name());

            // Get all sub-types for this type
            List<OprationLogRecordSubTypeEnum> subTypes = groupedByParent.getOrDefault(type.name(), new ArrayList<>());

            // Convert sub-types to children format
            List<LogRecordActionMenu> children = subTypes.stream()
                    .map(subType -> new LogRecordActionMenu(
                            subType.getDesc(),
                            subType.name()
                    ))
                    .collect(Collectors.toList());


            // Add to result
            result.add(new LogRecordTypeMenuResp(
                    type.getDesc(),
                    typeKey,
                    children
            ));
        }

        return result;
    }

    private static String convertToCamelCase(String enumName) {
        // Convert enum name like "FILE_MANAGEMENT" to "FileManagement"
//        return Arrays.stream(enumName.split("_"))
//                .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
//                .collect(Collectors.joining());
        return enumName;
    }

    private static String convertActionToKey(String actionName) {
        // Simple conversion for action names to keys
        return actionName.replace(" ", "");
    }


    // Entity classes
    public static class LogRecordTypeMenuResp {
        private String name;
        private String key;
        private List<LogRecordActionMenu> children;

        public LogRecordTypeMenuResp(String name, String key, List<LogRecordActionMenu> children) {
            this.name = name;
            this.key = key;
            this.children = children;
        }

        // Getters and setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public List<LogRecordActionMenu> getChildren() {
            return children;
        }

        public void setChildren(List<LogRecordActionMenu> children) {
            this.children = children;
        }
    }

    public static class LogRecordActionMenu {
        private String name;
        private String key;

        public LogRecordActionMenu(String name, String key) {
            this.name = name;
            this.key = key;
        }

        // Getters and setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }
}