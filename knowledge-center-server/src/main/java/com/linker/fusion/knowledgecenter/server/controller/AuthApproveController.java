package com.linker.fusion.knowledgecenter.server.controller;

import com.alibaba.fastjson.JSON;
import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.ApproveProcessResp;
import com.linker.fusion.knowledgecenter.server.biz.AuthApproveBiz;
import com.linker.fusion.knowledgecenter.server.biz.AuthBiz;
import com.linker.fusion.knowledgecenter.server.dto.auth.AuthApproveAddReq;
import com.linker.fusion.knowledgecenter.server.dto.auth.AuthApproveCallbackReq;
import com.linker.fusion.knowledgecenter.server.dto.auth.AuthApproveLibProcessReq;
import com.linker.fusion.knowledgecenter.server.dto.auth.UpdateReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.linker.core.auth.enums.InterfaceType.TOC;

@Slf4j
@Api(tags = "权限审批")
@RestController
@RequestMapping("/auth/approve")
public class AuthApproveController {

    @Resource
    private AuthApproveBiz authApproveBiz;

    @Resource
    private AuthBiz authBiz;

    @GetMapping("process/list")
    @ApiOperation(value = "审批流列表")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<ApproveProcessResp>> processList() {
        return new BaseResp<>(authApproveBiz.processList());
    }

    @PostMapping("process/lib")
    @ApiOperation(value = "设置知识库审批流")
    public BaseResp<Void> setLibProcess(@RequestBody @Valid AuthApproveLibProcessReq req) {
        authApproveBiz.setLibProcess(req.getGroupId(), req.getApproveProcessKey());
        return new BaseResp<>();
    }

    @PostMapping("apply")
    @ApiOperation(value = "发起审批")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> apply(@RequestBody @Valid AuthApproveAddReq req) {
        authApproveBiz.apply(req);
        return new BaseResp<>();
    }

    @PostMapping("callback")
    @ApiOperation(value = "审批回调", hidden = true)
    public BaseResp<Void> callBack(@RequestBody @Valid AuthApproveCallbackReq req) {
        UpdateReq updateReq = authApproveBiz.callback(req);
        if (updateReq != null && updateReq.getSourceId() != null) {
            log.info("callback 更新权限 {}", JSON.toJSONString(updateReq));
            authBiz.update(updateReq.getTenantId(), "", updateReq);
        }
        return new BaseResp<>();
    }

}
