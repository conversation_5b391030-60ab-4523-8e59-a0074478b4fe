package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.linker.fusion.knowledgecenter.infrastructure.dto.query.QbAnswerDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.LinkedList;

/**
 * @Author: YangQiyan
 * @Description: 类说明
 * @Date: Created in 2025/4/2
 */
@ApiModel(description = ": 类说明")
@Data
public class PaperScoreResp {
    /**
     * 试卷回答历史记录
     */
    @ApiModelProperty("试卷回答历史记录")
    private LinkedList<PaperAnswerRecordDTO> paperAnswerRecordList;
    /**
     * 总题目数
     */
    @ApiModelProperty("总题目数")
    private Integer totalNumber;

    /**
     * 总共完成题目数
     */
    @ApiModelProperty("总共完成题目数")
    private Integer completeTotalNumber;

    /**
     * 正确题目数
     */
    @ApiModelProperty("正确题目数")
    private Integer realTotalNumber;

    /**
     * 错误题目数
     */
    @ApiModelProperty("错误题目数")
    private Integer errorTotalNumber;

    /**
     * 正确率
     */
    @ApiModelProperty("正确率")
    private BigDecimal realRate;

    /**
     * 试卷状态, 0:回答中, 1:完成
     */
    @ApiModelProperty("试卷状态, 0:回答中, 1:完成")
    private Integer paperStatus;

    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    private String finishTime;
    @ApiModel
    @Data
    @Accessors(chain = true)
    public static class PaperAnswerRecordDTO {
        /**
         * 问题序号
         */
        @ApiModelProperty("问题序号")
        private Integer questionNumber;
        /**
         * 问题内容
         */
        @ApiModelProperty("问题内容")
        private String questionContent;
        /**
         * 问题类型, CHOICE:选题, TRUE_FALSE:判断题
         */
        @ApiModelProperty("问题类型, CHOICE:选题, TRUE_FALSE:判断题")
        /**
         * 问题类型, CHOICE("选择题"),MULTIPLE_CHOICE("多选题"),TRUE_FALSE("判断题"),FILL_IN_BLANK("填空题");
         * @see com.linker.fusion.knowledgecenter.infrastructure.enums.QuestionTypeEnum
         */
        private String questionType;
        /**
         * 问题答案选型
         * key选型, value选项内容
         */
        @ApiModelProperty("问题答案选型 key选型, value选项内容")
        private LinkedList<QbAnswerDTO> answerList;
        /**
         * 填写答案 A/B/C/D/0/1
         */
        @ApiModelProperty("填写答案 A/B/C/D/0/1")
        private Object fillAnswer;
        /**
         * 正确答案 A/B/C/D/0/1
         */
        @ApiModelProperty("正确答案 A/B/C/D/0/1")
        private Object correctAnswer;
        /**
         * 回答结果, 0:错误, 1:正确
         */
        @ApiModelProperty("回答结果, 0:错误, 1:正确")
        private Integer answerResult;
        /**
         * 答案解析
         */
        @ApiModelProperty("答案解析")
        private String answerAnalysis;

    }
}
