package com.linker.fusion.knowledgecenter.server.convert;

import com.linker.fusion.knowledgecenter.infrastructure.dto.query.LogRecordPageQuery;
import com.linker.fusion.knowledgecenter.infrastructure.entity.LogRecordEntity;
import com.linker.fusion.knowledgecenter.server.dto.req.operationlog.LogRecordPageReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.operationlog.LogRecordPageResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface LogRecordConvert {

    LogRecordConvert INSTANCE = Mappers.getMapper(LogRecordConvert.class);

    LogRecordPageQuery reqToQuery(LogRecordPageReq req);

    LogRecordPageResp pageToResp(LogRecordEntity c);
}
