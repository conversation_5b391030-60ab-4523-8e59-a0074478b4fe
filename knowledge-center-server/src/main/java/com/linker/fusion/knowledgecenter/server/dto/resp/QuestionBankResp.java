package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.linker.fusion.knowledgecenter.infrastructure.model.AuthActionModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.QuestionBankOptionModel;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.BaseEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.KnowledgeGroupParentResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class QuestionBankResp extends BaseEntityResp {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("类目ID")
    private Long groupId;

    @ApiModelProperty("问题")
    private String question;

    @ApiModelProperty("题目类型")
    /**
     * 问题类型, CHOICE("选择题"),MULTIPLE_CHOICE("多选题"),TRUE_FALSE("判断题"),FILL_IN_BLANK("填空题");
     * @see com.linker.fusion.knowledgecenter.infrastructure.enums.QuestionTypeEnum
     */
    private String questionType;

    @ApiModelProperty("答案")
    private String answer;

    @ApiModelProperty("答案解析")
    private String analysis;

    @ApiModelProperty("问题图片URL")
    private String questionImage;

    @ApiModelProperty("选项列表")
    private List<QuestionBankOptionModel> options;

    @ApiModelProperty("是否启用")
    private Boolean enable;

    @ApiModelProperty("是否AI生成")
    private Boolean isAiGenerated;

    @ApiModelProperty("权限")
    private AuthActionModel authActions;
    @ApiModelProperty("授权编码列表")
    private List<String> authCodes;
    @ApiModelProperty("引用的问答对信息")
    private String faqInfo;

    @ApiModelProperty("类目路径")
    private KnowledgeGroupParentResp groupParentResp;
} 