package com.linker.fusion.knowledgecenter.server.dto.api.v1;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Request
 *
 * <AUTHOR>
 */
@Data
public class ResourceIdReq {
    @JsonProperty("resource_id")
    @NotNull
    @ApiModelProperty(value = "文件id", required = true)
    private String resourceId;
}