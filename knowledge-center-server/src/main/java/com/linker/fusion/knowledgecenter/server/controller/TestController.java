package com.linker.fusion.knowledgecenter.server.controller;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.HttpPersonSearchReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.HttpPersonSearchResp;
import com.linker.fusion.knowledgecenter.infrastructure.config.PromptMappingConfig;
import com.linker.fusion.knowledgecenter.infrastructure.constants.LogRecordSubType;
import com.linker.fusion.knowledgecenter.infrastructure.constants.LogRecordType;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.TaskTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IKnowledgeResourceManager;
import com.linker.fusion.knowledgecenter.infrastructure.model.AuthActionModel;
import com.linker.fusion.knowledgecenter.server.biz.TestBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.XXXReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.ImportReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.XXXResp;
import com.linker.fusion.knowledgecenter.service.domain.http.IHttpService;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.ComplexWorker;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.OSImageCaptionWorker;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.OSImageDrawRectWorker;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.docimport.OsDocSplitResultWorker;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.*;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.rag.QueryAggregateTask;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.search.AdvancedSearchChunkRecallFileWorker;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.search.AiSearchIntentRecognitionWorker;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.search.AiSearchQueryRouteTask;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.search.model.*;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ResourceUtilService;
import com.linker.fusion.knowledgecenter.service.domain.task.TaskService;
import com.linker.fusion.knowledgecenter.service.domain.task.dto.UpdateResourceChunkEnableDTO;
import com.linker.logapi.starter.annotation.LogRecord;
import com.linker.omos.client.config.WorkflowTaskContext;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.linker.core.auth.enums.InterfaceType.PUBLIC;
import static com.linker.core.auth.enums.InterfaceType.TOC;

/**
 * <AUTHOR>
 * @Date 2023/4/26 上午11:07
 * @DESC:
 */
@RestController
@RequestMapping("/test")
@Api(tags = "测试业务组装controller层")
@Slf4j
public class TestController {
    @Autowired
    TestBiz testBiz;
    @Autowired
    private PromptMappingConfig promptMappingConfig;

    @Autowired
    ComplexWorker complexWorker;

    @Resource
    OSImageCaptionWorker osImageCaptionWorker;

    @Resource
    private OSImageDrawRectWorker osImageDrawRectWorker;

    @Resource
    private IHttpService httpService;

    @Resource
    private AiSearchQueryRouteTask aiSearchQueryRouteTask;

    @Resource
    private QueryAggregateTask queryAggregateTask;

    @Resource
    private AiSearchIntentRecognitionWorker aiSearchIntentRecognitionWorker;

    @Resource
    private ResourceUtilService resourceUtilService;

    @Resource
    private OsDocSplitResultWorker osDocSplitResultWorker;
    @Resource
    private TaskService taskService;
    @Resource
    private IKnowledgeResourceManager iKnowledgeResourceManager;

    @Resource
    private AdvancedSearchChunkRecallFileWorker advancedSearchChunkRecallFileWorker;


    @GetMapping("/test")
    public BaseResp test() {
        List<String> keys = ListUtil.toList("1", "2", "3");
        List<AuthActionModel> key = taskService.testCache(keys);

        // List<KnowledgeGroupEntity> groups = iAuthService.getAuthedGroup("TOfgho0skvt2m", "21ae927f-e967-415f-9f28-76a85aae849a", new ArrayList<>(), KnowledgeTypeEnum.FILE.getType(), Arrays.asList(0L));
        return new BaseResp();
    }

    @PostMapping("/view")
    public BaseResp<XXXResp> view(@RequestBody XXXReq testReq) {
        log.info(JSON.toJSONString(promptMappingConfig));

        return testBiz.view(testReq);
    }

    @PostMapping("/test1")
    public ComplexOutput test1(@RequestBody ComplexInput input) {
        ComplexOutput execute = complexWorker.onTask(input, new WorkflowTaskContext("1"));
        return execute;
    }

    @PostMapping("/test2")
    public ImageCaptionOutput test2(@RequestBody ImageCaptionInput input) {
        return osImageCaptionWorker.onTask(input, new WorkflowTaskContext("1"));
    }

    @PostMapping("/test3")
    public ImageDrawRectOutput test3(@RequestBody ImageDrawRectInput input) {
        return osImageDrawRectWorker.onTask(input, new WorkflowTaskContext("1"));
    }

    @PostMapping("/view2")
    public BasePaginResp<XXXResp> viewPages(@RequestBody XXXReq testPageReq) {
        return testBiz.viewPages(testPageReq);
    }

    /**
     * 测试人脸识别
     */
    @PostMapping("/testPersonSearch")
    public BaseResp<HttpPersonSearchResp> testPersonSearch(@RequestBody HttpPersonSearchReq req) {
        return new BaseResp<>(httpService.personSearch(req));
    }

    @PostMapping("/import/aiSearch")
    public BaseResp<AiSearchQueryRouteOutput> importResource(@RequestBody AiSearchQueryRouteInput req) {
        return new BaseResp<>(aiSearchQueryRouteTask.onTask(req, new WorkflowTaskContext("1")));
    }

    @PostMapping("/rerank")
    public BaseResp<RerankOutput> importResource(@RequestBody RerankInput req) {
        return new BaseResp<>(queryAggregateTask.onTask(req, new WorkflowTaskContext("1")));
    }

    @PostMapping("/intent")
    public BaseResp<AiSearchIntentRecognitionOutput> importResource(@RequestBody AiSearchIntentRecognitionInput req) {
        return new BaseResp<>(aiSearchIntentRecognitionWorker.onTask(req, new WorkflowTaskContext("1")));
    }

    @PostMapping("/docSplitResult")
    public BaseResp<OsDocSplitResultOutput> docSplitResult(@RequestBody OsDocSplitResultInput req) {
        return new BaseResp<>(osDocSplitResultWorker.onTask(req, new WorkflowTaskContext("1")));
    }

    @PostMapping("/chunkRecallFile")
    public BaseResp<AdvancedSearchChunkRecallFileOutput> chunkRecallFile(@RequestBody AdvancedSearchChunkRecallFileInput req) {
        return new BaseResp<>(advancedSearchChunkRecallFileWorker.onTask(req, new WorkflowTaskContext("1")));
    }

    @PostMapping(("/testEventCheck"))
    public BaseResp<List<String>> testEventCheck() {
        List<String> list = resourceUtilService.eventCheck("https://om-agent-pre-minio.linker.cc/goal/cover/67dbf7e9e4b0123fd741fd5a_1742469097210.jpeg", Arrays.asList("单人谈话", "视线脱离谈话对象", "未及时关闭房门"));
        log.info("list:{}", list);
        return new BaseResp<>(list);
    }



    @PostMapping(("/testlog"))
    @LogRecord(success = "导入{{#req.fileName}}{getKnowledgeGroupTree{#req.groupId}}",
            type = LogRecordType.FILE_MANAGEMENT,
            subType = LogRecordSubType.IMPORT_FILE, bizId = "{{#req.resId}}")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<String> testlog(@RequestBody ImportReq req) {

        return new BaseResp<>();
    }

    @PostMapping(("/disableDeletedResourceChunk"))
    @FunctionPoint(interfaceType = PUBLIC)
    public BaseResp<String> disableDeletedResourceChunk(@RequestParam(value = "fileType", required = false) Integer fileType,
                                                        @RequestParam(value = "tenantId", required = false) String tenantId) {
        List<KnowledgeResourceEntity> list = iKnowledgeResourceManager.list(
                new LambdaQueryWrapper<KnowledgeResourceEntity>()
                        .eq(KnowledgeResourceEntity::getDeleted, true)
                        .eq(fileType != null, KnowledgeResourceEntity::getType, fileType)
                        .eq(StringUtils.isNotBlank(tenantId), KnowledgeResourceEntity::getTenantId, tenantId)
        );
        List<Long> ids = list.stream().map(KnowledgeResourceEntity::getId).collect(Collectors.toList());
        UpdateResourceChunkEnableDTO updateResourceChunkEnableDTO = new UpdateResourceChunkEnableDTO(ids, false);
        TaskEntity taskEntity = taskService.send("system", "system", 0L, updateResourceChunkEnableDTO, UUID.randomUUID().toString(), TaskTypeEnum.UpdateResourceChunk);
        return new BaseResp<>(taskEntity.getResult());
    }

    @GetMapping("/fixChunkCreateTime")
    @FunctionPoint(interfaceType = PUBLIC)
    public BaseResp<List<String>> fixChunkCreateTime(@RequestParam(value = "tenantId", required = false) String tenantId) {
        List<KnowledgeResourceEntity> list = iKnowledgeResourceManager.list(
                new LambdaQueryWrapper<KnowledgeResourceEntity>()
                        .in(KnowledgeResourceEntity::getType, Arrays.asList(1, 2, 3, 4))
                        .eq(StringUtils.isNotBlank(tenantId), KnowledgeResourceEntity::getTenantId, tenantId)
                        .select(KnowledgeResourceEntity::getId)
        );
        List<Long> ids = list.stream().map(KnowledgeResourceEntity::getId).collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        for (List<Long> longs : ListUtil.split(ids, 1000)) {
            TaskEntity taskEntity = taskService.send("system", "system", 0L, longs, UUID.randomUUID().toString(), TaskTypeEnum.FixChunkCreateTime);
            result.add(taskEntity.getResult());
        }
        return new BaseResp<>(result);
    }

}
