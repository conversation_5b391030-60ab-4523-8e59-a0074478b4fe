package com.linker.fusion.knowledgecenter.server.convert;

import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceASREntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceSegmentEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.AudioChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.ImageChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoFrameEntity;
import com.linker.fusion.knowledgecenter.server.dto.resp.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface FileConvert {

    FileConvert INSTANCE = Mappers.getMapper(FileConvert.class);

    SegmentPageResp toResp(ResourceSegmentEntity resourceSegment);

    List<SegmentPageResp> toResp(List<ResourceSegmentEntity> resourceSegments);

    List<VideoChunkListResp> toVideoChunkResp(List<VideoChunkEntity> videoChunks);

    List<AudioChunkResp> toAudioChunkResp(List<AudioChunkEntity> audioChunks);

    List<ImageChunkResp> toImageChunkResp(List<ImageChunkEntity> imageChunks);

    List<AsrListResp> toAsrResp(List<ResourceASREntity> resourceASREntities);
    List<FrameResp> toFrameResp(List<VideoFrameEntity> videoFrameEntities);
}
