package com.linker.fusion.knowledgecenter.server.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.exception.ServiceException;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthNodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OperatorTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.server.biz.VideoBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.core.BaseDocIdReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.IdBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.video.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.*;
import com.linker.fusion.knowledgecenter.service.domain.auth.GroupNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.auth.MenuAndResNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.task.ITaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.linker.core.auth.enums.InterfaceType.TOC;

@Api(tags = "视频管理")
@RestController
@RequestMapping("file/video")
@Slf4j
public class VideoController {
    @Resource
    VideoBiz videoBiz;
    @Resource
    IResourceService iResourceService;
    @Resource
    ITaskService iTaskService;

    @PostMapping("chunk/list")
    @ApiOperation(value = "分块列表", tags = "v1.5.0")
    @FunctionPoint(interfaceType = TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<List<VideoChunkListResp>> chunkList(@RequestBody VideoChunkListReq req) {
        return videoBiz.chunkList(req);
    }

    @PostMapping("chunk/update")
    @ApiOperation(value = "分块更新", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<Void> updateChunk(@RequestBody @Valid VideoChunkUpdateReq req) {
        videoBiz.updateChunk(req);
        return new BaseResp<>();
    }

    @PostMapping("chunk/add")
    @ApiOperation(value = "分块添加", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<Void> addChunk(@RequestBody @Valid VideoChunkAddReq req) {
        videoBiz.addChunk(req);
        return new BaseResp<>();
    }

    @PostMapping("chunk/delete")
    @ApiOperation(value = "分块删除", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, chunkIds = "{#req.chunkIds}")
    public BaseResp<Void> deleteChunk(@RequestBody VideoChunkDeleteReq req) {
        videoBiz.deleteChunk(req);
        return new BaseResp<>();
    }

    @PostMapping("segment/asr/list")
    @ApiOperation(value = "语音分块列表", tags = "v1.5.0")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<AudioChunkResp>> segmentAsrList(@RequestBody VideoSegAsrListReq req) {
        return videoBiz.asrChunkList(req);
    }


    @PostMapping("tag-frame")
    @ApiOperation(value = "标签抽帧数据", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<TagFrameResp> tagFrame(@RequestBody VideoTagReq req) {
        return videoBiz.tagFrame(req);
    }

    @PostMapping("tag-frame/delete")
    @ApiOperation(value = "标签抽帧数据删除", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> tagFrameDelete(@RequestBody VideoTagFrameDeleteReq req) {
        return videoBiz.tagFrameDelete(req);
    }

    @PostMapping("supplement/list")
    @ApiOperation(value = "异常补充列表", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<SupplementResp>> supplementList(@RequestBody SupplementListReq req) {
        return videoBiz.supplementList(req);
    }

    @PostMapping("supplement/add")
    @ApiOperation(value = "异常补充新增", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> supplementAdd(@RequestBody SupplementAddReq req) {
        return videoBiz.supplementAdd(req);
    }

    @PostMapping("supplement/update")
    @ApiOperation(value = "异常补充修改", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> supplementUpdate(@RequestBody SupplementUpdateReq req) {
        return videoBiz.supplementUpdate(req);
    }

    @PostMapping("supplement/delete")
    @ApiOperation(value = "异常补充删除", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> supplementDelete(@RequestBody IdBaseReq<Long> req) {
        return videoBiz.supplementDelete(req);
    }

    @PostMapping("inspection/conclusion")
    @ApiOperation(value = "巡检结论", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<InspectionConclusionResp> getInspectionConclusion(@RequestBody InspectionConclusionReq req) {
        return videoBiz.getInspectionConclusion(req);
    }

    @PostMapping("inspection/conclusion/edit")
    @ApiOperation(value = "修改巡检结论", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> editInspectionConclusion(@RequestBody InspectionConclusionEditReq req) {
        return videoBiz.editInspectionConclusion(req);
    }

    @PostMapping("inspection/conclusion/rerun")
    @ApiOperation(value = "重新生成巡检结论", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> rerunInspectionConclusion(@RequestBody InspectionConclusionReq req) {
        return videoBiz.rerunInspectionConclusion(req);
    }

    @PostMapping("inspection/conclusion/set/status")
    @ApiOperation(value = "设置状态", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> setInspectionConclusionStatus(@RequestBody InspectionConclusionStatusSetReq req) {
        return videoBiz.setInspectionConclusionStatus(req);
    }

    @PostMapping("cut")
    @FunctionPoint(interfaceType = TOC)
    @ApiOperation(value = "视频剪切", tags = "v1.7.0")
    public BaseResp<String> cut(@RequestBody @Valid VideoCutSavaReq req) {
        return videoBiz.videoCut(req);
    }
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(5, TimeUnit.MINUTES)
            .connectionPool(new ConnectionPool(20, 5, TimeUnit.MINUTES))
            .build();

    @GetMapping("cut/download")
    @ApiOperation(value = "视频剪切", tags = "v1.7.0")
    @LogFilter
    public void cutDownload(HttpServletResponse response, @RequestParam("docId") String docId, @RequestParam("taskId") Long taskId) throws IOException, InterruptedException {
        KnowledgeResourceEntity file = iResourceService.get(docId);
        if (Objects.isNull(file)) {
            response.setStatus(404);
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "文件");
        }
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setContentType("video/mp4");
        String fileName = URLEncoder.encode(file.getTitle(), "UTF-8") + DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + "." + file.getSuffix();
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        TaskEntity task = iTaskService.get(taskId);
        do {
            if (task.getStatus().equals(ProcessEnum.Success.getValue())) {
                break;
            }
            Thread.sleep(500);
            task = iTaskService.get(taskId);
        }
        while (LocalDateTime.now().isBefore(task.getCreateTime().plusMinutes(50)));
        String url = "";
        if (task.getStatus().equals(ProcessEnum.Success.getValue())) {
            JSONObject result = JSONObject.parseObject(task.getResult());
            url = result.getString("ret");
        } else {
            response.setStatus(500);
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.CUT_ERROR);
        }
        try (Response fileResp = client.newCall(new Request.Builder()
                .url(url)
                .header("Accept-Encoding", "application/octet-stream")
                .build()).execute()) {

            if (!fileResp.isSuccessful()) {
                throw new IOException("服务器返回错误: " + fileResp.code());
            }

            try (InputStream inputStream = fileResp.body().byteStream();
                 OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
        } catch (IOException e) {
            log.error("下载失败: docId={}, taskId={}", docId, taskId, e);
            response.setStatus(500);
        }
    }



    @PostMapping("/cut/save")
    @ApiOperation(value = "保存剪切视频", tags = "v1.6.4")
    @FunctionPoint(interfaceType = TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.SAVE_PART, node = AuthNodeEnum.RESOURCE_SAVE_PART, resIds = "{#req.docId}")
    @GroupNodeAuth(node = AuthNodeEnum.RESOURCE_IMPORT, groupIds = "{#req.groupId}")
    public BaseResp<Void> cutSave(@RequestBody @Valid VideoCutSavaReq req) {
        req.setFileName(StringUtils.trim(req.getFileName()));
        if (StringUtil.isBlank(req.getFileName())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EMPTY, "文件名");
        }
        return videoBiz.videoCutSave(req);
    }

    @GetMapping("/sub/list")
    @ApiOperation(value = "视频合并分段列表", tags = "v1.6.6", notes = "当该视频由N个视频合成而来时，此列表才会有内容")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<VideoSubResp>> subList(@RequestParam("docId") String docId) {
        return new BaseResp<>(videoBiz.subList(docId));
    }

    @PostMapping("/sub/save")
    @ApiOperation(value = "视频合并分段保存", tags = "v1.6.6")
    @FunctionPoint(interfaceType = TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.SAVE_PART, node = AuthNodeEnum.RESOURCE_SAVE_PART, resIds = "{#req.docId}")
    @GroupNodeAuth(node = AuthNodeEnum.RESOURCE_IMPORT, groupIds = "{#req.groupId}")
    public BaseResp<String> subSave(@RequestBody @Valid VideoSubSavaReq req) {
        if (StringUtil.isBlank(req.getFileName())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EMPTY, "文件名");
        }
        return new BaseResp<>(videoBiz.subSave(req));
    }

    @PostMapping("/merge")
    @ApiOperation(value = "视频合并", tags = "v1.6.8")
    @FunctionPoint(interfaceType = TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.VIDEO_MERGE_SPLIT, node = AuthNodeEnum.RESOURCE_MERGE_SPLIT, resIds = "{#req.docIds}")
    public BaseResp<Void> merge(@RequestBody @Valid VideoMergeReq req) {
        videoBiz.merge(req);
        return new BaseResp<>();
    }

    @PostMapping("/split")
    @ApiOperation(value = "视频拆分", tags = "v1.6.8")
    @FunctionPoint(interfaceType = TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.VIDEO_MERGE_SPLIT, node = AuthNodeEnum.RESOURCE_MERGE_SPLIT, resIds = "{#req.docId}")
    public BaseResp<Void> split(@RequestBody @Valid BaseDocIdReq req) {
        videoBiz.split(req.getDocId());
        return new BaseResp<>();
    }

    @GetMapping("personExtraction")
    @ApiOperation(value = "获取人员分段提取时间段", tags = "v1.6.2-jw")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<PersonExtractionSubResp> getPersonExtraction(@RequestParam(name = "docId") String docId) {
        return videoBiz.getPersonExtraction(docId);
    }

    @PostMapping("inspection/export")
    @ApiOperation(value = "巡检详情excel导出", tags = "v1.6.2-jw", produces = "application/octet-stream")
//    @FunctionPoint(interfaceType = TOC)
    @LogFilter
    public void inspectionExport(@RequestBody InspectionExportReq req, HttpServletResponse response) {
        List<String> docIds = req.getDocIds();
        videoBiz.inspectionExport(docIds, response);
    }


}
