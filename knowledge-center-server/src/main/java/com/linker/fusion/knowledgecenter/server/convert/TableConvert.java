package com.linker.fusion.knowledgecenter.server.convert;


import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.server.dto.req.table.TablePageReq;
import com.linker.fusion.knowledgecenter.server.dto.req.table.TableSlicePageReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.TableInfoEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.TableSliceResp;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TableEmbeddingData;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TablePageDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TableSliceDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TableSlicePageDTO;
import com.linker.user.api.dto.UserInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TableConvert {

    TableConvert INSTANCE = Mappers.getMapper(TableConvert.class);

    @Mappings({
            @Mapping(target = "code", source = "handleFailReason.code"),
            @Mapping(target = "message", source = "handleFailReason.message")
    })
    TableInfoEntityResp entityToResp(KnowledgeResourceEntity dto);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId")
    })
    TablePageDTO pageReqToDTO(TablePageReq dto, UserInfo userInfo);

    TableSlicePageDTO tableSlicePageToDTO(TableSlicePageReq dto);

    TableSliceResp tableSliceEmbeddingDataToResp(TableEmbeddingData dto);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId")
    })
    TableSliceDTO toDTO(TableSliceResp dto, UserInfo userInfo);
}
