package com.linker.fusion.knowledgecenter.server.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.URLUtil;
import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthNodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OperatorTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoFrameEntity;
import com.linker.fusion.knowledgecenter.server.biz.DocBiz;
import com.linker.fusion.knowledgecenter.server.biz.FileBiz;
import com.linker.fusion.knowledgecenter.server.biz.GroupBiz;
import com.linker.fusion.knowledgecenter.server.dto.api.ApiBatchResReq;
import com.linker.fusion.knowledgecenter.server.dto.api.ApiEventItemResp;
import com.linker.fusion.knowledgecenter.server.dto.api.ApiResourceInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.file.SpaceInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.req.core.BatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.AddSegmentReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.DeleteSegmentReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.*;
import com.linker.fusion.knowledgecenter.server.dto.req.file.video.EventRecognizeReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.video.EventRecognizeResp;
import com.linker.fusion.knowledgecenter.server.dto.req.file.video.SegmentRetryReq;
import com.linker.fusion.knowledgecenter.server.dto.req.group.MoveReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.AppInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.DocInfoEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.SegmentPageResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.SegmentResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.GroupNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.auth.MenuAndResNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import com.linker.omagent.core.repository.embedding.SearchHit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.ResponseBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.linker.core.auth.enums.InterfaceType.TOC;

@Slf4j
@Api(tags = {"文件管理"})
@RestController
@RequestMapping("file")
public class FileController {

    @Resource
    private FileBiz fileBiz;
    @Resource
    private GroupBiz groupBiz;
    @Resource
    private DocBiz docBiz;
    @Resource
    private KnowledgeGroupFactory knowledgeGroupFactory;

    @PostMapping("/search")
    @ApiOperation(value = "文件全目录搜索", tags = {"v1.6.2"})
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public BaseResp<List<DocInfoEntityResp>> search(@RequestBody @Valid FileSearchReq req) {
        List<Long> groupIds = groupBiz.getAuthedGroupIdFilter(req.getType());
        return fileBiz.search(req, groupIds);
    }

    @PostMapping("segment/add")
    @ApiOperation(value = "分段添加")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, resIds = "{#req.docId}")
    public BaseResp<Void> addSegment(@RequestBody @Validated AddSegmentReq req) {
        fileBiz.addSegment(req);
        return new BaseResp<>();
    }

    @GetMapping("segment/get")
    @ApiOperation(value = "获取指定分段")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#segmentId}")
    public BaseResp<SegmentResp> get(@RequestParam(value = "segmentId") String segmentId) {
        return new BaseResp<>(fileBiz.getSegment(segmentId));
    }

    @PostMapping("segment/page")
    @ApiOperation(value = "分段列表", tags = "v1.6.6")
    @FunctionPoint(interfaceType = TOC)
    // @NodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, resIds = "{#req.docId}")
    public BasePaginResp<SegmentPageResp> segmentPage(@RequestBody @Validated SegmentPageReq req) {
        return fileBiz.segmentPage(req);
    }

    @PostMapping("segment/update")
    @ApiOperation(value = "分段更新")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, resIds = "{#req.docId}")
    public BaseResp<Void> updateSegment(@RequestBody @Validated UpdateSegmentReq req) {
        fileBiz.updateSegment(req);
        return new BaseResp<>();
    }

    @PostMapping("segment/delete")
    @ApiOperation(value = "分段删除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentIds}")
    public BaseResp<Void> deleteSegment(@RequestBody @Validated DeleteSegmentReq req) {
        fileBiz.deleteSegment(req);
        return new BaseResp<>();
    }

    @PostMapping("face/list")
    @ApiOperation(value = "人脸列表", tags = "v1.5.0")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<FaceListResp>> faceList(@RequestBody @Validated FaceListReq req) {
        return fileBiz.faceList(req);
    }

    @PostMapping("od-tag/list")
    @ApiOperation(value = "实体标签", tags = "v1.6.6")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<OdTagListResp>> odTagList(@RequestBody @Validated BaseSegmentReq req) {
        return fileBiz.odTagList(req);
    }

    @PostMapping("terminology/list")
    @ApiOperation(value = "专名标签", tags = "v1.6.6")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<TerminologyListResp>> terminologyList(@RequestBody @Validated BaseSegmentReq req) {
        return fileBiz.terminologyList(req);
    }

    @PostMapping("event/recognize/list")
    @ApiOperation(value = "事件分析", tags = "v1.7.4")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<EventRecognizeResp>> eventRecognizeList(@RequestBody @Validated EventRecognizeReq req) {
        return fileBiz.eventRecognizeList(req);
    }


    @PostMapping("info")
    @ApiOperation(value = "关于本机", tags = "v1.5.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<AppInfoResp> appInfo() {
        return fileBiz.appInfo();
    }

    @PostMapping("temp/clear")
    @ApiOperation(value = "临时文件清理", tags = "v1.5.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<AppInfoResp> clearTemp() {
        return fileBiz.clearTemp();
    }

    @PostMapping("clear")
    @ApiOperation(value = "清理指定文件", tags = "v1.5.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> clear(@RequestBody ClearReq req) {
        return fileBiz.clear(req.getDocId());
    }

    @GetMapping("event/list")
    @ApiOperation(value = "事件列表", tags = "v1.6.1")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<String>> eventList() {
        return fileBiz.eventList();
    }

    @PostMapping("segment/retry")
    @ApiOperation(value = "视频分块重试", tags = "v1.5.0")
    @FunctionPoint(interfaceType = TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<TaskEntity> segmentRetry(@RequestBody SegmentRetryReq req) {
        TaskEntity task = fileBiz.segmentRetry(req);
        return new BaseResp<>(task);
    }

    @GetMapping("frame/list")
    @ApiOperation(value = "抽帧列表", tags = "v1.6.4")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<SearchHit<VideoFrameEntity>>> frameList(String fileId) {
        return new BaseResp<>(fileBiz.frameList(fileId));
    }

    @PostMapping("/download")
    @ApiOperation(value = "下载", tags = "v1.6.6")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.DOWNLOAD, node = AuthNodeEnum.RESOURCE_DOWNLOAD, resIds = "{#req.docId}")
    public BaseResp<String> download(@RequestBody DownloadFileReq req) {
        return new BaseResp<>(fileBiz.download(req));
    }


    @GetMapping("/download/url")
    @ApiOperation(value = "下载Url", tags = "v1.6.6")
    @LogFilter
    public StreamingResponseBody cut(HttpServletResponse response, @RequestParam("url") String url, @RequestParam("fileName") String fileName) throws IOException {
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        fileName = URLEncoder.encode(fileName, "UTF-8").replace("+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        response.getOutputStream().flush();
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.MINUTES)
                .callTimeout(5, TimeUnit.MINUTES)
                .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
                .build();
        // 构建流式响应体
        return outputStream -> {
            CountDownLatch latch = new CountDownLatch(1);
            Request request = new Request.Builder()
                    .url(URLUtil.decode(url))
                    .header("Accept", "application/octet-stream")
                    .build();
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onResponse(Call call, okhttp3.Response response) throws IOException {
                    if (!response.isSuccessful()) throw new IOException("Unexpected code: " + response.code());
                    try (ResponseBody body = response.body();
                         InputStream inputStream = body.byteStream()) {
                        byte[] buffer = new byte[32 * 1024];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                            outputStream.flush();
                        }
                    }
                    latch.countDown();
                }

                @Override
                public void onFailure(Call call, IOException e) {
                    if (!call.isCanceled()) log.error("Transfer failed", e);
                    IOUtils.closeQuietly(outputStream, null);
                    latch.countDown();
                }
            });
            try {
                latch.await(30, TimeUnit.MINUTES);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        };
    }

    @PostMapping("/copy")
    @ApiOperation(value = "复制", tags = "v1.6.6")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.COPY, node = AuthNodeEnum.RESOURCE_COPY, resIds = "{#req.fileIds}")
    @GroupNodeAuth(node = AuthNodeEnum.RESOURCE_IMPORT, groupIds = "{#req.targetId}")
    public BaseResp<Void> copy(@RequestBody @Valid CopyReq req) {
        fileBiz.copy(req);
        return new BaseResp<>();
    }

    @ApiOperation(value = "通过会话id删除文件", tags = "v1.6.7")
    @PostMapping("/delete/conversationId")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> deleteByConversationId(@RequestBody DeleteByConversationIdReq req) {
        fileBiz.deleteByConversationId(req);
        return new BaseResp<>();
    }

//    @ApiOperation(value = "删除过期文件", tags = "v1.6.7")
//    @GetMapping("/delete/expired")
//    public BaseResp<Void> deleteExpired() {
//        fileBiz.deleteExpired();
//        return new BaseResp<>();
//    }

    @ApiOperation(value = "场景标签翻库", tags = "v1.6.8")
    @PostMapping("/scene/tag/rerun")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> sceneTagRerun(@RequestBody SceneTagRerunReq req) {
        fileBiz.sceneTagRerun(req);
        return new BaseResp<>();
    }

    @PostMapping("/resource/list")
    @ApiOperation(value = "文件信息列表")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<ApiResourceInfoResp>> resourceList(@RequestBody @Validated ApiBatchResReq req) {
        if (CollectionUtils.isEmpty(req.getResourceIds())) {
            return new BaseResp<>(Collections.emptyList());
        }
        List<KnowledgeResourceEntity> resourceList = fileBiz.list(req.getResourceIds());
        List<ApiResourceInfoResp> apiResourceInfoRespList = resourceList.stream().map(ApiResourceInfoResp::new).collect(Collectors.toList());

        apiResourceInfoRespList.forEach(resp -> {
            List<ApiEventItemResp> sameNamelist = new ArrayList<>();
        });
        return new BaseResp<>(apiResourceInfoRespList);
    }
    /**
     * @description: 查询事件列表
     * @author: hejianbao
     * @date: 2025/7/25 09:12
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    @ApiOperation(value = "查询事件列表", tags = "v1.7.1")
    @PostMapping(value = "/listResourceOdTag")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<ResourceOdTagResp> listResourceOdTag(@RequestBody ResourceOdTagReq resourceOdTagReq) {
        return new BaseResp(fileBiz.listResourceOdTag(resourceOdTagReq));
    }

    /**
     * @description: 查询人脸列表
     * @author: hejianbao
     * @date: 2025/7/25 09:12
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    @ApiOperation(value = "查询人脸列表", tags = "v1.7.1")
    @PostMapping(value = "/listResourceFace")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<ResourceFaceResp> listResourceFace(@RequestBody ResourceFaceReq resourceFaceReq) {
        return new BaseResp(fileBiz.listResourceFace(resourceFaceReq));
    }


    @ApiOperation(value = "查询标签分页列表", tags = "v1.7.5")
    @PostMapping(value = "/page/tag")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<ResourceOdTagResp> pageResourceTag(@RequestBody ResourceOdTagReq resourceOdTagReq) {
        return fileBiz.pageResourceTag(resourceOdTagReq);
    }

    @ApiOperation(value = "查询人脸分页列表", tags = "v1.7.5")
    @PostMapping(value = "/page/faces")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<ResourceFaceResp> pageResourceFace(@RequestBody ResourceFaceReq resourceFaceReq) {
        return fileBiz.pageResourceFace(resourceFaceReq);
    }

    @ApiOperation(value = "查询文本标签分页列表", tags = "v1.7.5")
    @PostMapping(value = "/page/terminology")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<ResourceOdTagResp> pageTerminology(@RequestBody ResourceTerminologyReq resourceFaceReq) {
        return fileBiz.pageTerminology(resourceFaceReq);
    }
    /**
     * @description: 示例库文件查询
     * @author: hejianbao
     * @date: 2025/7/25 13:57
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    @GetMapping("/listSampleResource")
    @ApiOperation(value = "示例库文件查询", tags = "v1.7.1")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<DocInfoEntityResp>> listSampleResource() {
        return new BaseResp<>(fileBiz.listSampleResource());
    }

    /**
     * @description: 示例库复制文件到指定知识库
     * @author: hejianbao
     * @date: 2025/7/25 16:00
     **/
    @PostMapping("/copySampleResource")
    @ApiOperation(value = "示例库复制文件到指定知识库", tags = "v1.7.1")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> copySampleResource(@RequestBody CopySampleReq req) {
        fileBiz.copySampleResource(req);
        return new BaseResp<>();
    }

    @PostMapping("/move")
    @ApiOperation(value = "文件和目录移动", tags = "v1.7.1")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> move(@RequestBody @Validated FileMoveReq req) {
        if (CollectionUtil.isNotEmpty(req.getResIds())) {
            KnowledgeGroupEntity group = groupBiz.get(req.getTargetId());
            knowledgeGroupFactory.checkMenuAndNodeAuth(group, OperatorTypeEnum.MANAGER, AuthNodeEnum.RESOURCE_IMPORT);
            knowledgeGroupFactory.checkMenuAndResNodeAuth(req.getResIds(), KnowledgeTypeEnum.FILE, OperatorTypeEnum.MOVE, AuthNodeEnum.RESOURCE_MOVE);
        }
        if (CollectionUtil.isNotEmpty(req.getGroupIds())) {
            req.getGroupIds().forEach(groupId -> {
                groupBiz.move(new MoveReq(groupId, req.getTargetId()));
            });
        }
        if (CollectionUtil.isNotEmpty(req.getResIds())) {
            docBiz.move(new com.linker.fusion.knowledgecenter.server.dto.req.doc.MoveReq(req.getResIds(), req.getTargetId()));
        }
        return new BaseResp<>();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "文件和目录删除", tags = "v1.7.1")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> delete(@RequestBody FileDeleteReq req) {
        if (CollectionUtil.isNotEmpty(req.getResIds())) {
            knowledgeGroupFactory.checkMenuAndResNodeAuth(req.getResIds(), KnowledgeTypeEnum.FILE, OperatorTypeEnum.DELETE, AuthNodeEnum.RESOURCE_DELETE);
        }
        if (CollectionUtil.isNotEmpty(req.getResIds())) {
            docBiz.delete(new BatchBaseReq(req.getResIds(), null));
        }
        if (CollectionUtil.isNotEmpty(req.getGroupIds())) {
            for (Long groupId : req.getGroupIds()) {
                groupBiz.delete(groupId);
            }
        }
        return new BaseResp<>();
    }

    @GetMapping("space/info")
    @ApiOperation(value = "空间信息", tags = "v1.7.5")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<SpaceInfoResp> spaceInfo() {
        return new BaseResp<>(fileBiz.spaceInfo());
    }
}