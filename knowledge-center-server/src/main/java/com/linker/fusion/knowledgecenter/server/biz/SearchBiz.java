package com.linker.fusion.knowledgecenter.server.biz;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.exception.ServiceException;
import com.linker.core.utils.Md5Utils;
import com.linker.fusion.knowledgecenter.infrastructure.client.TranscodeClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.AasWorkflowClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowStatus;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowSyncRunReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowSyncRunResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.VideoCutFrameReq;
import com.linker.fusion.knowledgecenter.infrastructure.config.AasWorkflowProperties;
import com.linker.fusion.knowledgecenter.infrastructure.config.AiSearchProperties;
import com.linker.fusion.knowledgecenter.infrastructure.entity.CustomEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceSegmentEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.StoragePathEnum;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.server.dto.model.AiSearchWorkflowInput;
import com.linker.fusion.knowledgecenter.server.dto.req.search.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.KnowledgeGroupParentResp;
import com.linker.fusion.knowledgecenter.service.domain.common.IUtilService;
import com.linker.fusion.knowledgecenter.service.domain.custom.CustomService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.SegmentDTO;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.search.AdvancedSearchQueryRouteTask;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.search.model.AdvancedSearchQueryRouteInput;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.search.model.AdvancedSearchQueryRouteOutput;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceSegmentService;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ResourceSegmentService;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.QueryParams;
import com.linker.omos.client.config.WorkflowTaskContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SearchBiz extends BaseBiz {

    private final ExecutorService fixedThreadPool = new ThreadPoolExecutor(5, Integer.MAX_VALUE, 300L, TimeUnit.SECONDS, new SynchronousQueue<Runnable>());

    @Resource
    private AasWorkflowClient aasWorkflowClient;

    @Resource
    private AasWorkflowProperties aasWorkflowProperties;

    @Resource
    private IResourceService iResourceService;

    @Resource
    private TranscodeClient transcodeClient;

    @Resource
    private CustomService customService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IResourceSegmentService iResourceSegmentService;

    @Resource
    private AiSearchProperties aiSearchProperties;

    @Resource
    private AdvancedSearchQueryRouteTask advancedSearchQueryRouteTask;

    @Resource
    private KnowledgeGroupService knowledgeGroupService;
    @Autowired
    private IUtilService iUtilService;

    @Resource
    private ResourceSegmentService resourceSegmentService;

    // TODO
    public AiSearchResp search(AiSearchReq aiSearchReq) {
        AiSearchResp aiSearchResp = new AiSearchResp();
        WorkflowSyncRunReq workflowSyncRunReq = new WorkflowSyncRunReq();
        workflowSyncRunReq.setId(aasWorkflowProperties.getWorkflowId().getAiSearch());
        String tenantId = getTenantId();
        AiSearchWorkflowInput input = new AiSearchWorkflowInput();
        input.setTenantId(tenantId);
        input.setUserId(getUserCode());
        input.setDepartmentCodes(getDepartmentCodes());
        String query = AiSearchReq.getQuery(aiSearchReq.getSearchParams());
        String picUrl = AiSearchReq.getPicUrl(aiSearchReq.getSearchParams());
//        if (StringUtils.isBlank(query) && StringUtils.isBlank(picUrl)) {
//            throw new BusinessException("请输入搜索内容");
//        }
        input.setQuery(query);
        input.setPicUrl(picUrl);
        input.setGroupIds(aiSearchReq.getGroupIds());
        input.setFileTypes(aiSearchReq.getFileTypes());
        input.setTitleParams(AiSearchReq.filterValidTitleParams(aiSearchReq.getTitleParams()));
        input.setMetaParams(validMetaParams(aiSearchReq.getMetaParams()));
        input.setVisionParams(AiSearchReq.filterValidVisionParams(aiSearchReq.getVisionParams()));
        input.setTerminologyParams(AiSearchReq.filterValidTerminologyParams(aiSearchReq.getTerminologyParams()));

        input.setMaxResults(aiSearchProperties.getMaxResults());
        input.setTopK(aiSearchProperties.getTopK());
        input.setThreshold(aiSearchProperties.getThreshold());
        input.setIntentRecognitionEnable(aiSearchReq.getIntentRecognitionEnable());
        log.info("input:{}", JSON.toJSONString(input));
        workflowSyncRunReq.setInput(JSONObject.parseObject(JSON.toJSONString(input)));
        workflowSyncRunReq.setTimeout(aiSearchProperties.getTimeout());

        log.info("workflowSyncRunReq:{}", JSON.toJSONString(workflowSyncRunReq));
        BaseResp<WorkflowSyncRunResp> baseResp = null;
        try {
            baseResp = aasWorkflowClient.runSync(workflowSyncRunReq);
        } catch (Exception e) {
            log.error("工作流执行失败", e);
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.WORKFLOW_ERROR);
        }
        log.debug("baseResp:{}", JSON.toJSONString(baseResp));
        if (baseResp == null || baseResp.getData() == null) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.WORKFLOW_ERROR);
        }
        WorkflowSyncRunResp workflowSyncRunResp = baseResp.getData();
        if (!WorkflowStatus.COMPLETED.equals(workflowSyncRunResp.getStatus())) {
            log.error("ai search error workflow id: {}", workflowSyncRunResp.getWorkflowInstanceId());
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.TIME_OUT_SEARCH);
        }
        String workflowInstanceId = workflowSyncRunResp.getWorkflowInstanceId();
        aiSearchResp.setWorkflowId(workflowInstanceId);
        JSONObject output = workflowSyncRunResp.getOutput();
        JSONArray result = output.getJSONArray("result");
        if (result == null) {
            return aiSearchResp;
        }
        List<SegmentDTO> segmentDTOS = JSON.parseArray(result.toJSONString(), SegmentDTO.class);
        if (CollectionUtils.isNotEmpty(aiSearchReq.getFileTypes())) {
            // 指定音频搜索时，可能搜到视频的分块，做一下二次过滤
            segmentDTOS = segmentDTOS.stream().filter(s -> aiSearchReq.getFileTypes().contains(s.getFileType())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(segmentDTOS)) {
            return aiSearchResp;
        }

        // 排序
        segmentDTOS = segmentDTOS.stream().sorted(Comparator.comparing(SegmentDTO::getScore, Comparator.reverseOrder())).collect(Collectors.toList());
        // 去重
        List<String> docIds = segmentDTOS.stream().map(SegmentDTO::getDocId).distinct().collect(Collectors.toList());
        List<KnowledgeResourceEntity> listByDocIds = iResourceService.listByResIds(docIds);
        Map<String, KnowledgeResourceEntity> docIdFileMap = new LinkedHashMap<>();
        for (String docId : docIds) {
            listByDocIds.stream()
                    .filter(s -> s.getDocId().equals(docId)).findFirst()
                    .ifPresent(resourceEntity -> {
                        // 修复resourceEntity的页数
                        if (FileTypeEnum.DOCUMENT.getType().equals(resourceEntity.getType()) && resourceEntity.getCount() == 0L) {
                            ResourceSegmentEntity latestSegmentByDocId = iResourceSegmentService.getLatestSegmentByDocId(resourceEntity.getDocId());
                            if (latestSegmentByDocId != null) {
                                resourceEntity.setCount(Long.valueOf(latestSegmentByDocId.getPage()));
                            }
                        }
                        docIdFileMap.put(docId, resourceEntity);
                    });
        }
        fixSegmentUrl(segmentDTOS, docIdFileMap, tenantId);
        aiSearchResp.setSegments(formatSegment(segmentDTOS, docIdFileMap));
        aiSearchResp.setFiles(formatFiles(segmentDTOS, docIdFileMap));
        return aiSearchResp;
    }

    void fixSegmentUrl(List<SegmentDTO> segmentDTOS, Map<String, KnowledgeResourceEntity> docIdFileMap, String tenantId) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (SegmentDTO segmentDTO : segmentDTOS) {
            CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> {
                KnowledgeResourceEntity resourceEntity = docIdFileMap.get(segmentDTO.getDocId());
                if (resourceEntity != null) {
                    FileTypeEnum fileTypeEnum = FileTypeEnum.valueOf(resourceEntity.getType());
                    if (FileTypeEnum.IMAGE.equals(fileTypeEnum)) {
                        segmentDTO.setThumbnail(resourceEntity.getUrl());
                    } else if (FileTypeEnum.VIDEO.equals(fileTypeEnum)) {
                        if (StringUtils.isBlank(segmentDTO.getUrl())) {
                            VideoCutFrameReq.Data data = new VideoCutFrameReq.Data();
                            data.setSourceUrl(resourceEntity.getUrl());
                            data.setCutTime(segmentDTO.getStartTimestamp());
                            VideoCutFrameReq videoCutFrameReq = new VideoCutFrameReq(data);
                            RLock lock = redissonClient.getLock("knowledge-center:videoCutFrame:" + Md5Utils.md5Hex(JSON.toJSONString(videoCutFrameReq)));
                            try {
                                lock.lock();
                                long l = System.currentTimeMillis();
                                String segmentId = segmentDTO.getSegmentId();
                                log.info("视频片段{}截帧入参:{}", segmentId, JSON.toJSONString(videoCutFrameReq));
                                JSONObject jsonObject = transcodeClient.videoCutFrame(videoCutFrameReq);
                                log.info("视频片段{}截帧结果:{}", segmentId, jsonObject);
                                if (jsonObject != null) {
                                    String url = jsonObject.getString("data");
                                    //将url转成永久桶文件
                                    String newUrl = iUtilService.moveTempToDefault(url, StoragePathEnum.Template.getValueByUrl(tenantId, segmentDTO.getDocId(), url));
                                    segmentDTO.setThumbnail(newUrl);
                                    log.info("视频片段{}转移到新的地址:{}", segmentId, newUrl);
                                    //更新url地址
                                    resourceSegmentService.updateThumbnail(segmentId, newUrl);
                                }
                                log.info("视频片段抽取封面耗时{}ms", System.currentTimeMillis() - l);
                            } catch (Exception e) {
                                log.error("视频片段抽取封面失败", e);
                            } finally {
                                lock.unlock();
                            }
                        }
                    }
                }
                return null;
            }, fixedThreadPool);
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    private List<AiSearchResp.Segment> formatSegment(List<SegmentDTO> segmentDTOS, Map<String, KnowledgeResourceEntity> docIdFileMap) {
        List<AiSearchResp.Segment> segments = new ArrayList<>();
        for (SegmentDTO segmentDTO : segmentDTOS) {
            AiSearchResp.Segment segment = BeanUtil.toBean(segmentDTO, AiSearchResp.Segment.class);
            segment.setCoverUrl(segmentDTO.getUrl());
            KnowledgeResourceEntity resourceEntity = docIdFileMap.get(segment.getDocId());
            if (resourceEntity == null) {
                continue;
            }
            AiSearchResp.File file = BeanUtil.toBean(resourceEntity, AiSearchResp.File.class);
            segment.setFile(file);
//            segment.setSuffix(resourceEntity.getSuffix());
//            segment.setTitle(resourceEntity.getTitle());
            segments.add(segment);
        }
        return segments;
    }

    private List<AiSearchResp.File> formatFiles(List<SegmentDTO> segmentDTOS, Map<String, KnowledgeResourceEntity> docIdFileMap) {
        List<AiSearchResp.File> files = new ArrayList<>();
        for (Map.Entry<String, KnowledgeResourceEntity> entry : docIdFileMap.entrySet()) {
            String docId = entry.getKey();
            KnowledgeResourceEntity resourceEntity = entry.getValue();

            AiSearchResp.File file = BeanUtil.toBean(resourceEntity, AiSearchResp.File.class);

            List<SegmentDTO> segmentDTOList = segmentDTOS.stream().filter(segmentDTO -> segmentDTO.getDocId().equals(docId)).collect(Collectors.toList());
            List<AiSearchResp.Segment> segments = new ArrayList<>();
            for (SegmentDTO segmentDTO : segmentDTOList) {
                AiSearchResp.Segment segment = BeanUtil.toBean(segmentDTO, AiSearchResp.Segment.class);
                segment.setCoverUrl(segmentDTO.getUrl());
//                segment.setSuffix(resourceEntity.getSuffix());
//                segment.setTitle(resourceEntity.getTitle());
                segments.add(segment);
            }
            file.setSegments(segments);

            files.add(file);
        }
        return files;
    }

    private List<QueryParams.MetaParam> validMetaParams(List<MetaParamReq> requestMetaParams) {
        List<CustomEntity> customEntityList = customService.queryFillInfo(getTenantId(), null);
        List<QueryParams.MetaParam> metaParams = new ArrayList<>();
        for (MetaParamReq requestMetaParam : requestMetaParams) {
            if (requestMetaParam.getFieldId() == null || requestMetaParam.getValue() == null) {
                continue;
            }
            customEntityList.stream().filter(customEntity -> Objects.equals(requestMetaParam.getFieldId(), customEntity.getId())).findFirst().ifPresent(
                    customEntity -> {
                        QueryParams.MetaParam metaParam = new QueryParams.MetaParam();
                        metaParam.setFileType(customEntity.getManageType());
                        metaParam.setField(customEntity.getField());
                        metaParam.setFieldType(customEntity.getFieldType());
                        metaParam.setOperator(requestMetaParam.getOperator());
                        metaParam.setValue(requestMetaParam.getValue());
                        metaParams.add(metaParam);
                    }
            );
        }
        return metaParams;
    }

    public AdvancedSearchResp advancedSearch(AdvancedSearchReq req) {
//        if (StringUtils.isAllBlank(req.getKeyword(), req.getEmbeddedKeyword(), req.getPicUrl())) {
//            throw new ServiceException(NOT_EMPTY, "搜索条件");
//        }
        AdvancedSearchQueryRouteInput.Data input = new AdvancedSearchQueryRouteInput.Data();
        input.setTenantId(getTenantId());
        input.setUserCode(getUserCode());
        input.setDepartmentCodes(getDepartmentCodes());
        BeanUtil.copyProperties(req, input);
        input.setMetaParams(validMetaParams(req.getMetaParams()));
        WorkflowSyncRunReq workflowSyncRunReq = new WorkflowSyncRunReq();
        workflowSyncRunReq.setId(aasWorkflowProperties.getWorkflowId().getAdvancedSearch());
        workflowSyncRunReq.setInput(JSONObject.parseObject(JSON.toJSONString(new AdvancedSearchQueryRouteInput(input))));
        workflowSyncRunReq.setTimeout(aiSearchProperties.getTimeout());
        log.info("workflowSyncRunReq:{}", JSON.toJSONString(workflowSyncRunReq));
        BaseResp<WorkflowSyncRunResp> baseResp = null;
        try {
            baseResp = aasWorkflowClient.runSync(workflowSyncRunReq);
        } catch (Exception e) {
            log.error("工作流执行失败", e);
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.TIME_OUT_SEARCH);
        }
        log.debug("baseResp:{}", JSON.toJSONString(baseResp));
        if (baseResp == null || baseResp.getData() == null) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.TIME_OUT_SEARCH);
        }
        WorkflowSyncRunResp workflowSyncRunResp = baseResp.getData();
        if (!WorkflowStatus.COMPLETED.equals(workflowSyncRunResp.getStatus())) {
            log.error("advanced search error workflow id: {}", workflowSyncRunResp.getWorkflowInstanceId());
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.TIME_OUT_SEARCH);
        }
        String workflowInstanceId = workflowSyncRunResp.getWorkflowInstanceId();

        List<AdvancedSearchResp.File> files = workflowSyncRunResp.getOutput()
                .getJSONObject("output")
                .getJSONArray("files")
                .toJavaList(AdvancedSearchResp.File.class);
        setGroupParentResp(files);
        AdvancedSearchResp advancedSearchResp = new AdvancedSearchResp();
        advancedSearchResp.setWorkflowId(workflowInstanceId);
        advancedSearchResp.setFiles(files);
        return advancedSearchResp;
    }

    private void setGroupParentResp(List<AdvancedSearchResp.File> files) {
        if (CollectionUtils.isEmpty(files)) {
            return;
        }
        long l = System.currentTimeMillis();
        for (AdvancedSearchResp.File file : files) {
            KnowledgeGroupEntity group = knowledgeGroupService.get(file.getGroupId());
            if (Boolean.TRUE.equals(group.getDeleted())) {
                log.error("file group is deleted, fileId:{},groupId:{}", file.getDocId(), file.getGroupId());
            }
            List<KnowledgeGroupEntity> parents = knowledgeGroupService.getParents(group);
            parents.add(group);
            file.setGroupParentResp(new KnowledgeGroupParentResp(group.getId(), group.getType(), group.getVisibleType(), parents));
        }
        log.info("setGroupParentResp time:{}", System.currentTimeMillis() - l);
    }

    public Object advancedSearchTest(@Valid AdvancedSearchReq req) {
        AdvancedSearchQueryRouteInput.Data input = new AdvancedSearchQueryRouteInput.Data();
        input.setTenantId(getTenantId());
        input.setUserCode(getUserCode());
        input.setDepartmentCodes(getDepartmentCodes());
        BeanUtil.copyProperties(req, input);
        WorkflowTaskContext context = new WorkflowTaskContext("1");
        AdvancedSearchQueryRouteOutput advancedSearchQueryRouteOutput = advancedSearchQueryRouteTask.onTask(new AdvancedSearchQueryRouteInput(input), context);
        return advancedSearchQueryRouteOutput;
    }

}
