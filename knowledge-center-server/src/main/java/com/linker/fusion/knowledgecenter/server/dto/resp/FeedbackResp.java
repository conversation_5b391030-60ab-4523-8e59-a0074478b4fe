package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.UserInfoResp;
import com.linker.fusion.knowledgecenter.infrastructure.common.UserMapList;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.BaseEntityResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

@Data
public class FeedbackResp extends BaseEntityResp {

    private Long id;

    private String uid;

    @ApiModelProperty(value = "智能体ID")
    private Long agentId;

    @ApiModelProperty(value = "用户问题")
    private String question;

    @ApiModelProperty(value = "反馈内容")
    private String content;

    @ApiModelProperty(value = "AI学习状态 0-待学习 1-学习通过 2-学习不通过")
    private Integer learnStatus;

    @ApiModelProperty(value = "人为操作 0-初始化 1-采纳 2-忽略")
    private Integer operationTag;

    @ApiModelProperty(value = "操作人ID")
    private String operatorId;

    @ApiModelProperty(value = "操作人")
    private UserInfoResp operator;

    public UserInfoResp getOperator() {
        return StringUtils.isNotBlank(operatorId) ? UserMapList.getRpcUserInfoRespResp(operatorId) : null;
    }

    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationTime;

    private Long faqId;

    @ApiModelProperty(value = "关联的FAQ信息")
    private FaqInfoEntityResp faq;
}
