package com.linker.fusion.knowledgecenter.server.convert;

import com.linker.fusion.knowledgecenter.infrastructure.entity.TemplateEntity;
import com.linker.fusion.knowledgecenter.server.dto.req.template.TemplateCreateReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TemplateConvert {

    TemplateConvert INSTANCE = Mappers.getMapper(TemplateConvert.class);

    TemplateEntity toEntity(TemplateCreateReq req);

}
