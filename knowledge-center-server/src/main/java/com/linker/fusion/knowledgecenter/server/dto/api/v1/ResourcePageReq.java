package com.linker.fusion.knowledgecenter.server.dto.api.v1;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.linker.core.base.baseclass.page.BasePaginApiReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * Request
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResourcePageReq extends BasePaginApiReq {
    @JsonProperty("resource_ids")
    @NotNull
    @ApiModelProperty(value = "文件id列表", required = true)
    private String resourceIds;
}