//package com.linker.fusion.knowledgecenter.server.biz;
//
//import cn.hutool.core.date.LocalDateTimeUtil;
//import com.alibaba.fastjson.JSON;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.google.common.collect.Lists;
//import com.linker.core.base.baseclass.page.BasePaginResp;
//import com.linker.fusion.knowledgecenter.infrastructure.client.HttpPersonClient;
//import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.HttpPersonInfoReq;
//import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.PersonInfoDTO;
//import com.linker.fusion.knowledgecenter.infrastructure.entity.*;
//import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
//import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
//import com.linker.fusion.knowledgecenter.infrastructure.enums.RenDaSearchTypeEnum;
//import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceVisibleTypeEnum;
//import com.linker.fusion.knowledgecenter.infrastructure.es.VideoFrameEntity;
//import com.linker.fusion.knowledgecenter.infrastructure.manager.IKnowledgeResourceManager;
//import com.linker.fusion.knowledgecenter.infrastructure.manager.IResourceFaceManager;
//import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
//import com.linker.fusion.knowledgecenter.server.convert.DocConvert;
//import com.linker.fusion.knowledgecenter.server.dto.req.npc.RenDaSearchVideoReq;
//import com.linker.fusion.knowledgecenter.server.dto.resp.DocInfoEntityResp;
//import com.linker.fusion.knowledgecenter.server.dto.resp.npc.RenDaPersonStatsResp;
//import com.linker.fusion.knowledgecenter.server.dto.resp.npc.RenDaSearchSuggestionResp;
//import com.linker.fusion.knowledgecenter.server.dto.resp.npc.VideoInfoResp;
//import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
//import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
//import com.linker.fusion.knowledgecenter.service.domain.group.model.SearchPreChekDto;
//import com.linker.fusion.knowledgecenter.service.domain.npc.INpcSearchLogService;
//import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceASRService;
//import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceFaceService;
//import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IVideoFrameIBaseService;
//import com.linker.fusion.knowledgecenter.service.domain.video.merge.SubVideoService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.collections4.ListUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.time.temporal.ChronoUnit;
//import java.util.*;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.SynchronousQueue;
//import java.util.concurrent.ThreadPoolExecutor;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
//import static com.linker.fusion.knowledgecenter.infrastructure.common.Constants.RESOURCE_EXT_INFO_SEARCH;
//
///**
// * <AUTHOR>
// */
//@Slf4j
//@Component
//public class RenDaSearchBiz extends BaseBiz {
//
//    private final ExecutorService fixedThreadPool = new ThreadPoolExecutor(30, Integer.MAX_VALUE, 300L, TimeUnit.SECONDS, new SynchronousQueue<Runnable>());
//
//    @Resource
//    private IKnowledgeResourceManager iKnowledgeResourceManager;
//
//    @Resource
//    private IResourceFaceService iResourceFaceService;
//
//    @Resource
//    private IResourceFaceManager faceManager;
//
//    @Resource
//    private IResourceASRService asrService;
//
//    @Resource
//    private KnowledgeGroupService knowledgeGroupService;
//
//    @Resource
//    private KnowledgeGroupFactory knowledgeGroupFactory;
//
//    @Resource
//    private INpcSearchLogService iNpcSearchLogService;
//
//    @Resource
//    private HttpPersonClient personClient;
//
//    @Resource
//    private SubVideoService subVideoService;
//
//    @Resource
//    private IVideoFrameIBaseService iVideoFrameIBaseService;
//
//    public BasePaginResp<VideoInfoResp> searchVideo(RenDaSearchVideoReq req) {
//        RenDaSearchTypeEnum renDaSearchTypeEnum = RenDaSearchTypeEnum.valueOf(req.getSearchType());
//        if (renDaSearchTypeEnum == null) {
//            return new BasePaginResp<>(0L, Collections.emptyList());
//        }
//        // 公共空间所有文件库ID
//        List<Long> groupIds = groupIds();
//        if (CollectionUtils.isEmpty(groupIds)) {
//            return new BasePaginResp<>(0L, Collections.emptyList());
//        }
//        // 预筛选
//        Set<String> docIdsByFaceIds = null;
//        if (CollectionUtils.isNotEmpty(req.getPersonIds())) {
//            docIdsByFaceIds = iResourceFaceService.getDocIdsByFaceIds(req.getPersonIds());
//            // 通过AI人员搜索无结果
//            if (CollectionUtils.isEmpty(docIdsByFaceIds)) {
//                return new BasePaginResp<>(0L, Collections.emptyList());
//            }
//        }
//        Set<String> docIdsByAsrContent = null;
//        if (StringUtils.isNotBlank(req.getAsrContent())) {
//            docIdsByAsrContent = asrService.getDocIdsByContent(getTenantId(), req.getAsrContent());
//            // 通过ASR内容搜索无结果
//            if (CollectionUtils.isEmpty(docIdsByAsrContent)) {
//                return new BasePaginResp<>(0L, Collections.emptyList());
//            }
//        }
//        LambdaQueryWrapper<KnowledgeResourceEntity> queryWrapper = initQuery(req, docIdsByFaceIds, docIdsByAsrContent);
//
//        if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.TITLE)) {
//            queryWrapper.like(KnowledgeResourceEntity::getTitle, StringComUtils.replaceSqlEsc(req.getSearchContent()));
//        } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.PERSON)) {
//            Set<String> docIds = iResourceFaceService.getDocIdsByFaceIds(Collections.singletonList(req.getSearchContent()));
//            if (CollectionUtils.isEmpty(docIds)) {
//                return new BasePaginResp<>(0L, Collections.emptyList());
//            }
//            queryWrapper.in(KnowledgeResourceEntity::getDocId, docIds);
//        } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.ASR)) {
//            Set<String> docIds = asrService.getDocIdsByContent(getTenantId(), req.getSearchContent());
//            if (CollectionUtils.isEmpty(docIds)) {
//                return new BasePaginResp<>(0L, Collections.emptyList());
//            }
//            queryWrapper.in(KnowledgeResourceEntity::getDocId, docIds);
//        }
//        Page<KnowledgeResourceEntity> page = new Page<>(req.getPage(), req.getPageSize());
//        iKnowledgeResourceManager.page(page, queryWrapper);
//        List<KnowledgeResourceEntity> records = page.getRecords();
//        List<DocInfoEntityResp> respList = DocConvert.INSTANCE.toResp(records);
//
//        List<VideoInfoResp> videoInfoList = null;
//        if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.TITLE)) {
//            videoInfoList = respList.stream().map(VideoInfoResp::init).collect(Collectors.toList());
//        } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.PERSON)) {
//            List<ResourceFaceEntity> faceEntityList = iResourceFaceService.listByDocIdAndFaceId(respList.stream().map(DocInfoEntityResp::getDocId).collect(Collectors.toList()), req.getSearchContent());
//            Map<String, List<ResourceFaceEntity>> faceMap = faceEntityList.stream().collect(Collectors.groupingBy(ResourceFaceEntity::getDocId));
//            videoInfoList = respList.stream().map(s -> {
//                VideoInfoResp videoInfoResp = VideoInfoResp.init(s);
//                List<VideoInfoResp.SceneItem> personScenes = faceMap.get(s.getDocId()).stream().map(resourceFaceEntity -> new VideoInfoResp.SceneItem(resourceFaceEntity.getTimePoint(), resourceFaceEntity.getTimePoint())).collect(Collectors.toList());
//                videoInfoResp.setPersonScenes(personScenes);
//                videoInfoResp.setMatchDurationMs(personScenes.size() * 1000L);
//                return videoInfoResp;
//            }).collect(Collectors.toList());
//        } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.ASR)) {
//            List<ResourceASREntity> asrList = asrService.listByDocIdsAndContent(respList.stream().map(DocInfoEntityResp::getDocId).collect(Collectors.toList()), req.getSearchContent());
//            Map<String, List<ResourceASREntity>> asrMap = asrList.stream().collect(Collectors.groupingBy(ResourceASREntity::getDocId));
//            videoInfoList = respList.stream().map(s -> {
//                VideoInfoResp videoInfoResp = VideoInfoResp.init(s);
//                List<VideoInfoResp.SceneItem> contentScenes = asrMap.get(s.getDocId()).stream().map(resourceFaceEntity -> new VideoInfoResp.SceneItem(resourceFaceEntity.getStartTimestamp(), resourceFaceEntity.getEndTimestamp())).collect(Collectors.toList());
//                videoInfoResp.setContentScenes(contentScenes);
//                long matchDurationMs = contentScenes.stream().map(sceneItem -> sceneItem.getEndTimestamp() - sceneItem.getStartTimestamp()).mapToLong(Long::longValue).sum();
//                videoInfoResp.setMatchDurationMs(matchDurationMs);
//                return videoInfoResp;
//            }).collect(Collectors.toList());
//        }
////        List<SubVideoEntity> subVideoEntityList = subVideoService.listByDocIds(videoInfoList.stream().map(VideoInfoResp::getDocId).collect(Collectors.toList()));
////        for (VideoInfoResp videoInfoResp : videoInfoList) {
////            Optional<SubVideoEntity> optional = subVideoEntityList.stream().filter(subVideoEntity -> subVideoEntity.getDocId().equals(videoInfoResp.getDocId())).findFirst();
////            optional.ifPresent(subVideoEntity -> videoInfoResp.setThumbnail(subVideoEntity.getThumbnail()));
////            if (StringUtils.isBlank(videoInfoResp.getThumbnail())) {
////                VideoFrameEntity firstFrame = iVideoFrameIBaseService.getFirst(getTenantId(), videoInfoResp.getDocId());
////                if (firstFrame != null) {
////                    videoInfoResp.setThumbnail(firstFrame.getUrl());
////                }
////            }
////        }
//        return new BasePaginResp<>(page.getTotal(), videoInfoList);
//    }
//
//    private LambdaQueryWrapper<KnowledgeResourceEntity> initQuery(RenDaSearchVideoReq req, Collection<String> docIdsByFaceIds, Collection<String> docIdsByAsrContent) {
//        List<Long> notIds = Lists.newArrayList();
//        List<Long> groupIds = groupIds();
//        if (CollectionUtils.isNotEmpty(groupIds)) {
//            SearchPreChekDto preChekDto = knowledgeGroupFactory.searchPreCheckForWorker(getTenantId(), getUserCode(), getDepartmentCodes(), groupIds, KnowledgeTypeEnum.FILE);
//            groupIds = preChekDto.getAuthedGroupIds();
//            groupIds = groupIds.stream().distinct().collect(Collectors.toList());
//            notIds = preChekDto.getNotIds();
//        }
//
//        Long meetingDateGt = StringUtils.isNotBlank(req.getMeetingDateGt()) ? StringComUtils.convertToTimestamp(req.getMeetingDateGt()) : null;
//        Long meetingDateLt = StringUtils.isNotBlank(req.getMeetingDateLt()) ? StringComUtils.convertToTimestamp(req.getMeetingDateLt()) : null;
//        return new LambdaQueryWrapper<KnowledgeResourceEntity>()
//                .eq(KnowledgeResourceEntity::getDeleted, false)
//                .eq(KnowledgeResourceEntity::getTenantId, getTenantId())
//                .eq(KnowledgeResourceEntity::getType, FileTypeEnum.VIDEO.getType())
//                .eq(KnowledgeResourceEntity::getEnable, true)
//                .in(CollectionUtils.isNotEmpty(groupIds), KnowledgeResourceEntity::getGroupId, groupIds)
//                .notIn(CollectionUtils.isNotEmpty(notIds), KnowledgeResourceEntity::getId, notIds)
//                .in(CollectionUtils.isNotEmpty(req.getHandleStatus()), KnowledgeResourceEntity::getHandleStatus, req.getHandleStatus())
//                .like(StringUtils.isNotBlank(req.getMeetingTitle()), KnowledgeResourceEntity::getTitle, StringComUtils.replaceSqlEsc(req.getMeetingTitle()))
//                .apply(StringUtils.isNotBlank(req.getMeetingLocation()), "JSON_EXTRACT(extInfo, '$.customer_meeting_location') LIKE '%" + StringComUtils.replaceSqlEsc(req.getMeetingLocation()) + "%'")
//                .apply(meetingDateGt != null, "JSON_EXTRACT(extInfo, '$." + RESOURCE_EXT_INFO_SEARCH + "meeting_date') >= " + meetingDateGt)
//                .apply(meetingDateLt != null, "JSON_EXTRACT(extInfo, '$." + RESOURCE_EXT_INFO_SEARCH + "meeting_date') <= " + meetingDateLt)
//                .apply(StringUtils.isNotBlank(req.getMeetingModerator()), "JSON_EXTRACT(extInfo, '$.customer_meeting_moderator') LIKE '%" + StringComUtils.replaceSqlEsc(req.getMeetingModerator()) + "%'")
//                .apply(StringUtils.isNotBlank(req.getMeetingParticipant()), "JSON_EXTRACT(extInfo, '$.customer_meeting_participant') LIKE '%" + StringComUtils.replaceSqlEsc(req.getMeetingParticipant()) + "%'")
//                .in(CollectionUtils.isNotEmpty(docIdsByFaceIds), KnowledgeResourceEntity::getDocId, docIdsByFaceIds)
//                .in(CollectionUtils.isNotEmpty(docIdsByAsrContent), KnowledgeResourceEntity::getDocId, docIdsByAsrContent)
//                .last("ORDER BY JSON_EXTRACT(extInfo, '$." + RESOURCE_EXT_INFO_SEARCH + "meeting_date') DESC");
//    }
//
//    private List<Long> groupIds() {
//        List<KnowledgeGroupEntity> groupEntityList = knowledgeGroupService.listAll(getTenantId(), null, KnowledgeTypeEnum.FILE.getType(), ResourceVisibleTypeEnum.PUBLIC.getValue());
//        return groupEntityList.stream().map(KnowledgeGroupEntity::getId).collect(Collectors.toList());
//    }
//
//    public List<PersonInfoDTO> personRelated(RenDaSearchVideoReq req) {
//        RenDaSearchTypeEnum renDaSearchTypeEnum = RenDaSearchTypeEnum.valueOf(req.getSearchType());
//        if (renDaSearchTypeEnum == null || renDaSearchTypeEnum == RenDaSearchTypeEnum.PERSON) {
//            return Collections.emptyList();
//        }
//        try {
//            // 公共空间所有文件库ID
//            List<Long> groupIds = groupIds();
//            if (CollectionUtils.isEmpty(groupIds)) {
//                return Collections.emptyList();
//            }
//            // 预筛选
//            Set<String> docIdsByFaceIds = null;
//            if (CollectionUtils.isNotEmpty(req.getPersonIds())) {
//                docIdsByFaceIds = iResourceFaceService.getDocIdsByFaceIds(req.getPersonIds());
//                // 通过AI人员搜索无结果
//                if (CollectionUtils.isEmpty(docIdsByFaceIds)) {
//                    return Collections.emptyList();
//                }
//            }
//            Set<String> docIdsByAsrContent = null;
//            if (StringUtils.isNotBlank(req.getAsrContent())) {
//                docIdsByAsrContent = asrService.getDocIdsByContent(getTenantId(), req.getAsrContent());
//                // 通过ASR内容搜索无结果
//                if (CollectionUtils.isEmpty(docIdsByAsrContent)) {
//                    return Collections.emptyList();
//                }
//            }
//            LambdaQueryWrapper<KnowledgeResourceEntity> queryWrapper = initQuery(req, docIdsByFaceIds, docIdsByAsrContent);
//            if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.TITLE)) {
//                queryWrapper.like(KnowledgeResourceEntity::getTitle, StringComUtils.replaceSqlEsc(req.getSearchContent()));
//            } else if (renDaSearchTypeEnum.equals(RenDaSearchTypeEnum.ASR)) {
//                Set<String> docIds = asrService.getDocIdsByContent(getTenantId(), req.getSearchContent());
//                if (CollectionUtils.isEmpty(docIds)) {
//                    return Collections.emptyList();
//                }
//                queryWrapper.in(KnowledgeResourceEntity::getDocId, docIds);
//            }
//            queryWrapper.select(KnowledgeResourceEntity::getDocId);
//            List<String> docIds = iKnowledgeResourceManager.list(queryWrapper).stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList());
//
//            if (CollectionUtils.isEmpty(docIds)) {
//                return Collections.emptyList();
//            }
//
//            List<Map<String, Object>> maps = faceManager.listGroupBySourceIdOrderByCount(docIds);
//            List<PersonInfoDTO> personInfoDTOList = new ArrayList<>();
//            if (CollectionUtils.isNotEmpty(maps)) {
//                for (Map<String, Object> map : maps) {
//                    String sourceId = map.get("source_id").toString();
//                    long count = (long) map.get("count");
//                    List<PersonInfoDTO> data = personClient.personInfo(new HttpPersonInfoReq().setTenantId(getTenantId()).setIds(Collections.singletonList(sourceId))).getData();
//                    if (CollectionUtils.isNotEmpty(data)) {
//                        personInfoDTOList.add(data.get(0));
//                    }
//                    if (personInfoDTOList.size() == 12) {
//                        break;
//                    }
//                }
//            }
//            return personInfoDTOList;
//        } finally {
//            fixedThreadPool.submit(() -> {
//                try {
//                    NpcSearchLogEntity npcSearchLogEntity = new NpcSearchLogEntity();
//                    npcSearchLogEntity.init(getTenantId(), getUserCode());
//                    npcSearchLogEntity.setSearchType(req.getSearchType());
//                    npcSearchLogEntity.setSearchContent(req.getSearchContent());
//                    npcSearchLogEntity.setInput(JSON.toJSONString(req));
//                    iNpcSearchLogService.save(npcSearchLogEntity);
//                } catch (Exception e) {
//                    log.error("异步保存日志失败", e);
//                }
//            });
//        }
//    }
//
//    public RenDaPersonStatsResp personStats(RenDaSearchVideoReq req) {
//        RenDaPersonStatsResp renDaPersonStatsResp = new RenDaPersonStatsResp();
//        RenDaSearchTypeEnum renDaSearchTypeEnum = RenDaSearchTypeEnum.valueOf(req.getSearchType());
//        if (!RenDaSearchTypeEnum.PERSON.equals(renDaSearchTypeEnum)) {
//            return renDaPersonStatsResp;
//        }
//        try {
//            List<PersonInfoDTO> data = personClient.personInfo(new HttpPersonInfoReq().setTenantId(getTenantId()).setIds(Collections.singletonList(req.getSearchContent()))).getData();
//            if (CollectionUtils.isNotEmpty(data)) {
//                renDaPersonStatsResp.setPersonInfo(data.get(0));
//            }
//
//            // 公共空间所有文件库ID
//            List<Long> groupIds = groupIds();
//            if (CollectionUtils.isEmpty(groupIds)) {
//                return renDaPersonStatsResp;
//            }
//            Set<String> coreDocIds = iResourceFaceService.getDocIdsByFaceIds(Collections.singletonList(req.getSearchContent()));
//            if (CollectionUtils.isEmpty(coreDocIds)) {
//                return renDaPersonStatsResp;
//            }
//            // 预筛选
//            Set<String> docIdsByFaceIds = null;
//            if (CollectionUtils.isNotEmpty(req.getPersonIds())) {
//                docIdsByFaceIds = iResourceFaceService.getDocIdsByFaceIds(req.getPersonIds());
//                // 通过AI人员搜索无结果
//                if (CollectionUtils.isEmpty(docIdsByFaceIds)) {
//                    return renDaPersonStatsResp;
//                }
//            }
//            Set<String> docIdsByAsrContent = null;
//            if (StringUtils.isNotBlank(req.getAsrContent())) {
//                docIdsByAsrContent = asrService.getDocIdsByContent(getTenantId(), req.getAsrContent());
//                // 通过ASR内容搜索无结果
//                if (CollectionUtils.isEmpty(docIdsByAsrContent)) {
//                    return renDaPersonStatsResp;
//                }
//            }
//            LambdaQueryWrapper<KnowledgeResourceEntity> queryWrapper = initQuery(req, docIdsByFaceIds, docIdsByAsrContent);
//            queryWrapper.in(KnowledgeResourceEntity::getDocId, coreDocIds);
//            queryWrapper.select(KnowledgeResourceEntity::getDocId);
//
//            List<String> docIds = iKnowledgeResourceManager.list(queryWrapper).stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(docIds)) {
//                return renDaPersonStatsResp;
//            }
//
//            List<ResourceFaceEntity> list = faceManager.list(
//                    new LambdaQueryWrapper<ResourceFaceEntity>()
//                            .eq(ResourceFaceEntity::getDeleted, false)
//                            .eq(ResourceFaceEntity::getTenantId, getTenantId())
//                            .eq(ResourceFaceEntity::getSourceId, req.getSearchContent())
//                            .in(ResourceFaceEntity::getDocId, docIds)
//                            .select(ResourceFaceEntity::getDocId)
//            );
//            renDaPersonStatsResp.setVideoCount(list.stream().map(ResourceFaceEntity::getDocId).distinct().count());
//            renDaPersonStatsResp.setDurationSecond(list.size());
//            return renDaPersonStatsResp;
//        } finally {
//            NpcSearchLogEntity npcSearchLogEntity = new NpcSearchLogEntity();
//            npcSearchLogEntity.init(getTenantId(), getUserCode());
//            npcSearchLogEntity.setSearchType(req.getSearchType());
//            npcSearchLogEntity.setSearchContent(req.getSearchContent());
//            npcSearchLogEntity.setInput(JSON.toJSONString(req));
//            fixedThreadPool.submit(() -> {
//                try {
//                    iNpcSearchLogService.save(npcSearchLogEntity);
//                } catch (Exception e) {
//                    log.error("异步保存日志失败", e);
//                }
//            });
//        }
//    }
//
//    public RenDaSearchSuggestionResp suggestion(Integer searchType) {
//        RenDaSearchSuggestionResp renDaSearchSuggestionResp = new RenDaSearchSuggestionResp();
//        List<NpcSearchLogEntity> userLatestSearchList = iNpcSearchLogService.listByUser(getTenantId(), getUserCode(), searchType);
//        List<RenDaSearchSuggestionResp.SearchParam> userLatestList = userLatestSearchList.stream().map(NpcSearchLogEntity::getSearchContent).distinct().limit(5).map(searchContent -> {
//            RenDaSearchSuggestionResp.SearchParam searchParam = new RenDaSearchSuggestionResp.SearchParam();
//            searchParam.setSearchType(searchType);
//            searchParam.setSearchContent(searchContent);
//            searchParam.setDisplayContent(searchContent);
//            return searchParam;
//        }).collect(Collectors.toList());
//        renDaSearchSuggestionResp.setUserLatestList(userLatestList);
//
//        LocalDateTime startTime = LocalDateTimeUtil.offset(LocalDateTime.now(), -1, ChronoUnit.MONTHS);
//        List<NpcSearchLogEntity> tenantLatestSearchList = iNpcSearchLogService.listByTenant(getTenantId(), searchType, startTime);
//        List<Map.Entry<String, Long>> sortedSearchContent = tenantLatestSearchList.stream()
//                .filter(e -> StringUtils.isNotBlank(e.getSearchContent()))
//                .collect(Collectors.groupingBy(
//                        s -> s.getSearchContent().trim(),
//                        Collectors.counting()
//                ))
//                .entrySet().stream()
//                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
//                .limit(5)
//                .collect(Collectors.toList());
//
//        List<RenDaSearchSuggestionResp.SearchParam> tenantHotList = sortedSearchContent.stream().map(e -> {
//            RenDaSearchSuggestionResp.SearchParam searchParam = new RenDaSearchSuggestionResp.SearchParam();
//            searchParam.setSearchType(searchType);
//            searchParam.setSearchContent(e.getKey());
//            searchParam.setDisplayContent(e.getKey());
//            searchParam.setCount(e.getValue());
//            return searchParam;
//        }).collect(Collectors.toList());
//        renDaSearchSuggestionResp.setTenantHotList(tenantHotList);
//
//        if (searchType == 2) {
//            faceInfo(renDaSearchSuggestionResp);
//        }
//        return renDaSearchSuggestionResp;
//    }
//
//    private void faceInfo(RenDaSearchSuggestionResp renDaSearchSuggestionResp) {
//
//        List<String> list1 = renDaSearchSuggestionResp.getUserLatestList().stream().map(RenDaSearchSuggestionResp.SearchParam::getSearchContent).collect(Collectors.toList());
//        List<String> list2 = renDaSearchSuggestionResp.getTenantHotList().stream().map(RenDaSearchSuggestionResp.SearchParam::getSearchContent).collect(Collectors.toList());
//
//        List<PersonInfoDTO> personInfoDTOList = new ArrayList<>();
//        List personIds = ListUtils.union(list1, list2);
//        if (CollectionUtils.isNotEmpty(personIds)) {
//            personInfoDTOList = personClient.personInfo(new HttpPersonInfoReq().setTenantId(getTenantId()).setIds(personIds)).getData();
//        }
//        Map<String, PersonInfoDTO> personInfoMap = personInfoDTOList.stream().collect(Collectors.toMap(PersonInfoDTO::getId, person -> person));
//
//        renDaSearchSuggestionResp.getUserLatestList().forEach(e -> {
//            PersonInfoDTO personInfoDTO = personInfoMap.get(e.getSearchContent());
//            if (personInfoDTO != null) {
//                e.setDisplayContent(personInfoDTO.getName());
//            }
//            e.setPersonInfo(personInfoDTO);
//        });
//        renDaSearchSuggestionResp.getTenantHotList().forEach(e -> {
//            PersonInfoDTO personInfoDTO = personInfoMap.get(e.getSearchContent());
//            if (personInfoDTO != null) {
//                e.setDisplayContent(personInfoDTO.getName());
//            }
//            e.setPersonInfo(personInfoDTO);
//        });
//    }
//
//    public void suggestionClear(Integer searchType) {
//        iNpcSearchLogService.deleteByUser(getTenantId(), getUserCode(), searchType);
//    }
//}
