package com.linker.fusion.knowledgecenter.server.biz;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.base.exception.BusinessException;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ImportTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.WordsStateEnum;
import com.linker.fusion.knowledgecenter.server.convert.WordsConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.word.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.WordsPageResp;
import com.linker.fusion.knowledgecenter.service.domain.common.ExcelUtils;
import com.linker.fusion.knowledgecenter.service.domain.common.ReadExcelDTO;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.word.WordsService;
import com.linker.fusion.knowledgecenter.service.domain.word.model.WordsDTO;
import com.linker.fusion.knowledgecenter.service.domain.word.model.WordsListDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.linker.core.base.enums.RespCodeEnum.BUSINESS_EXCEPTION;
import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.TABLE_HEAD_MAX_100;
import static org.springframework.http.HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS;
import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;

/**
 * <AUTHOR>
 * @description 词组工厂Biz
 **/
@Component
@Slf4j
public class WordsBiz extends BaseBiz {


    @Resource
    private KnowledgeGroupService knowledgeGroupService;
    /**
     * 100MB
     */
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024;

    public Long create(WordsCreateReq cmd) {
        WordsService service = WordsService.getService(cmd.getType());
        return service.create(WordsConvert.INSTANCE.toDto(cmd));
    }

    public void modify(WordsModifyReq cmd) {
        WordsService service = WordsService.getService(cmd.getType());
        service.modify(WordsConvert.INSTANCE.toDto(cmd));
    }

    public void delete(WordsDeletedReq cmd) {
        WordsService service = WordsService.getService(cmd.getType());
        service.batchDelete(WordsConvert.INSTANCE.toDto(cmd));
    }

    public void changeState(WordsChangeStateReq cmd) {
        WordsService service = WordsService.getService(cmd.getType());
        service.batchChangeState(WordsConvert.INSTANCE.toDto(cmd));
    }

    public static void main(String[] args) throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("template/专业词库导入模板.xlsx");
        System.out.println(classPathResource.size());
    }

    public BasePaginResp<WordsPageResp> page(WordsSearchReq query) {
        KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(query.getGroupId());
        WordsService service = WordsService.getService(query.getType());
        BasePaginResp<WordsListDTO> page = service.page(WordsConvert.INSTANCE.toDto(query));
        return new BasePaginResp<>(page.getTotal(), WordsConvert.INSTANCE.toResp(page.getData()));
    }

    public List<WordsPageResp> findList(WordsFindListReq wordsFindListReq) {
        WordsService service = WordsService.getService(wordsFindListReq.getType());
        List<WordsListDTO> result = service.findList(WordsConvert.INSTANCE.toDto(wordsFindListReq), wordsFindListReq.getTenantId());
        return WordsConvert.INSTANCE.toResp(result);
    }

    public void importWords(MultipartFile file, Long groupId, Integer type) {
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("文件大小超过 100MB: " + file.getSize() + " bytes");
        }

        // 分批处理Excel数据
        try {
            LocalDateTime start = LocalDateTime.now();
            processExcelByEasyExcel(file, groupId, type);
            LocalDateTime end = LocalDateTime.now();
            log.info("导入耗时:{}毫秒", Duration.between(start, end).toMillis());
        } catch (IOException e) {
            log.warn("读取excel失败,e:{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException("读取excel失败");
        }
    }

    /**
     * 使用EasyExcel分批处理Excel数据
     *
     * @param file
     * @param groupId
     * @param type
     * @throws IOException
     */
    private void processExcelByEasyExcel(MultipartFile file, Long groupId, Integer type) throws IOException {
        // 定义批次大小
        int batchSize = 1000;
        // 创建监听器处理数据
        ExcelDataListener listener = new ExcelDataListener(batchSize, groupId, type, this);
        // 使用EasyExcel读取数据
        EasyExcel.read(file.getInputStream(), WordsExcelRwoDTO.class, listener)
                .sheet()
                //excel中有说明性文字，需要通过找到表头位置
                .headRowNumber(2)
                .doRead();
    }

    /**
     * 分批处理Excel数据
     *
     * @param file
     * @param groupId
     * @param type
     * @throws IOException
     */
    private void processExcelInBatches(MultipartFile file, Long groupId, Integer type) throws IOException {
        // 定义批次大小
        int batchSize = 1000;
        int currentRow = 0;

        // 使用流式处理读取Excel数据
        try (InputStream inputStream = file.getInputStream()) {
            ReadExcelDTO readExcelDTO = ExcelUtils.readExcelFromUrl(inputStream, 0, file.getOriginalFilename());
            List<List<Object>> rows = readExcelDTO.getRows();

            if (CollectionUtils.isEmpty(rows)) {
                return;
            }

            // 校验模板
            checkExcel(readExcelDTO);

            // 跳过表头
            List<List<Object>> content = rows.subList(1, rows.size());

            List<WordsExcelRwoDTO> batchData = new ArrayList<>(batchSize);

            for (List<Object> row : content) {
                if ((row.get(0) == null || StringUtils.isBlank(String.valueOf(row.get(0))))) {
                    continue;
                }

                WordsExcelRwoDTO wordsExcelRwoDTO = new WordsExcelRwoDTO();
                wordsExcelRwoDTO.setName(String.valueOf(row.get(0)));
                String state = String.valueOf(row.get(1));
                if (!"启用".equals(state) && !"禁用".equals(state)) {
                    state = "启用";
                }
                wordsExcelRwoDTO.setState(state);
                wordsExcelRwoDTO.setSynonymsStr(String.valueOf(row.get(2)));

                batchData.add(wordsExcelRwoDTO);
                currentRow++;

                // 当达到批次大小时，处理这一批数据
                if (batchData.size() >= batchSize) {
                    processBatch(batchData, groupId, type);
                    batchData.clear();
                }
            }

            // 处理最后一批数据
            if (!batchData.isEmpty()) {
                processBatch(batchData, groupId, type);
            }
        }
    }

    // 处理一批数据
    private void processBatch(List<WordsExcelRwoDTO> rowDataList, Long groupId, Integer type) {
        List<WordsDTO> param = convert(rowDataList, groupId, type);
        WordsService service = WordsService.getService(type);
        service.batchCreate(param, groupId, type);
    }

    private List<WordsDTO> convert(List<WordsExcelRwoDTO> rowDataList, Long groupId, Integer type) {
        List<WordsDTO> param = Lists.newArrayListWithExpectedSize(rowDataList.size());
        for (WordsExcelRwoDTO wordsExcelRwoDTO : rowDataList) {
            WordsDTO element = convert(wordsExcelRwoDTO);
            element.setGroupId(groupId);
            element.setType(type);
            element.setImportSource(ImportTypeEnum.EXCEL.getCode());
            param.add(element);
        }
        return param;
    }


    public List<WordsExcelRwoDTO> readExcel(InputStream inputStream, String suffix) throws IOException {
        ReadExcelDTO readExcelDTO = ExcelUtils.readExcelFromUrl(inputStream, 0, suffix);
        List<List<Object>> rows = readExcelDTO.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return Lists.newArrayList();
        }
        // 校验一下 有没有更改模版表 和乱填
        checkExcel(readExcelDTO);

        List<WordsExcelRwoDTO> rowDataList = new ArrayList<>();
        try {
            List<List<Object>> content = rows.subList(1, rows.size());
            for (List<Object> row : content) {
                if ((row.get(0) == null || StringUtils.isBlank(String.valueOf(row.get(0))))) {
                    continue;
                }
                WordsExcelRwoDTO wordsExcelRwoDTO = new WordsExcelRwoDTO();
                wordsExcelRwoDTO.setName(String.valueOf(row.get(0)));
                String state = String.valueOf(row.get(1));
                if (!"启用".equals(state) && !"禁用".equals(state)) {
                    state = "启用";
                }
                wordsExcelRwoDTO.setState(state);
                wordsExcelRwoDTO.setSynonymsStr(String.valueOf(row.get(2)));
                rowDataList.add(wordsExcelRwoDTO);
            }
        } catch (IndexOutOfBoundsException e) {
            log.warn("未按模版输入");
            throw new ServiceException(TABLE_HEAD_MAX_100);
        }

        return rowDataList;
    }

    private void checkExcel(ReadExcelDTO readExcelDTO) {
//        Sheet sheet = readExcelDTO.getSheet();

//        if (Objects.isNull(sheet)) {
//            throw new ServiceException(TABLE_HEAD_MAX_100);
//        }
//
//        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
//        if (CollectionUtils.isEmpty(mergedRegions) || mergedRegions.size() != 1) {
//            throw new ServiceException(TABLE_HEAD_MAX_100);
//        }
//        for (CellRangeAddress mergedRegion : mergedRegions) {
//            int lastColumn = mergedRegion.getLastColumn();
//            int firstColumn = mergedRegion.getFirstColumn();
//            int firstRow = mergedRegion.getFirstRow();
//            int lastRow = mergedRegion.getLastRow();
//            if (lastColumn != 19 ||
//                    firstColumn != 0 ||
//                    firstRow != 0 ||
//                    lastRow != 9) {
//                throw new ServiceException(TABLE_HEAD_MAX_100);
//            }
//        }
        List<List<Object>> rows = readExcelDTO.getRows();
        try {
            List<List<Object>> title = rows.subList(0, 1);
            // 这段逻辑主要处理 词条头是否被修改
            if (CollectionUtils.isEmpty(title) ||
                    !(("敏感词条（必填）".equals(String.valueOf(title.get(0).get(0))) && "敏感词状态（必填）".equals(String.valueOf(title.get(0).get(1))))
                            || ("专业词条（必填）".equals(String.valueOf(title.get(0).get(0))) && "专业词状态（必填）".equals(String.valueOf(title.get(0).get(1))) && "同义词".equals(String.valueOf(title.get(0).get(2)))))) {
                throw new ServiceException(TABLE_HEAD_MAX_100);
            }
        } catch (IndexOutOfBoundsException e) {
            log.warn("未按模版输入,e:{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException(TABLE_HEAD_MAX_100);
        }
    }

    private WordsDTO convert(WordsExcelRwoDTO wordsExcelRwoDTO) {
        if (wordsExcelRwoDTO == null) {
            return null;
        }
        WordsDTO wordsDTO = new WordsDTO();
        wordsDTO.setName(wordsExcelRwoDTO.getName());
        WordsStateEnum wordsStateEnum = WordsStateEnum.valueOfDesc(wordsExcelRwoDTO.getState());
        if (Objects.isNull(wordsStateEnum)) {
            wordsStateEnum = WordsStateEnum.ENABLE;
        }
        wordsDTO.setState(wordsStateEnum.getCode());
        if (StringUtils.isNotBlank(wordsExcelRwoDTO.getSynonymsStr())) {
            List<String> synonyms = Arrays.asList(wordsExcelRwoDTO.getSynonymsStr().split(","));
            wordsDTO.setProSynonyms(synonyms);
        }

        return wordsDTO;
    }

    public List<WordsPageResp> findList(WordsByGroupReq wordsFindListReq) {
        WordsService service = WordsService.getService(wordsFindListReq.getType());
        List<WordsListDTO> result = service.findList(wordsFindListReq.getGroupIds(),
                wordsFindListReq.getType(),
                wordsFindListReq.getTenantId());
        return WordsConvert.INSTANCE.toResp(result);
    }

    public void exportWords(WordsExportReq req, HttpServletResponse response) {
        WordsService service = WordsService.getService(req.getType());
        String templatePath = service.getExportTemplate();
        String exportFileName = service.getExportName();
        List<WordsListDTO> words = service.getListOrderByIdDesc(req.getWordIds());
        // 根据模板填充,数据到输出流
        export(words, response, templatePath, exportFileName);
    }

    private List<List<String>> rowDataList(List<WordsListDTO> words) {
        return words.stream().map(w -> {
            List<String> row = new ArrayList<>();
            row.add(w.getName());
            row.add(WordsStateEnum.valueOf(w.getState()).getDesc());
            row.add(org.apache.commons.collections4.CollectionUtils.isNotEmpty(w.getProSynonyms()) ? String.join(",", w.getProSynonyms()) : "");
            return row;
        }).collect(Collectors.toList());
    }

    private void export(List<WordsListDTO> words, HttpServletResponse response, String templatePath, String exportName) {

        try (InputStream inputStream = ResourceUtil.getStream(templatePath);
             OutputStream outputStream = response.getOutputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);

            // 模板前面有提示语和表头，数据从第12行开始
            int startRow = 11;

            for (List<String> rowData : rowDataList(words)) {
                Row row = sheet.createRow(startRow++);
                int cellNum = 0;
                for (String cellData : rowData) {
                    Cell cell = row.createCell(cellNum++);
                    cell.setCellValue(cellData);
                }
            }

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = exportName + "-" + DateUtil.format(new Date(), "yyyyMMddHHmm") + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader(ACCESS_CONTROL_EXPOSE_HEADERS, CONTENT_DISPOSITION);
            response.setHeader(CONTENT_DISPOSITION, "attachment;filename*=utf-8''" + encodedFileName);
            workbook.write(outputStream);
        } catch (Exception e) {
            log.error("导出失败", e);
            // 处理异常
            throw new ServiceException(BUSINESS_EXCEPTION);
        }
    }


    @Data
    public static class WordsExcelRwoDTO {
        @ExcelProperty(index = 0)
        private String name;

        @ExcelProperty(index = 1)
        private String state;

        /**
         * 逗号隔开的词组
         */
        @ExcelProperty(index = 2)
        private String synonymsStr;

    }

    // EasyExcel数据监听器
    @Data
    public static class ExcelDataListener extends AnalysisEventListener<WordsExcelRwoDTO> {
        private int batchSize;
        private Long groupId;
        private Integer type;
        private WordsBiz wordsBiz;

        private List<WordsExcelRwoDTO> batchData = new ArrayList<>();

        public ExcelDataListener() {
        }

        public ExcelDataListener(int batchSize, Long groupId, Integer type, WordsBiz wordsBiz) {
            this.batchSize = batchSize;
            this.groupId = groupId;
            this.type = type;
            this.wordsBiz = wordsBiz;
        }

        @Override
        public void invoke(WordsExcelRwoDTO data, AnalysisContext context) {

            // 数据校验和清洗
            if (data.getName() == null || StringUtils.isBlank(data.getName())) {
                return;
            }

            // 状态标准化
            if (!"启用".equals(data.getState()) && !"禁用".equals(data.getState())) {
                data.setState("启用");
            }
            batchData.add(data);
            // 批量处理
            if (batchData.size() >= batchSize) {
                wordsBiz.processBatch(new ArrayList<>(batchData), groupId, type);
                batchData.clear();
            }


        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 处理剩余数据
            if (!batchData.isEmpty()) {
                wordsBiz.processBatch(new ArrayList<>(batchData), groupId, type);
            }
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            //对设定的rowNumber进行处理
            ReadRowHolder readRowHolder = context.readRowHolder();
            Map<Integer, CellData> cellDataMap = (Map) readRowHolder.getCellMap();
            readRowHolder.setCurrentRowAnalysisResult(cellDataMap);
            int rowIndex = readRowHolder.getRowIndex();
            int currentHeadRowNumber = context.readSheetHolder().getHeadRowNumber();
            boolean isData = rowIndex >= currentHeadRowNumber;

            if (!isData && currentHeadRowNumber != rowIndex + 1) {
                return;
            }
            try {
                // 检查必要列是否存在
                if (headMap.size() < 2) {
                    throw new ServiceException(TABLE_HEAD_MAX_100);
                }
                // 获取前几列的表头名称
                String firstColumn = headMap.get(0);
                String secondColumn = headMap.get(1);
                String thirdColumn = headMap.get(2); // 可能为null
                boolean isValid = false;
                // 校验敏感词模板格式: 敏感词条（必填）, 敏感词状态（必填）
                if ("敏感词条（必填）".equals(firstColumn) && "敏感词状态（必填）".equals(secondColumn)) {
                    isValid = true;
                }
                // 校验专业词模板格式: 专业词条（必填）, 专业词状态（必填）, 同义词
                else if ("专业词条（必填）".equals(firstColumn) &&
                        "专业词状态（必填）".equals(secondColumn) &&
                        "同义词".equals(thirdColumn)) {
                    isValid = true;
                }
                if (!isValid) {
                    throw new ServiceException(TABLE_HEAD_MAX_100);
                }
            } catch (ServiceException e) {
                throw e;
            } catch (Exception e) {
                log.warn("表头校验失败,e:{}", Throwables.getStackTraceAsString(e));
                throw new ServiceException(TABLE_HEAD_MAX_100);
            }
        }
    }
}