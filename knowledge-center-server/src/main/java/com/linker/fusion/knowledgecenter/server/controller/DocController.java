package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowResultResp;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthNodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OperatorTypeEnum;
import com.linker.fusion.knowledgecenter.server.biz.DocBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.core.BatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.EnableReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.*;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.rpc.SegmentReChunkReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.ChunkItemResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.DocInfoEntityResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.GroupNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.auth.MenuAndResNodeAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "文档管理")
@RestController
@RequestMapping("/doc")
public class DocController {

    @Resource
    private DocBiz docBiz;

    @PostMapping("/page")
    @ApiOperation(value = "文件列表分页", tags = {"v1.6.7"})
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public BasePaginResp<DocInfoEntityResp> page(@RequestBody @Valid DocPageReq req) {
        return docBiz.page(req);
    }
    @PostMapping("/statusListener")
    @ApiOperation(value = "文件状态监听", tags = {"v1.5.1"})
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public SseEmitter statusListener(@RequestBody @Valid DocIdsReq req) {
        return docBiz.statusListener(req);
    }


    @GetMapping("/statusListener")
    @ApiOperation(value = "文件状态监听", tags = {"v1.5.1"})
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @LogFilter
    public SseEmitter statusListener(@RequestParam(value = "ids") List<Long> ids) {
        return docBiz.statusListener(ids);
    }

    @PostMapping("/enable")
    @ApiOperation(value = "文件 启用/禁用")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.ENABLE, node = AuthNodeEnum.RESOURCE_ENABLE, ids = "{#req.ids}")
    public BaseResp<Void> enable(@RequestBody @Validated EnableReq req) {
        return docBiz.enable(req);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "文件删除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.DELETE, node = AuthNodeEnum.RESOURCE_DELETE, ids = "{#req.ids}")
    public BaseResp<Void> delete(@RequestBody @Validated BatchBaseReq req) {
        return docBiz.delete(req);
    }

    @PostMapping("/update")
    @ApiOperation(value = "文件修改")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.EDIT, node = AuthNodeEnum.RESOURCE_EDIT, ids = "{#req.id}")
    public BaseResp<Void> update(@RequestBody @Validated DocUpdateReq req) {
        if(StringUtils.isNotBlank(req.getTitle())){
            req.setTitle(req.getTitle().trim());
        }
        return docBiz.update(req);
    }

    @PostMapping("/redo")
    @ApiOperation(value = "重新学习", tags = "v1.6.1")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.REDO, node = AuthNodeEnum.RESOURCE_REDO, ids = "{#req.ids}")
    public BaseResp<Void> redo(@RequestBody @Validated RedoReq req) {
        docBiz.redo(req);
        return new BaseResp<>();
    }

    @PostMapping("/redo/batch")
    @ApiOperation(value = "批量重新学习", tags = "v1.4.6")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.REDO, node = AuthNodeEnum.RESOURCE_REDO, ids = "{#req.ids}")
    public BaseResp<Void> redoBatch(@RequestBody @Validated RedoBatchReq req) {
        docBiz.batchRedo(req);
        return new BaseResp<>();
    }

    @PostMapping("/telredo")
    @ApiOperation(value = "租户失败资源重跑", tags = "v1.4.6")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> telRedo(@RequestBody @Validated RedoReq req) {
        docBiz.telRedo(req);
        return new BaseResp<>();
    }

    @PostMapping("/import")
    @ApiOperation(value = "文件导入", tags = "v1.6.1")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.IMPORT, node = AuthNodeEnum.RESOURCE_IMPORT, groupIds = "{#req.groupId}")
    public BaseResp<DocInfoEntityResp> importDoc(@RequestBody @Validated ImportReq req) {
        return new BaseResp<>(docBiz.importDoc(req));
    }

    @RequestMapping("/import/callback/{docId}")
    @ApiOperation(value = "文件导入工作流回调", hidden = true)
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    public BaseResp<Void> importCallback(@PathVariable(value = "docId") String docId, @RequestBody WorkflowResultResp body) {
        docBiz.importCallback(docId, body);
        return new BaseResp<>();
    }

    @PostMapping("/archive")
    @ApiOperation(value = "文件归档", tags = "v1.6.1")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.ARCHIVE, node = AuthNodeEnum.RESOURCE_ARCHIVE, ids = "{#req.ids}")
    public BaseResp<Void> archive(@RequestBody @Validated BatchBaseReq req) {
        docBiz.archive(req);
        return new BaseResp<>();
    }

    @PostMapping("/name/exists")
    @ApiOperation(value = "判断名称是否存在")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Boolean> exists(@RequestBody @Validated NameExistsReq req) {
        return docBiz.nameExists(req);
    }

    @GetMapping("/preview")
    @ApiOperation(value = "文件详情预览")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public BaseResp<DocInfoEntityResp> docPreviewInfo(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "docId", required = false) String docId) {
        return new BaseResp<>(docBiz.preview(id, docId));
    }

    @PostMapping("/segment/re-chunk")
    @ApiOperation(value = "重新分块", tags = {"v1.5.0"})
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<TaskEntity> reChunk(@RequestBody SegmentReChunkReq req) {
        return docBiz.reChunk(req);
    }

    @PostMapping("/chunk")
    @ApiOperation(value = "文档分片查询")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<List<ChunkItemResp>> chunk(@RequestBody ChunkReq req) {
        return docBiz.chunk(req);
    }

    @PostMapping("/chunk/update")
    @ApiOperation(value = "文档分片更新")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<Void> updateChunk(@RequestBody @Validated UpdateChunkReq req) {
        docBiz.updateChunk(req);
        return new BaseResp<>();
    }

    @PostMapping("/chunk/add")
    @ApiOperation(value = "文档分片添加")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<Void> addChunk(@RequestBody @Validated AddChunkReq req) {
        return docBiz.addChunk(req);
    }

    @PostMapping("/chunk/delete")
    @ApiOperation(value = "文档分片删除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, chunkIds = "{#req.chunkIds}")
    public BaseResp<Void> deleteChunk(@RequestBody DeleteChunkReq req) {
        return docBiz.deleteChunk(req);
    }

    @GetMapping("/image/caption")
    @ApiOperation(value = "图片caption")
    public BaseResp<String> imageCaption(String url) {
        return new BaseResp<>(docBiz.imageCaption(url));
    }

    @PostMapping("/move")
    @ApiOperation(value = "文件移动", tags = "v1.4.6")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MOVE, node = AuthNodeEnum.RESOURCE_MOVE, ids = "{#req.ids}")
    @GroupNodeAuth(node = AuthNodeEnum.RESOURCE_IMPORT, groupIds = "{#req.groupId}")
    public BaseResp<Void> move(@RequestBody @Validated MoveReq req) {
        docBiz.move(req);
        return new BaseResp<>();
    }
    @PostMapping("/rename")
    @ApiOperation(value = "文件重命名", tags = "v1.4.6")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.RENAME, node = AuthNodeEnum.RESOURCE_RENAME, ids = "{#req.id}")
    public BaseResp<Void> rename(@RequestBody @Validated RenameReq req) {
        docBiz.rename(req);
        return new BaseResp<>();
    }


}
