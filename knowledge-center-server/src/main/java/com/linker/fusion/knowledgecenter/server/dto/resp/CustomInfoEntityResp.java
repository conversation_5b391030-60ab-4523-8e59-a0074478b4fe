package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.linker.fusion.knowledgecenter.server.dto.resp.core.BaseEntityResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CustomInfoEntityResp extends BaseEntityResp {

    @ApiModelProperty(value = "编号")
    private Long id;

    @ApiModelProperty(value = "管理类型 1-文档 2-图片 3-视频 4-音频 5-FAQ 6-表格")
    private Integer manageType;

    @ApiModelProperty(value = "字段类型 0-字符串 1-数字 2-时间 3-标签 4-单选 5-多选 6-附件")
    private String name;

    @ApiModelProperty(value = "字段UID")
    private String field;

    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "多选信息")
    private String options;

    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    @ApiModelProperty(value = "必填")
    private Boolean required;

    @ApiModelProperty(value = "外部ID")
    private Boolean isExternal;

    /**
     * 标签是否展示
     */
    private Boolean tabDisplay;
}
