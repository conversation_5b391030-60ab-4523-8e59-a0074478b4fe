package com.linker.fusion.knowledgecenter.server.biz;

import com.alibaba.fastjson.JSON;
import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.client.ApproveClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.*;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.ApproveProcessConfig;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.ApproveProcessResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.UserInfoResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IUserCenter;
import com.linker.fusion.knowledgecenter.infrastructure.entity.*;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.SourceTypeEnum;
import com.linker.fusion.knowledgecenter.server.dto.auth.AuthApproveAddReq;
import com.linker.fusion.knowledgecenter.server.dto.auth.AuthApproveCallbackReq;
import com.linker.fusion.knowledgecenter.server.dto.auth.UpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.KnowledgeGroupParentResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthApproveService;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthLevelService;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.user.api.dto.UserInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.*;

@Slf4j
@Component
public class AuthApproveBiz extends BaseBiz {

    @Resource
    private ApproveClient approveClient;

    @Resource
    private IAuthApproveService iAuthApproveService;

    @Resource
    private IAuthLevelService iAuthLevelService;

    @Resource
    private KnowledgeGroupService knowledgeGroupService;

    @Resource
    private IResourceService iResourceService;

    @Resource
    private IUserCenter userCenter;

    @Resource
    private IAuthService iAuthService;

    @Value("${app.doc.preview.prefix:/center/#/docs/preview}")
    private String docPreviewPrefix;

    public List<ApproveProcessResp> processList() {

        return approveClient.processList(
                new ApproveProcessListReq()
                        .setTenantId(UserContext.tenantId())
                        .setProcessState(1)
        ).getData();
    }


    public void setLibProcess(@NotNull Long groupId, @NotBlank String approveProcessKey) {

        knowledgeGroupService.updateApproveProcessKey(groupId, approveProcessKey);
    }

    public void apply(AuthApproveAddReq req) {

        if (req.getAuthLevel() <= req.getCurrentLevel()) {
            throw new ServiceException(APPLY_AUTH_LEVEL_LIMIT);
        }
        String processKey = req.getApproveProcessKey();
        if (iAuthApproveService.exist(UserContext.tenantId(), UserContext.userCode(), req.getSourceType(), req.getSourceId().toString())) {
            throw new ServiceException(APPLY_DUPLICATE);
        }

        BaseResp<ApproveProcessResp> processInfoResp = approveClient.processInfo(new ApproveProcessInfoReq(processKey));
        if (processInfoResp == null || !processInfoResp.isSuccess()) {
            throw new ServiceException(RPC_ERROR);
        }
        ApproveProcessResp approveProcessResp = processInfoResp.getData();
        ApproveProcessConfig processModel = approveProcessResp.getConfig();
        ApproveProcessConfig.NodeConfig nodeConfig = processModel.getNodeConfig();
        ApproveProcessConfig.NodeConfig.ChildNode childNode = nodeConfig.getChildNode();


        ApproveStartReq approveStartReq = new ApproveStartReq();
        approveStartReq.setProcessKey(processKey);
        this.setProcessForm(req, approveStartReq);
        List<ApproveStartReq.RequestAssignee> assigneeList = new ArrayList<>();
        String approveUserCode = this.getApproveUser(req.getSourceType(), req.getSourceId());
        List<UserInfoResp> data = userCenter.getUserByCode(new UserSearchByIdsReq(Collections.singletonList(approveUserCode))).getData();
        String approveUserName = CollectionUtils.isNotEmpty(data) ? data.get(0).getNickName() : "";
        assigneeList.add(
                new ApproveStartReq.RequestAssignee()
                        .setNodeKey(childNode.getNodeKey())
                        .setType(childNode.getType())
                        .setUserCode(approveUserCode)
                        .setUserName(approveUserName)
        );
        approveStartReq.setAssigneeList(assigneeList);
        approveStartReq.setTenantId(UserContext.tenantId());
        approveStartReq.setUserCode(UserContext.userCode());
        approveStartReq.setUserName(UserContext.getUser().getUser().getNickName());
        BaseResp<String> instanceStartResp = approveClient.instanceStart(approveStartReq);
        if (instanceStartResp == null || !instanceStartResp.isSuccess()) {
            throw new ServiceException(RPC_ERROR);
        }
        String instanceId = instanceStartResp.getData();

        AuthApproveEntity authApproveEntity = new AuthApproveEntity();
        authApproveEntity.setTenantId(UserContext.tenantId());
        authApproveEntity.setCreatorId(UserContext.userCode());
        authApproveEntity.setSourceId(req.getSourceId().toString());
        authApproveEntity.setSourceType(req.getSourceType() == 1 ? SourceTypeEnum.Group.getValue() : SourceTypeEnum.File.getValue());
        authApproveEntity.setAuthLevel(req.getAuthLevel());
        authApproveEntity.setApproveProcessKey(processKey);
        authApproveEntity.setApproveInstanceId(instanceId);
        authApproveEntity.setState(0);
        iAuthApproveService.save(authApproveEntity);
    }

    private void setProcessForm(AuthApproveAddReq req, ApproveStartReq approveStartReq) {

        List<FormItem> formItemList = new ArrayList<>();
        formItemList.add(new FormItem("userName", "申请人", UserContext.getUser().getUser().getNickName()));
        formItemList.add(
                new FormItem("deptName", "申请人所在部门",
                        UserContext.deptList() != null ?
                                UserContext.deptList().stream().map(UserInfo.Department::getDepartmentName).collect(Collectors.joining(" ")) : ""
                )
        );
        if (req.getSourceType() == 1) {
            KnowledgeGroupEntity groupEntity = knowledgeGroupService.getNotNull(req.getSourceId());
            List<KnowledgeGroupEntity> parents = knowledgeGroupService.getParents(groupEntity);
            parents.add(groupEntity);
            String groupPath = new KnowledgeGroupParentResp(groupEntity.getId(), groupEntity.getType(), groupEntity.getVisibleType(), parents).getGroupNamePath();
            formItemList.add(new FormItem("groupName", "目录名称", groupPath));
        } else {
            KnowledgeResourceEntity resourceEntity = iResourceService.getNotNull(req.getSourceId());
            String resourceName = "<a href=\"" + docPreviewPrefix + "?docId=" + resourceEntity.getDocId() + "&page=1\" target=\"_blank\">" + resourceEntity.getTitle() + "</a>";
            formItemList.add(new FormItem("resourceName", "文件名称", resourceName));
//            formItemList.add(new FormItem("resourcePreviewUrl", "文件预览超链接", resourceEntity.getPreviewSrc()));
            KnowledgeGroupEntity groupEntity = knowledgeGroupService.getNotNull(resourceEntity.getGroupId());
            List<KnowledgeGroupEntity> parents = knowledgeGroupService.getParents(groupEntity);
            parents.add(groupEntity);
            String groupPath = new KnowledgeGroupParentResp(groupEntity.getId(), groupEntity.getType(), groupEntity.getVisibleType(), parents).getGroupNamePath();
            formItemList.add(new FormItem("groupName", "文件当前所在目录", groupPath));
        }
        HashMap<Integer, AuthLevelEntity> authLevelEntityHashMap = iAuthLevelService.map(getTenantId());
        formItemList.add(new FormItem("currentAuthLevel", "当前权限类型", req.getCurrentLevel() == null ? "" : authLevelEntityHashMap.get(req.getCurrentLevel()).getName()));
        formItemList.add(new FormItem("applyAuthLevel", "申请权限类型", authLevelEntityHashMap.get(req.getAuthLevel()).getName()));
        formItemList.add(new FormItem("applyReason", "申请理由", req.getApplyReason()));

        ApproveStartFormModel approveStartFormModel = new ApproveStartFormModel();
        Map<String, String> formData = new LinkedHashMap<>();
        List<ApproveStartFormModel.Schema.SchemaItem.Child> children = new ArrayList<>();
        for (FormItem formItem : formItemList) {
            formData.put(formItem.getField(), formItem.getValue());
            children.add(new ApproveStartFormModel.Schema.SchemaItem.Child()
                    .setField(formItem.getField())
                    .setLabel(formItem.getLabel())
                    .setId(formItem.getField()));
        }
        approveStartFormModel.setFormData(formData);
        approveStartFormModel.setSchema(
                new ApproveStartFormModel.Schema()
                        .setSchemas(
                                Collections.singletonList(
                                        new ApproveStartFormModel.Schema.SchemaItem()
                                                .setChildren(children)
                                )
                        )
        );
        approveStartReq.setProcessForm(JSON.toJSONString(approveStartFormModel));
        approveStartReq.setDes(formItemList.stream().map(FormItem::getValue).collect(Collectors.joining("\n")));
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class FormItem {

        private String field;
        private String label;
        private String value;
    }

    /**
     * 获取审批人ID、采用资源所属知识库的创建人ID
     */
    private String getApproveUser(Integer sourceType, Long sourceId) {
        Long groupId = null;
        if (sourceType == 1) {
            groupId = sourceId;
        } else {
            KnowledgeResourceEntity resourceEntity = iResourceService.getNotNull(sourceId);
            groupId = resourceEntity.getGroupId();
        }
        KnowledgeGroupEntity groupEntity = knowledgeGroupService.getNotNull(groupId);
        Long rootId = groupEntity.getRootId();
        if (rootId != null) {
            groupEntity = knowledgeGroupService.getNotNull(rootId);
        }
        return groupEntity.getCreatorId();
    }

    public UpdateReq callback(AuthApproveCallbackReq req) {
        AuthApproveEntity authApproveEntity = iAuthApproveService.getByInstanceId(req.getInstanceId());
        if (authApproveEntity == null) {
            log.warn("审批实例不存在,instanceId:{}", req.getInstanceId());
            return null;
        }
        UpdateReq updateReq = null;
        if (req.getState() == 1) {
            List<AuthEntity> auths = iAuthService.listBySourceIds(Collections.singletonList(authApproveEntity.getSourceId()), authApproveEntity.getSourceType());
            if (CollectionUtils.isEmpty(auths)) {
                Long parentId;
                if (SourceTypeEnum.Group.getValue().equals(authApproveEntity.getSourceType())) {
                    KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(Long.parseLong(authApproveEntity.getSourceId()));
                    parentId = group.getParentId();

                } else {
                    KnowledgeResourceEntity doc = iResourceService.get(Long.parseLong(authApproveEntity.getSourceId()));
                    parentId = doc.getGroupId();
                }
                auths = iAuthService.listAuthsByGroupId(parentId);
            }
            AuthEntity auth = auths.stream().filter(a -> AuthTypeEnum.User.getValue().equals(a.getAuthType()) && a.getAuthId().equals(authApproveEntity.getCreatorId())).findFirst().orElse(null);
            //AuthEntity auth = iAuthService.get(authApproveEntity.getSourceId(), authApproveEntity.getSourceType(), authApproveEntity.getCreatorId(), AuthTypeEnum.User.getValue());
            updateReq = new UpdateReq();
            updateReq.setId(auth == null ? null : auth.getId());
            updateReq.setLevel(authApproveEntity.getAuthLevel());
            updateReq.setSourceType(authApproveEntity.getSourceType());
            updateReq.setSourceId(Long.valueOf(authApproveEntity.getSourceId()));
            updateReq.setAuthId(authApproveEntity.getCreatorId());
            updateReq.setAuthType(AuthTypeEnum.User.getValue());
            updateReq.setTenantId(authApproveEntity.getTenantId());
        }
        authApproveEntity.setState(req.getState());
        iAuthApproveService.update(authApproveEntity);
        return updateReq;
    }

}
