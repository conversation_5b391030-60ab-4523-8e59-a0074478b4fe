package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.server.biz.LogRecordMenuConverter;
import com.linker.fusion.knowledgecenter.server.biz.OperationLogBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.operationlog.LogRecordPageReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.operationlog.LogRecordPageResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.linker.core.auth.enums.InterfaceType.TOC;

/**
 * <AUTHOR>
 */
@Api(tags = "操作日志管理")
@RestController
@RequestMapping("operation/log")
public class OperationLogController {
    @Autowired
    OperationLogBiz operationLogBiz;

    @PostMapping("page")
    @ApiOperation(value = "操作日志分页列表", tags = "v1.6.6")
    @FunctionPoint(interfaceType = TOC)
    public BasePaginResp<LogRecordPageResp> logRecordPage(@RequestBody LogRecordPageReq req) {
        return operationLogBiz.logRecordPage(req);

    }

    @PostMapping("type/list")
    @ApiOperation(value = "操作日志类型和子类型枚举列表", tags = "v1.6.6")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<LogRecordMenuConverter.LogRecordTypeMenuResp>> operationTypeList() {
        List<LogRecordMenuConverter.LogRecordTypeMenuResp> logRecordTypeMenuResp = LogRecordMenuConverter.convertToMenuStructure();
        return new BaseResp<>(logRecordTypeMenuResp);
    }
}
