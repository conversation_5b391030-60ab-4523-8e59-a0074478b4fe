package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.linker.fusion.knowledgecenter.infrastructure.model.AuthActionModel;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.BaseEntityResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class FaqInfoEntityResp extends BaseEntityResp {

    @ApiModelProperty(value = "编号")
    private Long id;

    @ApiModelProperty(value = "类目ID")
    private Long groupId;

    @ApiModelProperty(value = "标准问题")
    private String standardQuestion;

    @ApiModelProperty(value = "相似问题")
    private List<String> similarQuestions;

    @ApiModelProperty(value = "是否开启大模型回答润色")
    private Boolean isLlmEnhanced;

    @ApiModelProperty(value = "回答类型 0-纯文本 1-富文本")
    private Integer answerType;

    @ApiModelProperty(value = "回答列表")
    private List<String> answers;

    @ApiModelProperty(value = "是否启用, true-启用 false-禁用")
    private Boolean enable;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("问答对路径")
    private String groupNamePath;

    private List<String> authCodes;
    @ApiModelProperty("授权信息")
    private AuthActionModel authActions;

    @ApiModelProperty("权限等级")
    private Integer authLevel;
}
