package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.alibaba.fastjson.JSONObject;
import com.linker.fusion.knowledgecenter.infrastructure.es.ChunkEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ChunkItemResp {

    @ApiModelProperty("es唯一id,用于更新")
    private String uid;
    @ApiModelProperty("分片id,用于关联")
    private String chunkId;
    @ApiModelProperty("分段id")
    private String segmentId;
    @ApiModelProperty("文档id")
    private String docId;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("图片地址")
    private String url;
    @ApiModelProperty("文档内容")
    private String content;
    @ApiModelProperty("文件路径")
    private String path;
    @ApiModelProperty("附件数据")
    private JSONObject meta;
    @ApiModelProperty("分片类型")
    private String srcType;
    @ApiModelProperty("页码")
    private Integer page;
    @ApiModelProperty("排序")
    private Double sort;
    @ApiModelProperty("位置")
    private String position;

    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("处理状态 0学习中 1成功 2失败")
    private Integer status;

    public ChunkItemResp() {

    }

    public ChunkItemResp(ChunkEntity entity) {
        this.uid = entity.getUid();
        this.chunkId = entity.getChunkId();
        this.segmentId = entity.getSegmentId();
        this.docId = entity.getDocId();
        this.title = entity.getTitle();
        this.url = entity.getUrl();
        this.content = entity.getContent();
        this.meta = entity.getMeta();
        this.srcType = entity.getSrcType();
        this.page = entity.getPage();
        this.sort = entity.getSort();
        this.position = entity.getPosition();
        this.createTime = entity.getCreateTime();
        this.status = entity.getStatus();
    }
}
