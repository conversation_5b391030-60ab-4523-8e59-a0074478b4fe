package com.linker.fusion.knowledgecenter.server.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.web.annotation.LogFilter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.Duration;

@RestController
@RequestMapping("callback")
@Api(tags = "回调")
@Slf4j
@LogFilter
public class CallbackController {
    @Autowired
    RedissonClient redissonClient;
    @PostMapping("pdf")
    @ApiOperation("pdf解析回调")
    public BaseResp<Void> handlePdfCallback(@RequestBody JSONObject req) throws InterruptedException {
        String taskId = req.getJSONObject("body").getString("taskId");
        log.info("====任务{}，状态：{}====", taskId, req.getJSONObject("body").getString("task_status"));
        if (StringUtils.isNotBlank(taskId)) {
            RBlockingQueue<Object> blockingQueue = redissonClient.getBlockingQueue(taskId);
            blockingQueue.expire(Duration.ofMinutes(60));
            blockingQueue.put(JSON.toJSONString(req));
        }
        return new BaseResp<>();
    }

    @ApiOperation("资源代理")
    @GetMapping("proxy")
    public void proxy(@RequestParam("url") @Valid String urls, HttpServletResponse response) throws Exception {
        URL url = new URL(urls);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        String contentType = connection.getContentType();
        InputStream inputStream = connection.getInputStream();
        BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }
        byte[] imageBytes = outputStream.toByteArray();
        response.setContentType(contentType);
        response.getOutputStream().write(imageBytes);
        bufferedInputStream.close();
        outputStream.close();
    }
}