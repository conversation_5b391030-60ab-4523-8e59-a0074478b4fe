package com.linker.fusion.knowledgecenter.server.convert;

import com.linker.fusion.knowledgecenter.infrastructure.entity.CustomEntity;
import com.linker.fusion.knowledgecenter.server.dto.req.custom.CustomAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.custom.CustomPageReq;
import com.linker.fusion.knowledgecenter.server.dto.req.custom.CustomUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.CustomFillInResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.CustomInfoEntityResp;
import com.linker.fusion.knowledgecenter.service.domain.custom.model.CustomPageDTO;
import com.linker.fusion.knowledgecenter.service.domain.custom.model.CustomSaveDTO;
import com.linker.user.api.dto.UserInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CustomConvert {

    CustomConvert INSTANCE = Mappers.getMapper(CustomConvert.class);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId"),
            @Mapping(source = "userInfo.user.userCode", target = "userId"),
    })
    CustomSaveDTO toDto(CustomAddReq req, UserInfo userInfo);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId"),
            @Mapping(source = "userInfo.user.userCode", target = "userId"),
    })
    CustomSaveDTO toDto(CustomUpdateReq req, UserInfo userInfo);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId"),
    })
    CustomPageDTO toDto(CustomPageReq req, UserInfo userInfo);

    List<CustomInfoEntityResp> toResp(List<CustomEntity> customEntityList);

    List<CustomFillInResp> toFillInfo(List<CustomEntity> customEntityList);
}
