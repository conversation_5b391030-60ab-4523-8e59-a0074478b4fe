//package com.linker.fusion.knowledgecenter.server.biz.event;
//
//import com.alibaba.fastjson.JSONObject;
//import com.linker.conductor.client.WorkflowStatusListener;
//import com.linker.fusion.knowledgecenter.infrastructure.enums.GlobalVariable;
//import com.linker.fusion.knowledgecenter.service.domain.resource.TableService;
//import com.netflix.conductor.common.run.Workflow;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//
//@Slf4j
//@Service
//public class ImportResourceWorkflowStatusListener implements WorkflowStatusListener {
//
//    @Resource
//    private TableService tableService;
//
//    @Override
//    public void onWorkflowCompleted(Workflow workflow) {
//        log.info("========={}-最终结果回调==========", workflow.getWorkflowId());
//        log.info(JSONObject.toJSONString(workflow.getInput()));
//        log.info(" workflow.getStatus() == " + workflow.getStatus() + "");
//        tableService.updateWorkFlowId(
//                // ((JSONObject) workflow.getInput().get("resource")).getLong("id"),
//                0L,
//                workflow.getWorkflowId(),
//                workflow.getStatus().equals(Workflow.WorkflowStatus.COMPLETED) ? 2 : 3);
//    }
//
//    @Override
//    public String getWorkflowName() {
//        return GlobalVariable.ImportWorkName;
//    }
//}
