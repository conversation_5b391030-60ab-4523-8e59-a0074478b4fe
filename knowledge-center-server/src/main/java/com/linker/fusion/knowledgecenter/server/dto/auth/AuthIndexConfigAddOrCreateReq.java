package com.linker.fusion.knowledgecenter.server.dto.auth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025年09月18日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class AuthIndexConfigAddOrCreateReq implements Serializable {

    /**
     * 索引是否开启定时任务 1-开启 0-关闭
     */
    @ApiModelProperty("索引是否开启定时任务 1-开启 0-关闭")
    @NotNull(message = "索引是否开启定时任务不能为空")
    private Boolean indexTimeScheduleOpen;

    /**
     * 索引定时任务配置:开始时间
     */
    @ApiModelProperty("索引定时任务配置:开始时间")
    private String indexTimeStartTime;

    /**
     * 索引定时任务配置:开始时间
     */
    @ApiModelProperty("索引定时任务配置:结束时间")
    private String indexTimeEndTime;
}
