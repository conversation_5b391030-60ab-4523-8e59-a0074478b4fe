package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.linker.fusion.knowledgecenter.infrastructure.dto.query.QbAnswerDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;

/**
 * @Author: YangQiyan
 * @Description: 类说明
 * @Date: Created in 2025/4/2
 */
@ApiModel(description = ": 类说明")
@Data
public class PaperInfoResp {

    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pagerGenerateTime;

    /**
     * 试卷状态, 0:回答中, 1:完成
     */
    @ApiModelProperty("试卷状态, 0:回答中, 1:完成")
    private Integer paperStatus;

    /**
     * 当前回答到试卷题目标识
     */
    @ApiModelProperty("当前回答到试卷题目标识")
    private Integer currentQuestionNumber;

    /**
     * 当前试卷进度
     */
    @ApiModelProperty("当前试卷进度")
    private String paperSchedule;

    /**
     * 所有试卷内容
     */
    @ApiModelProperty("所有试卷内容")
    private LinkedList<PaperContentDTO> paperContentList;
    /**
     * 试卷回答历史记录
     */
    @ApiModelProperty("试卷回答历史记录")
    private LinkedList<PaperAnswerRecordDTO> paperAnswerRecordList;

    @ApiModel
    @Data
    @Accessors(chain = true)
    public static class PaperContentDTO {
        /**
         * 问题序号
         */
        @ApiModelProperty("问题序号")
        private Integer questionNumber;
        /**
         * 问题内容
         */
        @ApiModelProperty("问题内容")
        private String questionContent;
        /**
         * 问题类型, CHOICE("选择题"),MULTIPLE_CHOICE("多选题"),TRUE_FALSE("判断题"),FILL_IN_BLANK("填空题");
         * @see com.linker.fusion.knowledgecenter.infrastructure.enums.QuestionTypeEnum
         */
        private String questionType;
        /**
         * 问题图片
         */
        private List<String> questionImages;
        /**
         * 问题答案选型
         * key选型, value选项内容，images 图片列表
         */
        @ApiModelProperty("问题答案选型 key选型, value选项内容，images 图片列表")
        private LinkedList<QbAnswerDTO> answerList;
    }

    @ApiModel
    @Data
    @Accessors(chain = true)
    public static class PaperAnswerRecordDTO {
        /**
         * 问题序号
         */
        @ApiModelProperty("问题序号")
        private Integer questionNumber;
        /**
         * 问题内容
         */
        @ApiModelProperty("问题内容")
        private String questionContent;
        /**
         * 问题图片
         */
        private List<String> questionImages;
        /**
         * 问题类型, CHOICE:选题, TRUE_FALSE:判断题
         */
        @ApiModelProperty("问题类型, CHOICE:选题, TRUE_FALSE:判断题")
        /**
         * 问题类型, CHOICE("选择题"),MULTIPLE_CHOICE("多选题"),TRUE_FALSE("判断题"),FILL_IN_BLANK("填空题");
         * @see com.linker.fusion.knowledgecenter.infrastructure.enums.QuestionTypeEnum
         */
        private String questionType;
        /**
         * 问题答案选型
         * key选型, value选项内容
         */
        @ApiModelProperty("问题答案选型 key选型, value选项内容")
        private LinkedList<QbAnswerDTO> answerList;
        /**
         * 填写答案 A/B/C/D/0/1
         */
        @ApiModelProperty("填写答案 A/B/C/D/0/1")
        private Object fillAnswer;
    }

}
