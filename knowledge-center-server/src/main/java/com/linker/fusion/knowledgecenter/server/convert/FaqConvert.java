package com.linker.fusion.knowledgecenter.server.convert;

import com.linker.fusion.knowledgecenter.infrastructure.entity.FaqInfoEntity;
import com.linker.fusion.knowledgecenter.server.dto.req.faq.FaqCreateReq;
import com.linker.fusion.knowledgecenter.server.dto.req.faq.FaqFeedbackReq;
import com.linker.fusion.knowledgecenter.server.dto.req.faq.FaqPageReq;
import com.linker.fusion.knowledgecenter.server.dto.req.faq.FaqUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.FaqInfoEntityResp;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqPageDTO;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqSaveDTO;
import com.linker.user.api.dto.UserInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface FaqConvert {

    FaqConvert INSTANCE = Mappers.getMapper(FaqConvert.class);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId"),
            @Mapping(source = "userInfo.user.userCode", target = "userId"),
    })
    FaqSaveDTO toDto(FaqCreateReq req, UserInfo userInfo);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId"),
            @Mapping(source = "userInfo.user.userCode", target = "userId"),
    })
    FaqSaveDTO toDto(FaqFeedbackReq req, UserInfo userInfo);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId"),
            @Mapping(source = "userInfo.user.userCode", target = "userId"),
    })
    FaqSaveDTO toDto(FaqUpdateReq req, UserInfo userInfo);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId"),
    })
    FaqPageDTO toDto(FaqPageReq req, UserInfo userInfo);

    @Mappings({
        @Mapping(source = "question", target = "standardQuestion")
    })
    FaqInfoEntityResp toDto(FaqInfoEntity entity);

}
