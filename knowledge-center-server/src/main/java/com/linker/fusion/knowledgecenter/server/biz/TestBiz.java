package com.linker.fusion.knowledgecenter.server.biz;

import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.server.dto.req.XXXReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.XXXResp;
import org.springframework.stereotype.Component;

@Component
public class TestBiz {
    public BaseResp<XXXResp> view(XXXReq testReq) {
        return null;
    }

    public BasePaginResp<XXXResp> viewPages(XXXReq testPageReq) {
        return null;
    }
}
