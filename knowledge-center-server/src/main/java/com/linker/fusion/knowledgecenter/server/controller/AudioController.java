package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthNodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OperatorTypeEnum;
import com.linker.fusion.knowledgecenter.server.biz.AudioBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.file.AsrListReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.audio.AudioChunkAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.audio.AudioChunkDeleteReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.audio.AudioChunkListReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.audio.AudioChunkUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.AsrListResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.AudioChunkResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.MenuAndResNodeAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.linker.core.auth.enums.InterfaceType.TOC;

@Api(tags = "音频管理")
@RestController
@RequestMapping("file/audio")
public class AudioController {
    @Resource
    AudioBiz audioBiz;
    @PostMapping("chunk/list")
    @ApiOperation(value = "分块列表", tags = "v1.5.0")
    @FunctionPoint(interfaceType = TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<List<AudioChunkResp>> chunkList(@RequestBody @Validated AudioChunkListReq req) {
        return audioBiz.chunkList(req);
    }

    @PostMapping("chunk/update")
    @ApiOperation(value = "分块更新", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<Void> updateChunk(@RequestBody @Valid AudioChunkUpdateReq req) {
        audioBiz.updateChunk(req);
        return new BaseResp<>();
    }

    @PostMapping("chunk/add")
    @ApiOperation(value = "分块添加", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, segmentIds = "{#req.segmentId}")
    public BaseResp<Void> addChunk(@RequestBody @Valid AudioChunkAddReq req) {
        audioBiz.addChunk(req);
        return new BaseResp<>();
    }

    @PostMapping("chunk/delete")
    @ApiOperation(value = "分块删除", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MAINTENANCE, node = AuthNodeEnum.RESOURCE_MAINTENANCE, chunkIds = "{#req.chunkIds}")
    public BaseResp<Void> deleteChunk(@RequestBody @Validated  AudioChunkDeleteReq req) {
        audioBiz.deleteChunk(req);
        return new BaseResp<>();
    }

    @PostMapping("asr/list")
    @ApiOperation(value = "语音列表", tags = "v1.5.0")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<List<AsrListResp>> asrList(@RequestBody @Validated  AsrListReq req) {
        return audioBiz.list(req);
    }
}
