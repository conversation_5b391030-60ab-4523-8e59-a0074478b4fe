package com.linker.fusion.knowledgecenter.server.biz;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.template.QuickConfig;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.base.exception.ServiceException;
import com.linker.core.cos.model.req.UploadReq;
import com.linker.core.cos.model.resp.UploadResp;
import com.linker.core.cos.provider.CosHelper;
import com.linker.core.cos.provider.IProvider;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.AasWorkflowClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IUserCenterService;
import com.linker.fusion.knowledgecenter.infrastructure.config.AasWorkflowProperties;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.*;
import com.linker.fusion.knowledgecenter.infrastructure.model.AuthActionModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.AuthAssModel;
import com.linker.fusion.knowledgecenter.server.convert.TableConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.core.BatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.EnableReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.IdBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.DocPageReq;
import com.linker.fusion.knowledgecenter.server.dto.req.table.TableAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.table.TableUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.TableInfoEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.TableUploadResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.KnowledgeGroupParentResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthApproveService;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthLevelService;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthService;
import com.linker.fusion.knowledgecenter.service.domain.common.ExcelUtils;
import com.linker.fusion.knowledgecenter.service.domain.common.IUtilService;
import com.linker.fusion.knowledgecenter.service.domain.common.ReadExcelDTO;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import com.linker.fusion.knowledgecenter.service.domain.group.model.SearchPreChekDto;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.TableImportInput;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.TableSearchInput;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.TableService;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TableBatchSetDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TableEmbeddingData;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TableSaveDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TableSlicePageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.*;

@Slf4j
@Component
public class TableBiz extends BaseBiz {

    @Resource
    private KnowledgeGroupService knowledgeGroupService;
    @Resource
    private KnowledgeGroupFactory knowledgeGroupFactory;
    @Resource
    private TableService tableService;

    @Autowired
    private AasWorkflowProperties aasWorkflowProperties;

    @Resource
    private AasWorkflowClient aaasWorkflowClient;

    @Resource
    private CosHelper cosHelper;

    @Autowired
    private QuickConfig cacheConfig;

    @Autowired
    private CacheManager cacheManager;

    @Resource
    private IUtilService iUtilService;
    @Resource
    private IResourceService iResourceService;
    @Resource
    private IAuthService iAuthService;
    @Resource
    private IAuthLevelService iAuthLevelService;
    @Autowired
    private IUserCenterService iUserCenterService;
    @Resource
    private IAuthApproveService iAuthApproveService;

    @Transactional(rollbackFor = Exception.class)
    public void add(TableAddReq tableAddReq) throws IOException {
        tableAddReq.joinUrl();
        TableSaveDTO dto = new TableSaveDTO()
                .setGroupId(tableAddReq.getGroupId())
                .setUrl(tableAddReq.getUrl())
                .setTitle(tableAddReq.getTitle().trim())
                .setDescription(tableAddReq.getDescription() != null ? tableAddReq.getDescription().trim() : "")
                .setSize(tableAddReq.getSize())
                .setCount(tableAddReq.getCount())
                .setWorkFlowId("")
                .setUserId(getUserCode())
                .setTenantId(getTenantId());
        issue(tableService.create(dto));
    }

    public TableUploadResp upload(byte[] bytes, String suffix) throws IOException {
        TableUploadResp resp = new TableUploadResp();
        IProvider selectProvider = cosHelper.getSelectProvider();
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            // TODO 检测文件是否符合、并返回有效行数
            ReadExcelDTO readExcelDTO = readExcel(inputStream, suffix);
            long checkCount = readExcelDTO.getRows().stream().count();
            if (checkCount > 0) {
                resp.setCount(checkCount);
                // 上传文件至minio
                UploadReq uploadReq = new UploadReq(bytes);
                uploadReq.bucketName(iUtilService.getTempBucket().getName());
                uploadReq.fileKey(DateUtil.format(DateUtil.date(), "yyyyMMdd") + "/" + UUID.randomUUID() + "." + suffix);
                UploadResp uploadResp = selectProvider.upload(uploadReq);
                resp.setUrl(uploadResp.getFileUrl());
                resp.setSize(uploadResp.getFileSize());
                Cache<String, ReadExcelDTO> featruesCache = cacheManager.getOrCreateCache(cacheConfig);
                featruesCache.put(resp.getUrl(), readExcelDTO, 10, TimeUnit.MINUTES);
            } else {
                throw new ServiceException(TABLE_EXIST_ONE);
            }
        }
        return resp;
    }

    public String issue(KnowledgeResourceEntity entity) {
        if (Objects.isNull(entity) || Objects.isNull(entity.getId())) {
            throw new RuntimeException("资源信息不能为空");
        }
        String workId = handResource(entity);
        tableService.updateWorkFlowId(entity.getId(), workId, 1);
        return workId;
    }

    /**
     * 下发工作流
     */
    private String handResource(KnowledgeResourceEntity entity) {
        // 工作流id
        String workflowId = aasWorkflowProperties.getWorkflowId().getImportTable();

        // 工作流入参 setCreateTime为null，因为无法序列化
        Map<String, Object> inputs = new HashedMap<>();
        inputs.put("resource", JSONObject.toJSON(entity));

        // 异步发送工作流
        WorkflowAsyncRunResp runResp = aaasWorkflowClient.runAsync(
                new WorkflowAsyncRunReq()
                        .setId(workflowId)
                        .setInput(inputs)
                        .setCallback(aasWorkflowProperties.getWorkflowId().getImportCallback() + "/" + entity.getDocId())
        );
        if (runResp == null || !Objects.equals("0", runResp.getCode())) {
            log.error("下发工作流任务失败{}", JSON.toJSONString(runResp));
            throw new RuntimeException("下发工作流任务失败");
        }
        String workflowInstanceId = runResp.getData();
        log.info("文件导入下发工作流任务成功：docId:{}, workflowInstanceId:{}", entity.getDocId(), workflowInstanceId);
        return workflowInstanceId;
    }

    public ReadExcelDTO readExcel(InputStream inputStream, String suffix) throws IOException {
        ReadExcelDTO readExcelDTO = ExcelUtils.readExcelFromUrl(inputStream, 0, suffix);
        if (CollectionUtils.isEmpty(readExcelDTO.getHeader())) {
            throw new ServiceException(TABLE_HEAD_NOT_NULL);
        }
        if (readExcelDTO.getHeader().stream().count() > 100) {
            throw new ServiceException(TABLE_HEAD_MAX_100);
        }
        readExcelDTO.getHeader().stream().filter(x -> StringUtils.isBlank(x)).findAny().ifPresent(x -> {
            throw new ServiceException(TABLE_HEAD_NULL_COL);
        });
        return readExcelDTO;
    }

    public void update(TableUpdateReq req) {
        TableSaveDTO dto = new TableSaveDTO()
                .setId(req.getId())
                .setTitle(req.getTitle().trim())
                .setTenantId(getTenantId())
                .setUserId(getUserCode())
                .setDescription(req.getDescription() != null ? req.getDescription().trim() : "");
        tableService.update(dto);
    }

    public void relearn(BatchBaseReq req) {
        List<KnowledgeResourceEntity> resourceList = iResourceService.listByIds(req.getIds());
        resourceList.forEach(this::handResource);
    }

    public BasePaginResp<TableInfoEntityResp> page(DocPageReq req) {
        SearchPreChekDto dto = knowledgeGroupFactory.searchPreCheck(req);
        Page<KnowledgeResourceEntity> ret = iResourceService.page(req.getPage(), req.getPageSize(), dto.getTenantId(), req.getTypes(), dto.getAuthedGroupIds(), req.getEnable(), req.getHandleStatus(), dto.getNotIds(), null, req.getKeyword(), req.getOrder());
        List<KnowledgeGroupEntity> groupList = knowledgeGroupService.listByIds(ret.getRecords().stream().map(KnowledgeResourceEntity::getGroupId).collect(Collectors.toList()));
        List<Long> applyIds = iAuthApproveService.listAllIngIds(getTenantId(), getUserCode(), SourceTypeEnum.File.getValue());
        List<TableInfoEntityResp> respList = ret.getRecords().stream().map(this::toTableInfoResp).collect(Collectors.toList());
        respList.forEach(resp -> {
            resp.setAuthCodes(dto.getAuthedCodes(resp.getId(), resp.getGroupId()));
            resp.setAuthLevel(dto.getAuthedLevel(resp.getId(), resp.getGroupId()));
            groupList.stream().filter(g -> g.getId().equals(resp.getGroupId())).findFirst().ifPresent(g -> resp.setImportantLevel(g.getImportantLevel()));
            resp.setAuthActions(AuthActionModel.create(AuthLevelEnum.Manage.getValue(), true, false));
            resp.setApplying(applyIds.contains(resp.getId()));
        });

        return new BasePaginResp<>(ret.getTotal(), respList);
    }

    public TableInfoEntityResp toTableInfoResp(KnowledgeResourceEntity entity) {
        TableInfoEntityResp tableInfoResp = TableConvert.INSTANCE.entityToResp(entity);
        Integer groupType = FileTypeEnum.TABLE.getType().equals(entity.getType()) ? KnowledgeTypeEnum.TABLE.getType() : KnowledgeTypeEnum.FILE.getType();
        List<KnowledgeGroupEntity> parents = knowledgeGroupService.getParents(entity.getGroupId());
        if (CollectionUtils.isNotEmpty(parents)) {
            tableInfoResp.setGroupParentResp(new KnowledgeGroupParentResp(tableInfoResp.getId(), groupType, parents.get(0).getVisibleType(), parents));
        }
        return tableInfoResp;
    }

    public void enable(EnableReq req) {
        tableService.enable(getTenantId(), req.getIds(), req.getEnable());
    }

    public void delete(BatchBaseReq req) {
        List<KnowledgeResourceEntity> list = iResourceService.listByIds(req.getIds());
        list.forEach(f -> iUtilService.deleteFile(f.getUrl()));
        tableService.batchSet(new TableBatchSetDTO().setTenantId(getTenantId()).setIds(req.getIds()).setDeleted(true));
    }


    public void download(IdBaseReq<Long> req, HttpServletResponse response) {
        KnowledgeResourceEntity entityById = tableService.getEntityById(req.getId());
        if (entityById != null) {
            TableSlicePageDTO dto = new TableSlicePageDTO();
            dto.setTableId(req.getId());
            dto.setPage(1);
            dto.setPageSize(entityById.getCount());
            BasePaginResp<TableEmbeddingData> data = tableService.slicePage(getTenantId(), dto);
            if (CollectionUtils.isNotEmpty(data.getData())) {
                TableEmbeddingData tableEmbeddingData = data.getData().get(0);
                List<List<String>> head = new ArrayList<>();
                List<List<Object>> dataList = new ArrayList<>();
                data.getData().forEach(x -> {
                    dataList.add(x.getRow());
                });
                tableEmbeddingData.getHeader().forEach(y -> {
                    List<String> col = new ArrayList<>();
                    col.add(y);
                    head.add(col);
                });
                ExcelUtils.export(entityById.getTitle() + "." + entityById.getSuffix(), response, head, dataList);
            } else {
                throw new RuntimeException("表格未入库");
            }
        } else {
            throw new RuntimeException("表格不存在");
        }
    }

    public List<TableInfoEntityResp> details(BatchBaseReq req) {
        Boolean isManager = iUserCenterService.isManager(getToken(), getTenantId(), getUserCode());
        List<KnowledgeResourceEntity> tableInfoEntityResps = new ArrayList<>();
        List<KnowledgeResourceEntity> entityByIds = tableService.getEntityByIds(req.getIds());
        List<KnowledgeResourceEntity> entityByDocIds = tableService.getEntityByDocIds(req.getDocIds());
        tableInfoEntityResps.addAll(entityByIds);
        tableInfoEntityResps.addAll(entityByDocIds);
        List<TableInfoEntityResp> respList = new ArrayList<>();
        tableInfoEntityResps.forEach(resource -> {
            TableInfoEntityResp tableInfoResp = TableConvert.INSTANCE.entityToResp(resource);
            Integer groupType = FileTypeEnum.TABLE.getType().equals(resource.getType()) ? KnowledgeTypeEnum.TABLE.getType() : KnowledgeTypeEnum.FILE.getType();
            if (resource.getGroupId().equals(-1L)) {
                respList.add(tableInfoResp);
                return;
            }
            List<KnowledgeGroupEntity> groups = knowledgeGroupService.getParents(resource.getGroupId());
            KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(resource.getGroupId());
            groups.add(group);
            tableInfoResp.setGroupParentResp(new KnowledgeGroupParentResp(tableInfoResp.getId(), group.getType(), groupType, groups));
            tableInfoResp.setVisibleType(group.getVisibleType());
            tableInfoResp.setImportantLevel(group.getImportantLevel());
            if (CollectionUtils.isNotEmpty(groups)) {

                if (ResourceVisibleTypeEnum.PERSONAL.getValue().equals(groups.get(0).getVisibleType())) {
                    tableInfoResp.setAuthCodes(Arrays.stream(AuthNodeEnum.values()).map(Enum::toString).collect(Collectors.toList()));
                } else {
                    AuthAssModel authAssModel = new AuthAssModel(getTenantId(), getUserCode(), new ArrayList<>(), resource.getId().toString(), SourceTypeEnum.File);
                    authAssModel.setGroup(groups.get(groups.size() - 1));
                    authAssModel.setIsManager(isManager);
                    authAssModel.setParents(groups);
                    authAssModel.setRoot(groups.get(0));
                    iAuthService.getAuthNew(authAssModel);
                    if (authAssModel.getAuth().getAuthLevel() > 0) {
                        tableInfoResp.setAuthCodes(authAssModel.getAuthedCodes(iAuthLevelService.mapCodes(getTenantId()), groups.get(0).getIsSync() ? AuthNodeEnum.syncCodes() : null));
                    }
                    tableInfoResp.setAuthLevel(authAssModel.getAuth().getAuthLevel());
                }
            }
            respList.add(tableInfoResp);
        });

        return respList;
    }

    public void ibaseAdd(Long id) {
        TableImportInput tableImportInput = new TableImportInput();
        tableImportInput.setId(id);
        tableService.ibaseAdd("", tableImportInput);
    }

    public Object ibaseTextSearch(TableSearchInput req) {
        return tableService.textSearchIbase(req);
    }

}
