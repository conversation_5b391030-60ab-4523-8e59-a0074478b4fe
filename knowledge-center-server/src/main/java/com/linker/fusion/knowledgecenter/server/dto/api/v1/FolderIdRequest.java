package com.linker.fusion.knowledgecenter.server.dto.api.v1;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Request
 *
 * <AUTHOR>
 */
@Data
public class FolderIdRequest {
    @JsonProperty("folder_id")
    @NotNull
    @ApiModelProperty(value = "文件夹id", required = true)
    private String folderId;
}