package com.linker.fusion.knowledgecenter.server.dto.resp;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 词组返回
 **/
@Data
public class WordsPageResp  {

    /**
     * 词组id
     */
    private Long wordsId;

    /**
     *词组名称
     */
    private String name;

    /**
     * 有效状态 1有效 0无效
     */
    private Integer state;

    /**
     * 专业词的同义词
     */
    private List<String> proSynonyms;

    private Long groupId;

}
