package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthNodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OperatorTypeEnum;
import com.linker.fusion.knowledgecenter.server.biz.FaqBiz;
import com.linker.fusion.knowledgecenter.server.dto.faq.FaqInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.req.core.BatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.EnableReq;
import com.linker.fusion.knowledgecenter.server.dto.req.faq.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.FaqInfoEntityResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.GroupNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.auth.MenuAndResNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqSearchDTO;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Api(tags = "FAQ管理")
@RestController
@RequestMapping("/faq")
public class FaqController {

    @Resource
    private FaqBiz faqBiz;
    @Autowired
    private KnowledgeGroupFactory knowledgeGroupFactory;

    @PostMapping("/create")
    @ApiOperation(value = "创建")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FAQ, operatorType = OperatorTypeEnum.CREATE, node = AuthNodeEnum.FAQ_CREATE, groupIds = "{#req.groupId}")
    public BaseResp<Long> create(@RequestBody @Validated FaqCreateReq req) {
        return new BaseResp<>(faqBiz.create(req));
    }

    @Deprecated
    @PostMapping("/feedback")
    @ApiOperation(value = "反馈学习")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> feedback(@RequestBody @Validated FaqFeedbackReq req) {
//        faqBiz.feedback(req);
        // 1.4.4反馈学习结果本期不再同步到FAQ
        return new BaseResp<>();
    }

    @PostMapping("/generate-similar-questions")
    @ApiOperation(value = "生成相似问题")
    public BaseResp<List<String>> generateSimilarQuestions(@RequestBody @Validated FaqGenerateSimilarQuestionsReq req) {
        return faqBiz.generateSimilarQuestions(req);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<FaqInfoEntityResp> page(@RequestBody FaqPageReq req) {
        return faqBiz.page(req);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FAQ, operatorType = OperatorTypeEnum.EDIT, node = AuthNodeEnum.FAQ_EDIT, ids = "{#req.id}")
    public BaseResp<Void> update(@RequestBody @Validated FaqUpdateReq req) {
        return faqBiz.update(req);
    }

    @PostMapping("/enable")
    @ApiOperation(value = "启用/禁用")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FAQ, operatorType = OperatorTypeEnum.ENABLE, node = AuthNodeEnum.FAQ_ENABLE, ids = "{#req.ids}")
    public BaseResp<Void> enable(@RequestBody @Validated EnableReq req) {
        return faqBiz.enable(req);
    }

    @PostMapping("/move")
    @ApiOperation(value = "移动")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FAQ, operatorType = OperatorTypeEnum.MOVE, node = AuthNodeEnum.FAQ_MOVE, ids = "{#req.ids}")
    @GroupNodeAuth(node = AuthNodeEnum.FAQ_CREATE, groupIds = "{#req.groupId}")
    public BaseResp<Void> move(@RequestBody @Validated FaqMoveReq req) {
        return faqBiz.move(req);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FAQ, operatorType = OperatorTypeEnum.DELETE, node = AuthNodeEnum.FAQ_DELETE, ids = "{#req.ids}")
    public BaseResp<Void> delete(@RequestBody @Validated BatchBaseReq req) {
        return faqBiz.delete(req);
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出", produces = "application/octet-stream")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    // @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FAQ, operatorType = OperatorTypeEnum.EXPORT, node = AuthNodeEnum.FAQ_EXPORT, ids = "{#req.ids}")
    public void export(@RequestBody @Validated BatchBaseReq req, HttpServletResponse response) {
        knowledgeGroupFactory.checkMenuAndResNodeAuth(req.getIds(), KnowledgeTypeEnum.FAQ, OperatorTypeEnum.EXPORT, AuthNodeEnum.FAQ_EXPORT);
        faqBiz.export(req, response);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FAQ, operatorType = OperatorTypeEnum.IMPORT, node = AuthNodeEnum.FAQ_CREATE, groupIds = "{#groupId}")
    public BaseResp<List<String>> importFaq(@RequestPart(value = "file") MultipartFile file,
                                            @RequestParam(value = "groupId") Long groupId) throws IOException {
        return faqBiz.importFaq(file, groupId);
    }

    /**
     * FAQ命中测试
     */
    @PostMapping("/search")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<FaqSearchDTO> search(@RequestBody @Validated FaqSearchReq req) {

        return faqBiz.search(req);
    }

    @PostMapping("/searchQuestion")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<String>> searchQuestion(@RequestBody @Validated FaqSearchReq req) {
        return faqBiz.searchQuestion(req);
    }

    @PostMapping("/fix")
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    public BaseResp<List<String>> fix() {
        faqBiz.fixFaq();
        return new BaseResp<>();
    }

    @PostMapping("/ai/generate")
    @ApiOperation(value = "ai生成问答对", tags = "v1.6.2")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.FILE, operatorType = OperatorTypeEnum.MANAGER, node = AuthNodeEnum.RESOURCE_PREVIEW, resIds = "{#req.docId}")
    @GroupNodeAuth(operatorType = OperatorTypeEnum.AI_GENERATE, node = AuthNodeEnum.FAQ_CREATE, groupIds = "{#req.groupId}")
    public BaseResp<TaskEntity> aiGenerateQA(@RequestBody AIGenerateQAReq req) {
        return new BaseResp<>(faqBiz.aiGenerateQA(req));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "问答对详情", tags = "v1.7.5")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<FaqInfoResp> detail(Long id){
        return new BaseResp<>(faqBiz.detail(id));
    }


}
