package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.PersonInfoDTO;
import com.linker.fusion.knowledgecenter.server.biz.RenDaSearchGBaseBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.npc.RenDaSearchHistoryClearReq;
import com.linker.fusion.knowledgecenter.server.dto.req.npc.RenDaSearchReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.npc.RenDaPersonStatsResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.npc.RenDaSearchSuggestionResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.npc.NpcFileInfoResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("search/npc")
@Api(tags = "人大视频搜索（定制）")
public class RenDaSearchController {

    @Resource
    private RenDaSearchGBaseBiz biz;

    @PostMapping(value = {"/video/page", "/page"})
    @ApiOperation(value = "搜索")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<NpcFileInfoResp> searchVideo(@RequestBody @Valid RenDaSearchReq req) {
        return biz.searchVideo(req);
    }

    @PostMapping("person/related")
    @ApiOperation(value = "相关人物", notes = "支持按会议、内容")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<PersonInfoDTO>> personRelated(@RequestBody @Valid RenDaSearchReq req) {
        return new BaseResp<>(biz.personRelated(req));
    }

    @PostMapping("person/stats")
    @ApiOperation(value = "人物统计", notes = "支持按人物")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<RenDaPersonStatsResp> personStats(@RequestBody @Valid RenDaSearchReq req) {
        return new BaseResp<>(biz.personStats(req));
    }

    @GetMapping("suggestion")
    @ApiOperation(value = "推荐搜索")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<RenDaSearchSuggestionResp> suggestion(
            @ApiParam(name = "fileType", value = "文件类型 1-文档 2-图片 3-视频")
            @RequestParam(value = "fileType", defaultValue = "3", required = false) Integer fileType,
            @ApiParam(name = "searchType", value = "按什么搜索 1-会议（标题） 2-人员（人脸库） 3-内容（ASR）")
            @RequestParam(value = "searchType") Integer searchType) {
        return new BaseResp<>(biz.suggestion(fileType, searchType));
    }

    @PostMapping("suggestion/clear")
    @ApiOperation(value = "清除个人搜索记录")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> suggestionClear(@RequestBody @Valid RenDaSearchHistoryClearReq req) {
        biz.suggestionClear(req.getFileType(), req.getSearchType());
        return new BaseResp<>();
    }

}
