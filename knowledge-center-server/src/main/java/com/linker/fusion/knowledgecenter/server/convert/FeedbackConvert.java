package com.linker.fusion.knowledgecenter.server.convert;

import com.linker.fusion.knowledgecenter.infrastructure.entity.FeedbackEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FeedbackSettingEntity;
import com.linker.fusion.knowledgecenter.server.dto.req.feedback.FeedbackAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.feedback.FeedbackImportReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.FeedbackResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.FeedbackSettingResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface FeedbackConvert {

    FeedbackConvert INSTANCE = Mappers.getMapper(FeedbackConvert.class);

    FeedbackEntity toEntity(FeedbackAddReq req);

    List<FeedbackEntity> toEntity(List<FeedbackImportReq.Item> items);

    FeedbackResp toResp(FeedbackEntity entity);

    FeedbackSettingResp toResp(FeedbackSettingEntity feedbackSettingEntity);

}
