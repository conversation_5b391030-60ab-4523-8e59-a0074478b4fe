package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.server.biz.WordsBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.word.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.WordsPageResp;
import com.linker.omagent.omos.model.OmChatLanguageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @see com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum
 */
@Api(tags = "词条管理")
@RestController
@RequestMapping("/words")
public class WordsController {


    @Resource
    private WordsBiz wordsBiz;
    @Resource
    private OmChatLanguageModel omChatLanguageModel;

    @PostMapping("/create")
    @ApiOperation(value = "创建")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Long> create(@RequestBody @Validated WordsCreateReq cmd) {
        return new BaseResp<>(wordsBiz.create(cmd));
    }

    @PostMapping("/modify")
    @ApiOperation(value = "修改")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> modify(@RequestBody @Validated WordsModifyReq cmd) {
        wordsBiz.modify(cmd);
        return new BaseResp<>();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> delete(@RequestBody WordsDeletedReq cmd) {
        wordsBiz.delete(cmd);
        return new BaseResp<>();
    }

    @PostMapping("/change")
    @ApiOperation(value = "修改状态")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> changeState(@RequestBody WordsChangeStateReq cmd) {
        wordsBiz.changeState(cmd);
        return new BaseResp<>();
    }


    @GetMapping("/list")
    @ApiOperation(value = "查询")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<WordsPageResp> page(@RequestParam(value = "content", required = false) String content,
                                             @RequestParam(value = "groupId", required = false) Long groupId,
                                             @RequestParam(value = "type", required = false) Integer type,
                                             @RequestParam(value = "state", required = false) Integer state,
                                             @RequestParam(value = "page") Long page,
                                             @RequestParam(value = "pageSize") Long pageSize
    ) {
        WordsSearchReq req = new WordsSearchReq(content, groupId, type, state);
        req.setPage(page);
        req.setPageSize(pageSize);
        return wordsBiz.page(req);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public BaseResp<Void> importWords(@RequestPart(value = "file") MultipartFile file,
                                      @RequestParam(value = "groupId") Long groupId,
                                      @RequestParam(value = "type") Integer type) {
        wordsBiz.importWords(file, groupId, type);
        return new BaseResp<>();
    }

    @PostMapping(value = "/export")
    @ApiOperation(value = "导出")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public void exportWords(
            @RequestBody WordsExportReq req,
            HttpServletResponse response) {
        // 需求上不需要校验目录权限
        wordsBiz.exportWords(req, response);
    }

    @PostMapping("/find/list")
    @ApiOperation(value = "查询")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<WordsPageResp>> findList(@RequestBody WordsFindListReq wordsFindListReq) {
        return new BaseResp<>(wordsBiz.findList(wordsFindListReq));
    }

    @PostMapping("/find/group")
    @ApiOperation(value = "查询", hidden = true)
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<WordsPageResp>> findList(@RequestBody WordsByGroupReq wordsFindListReq) {
        return new BaseResp<>(wordsBiz.findList(wordsFindListReq));
    }



}


