package com.linker.fusion.knowledgecenter.server.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class TableSliceResp {

    @ApiModelProperty(value = "表格id", required = true)
    @NotNull
    private Long tableId;

    @ApiModelProperty("uid")
    @NotNull
    private String uid;

    @ApiModelProperty("表头")
    @NotEmpty
    private List<String> header;

    @ApiModelProperty("一行数据")
    @NotEmpty
    private List<Object> row;

    @ApiModelProperty("创建时间")
    private String createTime;

    public Integer getCharacter() {
        Integer total = 0;
        for (int i = 0; i < header.size(); i++) {
            total += header.get(i).length();
        }
        for (int i = 0; i < row.size(); i++) {
            total += row.get(i).toString().length();
        }
        return total;
    }
}
