package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.server.biz.SearchBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.search.AdvancedSearchReq;
import com.linker.fusion.knowledgecenter.server.dto.req.search.AdvancedSearchResp;
import com.linker.fusion.knowledgecenter.server.dto.req.search.AiSearchReq;
import com.linker.fusion.knowledgecenter.server.dto.req.search.AiSearchResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(tags = "搜索")
@RestController
@RequestMapping
public class SearchController {

    @Autowired
    private SearchBiz searchBiz;

    @PostMapping(value = {"/ai-search/search", "/search/ai"})
    @ApiOperation(value = "AI检索")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public BaseResp<AiSearchResp> search(@RequestBody @Valid AiSearchReq req) {
        AiSearchResp aiSearchResp = searchBiz.search(req);
        return new BaseResp<>(aiSearchResp);
    }

    @PostMapping("/search/advanced")
    @ApiOperation(value = "高级检索")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<AdvancedSearchResp> advancedSearch(@RequestBody @Valid AdvancedSearchReq req) {
        return new BaseResp<>(searchBiz.advancedSearch(req));
    }

    @PostMapping("/search/advanced/2")
    @ApiOperation(value = "高级检索测试", hidden = true)
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Object> advancedSearchTest(@RequestBody @Valid AdvancedSearchReq req) {
        return new BaseResp<>(searchBiz.advancedSearchTest(req));
    }
}
