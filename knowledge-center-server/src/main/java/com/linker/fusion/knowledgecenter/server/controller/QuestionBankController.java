package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.base.exception.BusinessException;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthNodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.OperatorTypeEnum;
import com.linker.fusion.knowledgecenter.server.biz.QuestionBankBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.question.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.QuestionBankResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.QuestionCountResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.GroupNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.auth.MenuAndResNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@Api(tags = "题库管理")
@RestController
@RequestMapping("/question-bank")
@Validated
public class QuestionBankController {

    @Resource
    private QuestionBankBiz questionBankBiz;
    @Autowired
    private KnowledgeGroupFactory knowledgeGroupFactory;


    @ApiOperation("分页查询题目")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/page")
    @LogFilter
    public BasePaginResp<QuestionBankResp> page(@Valid @RequestBody QuestionBankPageReq req) {

        return questionBankBiz.page(req);
    }

    @ApiOperation("新增题目")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/create")
    @LogFilter
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.QUESTION, operatorType = OperatorTypeEnum.CREATE, node = AuthNodeEnum.QUESTION_CREATE, groupIds = "{#req.groupId}")
    public BaseResp<QuestionBankResp> create(@Valid @RequestBody QuestionBankCreateReq req) {
        return questionBankBiz.create(req);
    }

    @ApiOperation("更新题目")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/update")
    @LogFilter
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.QUESTION, operatorType = OperatorTypeEnum.EDIT, node = AuthNodeEnum.QUESTION_EDIT, ids = "{#req.id}")
    public BaseResp<Void> update(@Valid @RequestBody QuestionBankUpdateReq req) {
        return questionBankBiz.update(req);
    }

    @ApiOperation("删除题目")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/delete")
    @LogFilter
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.QUESTION, operatorType = OperatorTypeEnum.DELETE, node = AuthNodeEnum.QUESTION_DELETE, ids = "{#req.ids}")
    public BaseResp<Void> delete(@Valid @RequestBody BatchQuestionDeleteReq req) {
        return questionBankBiz.delete(req);
    }


    @ApiOperation("启用/禁用题目")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/enable")
    @LogFilter
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.QUESTION, operatorType = OperatorTypeEnum.EDIT, node = AuthNodeEnum.QUESTION_ENABLE, ids = "{#req.id}")
    public BaseResp<Void> enable(@Valid @RequestBody QuestionEnableReq req) {
        return questionBankBiz.enable(req);
    }

    @ApiOperation("批量启用/禁用题目")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/batch-enable")
    @LogFilter
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.QUESTION, operatorType = OperatorTypeEnum.ENABLE, node = AuthNodeEnum.QUESTION_ENABLE, ids = "{#req.ids}")
    public BaseResp<Void> batchEnable(@Valid @RequestBody BatchQuestionEnableReq req) {
        return questionBankBiz.batchEnable(req);
    }

    @ApiOperation("移动题目")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/move")
    @LogFilter
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.QUESTION, operatorType = OperatorTypeEnum.MOVE, node = AuthNodeEnum.QUESTION_MOVE, ids = "{#req.ids}")
    @GroupNodeAuth(node = AuthNodeEnum.QUESTION_CREATE, groupIds = "{#req.targetGroupId}")
    public BaseResp<Void> move(@Valid @RequestBody QuestionMoveReq req) {
        return questionBankBiz.move(req);
    }

    @ApiOperation("查询分组下题目数量")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/count-by-groups")
    @LogFilter
    public BaseResp<List<QuestionCountResp>> countByGroups(@Valid @RequestBody QuestionCountReq req) {
        return new BaseResp<>(questionBankBiz.countByGroups(req));
    }

    @PostMapping("/ai/generate")
    @ApiOperation(value = "ai生成题目", tags = "v1.6.2")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.QUESTION, operatorType = OperatorTypeEnum.AI_GENERATE, node = AuthNodeEnum.QUESTION_CREATE, groupIds = "{#req.targetGroupId}")
    @GroupNodeAuth(node = AuthNodeEnum.FAQ_PREVIEW, groupIds = "{#req.selectGroupIds}")
    public BaseResp<TaskEntity> aiGenerateQA(@RequestBody AIGenerateQBReq req) {
        if (CollectionUtils.isEmpty(req.getSelectGroupIds()))
            throw new BusinessException("请选择问答对目录");
        return new BaseResp<>(questionBankBiz.aiGenerate(req));
    }

    @PostMapping("/ai/doc/generate")
    @ApiOperation(value = "根据文档ai生成题目", tags = "v1.6.3")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @MenuAndResNodeAuth(type = KnowledgeTypeEnum.QUESTION, operatorType = OperatorTypeEnum.DOC_GENERATE, node = AuthNodeEnum.QUESTION_CREATE, groupIds = "{#req.targetGroupId}")
    public BaseResp<TaskEntity> aiGenerateFromDoc(@RequestBody AIGenerateFromDocReq req) {
        if (req.getFile() == null || req.getFile().isEmpty()) {
            throw new BusinessException("请上传文件");
        }
        return new BaseResp<>(questionBankBiz.aiGenerateFromDoc(req));
    }

} 