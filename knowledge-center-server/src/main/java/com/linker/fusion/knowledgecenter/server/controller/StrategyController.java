package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.server.biz.StrategyBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.strategy.StrategyAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.strategy.StrategyUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.StrategyDetailResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api(tags = "解析策略")
@RestController
@RequestMapping("/strategy")
public class StrategyController {
    @Autowired
    private StrategyBiz strategyBiz;

    @GetMapping("/detail")
    @ApiOperation(value = "获取分组解析策略详情", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<StrategyDetailResp> getStrategyDetail(@RequestParam(value = "groupId") Long groupId) {
        return strategyBiz.getStrategyDetail(groupId);
    }

    @GetMapping("/getById")
    @ApiOperation(value = "根据id获取解析策略详情", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<StrategyDetailResp> getStrategyById(@RequestParam(value = "id") Long id) {
        return strategyBiz.getStrategyById(id);
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加解析策略", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> addStrategy(@RequestBody @Valid StrategyAddReq req) {
        strategyBiz.addStrategy(req);
        return new BaseResp<>();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新解析策略", tags = "v1.5.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> updateStrategy(@RequestBody @Valid StrategyUpdateReq req) {
        strategyBiz.updateStrategy(req);
        return new BaseResp<>();
    }
}
