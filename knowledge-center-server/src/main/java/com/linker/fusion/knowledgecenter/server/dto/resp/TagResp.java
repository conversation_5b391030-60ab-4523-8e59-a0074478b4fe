package com.linker.fusion.knowledgecenter.server.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class TagResp {
    @ApiModelProperty("标签名称")
    private String name;
    @ApiModelProperty("出现次数")
    private Integer times;
    @ApiModelProperty("占比")
    private String per;
    private List<FrameResp> frames=new ArrayList<>();
}
