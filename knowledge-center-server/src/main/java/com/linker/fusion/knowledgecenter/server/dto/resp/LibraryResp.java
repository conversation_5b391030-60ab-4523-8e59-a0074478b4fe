package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.linker.fusion.knowledgecenter.infrastructure.model.AuthActionModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LibraryResp {

    @ApiModelProperty(value = "编号", required = true)
    private Long id;

    @ApiModelProperty(value = "名称", required = true)
    private String name;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("图标")
    private String logo;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否是同步库")
    private Boolean isSync;

    @ApiModelProperty("授权信息")
    private AuthActionModel authActions;

}
