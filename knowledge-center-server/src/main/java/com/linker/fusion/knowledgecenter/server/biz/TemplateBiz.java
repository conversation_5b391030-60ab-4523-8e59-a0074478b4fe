package com.linker.fusion.knowledgecenter.server.biz;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.base.exception.BusinessException;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.common.Constants;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TemplateEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.StoragePathEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.AudioStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.DocStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.ImageStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.VideoStrategyModel;
import com.linker.fusion.knowledgecenter.server.convert.TemplateConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.core.IdBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.template.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.TemplateResp;
import com.linker.fusion.knowledgecenter.service.domain.common.IUtilService;
import com.linker.fusion.knowledgecenter.service.domain.common.RedisLock;
import com.linker.fusion.knowledgecenter.service.domain.strategy.TemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.*;

@Slf4j
@Component
public class TemplateBiz extends BaseBiz {

    @Resource
    private TemplateService templateService;

    @Resource
    private RedisLock redisLock;

    @Resource
    private IUtilService iUtilService;

    public Long create(TemplateCreateReq req) {
        Integer type = req.getType();
        if (Objects.equals(3, type) &&
                Objects.nonNull(req.getStrategyVideo()) &&
                Objects.equals(req.getStrategyVideo().getEnableEvent(), 1) && (
                Objects.isNull(req.getStrategyVideo().getEventSetting()) ||
                        CollectionUtils.isEmpty(req.getStrategyVideo().getEventSetting().getPromptInfos()))) {
            throw new BusinessException("事件分析提示词信息不能为空。");
        }
        if (Objects.equals(2, type)) {
            ImageStrategyModel strategyImage = req.getStrategyImage();
            if (Objects.equals(strategyImage.getEnableObjectDetection(), 1) && CollUtil.isEmpty(strategyImage.getOdList())) {
                throw new BusinessException("实体识别信息不能为空");
            }
        }
        if (Objects.equals(3, type)) {
            @Valid VideoStrategyModel strategyVideo = req.getStrategyVideo();
            if (Objects.equals(strategyVideo.getEnableObjectDetection(), 1) && CollUtil.isEmpty(strategyVideo.getOdList())) {
                throw new BusinessException("实体识别信息不能为空");
            }
        }
        return redisLock.lock(Constants.TEMPLATE_MODIFY_LOCK, getTenantId() + ":" + type, () -> {
            checkNameDuplicate(req.getName(), type, null);
            TemplateEntity templateEntity = TemplateConvert.INSTANCE.toEntity(req);
            templateEntity.setTenantId(getTenantId());
            templateEntity.setDeleted(false);
            templateEntity.setSource(1);
            templateEntity.setSort(TemplateEntity.defaultSort());
            templateEntity.setEnable(true);
            templateEntity.setIsDefault(false);
            templateEntity.setStrategy(req.getStrategy());

            templateService.create(templateEntity);
            if (StringUtils.isNotBlank(templateEntity.getPreviewUrl())) {
                String newUrl = iUtilService.moveTempToDefault(templateEntity.getPreviewUrl(), StoragePathEnum.Template.getValueByUrl(getTenantId(), templateEntity.getId().toString(), templateEntity.getPreviewUrl()));
                templateEntity.setPreviewUrl(newUrl);
                templateService.update(templateEntity);
            }
            return templateEntity.getId();
        });
    }

    private void checkNameDuplicate(String name, Integer type, Long id) {
        TemplateEntity templateEntity = templateService.queryByName(getTenantId(), name, type);
        if (templateEntity != null && !templateEntity.getId().equals(id)) {
            throw new ServiceException(TEMPLATE_NAME_DUPLICATE);
        }
    }

    public BasePaginResp<TemplateResp> page(TemplatePageReq req) {
        initTemplate();
        Page<TemplateEntity> page = templateService.page(getTenantId(), req.getPage(), req.getPageSize(), req.getType(), req.getKeyword(), req.getEnable());
        List<TemplateResp> respList = page.getRecords().stream().map(TemplateResp::new).collect(Collectors.toList());
        return new BasePaginResp<>(page.getTotal(), respList);
    }

    public List<TemplateResp> available(Integer type) {
        initTemplate();
        List<TemplateEntity> list = templateService.list(getTenantId(), type);
        return list.stream().map(TemplateResp::new).collect(Collectors.toList());
    }

    private void initTemplate() {
        redisLock.lock(Constants.TEMPLATE_INIT_LOCK, getTenantId(), () -> {
            List<TemplateEntity> systemTemplate = templateService.getSystemTemplate(getTenantId());
            if (CollectionUtils.isEmpty(systemTemplate)) {
                systemTemplate.add(systemDoc());
                systemTemplate.add(systemImage());
                systemTemplate.add(systemVideo());
                systemTemplate.add(systemAudio());
                templateService.saveBatch(systemTemplate);
            }
        });
    }

    public void update(TemplateUpdateReq req) {
        Integer type = req.getType();
        if (Objects.equals(3, type) &&
                Objects.nonNull(req.getStrategyVideo()) &&
                Objects.equals(req.getStrategyVideo().getEnableEvent(), 1) && (
                Objects.isNull(req.getStrategyVideo().getEventSetting()) ||
                        CollectionUtils.isEmpty(req.getStrategyVideo().getEventSetting().getPromptInfos()))) {
            throw new BusinessException("事件分析提示词信息不能为空。");
        }
        if (Objects.equals(2, type)) {
            ImageStrategyModel strategyImage = req.getStrategyImage();
            if (Objects.equals(strategyImage.getEnableObjectDetection(), 1) && CollUtil.isEmpty(strategyImage.getOdList())) {
                throw new BusinessException("实体识别信息不能为空");
            }
        }
        if (Objects.equals(3, type)) {
            @Valid VideoStrategyModel strategyVideo = req.getStrategyVideo();
            if (Objects.equals(strategyVideo.getEnableObjectDetection(), 1) && CollUtil.isEmpty(strategyVideo.getOdList())) {
                throw new BusinessException("实体识别信息不能为空");
            }
        }
        redisLock.lock(Constants.TEMPLATE_MODIFY_LOCK, getTenantId() + ":" + type, () -> {
            checkNameDuplicate(req.getName(), type, req.getId());
            TemplateEntity templateEntity = templateService.getNotNull(getTenantId(), req.getId());
            if (templateEntity.getSource() == 0) {
                throw new ServiceException(TEMPLATE_SYSTEM_LIMIT, "修改");
            }
            checkNameDuplicate(req.getName(), type, req.getId());
            templateEntity.setName(req.getName());
            templateEntity.setDescription(req.getDescription());
            templateEntity.setStrategy(req.getStrategy());

            if (StringUtils.isNotBlank(templateEntity.getPreviewUrl()) && !templateEntity.getPreviewUrl().equals(req.getPreviewUrl())) {
                iUtilService.deleteFile(templateEntity.getPreviewUrl());
            }
            if (StringUtils.isNotBlank(req.getPreviewUrl())) {
                String newUrl = iUtilService.moveTempToDefault(req.getPreviewUrl(), StoragePathEnum.Template.getValueByUrl(templateEntity.getTenantId(), templateEntity.getId().toString(), req.getPreviewUrl()));
                if (StringUtils.isNotBlank(newUrl)) {
                    req.setPreviewUrl(newUrl);
                }
            }
            templateEntity.setPreviewUrl(req.getPreviewUrl());
            templateService.update(templateEntity);
        });
    }

    public void delete(IdBaseReq<Long> req) {
        TemplateEntity templateEntity = templateService.getNotNull(getTenantId(), req.getId());
        if (templateEntity.getSource() == 0) {
            throw new ServiceException(TEMPLATE_SYSTEM_LIMIT, "删除");
        }
        if (templateEntity.getIsDefault()) {
            throw new ServiceException(TEMPLATE_DEFAULT_LIMIT, "删除");
        }
        if (StringUtils.isNotBlank(templateEntity.getPreviewUrl())) {
            iUtilService.deleteFile(templateEntity.getPreviewUrl());
        }
        templateService.delete(getTenantId(), req.getId());
    }

    private TemplateEntity systemDoc() {
        TemplateEntity templateEntity = initSystem();
        templateEntity.setName("通用文档模板");
        templateEntity.setDescription("适用于大多数类型的文档解析需求，可以自动检测文档中包含的标题、图片、表格等元素信息，并将提取到的文档内容，依据文档的原始格式和段落布局进行精准的分段解析。");
        templateEntity.setType(FileTypeEnum.DOCUMENT.getType());
        templateEntity.setStrategy(JSON.toJSONString(new DocStrategyModel()));
        return templateEntity;
    }

    private TemplateEntity systemImage() {
        TemplateEntity templateEntity = initSystem();
        templateEntity.setName("通用图片模板");
        templateEntity.setDescription("适用大多数类型的音频解析需求，可以自动检测图片中包含的人脸、实体等元素信息，并通过模型生成图片描述信息。");
        templateEntity.setType(FileTypeEnum.IMAGE.getType());
        templateEntity.setStrategy(JSON.toJSONString(new ImageStrategyModel()));
        return templateEntity;
    }

    private TemplateEntity systemVideo() {
        TemplateEntity templateEntity = initSystem();
        templateEntity.setName("通用视频模板");
        templateEntity.setDescription("适用大多数类型的视频解析需求，可以自动检测视频中包含的人脸、实体、专名等元素信息，并通过识别视频中的镜头切换进行进行精准的分段解析。");
        templateEntity.setType(FileTypeEnum.VIDEO.getType());
        templateEntity.setStrategy(JSON.toJSONString(new VideoStrategyModel()));
        return templateEntity;
    }

    private TemplateEntity systemAudio() {
        TemplateEntity templateEntity = initSystem();
        templateEntity.setName("通用音频模板");
        templateEntity.setDescription("适用大多数类型的音频解析需求，可以自动将音频内容转写成文字，并通过理解语音内容进行精准的分段解析。");
        templateEntity.setType(FileTypeEnum.AUDIO.getType());
        templateEntity.setStrategy(JSON.toJSONString(new AudioStrategyModel()));
        return templateEntity;
    }

    private TemplateEntity initSystem() {
        TemplateEntity templateEntity = new TemplateEntity();
        templateEntity.setSource(0);
        templateEntity.setSort(TemplateEntity.defaultSort());
        templateEntity.setEnable(true);
        templateEntity.setIsDefault(true);
        templateEntity.setTenantId(getTenantId());
        templateEntity.setCreateTime(LocalDateTime.now());
        templateEntity.setCreatorId("system");
        templateEntity.setDeleted(false);
        return templateEntity;
    }

    public void enable(TemplateEnableReq req) {
        TemplateEntity templateEntity = templateService.getNotNull(getTenantId(), req.getId());
        if (templateEntity.getIsDefault() && !req.getEnable()) {
            throw new ServiceException(TEMPLATE_DEFAULT_LIMIT, "禁用");
        }
        TemplateEntity template = new TemplateEntity();
        template.setId(req.getId());
        template.setEnable(req.getEnable());
        templateService.update(template);
    }

    public void setDefault(IdBaseReq<Long> req) {
        TemplateEntity templateEntity = templateService.getNotNull(getTenantId(), req.getId());
        redisLock.lock(Constants.TEMPLATE_MODIFY_LOCK, getTenantId() + ":" + templateEntity.getType(), () -> {
            TemplateEntity template = new TemplateEntity();
            template.setId(req.getId());
            template.setEnable(true);
            template.setIsDefault(true);
            templateService.update(template);

            templateService.cancelDefault(getTenantId(), templateEntity.getType(), req.getId());
        });
    }

    public void setSort(TemplateSortReq req) {
        templateService.getNotNull(getTenantId(), req.getId());
        TemplateEntity template = new TemplateEntity();
        template.setId(req.getId());
        template.setSort(req.getSort());
        templateService.update(template);
    }
}
