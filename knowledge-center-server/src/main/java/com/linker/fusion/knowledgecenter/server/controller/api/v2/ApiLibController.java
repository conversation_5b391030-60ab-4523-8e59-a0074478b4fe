package com.linker.fusion.knowledgecenter.server.controller.api.v2;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.server.biz.GroupBiz;
import com.linker.fusion.knowledgecenter.server.dto.api.v1.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = "文件库管理")
@RestController
@RequestMapping("api/knowledge/v1")
public class ApiLibController {
    @Resource
    private GroupBiz knowledgeGroupBiz;

    @PostMapping("lib/create")
    @ApiOperation(value = "新建文件库")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<String> createLib(@RequestBody @Validated ApiKnowledgeGroupCreateReq req) {
        return null;
    }

    @PostMapping("lib/List")
    @ApiOperation(value = "查询文件库列表")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<ApiKnowledgeLibResp> libList(@RequestBody ApiKnowledgeGroupListReq req) {
        return null;
    }

    @PostMapping("lib/info")
    @ApiOperation(value = "查询文件库详情")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<ApiKnowledgeLibResp> libInfo(@RequestBody @Validated LibIdRequest req) {
        return null;
    }

    @PostMapping("lib/update")
    @ApiOperation(value = "修改文件库")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> updateLib(@RequestBody @Validated ApiKnowledgeGroupUpdateReq req) {
//        knowledgeGroupBiz.update(req);
        return new BaseResp<>();
    }

    @PostMapping("lib/delete")
    @ApiOperation(value = "删除文件库")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> deleteLib(@RequestBody @Validated LibIdRequest req) {
//        knowledgeGroupBiz.delete(req.getId());
        return new BaseResp<>();
    }


}
