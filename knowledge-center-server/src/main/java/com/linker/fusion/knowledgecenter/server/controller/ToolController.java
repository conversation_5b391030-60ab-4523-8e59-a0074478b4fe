package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.exception.BaseException;
import com.linker.fusion.knowledgecenter.infrastructure.client.OcrClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.OcrReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.OcrResp;
import com.linker.fusion.knowledgecenter.infrastructure.config.PromptMappingConfig;
import com.linker.fusion.knowledgecenter.server.dto.req.tool.ToolOcrReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import static com.linker.core.base.enums.RespCodeEnum.INVALID_ARGUMENT;

@Api(tags = "工具包")
@RestController
@RequestMapping("/tool")
public class ToolController {

    @Resource
    private OcrClient ocrClient;

    @Autowired
    private PromptMappingConfig promptMappingConfig;

    @PostMapping("/ocr")
    @ApiOperation("OCR识别")
    public BaseResp<String> ocr(@RequestBody ToolOcrReq req) {
        if (StringUtils.isBlank(req.getBase64()) && StringUtils.isBlank(req.getUrl())) {
            throw new BaseException(INVALID_ARGUMENT);
        }
        OcrReq ocrReq = StringUtils.isNotBlank(req.getBase64()) ? OcrReq.fromBase64(req.getBase64(), promptMappingConfig.getOcrModel()) : OcrReq.fromUrl(req.getUrl(), promptMappingConfig.getOcrModel());

        OcrResp ocrResp = ocrClient.ocr(ocrReq);

        List<List<OcrResp.SingleSentence>> objects = ocrResp.getObjects();
        if (CollectionUtils.isEmpty(objects) || CollectionUtils.isEmpty(objects.get(0))) {
            return new BaseResp<>("");
        }
        StringBuilder sb = new StringBuilder();
        List<OcrResp.SingleSentence> singleSentences = objects.get(0);
        // Re-sort the OCR result sequence of the algorithm by ymin.
        Collections.sort(singleSentences, Comparator.comparingDouble(OcrResp.SingleSentence::getYmin));

        for (OcrResp.SingleSentence singleSentence : singleSentences) {
            sb.append(singleSentence.getLabel()).append(" ");
        }
        return new BaseResp<>(sb.toString());
    }

}
