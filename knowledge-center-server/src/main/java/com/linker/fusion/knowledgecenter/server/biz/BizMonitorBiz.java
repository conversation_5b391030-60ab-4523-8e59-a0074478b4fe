package com.linker.fusion.knowledgecenter.server.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.client.BGEClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.OcrClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.OcrReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.RankerEncodeReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.OcrResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.RankerEncodeResp;
import com.linker.fusion.knowledgecenter.infrastructure.config.PromptMappingConfig;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.service.domain.EmbeddingService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.omagent.core.data.embedding.Embedding;
import com.linker.omagent.core.data.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BizMonitorBiz {
    @Autowired
    private EmbeddingService embeddingService;
    @Autowired
    private BGEClient bgeClient;

    @Autowired
    private IResourceService resourceService;
    @Autowired
    private OcrClient ocrClient;

    @Autowired
    private PromptMappingConfig promptMappingConfig;

    public void algorithmInfo(String query, String url) {
        if (StringUtils.isEmpty(query)) {
            query = "你好,我是一段文字";
        }
        if (StringUtils.isEmpty(url)) {
            //随机选一张可用的图片做测试
            KnowledgeResourceEntity resource = resourceService.getTestImage();
            url = resource.getUrl();
        }

        // 文本向量化耗时统计
        long textEmbeddingStartTime = System.currentTimeMillis();
        try {
            //文本向量化
            Response<Embedding> embeddingResponse = embeddingService.embedText(query);
            Embedding content = embeddingResponse.content();
            if (content.vector().isEmpty()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR, "文本向量化");
            }
            long textEmbeddingDuration = System.currentTimeMillis() - textEmbeddingStartTime;
            if (textEmbeddingDuration > 5000) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR,
                        String.format("文本向量化执行超时，耗时: %d ms，超过5秒限制", textEmbeddingDuration));
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("文本向量化error:{}", e.getMessage());
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR, "文本向量化");
        }

        // 图片向量化耗时统计
        long imageEmbeddingStartTime = System.currentTimeMillis();
        try {
            //图片向量化
            Response<Embedding> embeddingResponse = embeddingService.embedImage(url);
            Embedding content = embeddingResponse.content();
            if (content.vector().isEmpty()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR, "图片向量化");
            }
            long imageEmbeddingDuration = System.currentTimeMillis() - imageEmbeddingStartTime;
            if (imageEmbeddingDuration > 5000) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR,
                        String.format("图片向量化执行超时，耗时: %d ms，超过5秒限制", imageEmbeddingDuration));
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("图片向量化error:{}", e.getMessage());
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR, "图片向量化");
        }

        //重排序
        long rerankStartTime = System.currentTimeMillis();
        List<List<String>> qa = new ArrayList<>();
        List<String> list = new ArrayList<>();
        list.add("今天天气怎么样");
        list.add("天气很好");
        qa.add(list);
        try {
            RankerEncodeResp rerank = bgeClient.rankerEncode(new RankerEncodeReq(qa));
            List<BigDecimal> features = rerank.getFeatures();
            if (features.isEmpty()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR, "重排序");
            }
            long rerankDuration = System.currentTimeMillis() - rerankStartTime;
            if (rerankDuration > 5000) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR,
                        String.format("重排序执行超时，耗时: %d ms，超过5秒限制", rerankDuration));
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("重排序算法异常:{}", e.getMessage());
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR, "重排序算法异常");
        }

        // OCR耗时统计
        long ocrStartTime = System.currentTimeMillis();
//        ocr
        OcrReq ocrReq = OcrReq.fromUrl(url, promptMappingConfig.getOcrModel());
        OcrResp ocrResp = ocrClient.ocr(ocrReq);
        if (ocrResp.getObjects() == null) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR, "ocr");
        }
        long ocrDuration = System.currentTimeMillis() - ocrStartTime;
        if (ocrDuration > 5000) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.ABILITY_ERROR,
                    String.format("OCR执行超时，耗时: %d ms，超过5秒限制", ocrDuration));
        }
    }

    /**
     * 获取全部失败的信息
     *
     * @param time
     * @return
     */
    public BaseResp<String> resourceLearnInfo(Integer time) {
        if (time == null) {
            time = 30;
        }
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMinutes(time);
        //通过状态
        Integer status = ProcessEnum.Fail.getValue();
        ArrayList<Integer> statusList = Lists.newArrayList(status);
        List<KnowledgeResourceEntity> knowledgeResourceEntities =
                resourceService.getFileFromTimeRange(statusList, startTime, endTime);
        if (knowledgeResourceEntities.isEmpty()) {
            return new BaseResp<>();
        }
        List<JSONObject> collect = knowledgeResourceEntities.stream().map(
                knowledgeResourceEntity -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("工作流id", knowledgeResourceEntity.getWorkflowId());
                    jsonObject.put("标题", knowledgeResourceEntity.getTitle());
                    jsonObject.put("失败原因", knowledgeResourceEntity.getHandleFailReason() != null ? knowledgeResourceEntity.getHandleFailReason().getMessage() : "");
                    jsonObject.put("租户id", knowledgeResourceEntity.getTenantId());
                    jsonObject.put("文档id", knowledgeResourceEntity.getDocId());
                    jsonObject.put("文件目录id", knowledgeResourceEntity.getGroupId());
                    return jsonObject;
                }
        ).collect(Collectors.toList());
        BaseResp<String> stringBaseResp = new BaseResp<>(JSON.toJSONString(collect));
        stringBaseResp.setCode(KnowledgeCenterErrorCodeEnum.LEARN_FAILED.getCode());
        stringBaseResp.setMessage(String.format(KnowledgeCenterErrorCodeEnum.LEARN_FAILED.getMessage(), knowledgeResourceEntities.size()));
        return stringBaseResp;
    }

    public Map queueInfo(Integer time) {
        if (time == null) {
            time = 30;
        }
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMinutes(time);
        //通过状态
        ArrayList<Integer> statusList = Lists.newArrayList(ProcessEnum.QUEUED.getValue()
                , ProcessEnum.PREPROCESSING.getValue(), ProcessEnum.Executing.getValue());
        List<KnowledgeResourceEntity> knowledgeResourceEntities =
                resourceService.getFileFromTimeRange(statusList, startTime, endTime);
        //统计每个租户的文件数量
        Map<String, Long> collect = knowledgeResourceEntities.stream()
                .collect(Collectors.groupingBy(KnowledgeResourceEntity::getTenantId, Collectors.counting()));
        return collect;
    }
}