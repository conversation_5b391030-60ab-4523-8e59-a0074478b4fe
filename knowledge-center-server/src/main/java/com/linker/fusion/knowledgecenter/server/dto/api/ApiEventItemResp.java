package com.linker.fusion.knowledgecenter.server.dto.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ApiEventItemResp {
    @ApiModelProperty("当前行为次数")
    private Integer count = 0;
    @ApiModelProperty("行为名称")
    private String actionName;
    @ApiModelProperty("设备名称")
    private String deviceName;
    @ApiModelProperty("发生事件时间")
    private String eventTime;
    @ApiModelProperty("抽帧时间点 单位毫秒")
    private Long timePoint;
    @ApiModelProperty("结束帧位置")
    private Long endTimePoint;
    private String startTime;
    private String endTime;
    @ApiModelProperty("图片地址")
    private List<ApiEventImageItemResp> images;
}
