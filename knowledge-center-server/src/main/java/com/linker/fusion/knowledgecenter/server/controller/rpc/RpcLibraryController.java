package com.linker.fusion.knowledgecenter.server.controller.rpc;

import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.server.biz.GroupBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.library.LibraryCreateRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.req.library.LibraryListRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.req.library.LibraryUpdateRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.GroupResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "RPC/知识库")
@RestController
@RequestMapping("rpc/library")
public class RpcLibraryController {

    @Resource
    private GroupBiz knowledgeGroupBiz;

    @PostMapping("create")
    @ApiOperation(value = "创建知识库")
    public BaseResp<Long> create(@RequestBody @Valid LibraryCreateRpcReq req) {
        return new BaseResp<>(knowledgeGroupBiz.createLib(req));
    }

    @PostMapping("update")
    @ApiOperation(value = "更新知识库")
    public BaseResp<Void> update(@RequestBody @Valid LibraryUpdateRpcReq req) {
        //不在设置扫盘类型
        req.setIsSync(true);
        knowledgeGroupBiz.updateLib(req);
        return new BaseResp<>();
    }

    @PostMapping("list")
    @ApiOperation(value = "知识库列表")
    public BaseResp<List<GroupResp>> list(@RequestBody @Valid LibraryListRpcReq req) {
        return new BaseResp<>(knowledgeGroupBiz.listLib(req));
    }

    @GetMapping("get")
    @ApiOperation(value = "获取知识库")
    public BaseResp<GroupResp> get(@RequestParam(value = "id") Long id) {
        return new BaseResp<>(knowledgeGroupBiz.getResp(id));
    }
}
