package com.linker.fusion.knowledgecenter.server.biz;

import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.client.FileScanClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IKnowPowerResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IUserCenterService;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.MenuKeyEnum;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AuthEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.BaseEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.*;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.AuthAssModel;
import com.linker.fusion.knowledgecenter.server.convert.KnowledgeGroupConvert;
import com.linker.fusion.knowledgecenter.server.dto.group.KnowledgeGroupListReq;
import com.linker.fusion.knowledgecenter.server.dto.req.group.*;
import com.linker.fusion.knowledgecenter.server.dto.req.library.LibraryCreateRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.req.library.LibraryListRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.req.library.LibraryUpdateRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.GroupResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.KnowledgeGroupTreeNodeEntityResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthApproveService;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthLevelService;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import com.linker.fusion.knowledgecenter.service.domain.group.model.GroupFolderSaveDTO;
import com.linker.fusion.knowledgecenter.service.domain.oprationlog.OperationLogProxyService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.task.ITaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.*;

@Component
@Slf4j
public class GroupBiz extends BaseBiz {

    @Resource
    private KnowledgeGroupService knowledgeGroupService;
    @Resource
    private IAuthService iAuthService;
    @Resource
    private IAuthLevelService iAuthLevelService;
    @Resource
    private IUserCenterService iUserCenterService;
    @Resource
    private FileScanClient fileScanClient;
    @Resource
    private ITaskService iTaskService;

    @Resource
    private IAuthApproveService iAuthApproveService;
    @Autowired
    private KnowledgeGroupFactory knowledgeServiceFactory;
    @Autowired
    private KnowledgeGroupFactory knowledgeGroupFactory;
    @Autowired
    private IResourceService iResourceService;

    /**
     * 获取有权限的分组id过滤条件
     *
     * @return
     * @type 文件类型
     */
    public List<Long> getAuthedGroupIdFilter(Integer type) {
        if (iUserCenterService.isManager(getToken(), getTenantId(), getUserCode()))
            return new ArrayList<>();
        List<KnowledgeGroupEntity> groupList = knowledgeGroupService.listAll(getTenantId(), getUserCode(), type, ResourceVisibleTypeEnum.PUBLIC.getValue());
        List<KnowledgeGroupTreeNodeEntityResp> treeNodes = buildTreeAuthedRoot(groupList);
        return flatTree(treeNodes).stream().map(x -> x.getId()).collect(Collectors.toList());
    }

    /**
     * 平铺数据
     *
     * @param treeNodes
     * @return
     */
    private List<KnowledgeGroupTreeNodeEntityResp> flatTree(List<KnowledgeGroupTreeNodeEntityResp> treeNodes) {
        List<KnowledgeGroupTreeNodeEntityResp> allNodes = new ArrayList<>();
        treeNodes.forEach(treeNode -> {
            allNodes.add(treeNode);
            allNodes.addAll(flatTree(treeNode.getChildren()));
        });
        return allNodes;
    }


    public Long create(KnowledgeGroupCreateReq req) {
        knowledgeServiceFactory.checkCreateAuth(req.getParentId(), req.getType(), req.getVisibleType());
        if (KnowledgeGroupEntity.ROOT_ID == req.getParentId()) {
            return createLib(req);
        } else {
            return createFolder(req);
        }
    }

    @Autowired
    OperationLogProxyService operationLogProxyService;

    private Long createLib(KnowledgeGroupCreateReq req) {

        // 敏感词库和专业词库同租户下不允许重名
        if (KnowledgeTypeEnum.SENSITIVE.getType().equals(req.getType()) || KnowledgeTypeEnum.PRO.getType().equals(req.getType())) {
            KnowledgeGroupEntity group = knowledgeGroupService.getLibByTenantIdAndTypeAndName(getTenantId(), req.getType(), req.getName());
            if (Objects.nonNull(group)) {
                throw new ServiceException(GROUP_NAME_DUPLICATE);
            }
        }

        Long libId = createLib(getTenantId(), getUserCode(), req.getType(), req.getName(), req.getDescription(), req.getLogo(), false, (double) req.getSort(), req.getImportantLevel(), req.getVisibleType());
        if (req.getType().equals(KnowledgeTypeEnum.FILE.getType())) {
            operationLogProxyService.createFileLibRecord(libId, req.getName(), ResourceVisibleTypeEnum.getName(req.getVisibleType()));
        }
        return libId;
    }

    public Long createLib(LibraryCreateRpcReq req) {
        return createLib(req.getTenantId(), req.getUserCode(), req.getType(), req.getName(), req.getDescription(), req.getLogo(), true, 0d, ResourceImportantLevelEnum.DEFAULT.getValue(), ResourceVisibleTypeEnum.PUBLIC.getValue());
    }

    private Long createLib(String tenantId, String userCode, Integer type,
                           String name, String desc, String logo, boolean isSync,
                           Double sort, Integer importantLevel, Integer visibleType
    ) {
        KnowledgeGroupEntity group = new KnowledgeGroupEntity();
        group.init(tenantId, userCode);
        // 标记为库
        group.setIsLibrary(true);
        if (ResourceVisibleTypeEnum.PERSONAL.getValue().equals(visibleType) && KnowledgeTypeEnum.FILE.getType().equals(type)) {
            KnowledgeGroupEntity parent = knowledgeGroupService.getOrCreateByBiz(getTenantId(), getUserCode(), 2);
            group.setPath(parent.getSearchPath());
            group.setParentId(parent.getId());
        } else {
            group.setParentId(KnowledgeGroupEntity.ROOT_ID);
        }
        group.setSort(sort);
        // 元数据
        group.setType(type);
        group.setName(name);
        group.setDescription(desc);
        group.setLogo(logo);
        group.setIsSync(isSync);
        group.setImportantLevel(importantLevel);
        group.setVisibleType(visibleType);
        knowledgeGroupService.saveOrUpdate(group);
        return group.getId();
    }

    private Long createFolder(KnowledgeGroupCreateReq req) {
        KnowledgeGroupEntity parentGroup = knowledgeGroupService.get(req.getParentId());
        if (parentGroup.getIsSync()) {
            throw new ServiceException(GROUP_SYNC_LIMIT, "新建");
        }
        req.setImportantLevel(parentGroup.getImportantLevel());
        req.setVisibleType(parentGroup.getVisibleType());
        GroupFolderSaveDTO saveDTO = KnowledgeGroupConvert.INSTANCE.createReqToDto(req, UserContext.getUser());
        saveDTO.setPath((StringUtils.isBlank(parentGroup.getPath()) ? "/" : parentGroup.getPath()) + parentGroup.getId().toString() + "/");
        Long folderId = knowledgeGroupService.create(saveDTO);
        if (req.getType().equals(KnowledgeTypeEnum.FILE.getType())) {
            operationLogProxyService.createFolderLogRecord(folderId, req.getName(), ResourceVisibleTypeEnum.getName(req.getVisibleType()));
        }
        return folderId;
    }

    public void updateLib(LibraryUpdateRpcReq req) {
        knowledgeGroupService.update(req.getId(), req.getUserCode(), req.getName(), req.getIsSync());
    }

    public List<GroupResp> listLib(LibraryListRpcReq req) {
        List<KnowledgeGroupEntity> groupEntityList = knowledgeGroupService.listByTypeAndParentId(
                req.getTenantId(),
                req.getType(),
                Collections.singletonList(KnowledgeGroupEntity.ROOT_ID),
                req.getIsSync());
        return groupEntityList.stream().map(GroupResp::new).collect(Collectors.toList());
    }

    public GroupResp getResp(Long id) {
        return new GroupResp(get(id));
    }

    public KnowledgeGroupEntity get(Long id) {
        KnowledgeGroupEntity groupEntity = knowledgeGroupService.get(id);
        if (Objects.isNull(groupEntity) || groupEntity.getDeleted()) {
            throw new ServiceException(GROUP_NOT_EXIST);
        }
        return groupEntity;
    }

    /**
     * @param type 类型
     * @param from 构建平台请求
     * @return 目录树
     */
    public BaseResp<List<KnowledgeGroupTreeNodeEntityResp>> tree(Integer type, Integer from, Integer visibleType) {
        boolean needAuth = !KnowledgeTypeEnum.SENSITIVE.getType().equals(type) && !KnowledgeTypeEnum.PRO.getType().equals(type) && ResourceVisibleTypeEnum.PUBLIC.getValue().equals(visibleType);
        List<KnowledgeGroupEntity> groupList = knowledgeGroupService.listAll(getTenantId(), getUserCode(), type, visibleType);
        //自动创建我的文件
        if (!KnowledgeTypeEnum.SENSITIVE.getType().equals(type) && !KnowledgeTypeEnum.PRO.getType().equals(type) && CollectionUtils.isEmpty(groupList) && ResourceVisibleTypeEnum.PERSONAL.getValue().equals(visibleType)) {
            knowledgeGroupService.getOrCreateByBiz(getTenantId(), getUserCode(), 2);
            groupList = knowledgeGroupService.listAll(getTenantId(), getUserCode(), type, visibleType);
        }
        List<KnowledgeGroupTreeNodeEntityResp> list;
        if (!needAuth || (Objects.nonNull(from) && 1 == from)) {
            list = buildTree(groupList);
        } else {
            list = buildTreeAuthedRoot(groupList);
        }
        if (!KnowledgeTypeEnum.SENSITIVE.getType().equals(type) && !KnowledgeTypeEnum.PRO.getType().equals(type)) {
            List<KnowledgeGroupTreeNodeEntityResp> allNodes = flatTree(list);
            //查询所有任务
            List<TaskEntity> tasks = iTaskService.listUnread(getTenantId(), getUserCode());
            if (CollectionUtils.isNotEmpty(tasks)) {

                tasks.forEach(task -> {
                    Optional<KnowledgeGroupTreeNodeEntityResp> oNode = allNodes.stream().filter(n -> n.getId().equals(task.getGroupId())).findFirst();
                    oNode.ifPresent(knowledgeGroupTreeNodeEntityResp -> knowledgeGroupTreeNodeEntityResp.setTask(task));
                });
            }
            List<Long> applyIds = iAuthApproveService.listAllIngIds(getTenantId(), getUserCode(), SourceTypeEnum.Group.getValue());
            allNodes.forEach(node -> {
                node.setApplying(applyIds.contains(node.getId()));
            });


        }


        return new BaseResp<>(list);
    }

    /**
     * 获取子目录
     *
     * @param parentId 父目录Id
     * @return 返回子目录列表
     */
    public BaseResp<List<KnowledgeGroupTreeNodeEntityResp>> sub(Long parentId) {
        KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(parentId);

        //knowledgeServiceFactory.


        return new BaseResp<>();

    }

    public BaseResp<List<Long>> authed(Integer type, Integer from, Integer visibleType) {
        boolean needAuth = !KnowledgeTypeEnum.SENSITIVE.getType().equals(type) && !KnowledgeTypeEnum.PRO.getType().equals(type) && ResourceVisibleTypeEnum.PUBLIC.getValue().equals(visibleType);
        List<KnowledgeGroupEntity> groupList = knowledgeGroupService.listAll(getTenantId(), getUserCode(), type, visibleType.equals(-1) ? null : visibleType);
        List<Long> list;
        if (visibleType.equals(-1)) {
            list = groupList.stream().filter(x -> x.getVisibleType().equals(ResourceVisibleTypeEnum.PERSONAL.getValue()) && x.getCreatorId().equals(getUserCode())).map(KnowledgeGroupEntity::getId).collect(Collectors.toList());
            List<KnowledgeGroupTreeNodeEntityResp> allNodes = flatTree(buildTreeAuthedRoot(groupList.stream().filter(x -> x.getVisibleType().equals(ResourceVisibleTypeEnum.PUBLIC.getValue())).collect(Collectors.toList())));
            List<Long> publicIds = allNodes.stream().filter(a -> CollectionUtils.isNotEmpty(a.getAuthCodes())).map(KnowledgeGroupTreeNodeEntityResp::getId).collect(Collectors.toList());
            list.addAll(publicIds);
        } else {
            if (!needAuth || (Objects.nonNull(from) && 1 == from)) {
                list = groupList.stream().filter(x -> x.getVisibleType().equals(ResourceVisibleTypeEnum.PERSONAL.getValue()) && x.getCreatorId().equals(getUserCode())).map(KnowledgeGroupEntity::getId).collect(Collectors.toList());
                list.addAll(groupList.stream().filter(x -> x.getVisibleType().equals(ResourceVisibleTypeEnum.PUBLIC.getValue())).map(KnowledgeGroupEntity::getId).collect(Collectors.toList()));
            } else {
                List<KnowledgeGroupTreeNodeEntityResp> allNodes = flatTree(buildTreeAuthedRoot(groupList));
                list = allNodes.stream().filter(a -> CollectionUtils.isNotEmpty(a.getAuthCodes())).map(KnowledgeGroupTreeNodeEntityResp::getId).collect(Collectors.toList());
            }
        }
        return new BaseResp<>(list);
    }

    private List<KnowledgeGroupTreeNodeEntityResp> buildTree(List<KnowledgeGroupEntity> groups) {
        List<KnowledgeGroupTreeNodeEntityResp> tree = new ArrayList<>();
        List<String> allCodes = Arrays.stream(AuthNodeEnum.values()).map(Enum::toString).collect(Collectors.toList());
        Map<Long, List<KnowledgeGroupEntity>> groupMap = groups.stream().collect(Collectors.groupingBy(KnowledgeGroupEntity::getParentId));
        if (!groupMap.containsKey(KnowledgeGroupEntity.ROOT_ID)) {
            return new ArrayList<>();
        }
        groupMap.get(KnowledgeGroupEntity.ROOT_ID).forEach(group -> {
            KnowledgeGroupTreeNodeEntityResp node = KnowledgeGroupConvert.INSTANCE.toTreeNode(group);
            node.setLevel(0); // 根节点层级设为0
            node.setLibId(group.getId());
            node.setAuthCodes(allCodes);
            List<KnowledgeGroupTreeNodeEntityResp> childes = buildTreeChildren(groupMap, node, 1, node.getLibId());
            node.setChildren(childes);
            tree.add(node);
        });
        return tree.stream().sorted(Comparator.comparing(KnowledgeGroupTreeNodeEntityResp::getSort)
                        .thenComparing(KnowledgeGroupTreeNodeEntityResp::getCreateTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }

    private List<KnowledgeGroupTreeNodeEntityResp> buildTreeChildren(Map<Long, List<KnowledgeGroupEntity>> groupMap, KnowledgeGroupTreeNodeEntityResp parent, int level, Long libId) {
        List<KnowledgeGroupTreeNodeEntityResp> curNodes = Collections.synchronizedList(new ArrayList<>());
        if (!groupMap.containsKey(parent.getId())) {
            return curNodes;
        }
        groupMap.get(parent.getId()).parallelStream().forEach(group -> {
            KnowledgeGroupTreeNodeEntityResp node = KnowledgeGroupConvert.INSTANCE.toTreeNode(group);
            node.setLevel(level);
            node.setLibId(libId);
            node.setIsSync(false);
            node.setAuthCodes(parent.getAuthCodes());
            node.setApproveProcessKey(parent.getApproveProcessKey());
            List<KnowledgeGroupTreeNodeEntityResp> childes = buildTreeChildren(groupMap, node, level + 1, libId);
            node.setChildren(childes);
            curNodes.add(node);
        });
        return curNodes.stream().sorted(Comparator.comparing(KnowledgeGroupTreeNodeEntityResp::getSort)
                        .thenComparing(KnowledgeGroupTreeNodeEntityResp::getCreateTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }

    /**
     * 构建树，并验证过滤无权限目录
     *
     * @param groups 所有分组列表
     * @return 目录树
     */
    private List<KnowledgeGroupTreeNodeEntityResp> buildTreeAuthedRoot(List<KnowledgeGroupEntity> groups) {
        List<KnowledgeGroupTreeNodeEntityResp> tree = new ArrayList<>();
        //遍历所有根节点
        String tenantId = getTenantId();
        String userCode = getUserCode();
        List<String> departmentCodes = getDepartmentCodes();
        boolean isManager = iUserCenterService.isManager(getToken(), tenantId, userCode);
        //查询所有授权目录
        List<AuthEntity> auths = iAuthService.listAuth(tenantId, userCode, departmentCodes, null, SourceTypeEnum.Group.getValue());
        Map<Integer, List<String>> authLevelMap = iAuthLevelService.mapCodes(tenantId);
        List<String> syncCodes = AuthNodeEnum.syncCodes();
        Map<Long, List<KnowledgeGroupEntity>> groupMap = groups.stream().collect(Collectors.groupingBy(KnowledgeGroupEntity::getParentId));
        if (!groupMap.containsKey(KnowledgeGroupEntity.ROOT_ID)) {
            return new ArrayList<>();
        }
        groupMap.get(KnowledgeGroupEntity.ROOT_ID).forEach(group -> {
            AuthAssModel authAss = new AuthAssModel(tenantId, userCode, departmentCodes, group);
            authAss.setAllAuths(auths);
            authAss.setIsManager(isManager);
            iAuthService.getAuthNew(authAss);
            KnowledgeGroupTreeNodeEntityResp node = KnowledgeGroupConvert.INSTANCE.toTreeNode(group);
            node.setChildren(new ArrayList<>());
            node.setAuthCodes(authAss.getAuthedCodes(authLevelMap, node.getIsSync() ? syncCodes : null));
            node.setLevel(0);
            node.setAuthLevel(authAss.getAuth().getAuthLevel());
            node.setLibId(node.getId());
            //父目录无权限 不显示子目录
            if (authAss.getAuth().getAuthLevel().equals(AuthLevelEnum.NoAuth.getValue())) {
                return;
            }
            List<KnowledgeGroupTreeNodeEntityResp> childes = buildTreeAuthedChildren(groupMap, 1, authAss, authLevelMap, syncCodes);
//            if (CollectionUtils.isEmpty(node.getAuthCodes()) && CollectionUtils.isEmpty(childes)) {
//                return;
//            }
            node.setChildren(childes);
            tree.add(node);

        });
        return tree.stream().sorted(Comparator.comparing(KnowledgeGroupTreeNodeEntityResp::getSort)
                        .thenComparing(KnowledgeGroupTreeNodeEntityResp::getCreateTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }

    /**
     * 构建授权的子目录
     *
     * @param groupMap      全目录
     * @param level         层级
     * @param parentAuthAss 父级权限
     * @return 有权限的目录
     */
    private List<KnowledgeGroupTreeNodeEntityResp> buildTreeAuthedChildren(Map<Long, List<KnowledgeGroupEntity>> groupMap, int level, AuthAssModel parentAuthAss, Map<Integer, List<String>> authLevelMap, List<String> syncCodes) {
        List<KnowledgeGroupTreeNodeEntityResp> curNodes = Collections.synchronizedList(new ArrayList<>());
        if (!groupMap.containsKey(parentAuthAss.getGroup().getId())) {
            return curNodes;
        }

        groupMap.get(parentAuthAss.getGroup().getId()).parallelStream().forEach(group -> {
            AuthAssModel authAss = new AuthAssModel(parentAuthAss, group);
            iAuthService.getAuthNew(authAss);
            KnowledgeGroupTreeNodeEntityResp node = KnowledgeGroupConvert.INSTANCE.toTreeNode(group);
            node.setChildren(new ArrayList<>());
            node.setLevel(level);
            node.setLibId(authAss.getRoot().getId());
            node.setAuthCodes(authAss.getAuthedCodes(authLevelMap, node.getIsSync() ? syncCodes : null));
            node.setAuthLevel(authAss.getAuth().getAuthLevel());
            node.setIsSync(false);
            node.setApproveProcessKey(parentAuthAss.getRoot().getApproveProcessKey());
            if (authAss.getAuth().getAuthLevel().equals(AuthLevelEnum.NoAuth.getValue())) {
                return;
            }
            List<KnowledgeGroupTreeNodeEntityResp> childes = buildTreeAuthedChildren(groupMap, level + 1, authAss, authLevelMap, syncCodes);
//            if (authAss.getAuth().getAuthLevel().equals(AuthLevelEnum.NoAuth.getValue()) && CollectionUtils.isEmpty(childes)) {
//                return;
//            }
            if ((groupMap.containsKey(node.getId()) && groupMap.get(node.getId()).size() != childes.size()) ||
                    childes.stream().anyMatch(n -> !n.getAuthCodes().contains(AuthNodeEnum.GROUP_MOVE.toString()))) {
                node.getAuthCodes().remove(AuthNodeEnum.GROUP_MOVE.toString());
            }
            node.setChildren(childes);
            curNodes.add(node);
        });
        return curNodes.stream().sorted(Comparator.comparing(KnowledgeGroupTreeNodeEntityResp::getCreateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 更新目录
     *
     * @param req 请求
     */
    public void update(KnowledgeGroupUpdateReq req) {
        KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(req.getId());
        knowledgeServiceFactory.checkUpdateAuth(group);
        if (group.getIsLibrary()) {
            updateLib(group, req);
        } else {
            if (Boolean.TRUE.equals(group.getIsSync())) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_EDIT, "同步库目录");
            }
            GroupFolderSaveDTO saveDTO = KnowledgeGroupConvert.INSTANCE.updateReqToDto(req, UserContext.getUser());
            knowledgeGroupService.update(saveDTO);
        }
    }

    /**
     * 更新库
     *
     * @param libEntity
     * @param req
     */
    private void updateLib(KnowledgeGroupEntity libEntity, KnowledgeGroupUpdateReq req) {
        // 敏感词库和专业词库同租户下不允许重名
        if (KnowledgeTypeEnum.SENSITIVE.getType().equals(libEntity.getType()) || KnowledgeTypeEnum.PRO.getType().equals(libEntity.getType())) {
            KnowledgeGroupEntity group = knowledgeGroupService.getLibByTenantIdAndTypeAndName(getTenantId(), libEntity.getType(), req.getName());
            if (Objects.nonNull(group) && !group.getId().equals(req.getId())) {
                throw new ServiceException(GROUP_NAME_DUPLICATE);
            }
        }
        //更新子目录的重要等级
        if (!libEntity.getImportantLevel().equals(req.getImportantLevel())) {
            knowledgeGroupService.updateImportLevelByPath(libEntity.getSearchPath(), req.getImportantLevel());
        }
        libEntity.setSort((double) req.getSort());
        libEntity.setName(req.getName());
        libEntity.setDescription(req.getDescription());
        libEntity.setLogo(req.getLogo());
        libEntity.setUpdateTime(LocalDateTime.now());
        libEntity.setUpdateId(getUserCode());
        libEntity.setImportantLevel(req.getImportantLevel());
        knowledgeGroupService.saveOrUpdate(libEntity);
    }

    /**
     * 删库库/目录
     *
     * @param id
     */

    public void delete(Long id) {
        KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(id);
        knowledgeServiceFactory.checkDeleteAuth(group);
        if (group.getIsLibrary()) {
            // 同步文件库，在知识中心删除时，同步删除同步任务
            if (group.getIsSync()) {
                if (!fileScanClient.removeTask(Collections.singletonList(group.getId())).isSuccess()) {
                    throw new ServiceException(KnowledgeCenterErrorCodeEnum.SYNC_TASK_DELETE);
                }
            }
        } else if (group.getIsSync()) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_DELETE, "同步库目录");
        }
        if (group.getType().equals(KnowledgeTypeEnum.FILE.getType())) {
            if (group.getIsLibrary()) {
                operationLogProxyService.deleteFileLibRecord(group.getId(), group.getName(), ResourceVisibleTypeEnum.getName(group.getVisibleType()));
            } else {
                operationLogProxyService.deleteFileFolderRecord(group.getId(), group.getName(), ResourceVisibleTypeEnum.getName(group.getVisibleType()));
            }
        }
        knowledgeServiceFactory.delete(getTenantId(), getUserCode(), group.getId());
    }

    /**
     * 过滤无权限或已删除目录
     * mediaManage使用
     *
     * @param req 请求
     * @return 目录id
     */
    public BaseResp<List<Long>> filterAvailable(FilterAvailableReq req) {
        if (Boolean.TRUE.equals(req.getCheckAuth())) {
            List<AuthAssModel> authAssModelList = knowledgeGroupFactory.listAuthedByGroupId(getTenantId(), getUserCode(), getDepartmentCodes(), getToken(), req.getIds(), false, false);
            return new BaseResp<>(authAssModelList.stream().map(a -> a.getGroup().getId()).collect(Collectors.toList()));
        } else {
            List<KnowledgeGroupEntity> groups = knowledgeGroupService.listByIds(req.getIds());
            return new BaseResp<>(groups.stream().filter(g -> !g.getDeleted()).map(BaseEntity::getId).collect(Collectors.toList()));
        }
    }


    /**
     * 过滤无权限或已删除分组
     *
     * @param ids 分组id
     * @return 有权限的分组
     */
    public List<Long> authFilter(List<Long> ids, Integer type) {
        List<Long> ret = new ArrayList<>();
        //todo 菜单权限校验
        List<KnowledgeGroupEntity> groups = knowledgeGroupService.listByIds(ids);
        ids.forEach(id -> {
            if (id.equals(0L)) {
                ret.add(id);
                return;
            }
            Optional<KnowledgeGroupEntity> opGroup = groups.stream().filter(g -> id.equals(g.getId())).findFirst();
            if (!opGroup.isPresent())
                return;
            KnowledgeGroupEntity g = opGroup.get();
            if (!KnowledgeTypeEnum.SENSITIVE.getType().equals(g.getType()) && !KnowledgeTypeEnum.PRO.getType().equals(g.getType())) {
                AuthAssModel authAss = new AuthAssModel(getTenantId(), getUserCode(), getDepartmentCodes(), g);
                iAuthService.getAuthNew(authAss);
                if (authAss.getAuth().getAuthLevel() >= AuthLevelEnum.View.getValue()) {
                    ret.add(g.getId());
                }
            }
        });
        return ret;
    }

    /**
     * 移动分组
     *
     * @param req 请求
     * @return
     */
    public BaseResp<Void> move(MoveReq req) {
        if (req.getTargetId().equals(req.getId())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "指定目录");
        }
        KnowledgeGroupEntity source = knowledgeGroupService.getNotNull(req.getId());
        KnowledgeGroupEntity target = knowledgeGroupService.getNotNull(req.getTargetId());
        if (source.getParentId().equals(0L)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "库目录");
        }
        if (req.getTargetId().equals(source.getParentId())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录与当前目录相同,");
        }
        List<KnowledgeGroupEntity> childList = knowledgeGroupService.listByPath(source.getSearchPath());
        //不允许移动到自己的子目录
        if (childList.stream().anyMatch(c -> c.getId().equals(req.getTargetId()))) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录是当前子目录,");
        }
        if (target.getIsSync()) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录是同步目录,");
        }
        if (source.getIsSync()) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "资源所在目录是同步目录,");
        }
        if (source.getVisibleType() > target.getVisibleType()) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录可见等级不足,");
        }
        if (source.getImportantLevel() > target.getImportantLevel()) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录重要等级不足,");
        }
        //如果存在从个人空间移动到分组空间，需要校验分组空间是否已满
        if (ResourceVisibleTypeEnum.PUBLIC.getValue().equals(target.getVisibleType()) && ResourceVisibleTypeEnum.PERSONAL.getValue().equals(source.getVisibleType())) {
            //查询所有子目录
            List<KnowledgeGroupEntity> subGroups = knowledgeGroupService.listByPath(source.getSearchPath());
            //统计当前目录和子目录下的资源大小
            List<Long> groupIds = subGroups.stream().map(KnowledgeGroupEntity::getId).collect(Collectors.toList());
            groupIds.add(source.getId());
            Long size = iResourceService.sumSizeByGroupId(groupIds);
            if (size > 0) {
                IKnowPowerResp iKnowPowerResp = iUserCenterService.getIknowPower(getTenantId());
                if (Objects.nonNull(iKnowPowerResp.getOrgSpace()) && iKnowPowerResp.getOrgSpace() > 0) {
                    Long used = iResourceService.sumSizeByVisibleType(getTenantId(), null, ResourceVisibleTypeEnum.PUBLIC);
                    if (used + size > iKnowPowerResp.getOrgSpace()) {
                        throw new ServiceException(RESOURCE_SIZE_LIMIT);
                    }
                }
            }
        }


        knowledgeServiceFactory.moveToAuthCheck(source, childList, target);

        List<KnowledgeGroupEntity> updateList = new ArrayList<>();
        childList.forEach(child -> {
            child.setPath(child.getPath().replace(source.getPath(), target.getSearchPath()));
            child.setImportantLevel(target.getImportantLevel());
            child.setVisibleType(target.getVisibleType());
            updateList.add(child);
        });
        source.setImportantLevel(target.getImportantLevel());
        source.setVisibleType(target.getVisibleType());
        source.setParentId(target.getId());
        source.setPath(target.getSearchPath());
        updateList.add(source);
        knowledgeGroupService.updateBatch(updateList);
        operationLogProxyService.moveFolderRecord(source, target);
        return new BaseResp<>();
    }

    public Long create(GroupCreateRpcReq req) {
        KnowledgeGroupEntity parent = knowledgeGroupService.get(req.getLibId());
        if (Objects.isNull(parent)) {
            throw new ServiceException(NOT_EXIST, "知识库");
        }
        StringBuilder pathBuilder = new StringBuilder("/");
        for (String folderName : req.getPath().split("/")) {
            if (StringUtils.isBlank(folderName)) {
                continue;
            }
            Long parentId = parent.getId();
            pathBuilder.append(parentId).append("/");
            parent = knowledgeGroupService.get(parentId, folderName);
            if (Objects.isNull(parent)) {
                parent = new KnowledgeGroupEntity();
                parent.setName(folderName);
                parent.setType(KnowledgeTypeEnum.FILE.getType());
                parent.setParentId(parentId);
                parent.setDeleted(false);
                parent.setSort(0.0);
                parent.setIsSync(true);
                parent.setUpdateTime(LocalDateTime.now());
                parent.setUpdateId(req.getUserCode());
                parent.setCreatorId(req.getUserCode());
                parent.setCreateTime(LocalDateTime.now());
                parent.setTenantId(req.getTenantId());
                parent.setPath(pathBuilder.toString());
                knowledgeGroupService.saveOrUpdate(parent);
            }
        }
//        if (req.getProcessFlow() != null) {
//            TaskEntity taskEntity = new TaskEntity();
//            taskEntity.init(req.getTenantId(), req.getUserCode());
//            taskEntity.setGroupId(parent.getId());
//            taskEntity.setType(VideoMerge.getValue());
//            taskEntity.setReadStatus(0);
//            taskEntity.setKey(null);
//            taskEntity.setStatus(ProcessEnum.Waiting.getValue());
//            taskEntity.setContent(Objects.isNull(req.getExtInfo()) ? "{}" : JSON.toJSONString(req.getExtInfo()));
//            iTaskService.save(taskEntity);
//        }
        return parent.getId();
    }

    public void delete(GroupCreateRpcReq req) {
        KnowledgeGroupEntity library = knowledgeGroupService.get(req.getLibId());
        if (library != null) {
            KnowledgeGroupEntity groupEntity = null;
            for (String groupName : req.getPath().split("/")) {
                if (StringUtils.isBlank(groupName)) {
                    continue;
                }
                groupEntity = knowledgeGroupService.get(groupEntity == null ? library.getId() : groupEntity.getId(), groupName);
            }
            if (groupEntity != null) {
                knowledgeServiceFactory.delete(req.getTenantId(), library.getCreatorId(), groupEntity.getId());
            }
        }
    }

    public List<Long> clearEmpty(Integer type) {
        List<KnowledgeGroupEntity> groups = knowledgeGroupService.listAll(getTenantId(), getUserCode(), type, ResourceVisibleTypeEnum.PUBLIC.getValue());
        Map<Long, List<KnowledgeGroupEntity>> groupMap = groups.stream().collect(Collectors.groupingBy(KnowledgeGroupEntity::getParentId));
        if (!groupMap.containsKey(KnowledgeGroupEntity.ROOT_ID)) {
            return Collections.emptyList();
        }
        List<Long> needDeleteIds = new ArrayList<>();
        groupMap.get(KnowledgeGroupEntity.ROOT_ID).forEach(group -> {
            List<Long> subNeedDeletedIds = clearEmpty(groupMap, group.getId());
            needDeleteIds.addAll(subNeedDeletedIds);
        });

        knowledgeGroupService.logicDeleteBatch(needDeleteIds);

        return needDeleteIds;

    }

    private List<Long> clearEmpty(Map<Long, List<KnowledgeGroupEntity>> groups, Long parentId) {
        List<Long> needDeleteIds = new ArrayList<>();
        if (!groups.containsKey(parentId)) {
            Long count = iResourceService.getCount(parentId);
            if (count < 1) {
                needDeleteIds.add(parentId);
            }
        } else {
            List<KnowledgeGroupEntity> subGroups = groups.get(parentId);
            subGroups.forEach(group -> {
                List<Long> subNeedDeletedIds = clearEmpty(groups, group.getId());
                needDeleteIds.addAll(subNeedDeletedIds);
            });
            List<Long> ids = subGroups.stream().map(KnowledgeGroupEntity::getId).collect(Collectors.toList());
            if (needDeleteIds.containsAll(ids)) {
                Long count = iResourceService.getCount(parentId);
                if (count < 1) {
                    needDeleteIds.add(parentId);
                }
            }
        }
        return needDeleteIds;


    }

    /**
     * 获取跟人空间根目录
     *
     * @param type 类型
     * @return 目录列表
     */
    public List<KnowledgeGroupTreeNodeEntityResp> listPersonalLib(Integer type) {

        if (!iUserCenterService.hasAuth(getToken(), getTenantId(), getUserCode(), MenuKeyEnum.RESOURCE_PERSONAL_SPACE, false)) {
            return Collections.emptyList();
        }

        List<KnowledgeGroupEntity> groupList = knowledgeGroupService.list(getTenantId(), getUserCode(), type, 0L, ResourceVisibleTypeEnum.PERSONAL.getValue());
        groupList = groupList.stream().sorted(Comparator.comparing(KnowledgeGroupEntity::getCreateTime).reversed()).collect(Collectors.toList());
        return KnowledgeGroupConvert.INSTANCE.toTreeNode(groupList);
    }

    /**
     * 查询有权限的目录列表
     *
     * @param req 请求参数
     * @return 目录列表
     */
    public BaseResp<List<KnowledgeGroupTreeNodeEntityResp>> authedList(KnowledgeGroupListReq req) {
        Integer visibleType = ResourceVisibleTypeEnum.PUBLIC.getValue();
        List<KnowledgeGroupTreeNodeEntityResp> respList = new ArrayList<>();
        String tenantId = getTenantId();
        String userCode = getUserCode();
        List<String> departmentCodes = getDepartmentCodes();
        boolean isManager = iUserCenterService.isManager(getToken(), tenantId, userCode);
        Map<Integer, List<String>> authLevelMap = iAuthLevelService.mapCodes(tenantId);
        List<String> syncCodes = AuthNodeEnum.syncCodes();
        KnowledgeGroupEntity parent = null;
        AuthAssModel parentAuthAss;
        //验证父目录权限
        if (req.getParentId() != KnowledgeGroupEntity.ROOT_ID) {
            parent = knowledgeGroupService.getNotNull(req.getParentId());
            parentAuthAss = new AuthAssModel(tenantId, userCode, departmentCodes, parent);
            parentAuthAss.setIsManager(isManager);
            iAuthService.getAuthNew(parentAuthAss);
            if (AuthLevelEnum.NoAuth.getValue().equals(parentAuthAss.getAuth().getAuthLevel())) {
                throw new ServiceException(NO_AUTH, "目录");
            }
            visibleType = parent.getVisibleType();
        } else {
            parentAuthAss = null;
        }

        //只有公共库且是（文件库,表格库,FAQ库）才需要鉴权
        boolean needAuth = !KnowledgeTypeEnum.SENSITIVE.getType().equals(req.getType()) && !KnowledgeTypeEnum.PRO.getType().equals(req.getType())
                && ResourceVisibleTypeEnum.PUBLIC.getValue().equals(visibleType);
        if (needAuth) {
            List<KnowledgeGroupEntity> subGroups = knowledgeGroupService.list(tenantId, null, KnowledgeTypeEnum.FILE.getType(), req.getParentId(), visibleType);
            //获取所有子目录的直接授权
            List<AuthEntity> auths = iAuthService.listBySourceIds(subGroups.stream().map(g -> g.getId().toString()).collect(Collectors.toList()), SourceTypeEnum.Group.getValue());
            List<KnowledgeGroupTreeNodeEntityResp> finalRespList = respList;
            subGroups.stream().forEach(group -> {
                KnowledgeGroupTreeNodeEntityResp node = KnowledgeGroupConvert.INSTANCE.toTreeNode(group);
                if (group.getBizType().equals(2)) {
                    finalRespList.add(node);
                    return;
                }
                AuthAssModel authAss;
                if (Objects.nonNull(parentAuthAss)) {
                    authAss = new AuthAssModel(parentAuthAss, group);
                } else {
                    authAss = new AuthAssModel(tenantId, userCode, departmentCodes, group);
                }
                authAss.setAllAuths(auths);
                authAss.setIsManager(isManager);
                iAuthService.getAuthNew(authAss);

                node.setChildren(new ArrayList<>());
                node.setAuthCodes(authAss.getAuthedCodes(authLevelMap, node.getIsSync() ? syncCodes : null));
                node.setLevel(0);
                node.setAuthLevel(authAss.getAuth().getAuthLevel());
                node.setLibId(node.getId());
                if (node.getAuthLevel() > 0) {
                    finalRespList.add(node);
                }
            });

        } else {
            List<KnowledgeGroupEntity> subGroups = knowledgeGroupService.list(tenantId, userCode, KnowledgeTypeEnum.FILE.getType(), req.getParentId(), null);
            respList = KnowledgeGroupConvert.INSTANCE.toTreeNode(subGroups);
            respList.forEach(node -> {
                node.setAuthLevel(AuthLevelEnum.Manage.getValue());
            });
        }
        respList = respList.stream().sorted(Comparator.comparing(KnowledgeGroupTreeNodeEntityResp::getCreateTime).reversed()).collect(Collectors.toList());
        //库查询我的文件
        if (req.getParentId() == KnowledgeGroupEntity.ROOT_ID) {
            KnowledgeGroupEntity group = knowledgeGroupService.getOrCreateByBiz(tenantId, userCode, 2);
            respList.add(0, KnowledgeGroupConvert.INSTANCE.toTreeNode(group));
        }

        return new BaseResp<>(respList);
    }
}
