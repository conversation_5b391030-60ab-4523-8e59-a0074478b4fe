package com.linker.fusion.knowledgecenter.server.convert;

import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.DocPageReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.DocInfoEntityResp;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.DocPageDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DocConvert {

    DocConvert INSTANCE = Mappers.getMapper(DocConvert.class);

    DocPageDTO toDTO(DocPageReq req);

    List<DocInfoEntityResp> toResp(List<KnowledgeResourceEntity> resourceEntityList);

    @Mapping(target = "message", source = "handleFailReason.message")
    DocInfoEntityResp toResp(KnowledgeResourceEntity resource);
}
