package com.linker.fusion.knowledgecenter.server.controller.rpc;

import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.server.biz.GroupBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.group.GroupCreateRpcReq;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.bson.types.ObjectId;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Api(tags = "RPC/知识目录")
@RestController
@RequestMapping("/rpc/folder")
public class RpcFolderController {

    @Resource
    private GroupBiz groupBiz;

    @PostMapping("/create")
    @ApiOperation(value = "创建目录")
    public BaseResp<Long> create(@RequestBody @Valid GroupCreateRpcReq req) {
        return new BaseResp<>(groupBiz.create(req));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除目录")
    public BaseResp<Void> delete(@RequestBody @Valid GroupCreateRpcReq req) {
        groupBiz.delete(req);
        return new BaseResp<>();
    }
    @GetMapping("mongo/test")
    public BaseResp<Void> mongoTest(){
        ObjectId objectId = ObjectId.get();
        objectId.toHexString();
        MongoClient mongoClient = MongoClients.create("**********************************************");
        mongoClient.close();
        return new BaseResp<>();
    }
}
