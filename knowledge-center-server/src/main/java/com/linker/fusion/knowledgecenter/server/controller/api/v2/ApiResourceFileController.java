package com.linker.fusion.knowledgecenter.server.controller.api.v2;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.server.biz.GroupBiz;
import com.linker.fusion.knowledgecenter.server.dto.api.v1.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.linker.core.auth.enums.InterfaceType.TOC;

/**
 * <AUTHOR>
 */
@Api(tags = "文件管理")
@RestController
@RequestMapping("api/knowledge/v1")
public class ApiResourceFileController {
    @Resource
    private GroupBiz knowledgeGroupBiz;


    @PostMapping("folders/create")
    @ApiOperation(value = "新建文件夹")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Long> createFolders(@RequestBody @Validated ApiKnowledgeFolderCreateReq req) {
//        return new BaseResp<>(knowledgeGroupBiz.create(req));
        return null;
    }

    @PostMapping("folders/update")
    @ApiOperation(value = "修改文件夹")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> updateFolders(@RequestBody @Validated ApiKnowledgeGroupUpdateReq req) {
//        knowledgeGroupBiz.update(req);
        return new BaseResp<>();
    }

    @PostMapping("folders/delete")
    @ApiOperation(value = "删除文件夹")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> deleteFolders(@RequestBody @Validated FolderIdRequest req) {
//        knowledgeGroupBiz.delete(req.getId());
        return new BaseResp<>();
    }

    @PostMapping("folders/List")
    @ApiOperation(value = "查询文件夹列表")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<ApiKnowledgeLibResp> foldersList(ApiKnowledgeGroupListReq req) {
        return null;
    }

    @PostMapping("folders/info")
    @ApiOperation(value = "文件夹详情")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<ApiKnowledgeLibResp> foldersInfo(@RequestBody @Validated FolderIdRequest req) {
        return null;
    }

    @PostMapping("resource/import")
    @ApiOperation(value = "文件入库")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<ApiImportFileResp> importDoc(@RequestBody @Validated ApiImportResourceReq req) {
        return null;
    }


    @PostMapping("/resource/task/batch-redo")
    @ApiOperation(value = "文件重新解析")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> redoBatch(@RequestBody @Validated ResourceIdsReq req) {
//        docBiz.batchRedo(req);
        return new BaseResp<>();
    }

    @PostMapping("/resource/list")
    @ApiOperation(value = "查询文件列表")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BasePaginResp<ApiV1ResourceInfoResp> resourceList(@RequestBody @Validated ResourcePageReq req) {
//        docBiz.batchRedo(req);
        return null;
    }

    @PostMapping("/resource/info")
    @ApiOperation(value = "文件详情")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<ApiV1ResourceInfoResp> resourceInfo(@RequestBody @Validated ResourceIdReq req) {
//        docBiz.batchRedo(req);
        return new BaseResp<>();
    }

    @PostMapping("/resource/update")
    @ApiOperation(value = "更新文件信息", tags = "api")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> updateResource(@RequestBody @Validated ApiUpdateResourceInfoReq req) {
//        docBiz.batchRedo(req);
        return new BaseResp<>();
    }

    @PostMapping("/resource/delete")
    @ApiOperation(value = "文件删除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> delete(@RequestBody @Validated ResourceIdsReq req) {
        return null;
    }

}
