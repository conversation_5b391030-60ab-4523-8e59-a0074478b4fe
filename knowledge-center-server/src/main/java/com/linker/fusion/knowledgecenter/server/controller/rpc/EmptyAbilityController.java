
package com.linker.fusion.knowledgecenter.server.controller.rpc;

import com.linker.fusion.knowledgecenter.server.dto.req.rpc.ASRReq;
import com.linker.fusion.knowledgecenter.server.dto.req.rpc.ASRResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "能力空实现")
@RestController
@RequestMapping("/empty/ability")
public class EmptyAbilityController {

    @ApiOperation(value = "ASR")
    @PostMapping("/asr")
    public ASRResp importFile(@RequestBody ASRReq req) {
        return new ASRResp();
    }
}
