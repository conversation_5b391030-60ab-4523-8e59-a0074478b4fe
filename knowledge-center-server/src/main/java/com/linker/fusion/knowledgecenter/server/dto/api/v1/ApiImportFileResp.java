package com.linker.fusion.knowledgecenter.server.dto.api.v1;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.linker.fusion.knowledgecenter.server.dto.resp.DocInfoEntityResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class ApiImportFileResp {
    @ApiModelProperty("资源Id")
    @JsonProperty("resource_id")
    private String resourceId;
    @ApiModelProperty("任务Id")
    @JsonProperty("task_id")
    private String taskId;

    public ApiImportFileResp(DocInfoEntityResp resp) {
        resourceId = resp.getDocId();
        taskId = resp.getWorkflowId();
    }
}
