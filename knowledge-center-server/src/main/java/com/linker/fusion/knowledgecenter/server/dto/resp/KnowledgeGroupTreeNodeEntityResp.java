package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.model.AuthActionModel;
import com.linker.fusion.knowledgecenter.server.convert.KnowledgeGroupConvert;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.BaseEntityResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class KnowledgeGroupTreeNodeEntityResp extends BaseEntityResp {

    @ApiModelProperty(value = "所属知识库ID")
    private Long libId;

    // 前端要求
    @JsonProperty("key")
    private Long id;
    @ApiModelProperty("父级目录id")
    private Long parentId;

    // 前端要求
    @JsonProperty("title")
    private String name;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("图标")
    private String logo;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

    @ApiModelProperty("是否是同步库")
    private Boolean isSync;

    private Double sort;

    private Integer type;

    @ApiModelProperty("节点层级")
    private Integer level;
    @ApiModelProperty("授权节点列表")
    private List<String> authCodes;
    @ApiModelProperty("授权信息")
    private AuthActionModel authActions;
    @ApiModelProperty("权限等级")
    private Integer authLevel;
    @ApiModelProperty("1 重要")
    private Integer importantLevel;
    @ApiModelProperty("可见范围")
    private Integer visibleType;

    @ApiModelProperty("审批流程key")
    private String approveProcessKey;

    private List<KnowledgeGroupTreeNodeEntityResp> children;
    @ApiModelProperty("任务信息")
    private TaskEntity task;

    @ApiModelProperty("申请中")
    private Boolean applying;
    @ApiModelProperty("业务类型  1 会话")
    private Integer bizType;

    public static List<KnowledgeGroupTreeNodeEntityResp> toNodes(List<KnowledgeGroupEntity> groups) {
        List<KnowledgeGroupTreeNodeEntityResp> nodes = new ArrayList<>();
        for (KnowledgeGroupEntity entity : groups) {
            KnowledgeGroupTreeNodeEntityResp node = KnowledgeGroupConvert.INSTANCE.toTreeNode(entity);
            node.setChildren(new ArrayList<>());
            nodes.add(node);
        }
        return nodes;
    }
}
