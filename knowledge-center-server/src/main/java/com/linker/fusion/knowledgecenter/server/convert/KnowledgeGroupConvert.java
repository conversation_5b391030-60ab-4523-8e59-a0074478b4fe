package com.linker.fusion.knowledgecenter.server.convert;

import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.server.dto.req.group.KnowledgeGroupCreateReq;
import com.linker.fusion.knowledgecenter.server.dto.req.group.KnowledgeGroupUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.KnowledgeGroupTreeNodeEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.LibraryResp;
import com.linker.fusion.knowledgecenter.service.domain.group.model.GroupFolderSaveDTO;
import com.linker.user.api.dto.UserInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface KnowledgeGroupConvert {

    KnowledgeGroupConvert INSTANCE = Mappers.getMapper(KnowledgeGroupConvert.class);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId"),
            @Mapping(source = "userInfo.user.userCode", target = "userId"),
    })
    GroupFolderSaveDTO createReqToDto(KnowledgeGroupCreateReq req, UserInfo userInfo);

    @Mappings({
            @Mapping(source = "userInfo.tenantInfoDTO.tenantId", target = "tenantId"),
            @Mapping(source = "userInfo.user.userCode", target = "userId"),
    })
    GroupFolderSaveDTO updateReqToDto(KnowledgeGroupUpdateReq req, UserInfo userInfo);

    KnowledgeGroupTreeNodeEntityResp toTreeNode(KnowledgeGroupEntity group);

    List<KnowledgeGroupTreeNodeEntityResp> toTreeNode(List<KnowledgeGroupEntity> groupEntityList);

    LibraryResp toLibResp(KnowledgeGroupEntity group);
}
