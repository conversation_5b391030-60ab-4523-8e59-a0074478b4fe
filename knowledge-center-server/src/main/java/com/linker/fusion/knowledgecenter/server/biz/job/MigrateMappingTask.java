package com.linker.fusion.knowledgecenter.server.biz.job;

import com.linker.fusion.knowledgecenter.service.domain.job.MigrateMappingService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MigrateMappingTask {

    @Resource
    private MigrateMappingService migrateMappingService;

    @Resource
    private RedissonClient redissonClient;

    @Scheduled(initialDelay = 120, fixedDelay = Integer.MAX_VALUE)
    public void run() {
        // 文档库、FAQ、表格设置GroupId和Enable
        migrateMappingService.docAddGroupId();
        migrateMappingService.faqAddGroupId();
        migrateMappingService.tableAddGroupId();

        // 解决表格搜索要求大小写不敏感
        RLock lock = redissonClient.getLock("knowledge-center-migrate-mapping-tableReplaceRow");
        try {
            if (lock.tryLock(0, 10, TimeUnit.MINUTES)) {
                log.info("getLock success");
                try {
                    migrateMappingService.tableReplaceRow();
                } finally {
                    lock.unlock();
                }
            }
        } catch (InterruptedException e) {
            log.error("getLock error", e);
        }
        // https://zt.linker.cc/bug-view-158354.html
        migrateMappingService.fixSyncGroup();

        docSegmentToDb();
    }

    private void docSegmentToDb() {
        RLock lock = redissonClient.getLock("knowledge-center-doc-segment-to-db");
        try {
            if (lock.tryLock(0, 5, TimeUnit.MINUTES)) {
                log.info("knowledge-center-doc-segment-to-db getLock success");
                try {
                    migrateMappingService.docSegmentToDb();
                } finally {
                    lock.unlock();
                }
            }
        } catch (InterruptedException e) {
            log.error("getLock error", e);
        }
    }
}
