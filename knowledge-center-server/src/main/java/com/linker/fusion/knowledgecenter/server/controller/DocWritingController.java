package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.server.biz.DocWritingBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.ContentChatReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.ExampleItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("doc")
@Api(tags = "公文写作")
public class DocWritingController {

    @Resource
    private DocWritingBiz docWritingBiz;

    @GetMapping("/example/list")
    @ApiOperation("模板数据")
    public BaseResp<List<ExampleItem>> exampleList() {
        return new BaseResp<>(docWritingBiz.exampleList());
    }

    @PostMapping("/example/generate")
    @ApiOperation("模板生成")
    public SseEmitter exampleChat(@RequestBody @Valid ExampleItem req) {
        return docWritingBiz.exampleChat(req);
    }

    @PostMapping("/content/generate")
    @ApiOperation("内容生成")
    public SseEmitter ContentChat(@RequestBody @Valid ContentChatReq req) {
        return docWritingBiz.ContentChat(req);
    }

}
