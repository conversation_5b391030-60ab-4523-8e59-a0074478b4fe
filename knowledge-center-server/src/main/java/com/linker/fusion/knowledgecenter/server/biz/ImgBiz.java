package com.linker.fusion.knowledgecenter.server.biz;

import com.linker.fusion.knowledgecenter.server.dto.req.file.DeleteImageReq;
import com.linker.fusion.knowledgecenter.service.domain.common.IUtilService;
import com.linker.fusion.knowledgecenter.service.domain.img.ImgService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

@Service
public class ImgBiz {

    @Autowired
    private ImgService imgService;

    @Resource
    private IUtilService iUtilService;

    public String odMux(String image) {
        return imgService.odMux(image);
    }

    public List<String> mergeImages(List<String> imageUrls) throws IOException {
        return imgService.mergeImages(imageUrls);
    }

    public void delete(DeleteImageReq req) {
        if (CollectionUtils.isEmpty(req.getImages()))
            return;
        req.getImages().forEach(url -> iUtilService.deleteFile(url));
    }
}
