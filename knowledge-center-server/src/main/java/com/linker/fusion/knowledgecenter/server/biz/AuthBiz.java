package com.linker.fusion.knowledgecenter.server.biz;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.core.BaseClientResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.UserSearchByIdsReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.UserInfoResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.DepartmentReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.DepartmentResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IUserCenter;
import com.linker.fusion.knowledgecenter.infrastructure.entity.*;
import com.linker.fusion.knowledgecenter.infrastructure.enums.*;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexAuthManager;
import com.linker.fusion.knowledgecenter.server.dto.auth.*;
import com.linker.fusion.knowledgecenter.server.dto.req.group.RecoveryAuthReq;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthLevelService;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AuthBiz extends BaseBiz {
    @Resource
    IAuthService iAuthService;
    @Resource
    KnowledgeGroupService knowledgeGroupService;
    @Resource
    KnowledgeGroupFactory knowledgeGroupFactory;
    @Resource
    IResourceService iResourceService;
    @Resource
    IAuthLevelService authLevelService;
    @Resource
    IUserCenter iUserCenter;

    @Resource
    IIndexAuthManager iIndexAuthManager;

    /**
     * 添加授权
     *
     * @param req 请求
     */
    public void add(AddReq req) {
        Long parentId;
        if (SourceTypeEnum.Group.getValue().equals(req.getType())) {
            KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(req.getId());
            parentId = group.getParentId();

        } else {
            KnowledgeResourceEntity doc = iResourceService.get(req.getId());
            parentId = doc.getGroupId();
        }
        String userCode = getUserCode();
        String tenantId = getTenantId();
        List<AuthEntity> newAuths = req.getItems().stream().map(item -> new AuthEntity(tenantId, userCode, req.getId().toString(), parentId, req.getType(), item.getAuthId(), item.getType(), item.getLevel())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newAuths)) return;
        //获取继承授权
        saveExtendAuths(tenantId, userCode, req.getId().toString(), req.getType(), parentId);
        //移除相同的授权
        newAuths.forEach(auth -> {
            iAuthService.remove(auth.getSourceId(), auth.getSourceType(), auth.getAuthId(), auth.getAuthType());
        });

        // List<AuthEntity> curAuths = iAuthService.listBySourceIds(Collections.singletonList(req.getId().toString()), req.getType());
        // AuthEntity curTenantAuth = curAuths.stream().filter(a -> a.getAuthType().equals(AuthTypeEnum.Tenant.getValue())).findFirst().orElse(null);

//        //更新租户授权
//        if (newAuths.stream().anyMatch(auth -> auth.getAuthType().equals(AuthTypeEnum.Tenant.getValue())) && newAuths.size() == 1) {
//            if (Objects.isNull(curTenantAuth)) {
//                iAuthService.saveBatch(newAuths);
//            } else {
//                curTenantAuth = newAuths.get(0);
//                iAuthService.update(curTenantAuth.getSourceId(), curTenantAuth.getSourceType(), curTenantAuth.getAuthId(), curTenantAuth.getAuthType(), curTenantAuth.getAuthLevel());
//            }
//            return;
//        } else if (Objects.isNull(curTenantAuth)) {
//            curTenantAuth = new AuthEntity(tenantId, userCode, req.getId().toString(), parentId, req.getType(), tenantId, AuthTypeEnum.Tenant.getValue(), AuthLevelEnum.NoAuth.getValue());
//        }
//        //移除所有授权重新添加
//        iAuthService.remove(req.getId().toString(), req.getType());
//        newAuths.add(curTenantAuth);
        //填写新权限
        iAuthService.saveBatch(newAuths);
    }

    /**
     * 保存继承授权
     *
     * @param sourceId   授权Id
     * @param sourceType 授权类型
     * @param parentId   父级目录
     */
    private void saveExtendAuths(String tenantId, String userCode, String sourceId, Integer sourceType, Long parentId) {
        //获取当前授权
        List<AuthEntity> curAuths = iAuthService.listBySourceIds(Collections.singletonList(sourceId), sourceType);
        //获取继承授权
        if (CollectionUtils.isNotEmpty(curAuths)) {
            return;
        }
        List<AuthEntity> extendAuths = iAuthService.listAuthsByGroupId(parentId);
        //继承授权中不存在租户授权 添加租户默认权限
        if (extendAuths.stream().noneMatch(a -> a.getAuthType().equals(AuthTypeEnum.Tenant.getValue()))) {
            extendAuths.add(new AuthEntity(tenantId,
                    userCode,
                    sourceId,
                    parentId,
                    sourceType,
                    tenantId,
                    AuthTypeEnum.Tenant.getValue(),
                    AuthLevelEnum.NoAuth.getValue()));
        }
        //保存继承授权
        if (CollectionUtils.isNotEmpty(extendAuths)) {
            extendAuths.forEach(auth -> {
                auth.setId(0L);
                auth.setSourceType(sourceType);
                auth.setSourceId(sourceId);
                auth.setParentId(parentId);
            });
            iAuthService.saveOrUpdateBatch(extendAuths);
        }

    }

    /**
     * 移除授权
     *
     * @param req 请求
     */
    public void remove(RemoveReq req) {
        AuthEntity auth = authChange(getTenantId(), getUserCode(), req.getId(), req.getSourceId(), req.getSourceType());
        iAuthService.remove(req.getSourceId().toString(), req.getSourceType(), auth.getAuthId(), auth.getAuthType());
    }

    /**
     * 更新授权
     *
     * @param req 请求
     */
    public void update(String tenantId, String userCode, UpdateReq req) {
        if (Objects.isNull(req.getId())) {
            if (StringUtils.isNotBlank(req.getAuthId()) && Objects.nonNull(req.getAuthType())) {
                Long parentId;
                if (SourceTypeEnum.Group.getValue().equals(req.getSourceType())) {
                    KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(req.getSourceId());
                    parentId = group.getParentId();
                } else {
                    KnowledgeResourceEntity doc = iResourceService.get(req.getSourceId());
                    parentId = doc.getGroupId();
                }
                saveExtendAuths(tenantId, userCode, req.getSourceId().toString(), req.getSourceType(), parentId);
                AuthEntity auth = new AuthEntity(tenantId, userCode, req.getSourceId().toString(), parentId, req.getSourceType(), req.getAuthId(), req.getAuthType(), req.getLevel());
                iAuthService.saveOrUpdate(auth);
            }
        } else {
            AuthEntity auth = authChange(tenantId, userCode, req.getId(), req.getSourceId(), req.getSourceType());
            iAuthService.update(req.getSourceId().toString(), req.getSourceType(), auth.getAuthId(), auth.getAuthType(), req.getLevel());
        }
    }

    /**
     * 恢复到继承关系
     *
     * @param req 请求
     */
    public void recoveryAuth(@Valid RecoveryAuthReq req) {
        iAuthService.remove(req.getSourceId().toString(), req.getSourceType());
    }

    /**
     * 校验是否拥有权限管理权限
     *
     * @param sourceId   id
     * @param sourceType 类型
     */
    public void checkAuth(Long sourceId, Integer sourceType) {
        if (SourceTypeEnum.Group.getValue().equals(sourceType)) {
            KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(sourceId);
            knowledgeGroupFactory.checkMenuAndNodeAuth(group, OperatorTypeEnum.MANAGER, group.getParentId().equals(0L) ? AuthNodeEnum.LIBRARY_SET : AuthNodeEnum.GROUP_AUTH);
        } else {
            knowledgeGroupFactory.checkMenuAndResNodeAuth(Collections.singletonList(sourceId), KnowledgeTypeEnum.FILE, OperatorTypeEnum.AUTH, AuthNodeEnum.RESOURCE_AUTH);
        }
    }

    /**
     * 校验是否拥有权限管理权限
     *
     * @param id 权限id
     */
    public AuthEntity checkAuth(Long id) {
        AuthEntity auth = iAuthService.get(id);
        checkAuth(Long.parseLong(auth.getSourceId()), auth.getSourceType());
        return auth;
    }

    /**
     * 变更授权
     *
     * @param authId     原记录id
     * @param sourceId   资源/目录id
     * @param sourceType 类型
     * @return 授权记录
     */
    public AuthEntity authChange(String tenantId, String userCode, Long authId, Long sourceId, Integer sourceType) {
        AuthEntity auth = iAuthService.getNotNull(authId);
        Long parentId;
        if (SourceTypeEnum.Group.getValue().equals(sourceType)) {
            KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(sourceId);
            parentId = group.getParentId();
        } else {
            KnowledgeResourceEntity doc = iResourceService.get(sourceId);
            parentId = doc.getGroupId();
        }
        if (!auth.getSourceId().equals(sourceId.toString())) {
            saveExtendAuths(tenantId, userCode, sourceId.toString(), sourceType, parentId);
        }
        return auth;
    }


    /**
     * 获取权限等级列表
     *
     * @return 等级列表
     */
    public List<AuthLevelResp> listLevels() {
        List<AuthLevelEntity> list = authLevelService.list(getTenantId());
        return list.stream().map(l -> {
                    AuthLevelResp resp = new AuthLevelResp();
                    resp.setLevel(l.getLevel());
                    resp.setLevelDes(AuthLevelEnum.valueOf(l.getLevel()).getDesc());
                    resp.setCodes(JSON.parseArray(l.getCodes(), String.class));
                    resp.setName(l.getName());
                    resp.setId(l.getId());
                    return resp;
                }
        ).sorted(Comparator.comparing(AuthLevelResp::getLevel).reversed()).collect(Collectors.toList());
    }

    /**
     * 更新权限等级
     *
     * @param req 请求
     */
    public void updateAuthLevel(@Valid UpdateAuthLevelReq req) {

        AuthLevelEntity authLevelEntity = authLevelService.get(req.getId());
        if (Objects.isNull(authLevelEntity)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST);
        }
        req.setName(req.getName().trim());
        if (StringUtils.isBlank(req.getName())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EMPTY, "名称");
        }
        if (authLevelEntity.getIsSystem().equals(1)) {
            authLevelEntity.setIsSystem(0);
            authLevelEntity.setId(0L);
            authLevelEntity.init(getTenantId(), getUserCode());
        }
        authLevelEntity.setName(req.getName());
        if (authLevelEntity.getId() > 0 && authLevelService.exists(getTenantId(), req.getName(), authLevelEntity.getLevel())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.DUPLICATE_NAME);
        }
        authLevelEntity.setCodes(JSON.toJSONString(req.getCodes()));
        authLevelService.saveOrUpdate(authLevelEntity);
    }

    /**
     * 转让所有者
     *
     * @param req 请求
     */

    public void transferOwer(@Valid TransferOwnerReq req) {
        KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(req.getGroupId());
        if (!group.getIsLibrary() || !group.getCreatorId().equals(getUserCode())) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NO_AUTH, "转让");
        }
        if (req.getOwnerId().equals(group.getCreatorId())) {
            return;
        }
        String creatorId = group.getCreatorId();
        knowledgeGroupFactory.checkMenuAndNodeAuth(group, OperatorTypeEnum.MANAGER, AuthNodeEnum.LIBRARY_SET);
        group.setCreatorId(req.getOwnerId());
        knowledgeGroupService.saveOrUpdate(group);

        iAuthService.remove(group.getId().toString(), SourceTypeEnum.Group.getValue(), req.getOwnerId(), AuthTypeEnum.User.getValue());
        iAuthService.remove(group.getId().toString(), SourceTypeEnum.Group.getValue(), creatorId, AuthTypeEnum.User.getValue());
        AuthEntity auth = new AuthEntity();
        auth.init(getTenantId(), getUserCode());
        auth.setSourceId(group.getId().toString());
        auth.setSourceType(SourceTypeEnum.Group.getValue());
        auth.setAuthType(AuthTypeEnum.User.getValue());
        auth.setAuthLevel(AuthLevelEnum.Manage.getValue());
        auth.setAuthId(creatorId);
        auth.setParentId(0L);
        iAuthService.saveBatch(Collections.singletonList(auth));
    }

    /**
     * 获取授权列表
     *
     * @param sourceId   授权Id
     * @param sourceType 授权类型
     * @return 授权结果
     */
    public AuthResp auths(Long sourceId, Integer sourceType) {
        AuthResp resp = new AuthResp();
        //获取直接授权
        List<AuthEntity> authList = iAuthService.listBySourceIds(Collections.singletonList(sourceId.toString()), sourceType);
        Long groupId = sourceId;
        if (SourceTypeEnum.File.getValue().equals(sourceType)) {
            KnowledgeResourceEntity resource = iResourceService.getNotNull(sourceId);
            groupId = resource.getGroupId();
        }
        KnowledgeGroupEntity curGroup = knowledgeGroupService.getNotNull(groupId);
        List<KnowledgeGroupEntity> parents = knowledgeGroupService.getParents(groupId);
        parents.add(curGroup);
        KnowledgeGroupEntity rootGroup = parents.stream().filter(x -> x.getParentId().equals(KnowledgeGroupEntity.ROOT_ID)).findFirst().orElse(null);
        if (Objects.isNull(rootGroup)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "根目录");
        }
        resp.setInherit(SourceTypeEnum.Group.getValue().equals(sourceType) && curGroup.getIsLibrary());
        //读取继承权限
        if (CollectionUtils.isEmpty(authList)) {
            resp.setInherit(true);
            List<AuthEntity> allAuths = iAuthService.listBySourceIds(parents.stream().map(p -> p.getId().toString()).collect(Collectors.toList()), SourceTypeEnum.Group.getValue());
            Collections.reverse(parents);
            for (KnowledgeGroupEntity group : parents) {
                authList = allAuths.stream().filter(a -> a.getSourceId().equals(group.getId().toString()) && a.getSourceType().equals(SourceTypeEnum.Group.getValue())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(authList)) {
                    break;
                }
            }
        }
        //List<String> departmentIds = authList.stream().filter(a -> a.getAuthType().equals(AuthTypeEnum.Department.getValue())).map(AuthEntity::getAuthId).collect(Collectors.toList());
        List<String> userIds = authList.stream().filter(a -> a.getAuthType().equals(AuthTypeEnum.User.getValue())).map(AuthEntity::getAuthId).collect(Collectors.toList());
        userIds.add(rootGroup.getCreatorId());
        List<UserInfoResp> userInfoResps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userIds))
            userInfoResps = iUserCenter.getUserByCode(new UserSearchByIdsReq(userIds)).getData();
        AuthEntity defaultTenantAuth = AuthEntity.noAuth();
        defaultTenantAuth.setTenantId(getTenantId());
        defaultTenantAuth.setAuthType(AuthTypeEnum.Tenant.getValue());
        resp.setTenantAuth(new AuthItem(authList.stream().filter(a -> a.getAuthType().equals(AuthTypeEnum.Tenant.getValue())).findFirst().orElse(defaultTenantAuth)));
        //添加所有者
        resp.getItems().add(AuthItem.owner(userInfoResps.stream().filter(x -> x.getUserCode().equals(rootGroup.getCreatorId())).findFirst().orElse(null), rootGroup));
        for (AuthEntity auth : authList.stream().filter(a -> !a.getAuthType().equals(AuthTypeEnum.Tenant.getValue())).collect(Collectors.toList())) {
            AuthItem authItem = new AuthItem(auth);
            if (auth.getAuthType().equals(AuthTypeEnum.Department.getValue())) {
                BaseClientResp<List<DepartmentResp>> departmentResp = iUserCenter.getDepartments(getToken(), new DepartmentReq(getTenantId(), auth.getAuthId()));
                if (Objects.nonNull(departmentResp) && CollectionUtils.isNotEmpty(departmentResp.getData())) {
                    authItem.setDepartment(departmentResp.getData().get(0));
                }
            } else if (auth.getAuthType().equals(AuthTypeEnum.User.getValue())) {
                if (auth.getAuthId().equals(rootGroup.getCreatorId())) {
                    continue;
                }
                authItem.setUserList(userInfoResps.stream().filter(x -> x.getUserCode().equals(auth.getAuthId())).collect(Collectors.toList()));
            }
            resp.getItems().add(authItem);
        }
        return resp;
    }

    public AuthIndexConfigAddOrCreateResp addOrUpdateIndexConf(AuthIndexConfigAddOrCreateReq req) {
        String tenantId = getTenantId();
        //开启索引定时任务
        if (BooleanUtil.isTrue(req.getIndexTimeScheduleOpen())) {
            if (StringUtils.isBlank(req.getIndexTimeStartTime()) || StringUtils.isBlank(req.getIndexTimeEndTime())) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.INVALID_ARGUMENT, "索引定时任务配置:开始时间和结束时间不能为空");
            }

        }
        IndexAuthEntity indexAuth = iIndexAuthManager.getOne(new LambdaQueryWrapper<IndexAuthEntity>().eq(IndexAuthEntity::getTenantId, tenantId));
        return null;
    }

    public static void main(String[] args) {
        String a = "09:03";
        String b = "20:03";
        DateTime dateTime = DateUtil.parse(a,"HH:mm");
        DateTime dateTimei = DateUtil.parse(b,"HH:mm");
        DateTime now = DateTime.now();
        System.out.println(now);
        System.out.println(dateTime);
        System.out.println(dateTimei);
        System.out.println(dateTime.after(dateTimei));

    }
}
