package com.linker.fusion.knowledgecenter.server.biz;

import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AnalysisStrategyEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.server.dto.req.strategy.StrategyAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.strategy.StrategyUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.StrategyDetailResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.strategy.StrategyService;
import com.linker.user.api.dto.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Slf4j
@Component
public class StrategyBiz extends BaseBiz {

    @Resource
    private StrategyService strategyService;

    @Resource
    private KnowledgeGroupService knowledgeGroupService;

    @Resource
    private IAuthService iAuthService;

    /**
     * 获取分组下的策略详情
     *
     * @param groupId 分组id
     * @return
     */
    public BaseResp<StrategyDetailResp> getStrategyDetail(Long groupId) {
        KnowledgeGroupEntity knowledgeGroup = knowledgeGroupService.getNotNull(groupId);
        StrategyDetailResp resp = new StrategyDetailResp();
        AnalysisStrategyEntity entity = strategyService.getStrategyByGroupId(groupId);
        if (ObjectUtils.isNotEmpty(entity)) {
            BeanUtils.copyProperties(entity, resp);
        }
        return new BaseResp<>(resp);
    }

    public void addStrategy(StrategyAddReq req) {
        KnowledgeGroupEntity knowledgeGroup = knowledgeGroupService.getNotNull(req.getGroupId());
        UserInfo user = UserContext.getUser();
        AnalysisStrategyEntity entity = new AnalysisStrategyEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setTenantId(user.getTenantInfoDTO().getTenantId());
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreatorId(user.getUser().getUserId().toString());
        if (strategyService.getStrategyCount(req.getGroupId()) > 0) {
            throw new RuntimeException("该分组下已存在策略");
        }
        strategyService.addStrategy(entity);
    }

    public void updateStrategy(StrategyUpdateReq req) {
        UserInfo user = UserContext.getUser();
        AnalysisStrategyEntity entity = strategyService.getStrategyById(req.getId());
        if (ObjectUtils.isEmpty(entity)) {
            throw new RuntimeException("该策略不存在");
        }
        KnowledgeGroupEntity knowledgeGroup = knowledgeGroupService.getNotNull(entity.getGroupId());
        BeanUtils.copyProperties(req, entity);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateId(user.getUser().getUserId().toString());
        strategyService.updateStrategy(entity);
    }

    public BaseResp<StrategyDetailResp> getStrategyById(Long id) {
        StrategyDetailResp resp = new StrategyDetailResp();
        AnalysisStrategyEntity entity = strategyService.getStrategyById(id);
        if (ObjectUtils.isNotEmpty(entity)) {
            KnowledgeGroupEntity knowledgeGroup = knowledgeGroupService.getNotNull(entity.getGroupId());
            BeanUtils.copyProperties(entity, resp);
        }
        return new BaseResp<>(resp);
    }
}
