package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.server.biz.GroupBiz;
import com.linker.fusion.knowledgecenter.server.dto.group.KnowledgeGroupListReq;
import com.linker.fusion.knowledgecenter.server.dto.req.group.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.KnowledgeGroupTreeNodeEntityResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "知识目录")
@RestController
@RequestMapping("/knowledge/group")
public class GroupController {

    @Resource
    private GroupBiz knowledgeGroupBiz;

    @PostMapping("/create")
    @ApiOperation(value = "创建")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Long> create(@RequestBody @Validated KnowledgeGroupCreateReq req) {
        return new BaseResp<>(knowledgeGroupBiz.create(req));
    }

    @GetMapping("/tree")
    @ApiOperation(value = "类目树", tags = "v1.6.6")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public BaseResp<List<KnowledgeGroupTreeNodeEntityResp>> tree(
            @ApiParam(name = "type", value = "库类型 1-文件库 5-FAQ库 6-数据表库 8-敏感词库 9-专用词库", required = true)
            @RequestParam(value = "type") Integer type,
            @ApiParam(name = "from", value = "1 构建平台")
            @RequestParam(value = "from", required = false) Integer from,
            @ApiParam(name = "visibleType", value = "0个人空间 1公共空间")
            @RequestParam(value = "visibleType", required = false, defaultValue = "1") Integer visibleType
    ) {
        return knowledgeGroupBiz.tree(type, from, visibleType);
    }

    @PostMapping("authed/list")
    @ApiOperation(value = "有权限的目录列表", tags = "v1.7.5")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public BaseResp<List<KnowledgeGroupTreeNodeEntityResp>> authedList(@RequestBody @Validated KnowledgeGroupListReq req){
        return knowledgeGroupBiz.authedList(req);
    }


    @GetMapping("sub")
    @ApiOperation(value = "子目录", tags = "v1.7.1")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<KnowledgeGroupTreeNodeEntityResp>> sub(@RequestParam("parentId") Long parentId){
        return knowledgeGroupBiz.sub(parentId);
    }


    @GetMapping("/authed/ids")
    @ApiOperation(value = "有权限的目录id列表", tags = "v1.7.1")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @LogFilter
    public BaseResp<List<Long>> authed(
            @ApiParam(name = "type", value = "库类型 1-文件库 5-FAQ库 6-数据表库 8-敏感词库 9-专用词库", required = true)
            @RequestParam(value = "type") Integer type,
            @ApiParam(name = "from", value = "1 构建平台")
            @RequestParam(value = "from", required = false) Integer from,
            @ApiParam(name = "visibleType", value = "0个人空间 1公共空间")
            @RequestParam(value = "visibleType", required = false, defaultValue = "1") Integer visibleType
    ) {
        return knowledgeGroupBiz.authed(type, from, visibleType);
    }

    @GetMapping("/clear/empty")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<Long>> clearEmpty(
            @ApiParam(name = "type", value = "库类型 1-文件库 5-FAQ库 6-数据表库 8-敏感词库 9-专用词库", required = true) Integer type
    ) {
         return new BaseResp<>( knowledgeGroupBiz.clearEmpty(type));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> update(@RequestBody @Validated KnowledgeGroupUpdateReq req) {
        knowledgeGroupBiz.update(req);
        return new BaseResp<>();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> delete(@RequestBody @Validated KnowledgeGroupDelReq req) {
        knowledgeGroupBiz.delete(req.getId());
        return new BaseResp<>();
    }

    @PostMapping("/filter-available")
    @ApiOperation(value = "过滤可用的ID列表", notes = "mediaManager服务使用")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<Long>> filterAvailable(@RequestBody FilterAvailableReq req) {
        return knowledgeGroupBiz.filterAvailable(req);
    }

    @PostMapping("/auth/filter")
    @ApiOperation("过滤分组")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<Long>> authFilter(@RequestBody AuthFilterReq req) {
        List<Long> groupIds = knowledgeGroupBiz.authFilter(req.getIds(), req.getType());
        return new BaseResp<>(groupIds);
    }
    @PostMapping("move")
    @ApiOperation(value = "移动", tags = "v1.5.1")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> move(@RequestBody MoveReq req) {
        return knowledgeGroupBiz.move(req);
    }
    @GetMapping("personal/lib/list")
    @ApiOperation(value = "获取个人工空间根目录", tags = "v1.7.0")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<KnowledgeGroupTreeNodeEntityResp>>  personalLibList(@ApiParam(name = "type", value = "库类型 1-文件库 5-FAQ库 6-数据表库 8-敏感词库 9-专用词库", required = true)
                                                                             @RequestParam(value = "type") Integer type){
        return new BaseResp<>(knowledgeGroupBiz.listPersonalLib(type));
    }

}
