package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.server.biz.ImgBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.file.DeleteImageReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Api(tags = "图片管理")
@RestController
@RequestMapping("/img")
public class ImgController {

    @Autowired
    private ImgBiz imgBiz;

    @GetMapping("/odMux")
    @ApiOperation(value = "OD目标识别")
    public BaseResp odMux(
            @RequestParam(value = "image", required = true) String image) {
        // https://om.linker.cc/omos/webdav//upload/67121ee9e4b03370456a644d.jpg
        return new BaseResp<>(imgBiz.odMux(image));
    }


    @PostMapping("/mergeImages")
    @ApiOperation(value = "图片合并")
    public BaseResp mergeImages(@RequestBody List<String> imageUrls) throws IOException {
//        "https://test-om.linker.cc/minio/sample-library/omintel/org-0/B100105000/202503/18/a1e4996e-abc1-4db6-a3bd-b85a3291246f/000000000.jpg",
//                "https://test-om.linker.cc/minio/sample-library/omintel/org-0/B100105000/202503/18/a1e4996e-abc1-4db6-a3bd-b85a3291246f/000003000.jpg"
        return new BaseResp<>(imgBiz.mergeImages(imageUrls));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "图片删除")
    public BaseResp delete(@RequestBody DeleteImageReq req) throws IOException {
        imgBiz.delete(req);
        return new BaseResp<>();
    }
}
