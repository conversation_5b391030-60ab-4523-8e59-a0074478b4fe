package com.linker.fusion.knowledgecenter.server.biz;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Throwables;
import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.base.exception.BaseException;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IUserCenterService;
import com.linker.fusion.knowledgecenter.infrastructure.config.AppInfoConfig;
import com.linker.fusion.knowledgecenter.infrastructure.entity.*;
import com.linker.fusion.knowledgecenter.infrastructure.enums.AuthLevelEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.TaskTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.AuthActionModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.task.TaskContentForAIGenerateQAModel;
import com.linker.fusion.knowledgecenter.server.convert.FaqConvert;
import com.linker.fusion.knowledgecenter.server.dto.faq.FaqInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.faq.FaqSegmentExtResp;
import com.linker.fusion.knowledgecenter.server.dto.faq.RefFileInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.req.core.BatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.EnableReq;
import com.linker.fusion.knowledgecenter.server.dto.req.faq.*;
import com.linker.fusion.knowledgecenter.server.dto.resp.FaqInfoEntityResp;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthService;
import com.linker.fusion.knowledgecenter.service.domain.faq.FaqService;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqPageDTO;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqSaveDTO;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqSearchDTO;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import com.linker.fusion.knowledgecenter.service.domain.group.model.SearchPreChekDto;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IDocChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceSegmentService;
import com.linker.fusion.knowledgecenter.service.domain.task.ITaskService;
import com.linker.user.api.dto.UserInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.linker.core.base.enums.RespCodeEnum.BUSINESS_EXCEPTION;
import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.FAQ_COUNT_LIMIT;
import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.TABLE_HEAD_MAX_100;
import static org.springframework.http.HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS;
import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;

@Slf4j
@Component
public class FaqBiz extends BaseBiz {

    @Resource
    private FaqService faqService;
    @Resource
    private IAuthService iAuthService;
    @Resource
    private KnowledgeGroupService knowledgeGroupService;
    @Resource
    private KnowledgeGroupFactory knowledgeGroupFactory;
    @Resource
    private IUserCenterService iUserCenterService;
    @Resource
    private AppInfoConfig appInfoConfig;
    @Resource
    private IResourceService iResourceService;
    @Resource
    private ITaskService iTaskService;
    @Resource
    private IDocChunkIBaseServiceResource iChunkIBaseService;
    @Autowired
    private IResourceSegmentService iResourceSegmentService;

    public Long create(FaqCreateReq req) {
        // 校验传参
        req.validate();
        UserInfo userInfo = UserContext.getUser();
        FaqSaveDTO faqSaveDTO = FaqConvert.INSTANCE.toDto(req, userInfo);

        return faqService.create(faqSaveDTO);
    }


    public BaseResp<List<String>> generateSimilarQuestions(FaqGenerateSimilarQuestionsReq req) {
        return new BaseResp<>(faqService.generateSimilarQuestions(req.getStandardQuestion(), req.getSimilarQuestions()));
    }

    public BasePaginResp<FaqInfoEntityResp> page(FaqPageReq req) {

        SearchPreChekDto preChekDto = knowledgeGroupFactory.searchPreCheck(req);
        FaqPageDTO faqPageDTO = FaqConvert.INSTANCE.toDto(req, UserContext.getUser());
        faqPageDTO.setGroupIds(preChekDto.getAuthedGroupIds());
        Page<FaqInfoEntity> page = faqService.page(faqPageDTO);
        // 标准问题列表
        List<FaqInfoEntity> faqInfoEntityList = page.getRecords();
        // 相似问题列表
        List<FaqInfoEntity> faqListSimilar = faqService.listByStandardIds(faqInfoEntityList.stream().map(FaqInfoEntity::getId).collect(Collectors.toList()));
        // 相似问题分组
        Map<Long, List<String>> standardIdAndSimilarQuestionsMap = faqListSimilar.stream().collect(Collectors.groupingBy(FaqInfoEntity::getStandardId, Collectors.mapping(FaqInfoEntity::getQuestion, Collectors.toList())));
        // 数据转换
        List<FaqInfoEntityResp> faqInfoRespList = new ArrayList<>();
        for (FaqInfoEntity faqInfoEntity : faqInfoEntityList) {
            FaqInfoEntityResp faqInfoResp = FaqConvert.INSTANCE.toDto(faqInfoEntity);
            faqInfoResp.setSimilarQuestions(standardIdAndSimilarQuestionsMap.get(faqInfoEntity.getId()));
            faqInfoResp.setAuthCodes(preChekDto.getAuthedCodes(faqInfoEntity.getId(), faqInfoEntity.getGroupId()));
            faqInfoResp.setAuthLevel(preChekDto.getAuthedLevel(faqInfoEntity.getId(), faqInfoEntity.getGroupId()));
            faqInfoResp.setAuthActions(AuthActionModel.create(AuthLevelEnum.Manage.getValue(), true, false));
            faqInfoRespList.add(faqInfoResp);
        }
        return new BasePaginResp<>(page.getTotal(), faqInfoRespList);
    }

    public BaseResp<Void> update(FaqUpdateReq req) {
        req.validate();
        UserInfo userInfo = UserContext.getUser();
        FaqSaveDTO faqSaveDTO = FaqConvert.INSTANCE.toDto(req, userInfo);
        faqService.update(faqSaveDTO);
        return new BaseResp<>();
    }

    public BaseResp<Void> enable(EnableReq req) {
        faqService.enable(getTenantId(), req.getIds(), req.getEnable());
        return new BaseResp<>();
    }

    public BaseResp<Void> move(FaqMoveReq req) {
        List<FaqInfoEntity> faqList = faqService.listByIds(getTenantId(), req.getIds());
        KnowledgeGroupEntity target = knowledgeGroupService.getNotNull(req.getGroupId());
        List<KnowledgeGroupEntity> groups = knowledgeGroupService.listByIds(faqList.stream().map(FaqInfoEntity::getGroupId).collect(Collectors.toList()));
        for (KnowledgeGroupEntity source : groups) {
            if (source.getVisibleType() > target.getVisibleType()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录可见等级不足,");
            }
            if (source.getImportantLevel() > target.getImportantLevel()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录重要等级不足,");
            }
        }

        faqService.move(getTenantId(), faqList, req.getGroupId());
        return new BaseResp<>();
    }

    public BaseResp<Void> delete(BatchBaseReq req) {
        List<FaqInfoEntity> faqList = faqService.listByIds(UserContext.getUser().getTenantInfoDTO().getTenantId(), req.getIds());
        faqService.delete(faqList);
        return new BaseResp<>();
    }


    public void export(BatchBaseReq req, HttpServletResponse response) {
        List<FaqInfoEntity> faqList = faqService.listByIds(UserContext.getUser().getTenantInfoDTO().getTenantId(), req.getIds());
        try (InputStream inputStream = ResourceUtil.getStream("template/faq_template_150.xlsx");
             OutputStream outputStream = response.getOutputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);

            // 模板前面有提示语和表头，数据从第12行开始
            int startRow = 11;

            for (List<String> rowData : rowDataList(faqList)) {
                Row row = sheet.createRow(startRow++);
                int cellNum = 0;
                for (String cellData : rowData) {
                    Cell cell = row.createCell(cellNum++);
                    cell.setCellValue(cellData);
                }
            }

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = "faq-export-" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader(ACCESS_CONTROL_EXPOSE_HEADERS, CONTENT_DISPOSITION);
            response.setHeader(CONTENT_DISPOSITION, "attachment;filename*=utf-8''" + encodedFileName);

            workbook.write(outputStream);
        } catch (Exception e) {
            log.error("导出失败", e);
            // 处理异常
            throw new BaseException(BUSINESS_EXCEPTION);
        }
    }

    private List<List<String>> rowDataList(List<FaqInfoEntity> faqList) {
        List<List<String>> rows = new ArrayList<>();

        for (FaqInfoEntity faq : faqList) {
            List<String> row = new ArrayList<>();
            row.add(faq.getQuestion());
            row.add(Objects.equals(faq.getAnswerType(), 0) ? "纯文本" : "富文本");

            List<String> answers = faq.getAnswers();
            for (int i = 0; i < 5; i++) {
                row.add(i < answers.size() ? answers.get(i) : "");
            }
            rows.add(row);
        }
        return rows;
    }

    public BaseResp<List<String>> importFaq(MultipartFile file,
                                            Long groupId) throws IOException {
        LocalDateTime startTime = LocalDateTime.now();
        processFaqByEasyExcel(file, groupId);
        LocalDateTime endTime = LocalDateTime.now();
        long duration = Duration.between(startTime, endTime).toMillis();
        log.info("Faq导入耗时: {}ms", duration);
        return new BaseResp<>();
    }

    //        List<FaqExcelRowDTO> rowDataList = new ArrayList<>();
//        try {
//            EasyExcel.read(file.getInputStream(), FaqExcelRowDTO.class, new PageReadListener<FaqExcelRowDTO>(rowDataList::addAll))
//                    .sheet()
//                    // 模板前面有提示语和表头，数据从第12行开始
//                    .headRowNumber(11)
//                    .doRead();
//        } catch (IOException e) {
//            throw new BaseException(BUSINESS_EXCEPTION);
//        }
//
//        UserInfo userInfo = UserContext.getUser();
//
//        List<FaqInfoEntity> faqInfoList = new ArrayList<>();
//        for (FaqExcelRowDTO faqExcelRowDTO : rowDataList) {
//            if (StringUtils.isNotBlank(faqExcelRowDTO.getQuestion())
//                    && StringUtils.isNotBlank(faqExcelRowDTO.getAnswer1())
//                    && StringUtils.equalsAny(faqExcelRowDTO.getAnswerType(), "纯文本", "富文本")
//            ) {
//                FaqInfoEntity faqInfo = new FaqInfoEntity();
//                faqInfo.setQuestion(StringUtils.substring(faqExcelRowDTO.getQuestion(), 0, 200));
//                faqInfo.setAnswerType("纯文本".equals(faqExcelRowDTO.getAnswerType()) ? 0 : 1);
//                List<String> answers = Arrays.asList(faqExcelRowDTO.getAnswer1(), faqExcelRowDTO.getAnswer2(), faqExcelRowDTO.getAnswer3(), faqExcelRowDTO.getAnswer4(), faqExcelRowDTO.getAnswer5());
//                faqInfo.setAnswers(answers.stream().filter(StringUtils::isNotBlank).map(s -> StringUtils.substring(s, 0, 3000)).collect(Collectors.toList()));
//                faqInfoList.add(faqInfo);
//            }
//        }
//        if (!faqInfoList.isEmpty()) {
//            // 翻转列表，使导入时和XLS中顺序一致
//            Collections.reverse(faqInfoList);
//            if (Objects.nonNull(appInfoConfig.getQaLimit())) {
//                Long count = faqService.getCount(getTenantId());
//                if (faqInfoList.size() + count > appInfoConfig.getQaLimit()) {
//                    throw new ServiceException(FAQ_COUNT_LIMIT, String.valueOf(Math.max(0, appInfoConfig.getQaLimit() - count)));
//                }
//            }
//            FaqImportDTO faqImportDTO = faqService.importFaq(userInfo.getTenantInfoDTO().getTenantId(), userInfo.getUser().getUserCode(), groupId, faqInfoList);
//            if (faqImportDTO != null) {
//                return new BaseResp<>(faqImportDTO.getResponse().getCode(), faqImportDTO.getResponse().getMessage(), faqImportDTO.getDupQuestions());
//            }
//        }
//        return new BaseResp<>();
//    }
    private void processBatch(ArrayList<FaqExcelRowDTO> faqExcelRowDTOList, Long groupId) {
        UserInfo userInfo = UserContext.getUser();
        List<FaqInfoEntity> faqInfoList = new ArrayList<>();
        for (FaqExcelRowDTO faqExcelRowDTO : faqExcelRowDTOList) {
            FaqInfoEntity faqInfo = new FaqInfoEntity();
            faqInfo.setQuestion(StringUtils.substring(faqExcelRowDTO.getQuestion(), 0, 200));
            faqInfo.setAnswerType("纯文本".equals(faqExcelRowDTO.getAnswerType()) ? 0 : 1);
            List<String> answers = Arrays.asList(faqExcelRowDTO.getAnswer1(), faqExcelRowDTO.getAnswer2(), faqExcelRowDTO.getAnswer3(), faqExcelRowDTO.getAnswer4(), faqExcelRowDTO.getAnswer5());
            faqInfo.setAnswers(answers.stream().filter(StringUtils::isNotBlank).map(s -> StringUtils.substring(s, 0, 3000)).collect(Collectors.toList()));
            faqInfoList.add(faqInfo);
        }

        if (!faqInfoList.isEmpty()) {
            // 翻转列表，使导入时和XLS中顺序一致
            Collections.reverse(faqInfoList);
            if (Objects.nonNull(appInfoConfig.getQaLimit())) {
                Long count = faqService.getCount(getTenantId());
                if (faqInfoList.size() + count > appInfoConfig.getQaLimit()) {
                    throw new ServiceException(FAQ_COUNT_LIMIT, String.valueOf(Math.max(0, appInfoConfig.getQaLimit() - count)));
                }
            }
            faqService.batchImportFaq(userInfo.getTenantInfoDTO().getTenantId(), userInfo.getUser().getUserCode(), groupId, faqInfoList);
        }
    }

    public void processFaqByEasyExcel(MultipartFile file, Long groupId) throws IOException {
        // 定义批次大小
        int batchSize = 1000;
        // 创建监听器处理数据
        FaqBiz.ExcelDataListener listener = new ExcelDataListener(batchSize, groupId, this);
        // 使用EasyExcel读取数据
        EasyExcel.read(file.getInputStream(), FaqBiz.FaqExcelRowDTO.class, listener)
                .sheet()
                //excel中有说明性文字，需要找到表头位置
                .headRowNumber(2)
                .doRead();
    }

    public BaseResp<FaqSearchDTO> search(FaqSearchReq req) {
        return new BaseResp<>(faqService.search(UserContext.getUser().getTenantInfoDTO().getTenantId(), req.getQuestion(), req.getGroupIds(), true, req.getThreshold()));
    }

    public BaseResp<List<String>> searchQuestion(FaqSearchReq req) {
        if (req.isByEs()) {
            return new BaseResp<>(faqService.searchByEs(getTenantId(), req.getQuestion(), req.getGroupIds(), req.getLimit()));
        } else {
            return new BaseResp<>(faqService.searchBySql(getTenantId(), req.getQuestion(), req.getGroupIds(), req.getLimit()));
        }
    }

    public void fixFaq() {
        faqService.fixFaq();
    }

    /**
     * 问答对详情
     *
     * @param id 问答对id
     * @return 问答对详情
     */
    public FaqInfoResp detail(Long id) {
        FaqInfoEntity faqInfoEntity = faqService.getById(id);
        FaqInfoResp faqInfoResp = new FaqInfoResp();
        BeanUtils.copyProperties(faqInfoEntity, faqInfoResp);
        List<FaqInfoEntity> list = faqService.listByStandardIds(Collections.singletonList(id));
        faqInfoResp.setSimilarQuestions(list.stream().map(FaqInfoEntity::getQuestion).collect(Collectors.toList()));
        if (StringUtils.isNotBlank(faqInfoEntity.getResourceId())) {
            KnowledgeResourceEntity resource = iResourceService.getNotNull(faqInfoEntity.getResourceId());
            faqInfoResp.setRefFileInfo(new RefFileInfoResp(resource));
            if (StringUtils.isNotBlank(faqInfoEntity.getSegmentInfo())) {
                faqInfoResp.setRefSegmentInfos(JSON.parseArray(faqInfoEntity.getSegmentInfo(), FaqSegmentExtResp.class));
                faqInfoResp.getRefSegmentInfos().forEach(faqSegmentInfo -> {
                    ResourceSegmentEntity newSeg = iResourceSegmentService.get(faqSegmentInfo.getSegmentId());
                    faqSegmentInfo.setChunkSize(Objects.isNull(newSeg) ? faqSegmentInfo.getNumber() : newSeg.getNumber());
                });


            }
        }
        return faqInfoResp;
    }

    @Data
    public static class FaqExcelRowDTO {

        @ExcelProperty(index = 0)
        private String question;

        @ExcelProperty(index = 1)
        private String answerType;

        @ExcelProperty(index = 2)
        private String answer1;
        @ExcelProperty(index = 3)
        private String answer2;
        @ExcelProperty(index = 4)
        private String answer3;
        @ExcelProperty(index = 5)
        private String answer4;
        @ExcelProperty(index = 6)
        private String answer5;

    }

    @Data
    public static class ExcelDataListener extends AnalysisEventListener<FaqBiz.FaqExcelRowDTO> {
        private int batchSize;
        private Long groupId;
        private FaqBiz faqBiz;

        private List<FaqBiz.FaqExcelRowDTO> batchData = new ArrayList<>();

        public ExcelDataListener() {
        }

        public ExcelDataListener(int batchSize, Long groupId, FaqBiz faqBiz) {
            this.batchSize = batchSize;
            this.groupId = groupId;
            this.faqBiz = faqBiz;
        }

        @Override
        public void invoke(FaqBiz.FaqExcelRowDTO data, AnalysisContext context) {
            // 校验数据
            if (StringUtils.isBlank(data.getQuestion()) || StringUtils.isBlank(data.getAnswerType()) || StringUtils.isBlank(data.getAnswer1())) {
                return;
            }
            if (!StringUtils.equalsAny(data.getAnswerType(), "纯文本", "富文本")) {
                return;
            }
            batchData.add(data);
            // 批量处理
            if (batchData.size() >= batchSize) {
                faqBiz.processBatch(new ArrayList<>(batchData), groupId);
                batchData.clear();
            }

        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 处理剩余数据
            if (!batchData.isEmpty()) {
                faqBiz.processBatch(new ArrayList<>(batchData), groupId);
            }
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            //对设定的rowNumber进行处理
            ReadRowHolder readRowHolder = context.readRowHolder();
            Map<Integer, CellData> cellDataMap = (Map) readRowHolder.getCellMap();
            readRowHolder.setCurrentRowAnalysisResult(cellDataMap);
            int rowIndex = readRowHolder.getRowIndex();
            int currentHeadRowNumber = context.readSheetHolder().getHeadRowNumber();
            boolean isData = rowIndex >= currentHeadRowNumber;

            if (!isData && currentHeadRowNumber != rowIndex + 1) {
                return;
            }
            try {
                // 检查必要列是否存在
                if (headMap.size() < 3) {
                    throw new ServiceException(TABLE_HEAD_MAX_100);
                }
                // 获取前几列的表头名称
                String firstColumn = headMap.get(0);
                String secondColumn = headMap.get(1);
                String thirdColumn = headMap.get(2);
                boolean isValid = "标准问题（必填）".equals(firstColumn) && "回答类型（必填）".equals(secondColumn) && "问题回答1（必填）".equals(thirdColumn);
                // 校验模板格式: 标准问题（必填）, 回答类型（必填），问题回答1（必填）
                if (!isValid) {
                    throw new ServiceException(TABLE_HEAD_MAX_100);
                }
            } catch (ServiceException e) {
                throw e;
            } catch (Exception e) {
                log.warn("表头校验失败,e:{}", Throwables.getStackTraceAsString(e));
                throw new ServiceException(TABLE_HEAD_MAX_100);
            }
        }
    }


    /**
     * ai生成问答对
     *
     * @param req
     */
    public TaskEntity aiGenerateQA(AIGenerateQAReq req) {

        Long count = faqService.getCount(getTenantId());
        if (appInfoConfig.getQaLimit() <= count) {
            throw new ServiceException(FAQ_COUNT_LIMIT, "0");
        }
        KnowledgeResourceEntity resource = iResourceService.getNotNull(req.getDocId());
        KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(req.getGroupId());
        iTaskService.checkHasRun(getTenantId(), getUserCode(), req.getGroupId());
        TaskContentForAIGenerateQAModel qaModel = new TaskContentForAIGenerateQAModel();
        qaModel.setDocId(resource.getDocId());
        return iTaskService.send(getTenantId(), getUserCode(), group.getId(), qaModel, null, TaskTypeEnum.QA);
    }
}
