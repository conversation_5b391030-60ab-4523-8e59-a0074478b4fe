package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.alibaba.excel.metadata.data.WriteCellData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SupplementResp {
    private Long id;
    @ApiModelProperty("描述")
    private String desc;
    @ApiModelProperty("内容")
    private List<String> urls;
    private WriteCellData<Void> writeCellData;
}
