package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.server.biz.BigScreenBiz;
import com.linker.fusion.knowledgecenter.server.dto.resp.bigScreen.BigScreenFileStaResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.bigScreen.BigScreenSpaceStaResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.bigScreen.BigScreenStatusResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.bigScreen.BigScreenYearStaResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@RestController
@RequestMapping("big-screen")
@Api(tags = "大屏相关接口")
@Slf4j
@LogFilter
public class BigScreenController {

    @Resource
    BigScreenBiz bigScreenBiz;

    @GetMapping("sta/status")
    @ApiOperation("状态统计")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<BigScreenStatusResp> statusSta() {
        return bigScreenBiz.statusSta();
    }

    @GetMapping("sta/file")
    @ApiOperation("文件统计")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<BigScreenFileStaResp> fileSta() {
        return bigScreenBiz.fileSta();
    }

    @GetMapping("sta/space")
    @ApiOperation("空间统计")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<BigScreenSpaceStaResp> spaceSta() {
        return bigScreenBiz.spaceSta();
    }

    @GetMapping("sta/year")
    @ApiOperation("年份统计")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<List<BigScreenYearStaResp>> yearSta() {
        return bigScreenBiz.yearSta();
    }

}
