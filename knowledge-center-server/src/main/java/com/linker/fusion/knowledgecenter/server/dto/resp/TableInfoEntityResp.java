package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.linker.fusion.knowledgecenter.infrastructure.model.AuthActionModel;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.BaseEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.KnowledgeGroupParentResp;
import com.linker.fusion.knowledgecenter.service.domain.common.FileSizeFormatter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TableInfoEntityResp extends BaseEntityResp {

    @ApiModelProperty("编号")
    private Long id;

    @ApiModelProperty("docId")
    private String docId;

    @ApiModelProperty("workflowId")
    private String workflowId;

    @ApiModelProperty("知识类型 1-文档 2-图片 3-视频 4-音频 5-FAQ 6-表格")
    private String type;

    @ApiModelProperty("组ID")
    private Long groupId;

    @ApiModelProperty("资源名称")
    private String title;

    @ApiModelProperty("文件后缀")
    private String suffix;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("资源地址")
    private String url;

    @ApiModelProperty("预览地址")
    private String previewSrc;

    @ApiModelProperty("文件资源大小（byte）")
    private Long size;

    public String getFileSize() {
        return FileSizeFormatter.formatFileSize(size);
    }

    @ApiModelProperty("文档：页数；表格：行数")
    private Long count;

    @ApiModelProperty("定义了知识资源在处理过程中的各种状态：0-排队中 1-学习中, 2-学习成功, 3-学习失败, 4-归档, 5-合并中, 6-合并失败, 7-预处理中（转码、抽帧）, 8-暂停学习,9-取消学习")
    private Integer handleStatus;

    private Integer code;

    @ApiModelProperty("学习失败时错误信息")
    private String message;

    @ApiModelProperty("enable")
    private Boolean enable;

    @ApiModelProperty("扩展信息字段json")
    private String extInfo;

    @ApiModelProperty("类目信息")
    private KnowledgeGroupParentResp groupParentResp;
    @ApiModelProperty("授权信息")
    private AuthActionModel authActions;
    @ApiModelProperty("授权编码")
    private List<String> authCodes;
    @ApiModelProperty("权限等级")
    private Integer authLevel;
    @ApiModelProperty("重要等级")
    private Integer importantLevel;
    @ApiModelProperty("可见类型")
    private Integer visibleType;

    @ApiModelProperty("申请中")
    private Boolean applying;

}
