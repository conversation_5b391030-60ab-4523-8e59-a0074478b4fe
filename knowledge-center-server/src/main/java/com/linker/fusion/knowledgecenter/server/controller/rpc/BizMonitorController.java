package com.linker.fusion.knowledgecenter.server.controller.rpc;

import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.server.biz.BizMonitorBiz;
import com.linker.fusion.knowledgecenter.service.domain.rag.RagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 业务监控一接口
 */
@Api(tags = "RPC/业务监控，提供给运维调用")
@RestController
@RequestMapping("/rpc/monitor")
public class BizMonitorController {

    @Resource
    private BizMonitorBiz bizMonitorBiz;
    @Autowired
    RagService ragService;
    /**
     * 监控文件的学习情况
     * @param req
     * @return
     */
    @ApiOperation(value = "监控文件执行情况")
    @RequestMapping(value = "/resource/learn/info", method = {RequestMethod.GET, RequestMethod.POST})
    public BaseResp<String> resourceLearnInfo(Integer time) {
        return bizMonitorBiz.resourceLearnInfo(time);
    }

    /**
     * 监控文件的学习情况
     * @param req
     * @return
     */
    @ApiOperation(value = "监控算法状态情况")
    @RequestMapping(value = "/algorithm/info", method = {RequestMethod.GET, RequestMethod.POST})
    public BaseResp<String> algorithmInfo(@RequestParam(value = "query", required = false) String query,
                                          @RequestParam(value = "url", required = false) String url) {

        bizMonitorBiz.algorithmInfo(query, url);
        return new BaseResp<>();
    }

    /**
     * 监控文件的学习情况
     * @param req
     * @return
     */
    @ApiOperation(value = "监控租户队列排队情况")
    @RequestMapping(value = "/resource/queue/info", method = {RequestMethod.GET, RequestMethod.POST})
    public BaseResp<Map> queueInfo(@RequestParam(value = "time", required = false) Integer time) {

        return new BaseResp<>(bizMonitorBiz.queueInfo(time));
    }
}
