package com.linker.fusion.knowledgecenter.server.biz;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.infrastructure.dto.query.LogRecordPageQuery;
import com.linker.fusion.knowledgecenter.infrastructure.entity.LogRecordEntity;
import com.linker.fusion.knowledgecenter.server.convert.LogRecordConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.operationlog.LogRecordPageReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.operationlog.LogRecordPageResp;
import com.linker.fusion.knowledgecenter.service.domain.oprationlog.OperationLogRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OperationLogBiz {

    @Autowired
    private OperationLogRecordService operationLogRecordService;

    public BasePaginResp<LogRecordPageResp> logRecordPage(LogRecordPageReq req) {
        LogRecordPageQuery query = LogRecordConvert.INSTANCE.reqToQuery(req);
        String tenantId = UserContext.tenantId();
        query.setTenantId(tenantId);
        IPage<LogRecordEntity> page = operationLogRecordService.page(query);
        List<LogRecordPageResp> resp = page.getRecords().stream().map(c -> {
            return LogRecordConvert.INSTANCE.pageToResp(c);
        }).collect(Collectors.toList());
        return new BasePaginResp<>(page.getTotal(), resp);
    }
}
