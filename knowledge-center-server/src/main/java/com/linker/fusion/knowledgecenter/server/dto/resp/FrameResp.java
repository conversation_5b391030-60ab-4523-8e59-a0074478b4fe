package com.linker.fusion.knowledgecenter.server.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class FrameResp {
    @ApiModelProperty("帧图片地址")
    private String url;
    @ApiModelProperty("帧id")
    private String frameId;
    @ApiModelProperty("创建事件")
    private String createTime;
    @ApiModelProperty("标签名称")
    private List<String> tagNames = new ArrayList<>();

    @ApiModelProperty("时间戳 单位毫秒")
    private Long timePoint;
}
