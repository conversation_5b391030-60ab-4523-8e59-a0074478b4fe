package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.alibaba.fastjson2.JSONObject;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceSegmentEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.SegmentEntity;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.mysql.cj.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SegmentResp {

    @ApiModelProperty("es 唯一id 用户更新")
    private String uid;
    /**
     * 文档id
     */
    @ApiModelProperty("文档id")
    private String docId;
    /**
     * 标题
     */
    @ApiModelProperty("分段标题")
    private String title;
    @ApiModelProperty("分段内容")
    private String content;
    /**
     * 附加字段
     */
    @ApiModelProperty("附件数据")
    private JSONObject mate;
    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer page;
    /**
     * 分段id
     */
    @ApiModelProperty("分段id 用户关联分片")
    private String segmentId;
    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Double sort;
    /**
     * 位置
     */
    @ApiModelProperty("坐标")
    private String position;
    /**
     * 启用
     */
    @ApiModelProperty("启用/禁用")
    private Integer enable;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("序号值（1、2、3、4）")
    private Integer number;

    public SegmentResp() {
    }

    public SegmentResp(SegmentEntity entity) {
        this.uid = entity.getUid();
        this.docId = entity.getDocId();
        this.title = entity.getTitle().trim();
        if (StringUtils.isNullOrEmpty(this.title)) {
            this.title = this.title.replace("ζζ", "ζ");
        }
        if (StringUtils.isNullOrEmpty(this.title)) {
            this.title = this.title.replace("ζζ", "ζ");
        }
        if (StringUtils.isNullOrEmpty(this.title)) {
            this.title = this.title.replace("ζζ", "ζ");
        }
        if (this.title.startsWith("ζ")) {
            this.title = this.title.substring(1);
        }
        this.content = entity.getContent();
        this.mate = entity.getMate();
        this.page = entity.getPage();
        this.segmentId = entity.getSegmentId();
        this.sort = entity.getSort();
        this.position = entity.getPosition();
        this.enable = entity.getEnable();
        this.createTime = entity.getCreateTime();
        this.status = entity.getStatus();
        this.setNumber(entity.getNumber());
    }

    public SegmentResp(ResourceSegmentEntity entity) {
//        this.uid = entity.getUid();
        this.docId = entity.getDocId();
        this.title = entity.getTitle().trim();
        if (StringUtils.isNullOrEmpty(this.title)) {
            this.title = this.title.replace("ζζ", "ζ");
        }
        if (StringUtils.isNullOrEmpty(this.title)) {
            this.title = this.title.replace("ζζ", "ζ");
        }
        if (StringUtils.isNullOrEmpty(this.title)) {
            this.title = this.title.replace("ζζ", "ζ");
        }
        if (this.title.startsWith("ζ")) {
            this.title = this.title.substring(1);
        }
        this.content = entity.getContent();
//        this.mate = entity.getMate();
        this.page = entity.getPage();
        this.segmentId = entity.getSegmentId();
        this.sort = entity.getSort();
        this.position = entity.getPosition();
//        this.enable = entity.getEnable();
        this.createTime = StringComUtils.convertStr(entity.getCreateTime());
        this.status = entity.getStatus();
//        this.setNumber(entity.getNumber());
    }
}
