package com.linker.fusion.knowledgecenter.server.biz;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.core.base.exception.ServerException;
import com.linker.core.base.exception.ServiceException;
import com.linker.core.cos.provider.CosHelper;
import com.linker.core.cos.provider.IProvider;
import com.linker.core.redis.RedisUtil;
import com.linker.fusion.knowledgecenter.infrastructure.client.HttpPersonClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.AasWorkflowClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncCancelReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.HttpPersonInfoReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.PersonInfoDTO;
import com.linker.fusion.knowledgecenter.infrastructure.client.person.ODLibReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.person.ODLibResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IKnowPowerResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IUserCenterService;
import com.linker.fusion.knowledgecenter.infrastructure.config.AppInfoConfig;
import com.linker.fusion.knowledgecenter.infrastructure.config.EventConfig;
import com.linker.fusion.knowledgecenter.infrastructure.entity.*;
import com.linker.fusion.knowledgecenter.infrastructure.enums.*;
import com.linker.fusion.knowledgecenter.infrastructure.es.AudioChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.ChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoFrameEntity;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.DocStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.utils.BatchTaskUtils;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StopwatchUtil;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.server.convert.AppInfoConvert;
import com.linker.fusion.knowledgecenter.server.convert.DocConvert;
import com.linker.fusion.knowledgecenter.server.convert.FileConvert;
import com.linker.fusion.knowledgecenter.server.dto.file.SpaceInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.AddSegmentReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.DeleteSegmentReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.*;
import com.linker.fusion.knowledgecenter.server.dto.req.file.video.EventRecognizeReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.video.EventRecognizeResp;
import com.linker.fusion.knowledgecenter.server.dto.req.file.video.SegmentRetryReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.AppInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.DocInfoEntityResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.SegmentPageResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.SegmentResp;
import com.linker.fusion.knowledgecenter.service.domain.common.IUtilService;
import com.linker.fusion.knowledgecenter.service.domain.faq.FaqService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.TagExtInfoModel;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.task.TaskSceneTagRerunReq;
import com.linker.fusion.knowledgecenter.service.domain.oprationlog.OperationLogProxyService;
import com.linker.fusion.knowledgecenter.service.domain.question.QuestionBankService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IAudioChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IDocChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IVideoChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.*;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ResourceUtilService;
import com.linker.fusion.knowledgecenter.service.domain.task.ITaskService;
import com.linker.omagent.core.repository.embedding.SearchHit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.common.Constants.RESOURCE_HANDLE_STATUS_KEY_PREFIX;
import static com.linker.fusion.knowledgecenter.infrastructure.common.Constants.RESOURCE_STUDY_STATUS_KEY_PREFIX;
import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.*;

@Component
@Slf4j
public class FileBiz extends BaseBiz {

    @Resource
    private KnowledgeGroupService knowledgeGroupService;
    @Resource
    private IResourceService iResourceService;
    @Resource
    private ResourceUtilService resourceUtilService;
    @Resource
    private IResourceSegmentService iResourceSegmentService;

    @Resource
    private IResourceFaceService iResourceFaceService;

    @Resource
    private IResourceTerminologyService iResourceTerminologyService;

    @Resource
    private IResourceODTagService iResourceODTagService;

    @Resource
    private IDocChunkIBaseServiceResource iChunkIBaseService;

    @Resource
    private FaqService faqService;
    @Resource
    private CosHelper cosHelper;
    @Resource
    private EventConfig eventConfig;

    @Resource
    private QuestionBankService questionBankService;

    @Resource
    private ITaskService iTaskService;

    @Resource
    private IVideoFrameIBaseService iVideoFrameIBaseService;

    @Resource
    private HttpPersonClient personClient;

    @Autowired
    private OperationLogProxyService operationLogProxyService;
    @Autowired
    private IUtilService iUtilService;
    @Autowired
    private IResourceExtService iResourceExtService;
    @Resource
    IResourceASRService iResourceASRService;
    @Autowired
    private IVideoChunkIBaseServiceResource iVideoChunkIBaseServiceResource;

    @Autowired
    private AppInfoConfig appInfoConfig;
    @Autowired
    private IAudioChunkIBaseServiceResource iAudioChunkIBaseServiceResource;
    @Resource
    HttpPersonClient httpPersonClient;

    @Resource
    AasWorkflowClient aasWorkflowClient;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private IUserCenterService iUserCenterService;

    /**
     * 新增Segment
     */
    public void addSegment(AddSegmentReq req) {
        if (StringUtils.isBlank(req.getContent())) {
            throw new ServiceException(RESOURCE_SEGMENT_CONTENT_NOT_EMPTY);
        }
        KnowledgeResourceEntity file = iResourceService.getNotNull(req.getDocId());
        if (ProcessEnum.ARCHIVE.getValue().equals(file.getHandleStatus())) {
            throw new ServiceException(RESOURCE_ARCHIVE_LIMIT, "不支持新增分段");
        }
        if (!FileTypeEnum.DOCUMENT.getType().equals(file.getType())) {
            throw new ServiceException(RESOURCE_SEGMENT_ADD_LIMIT);
        }

        ResourceSegmentEntity segment = new ResourceSegmentEntity();
        segment.setSegmentId(UUID.randomUUID().toString());
        segment.setSort(iResourceSegmentService.getSort(req.getNumber(), req.getDocId(), "", 1D));
        segment.setNumber(req.getNumber());
        segment.setFileType(file.getType());
        segment.setDocId(file.getDocId());
        segment.setTitle(req.getTitle());
        segment.setContent(iResourceService.replaceAndDelete(file.getTenantId(), file.getDocId(), "", req.getContent(), StoragePathEnum.Segment));
        segment.setPage(req.getPage());
        segment.setPosition(req.getPosition());
        segment.setStatus(ProcessEnum.Success.getValue());
        segment.setCreatorId(getUserCode());
        segment.setTenantId(getTenantId());
        iResourceSegmentService.save(segment);
        reSort(segment.getDocId(), null, segment.getSort());
        List<ChunkEntity> chunkEntityList = iResourceSegmentService.reChunk(file, JSON.parseObject(file.getStrategy(), DocStrategyModel.class), segment);


        iChunkIBaseService.add(file.getTenantId(), chunkEntityList, true);

    }

    /**
     * 获取分段
     */
    public SegmentResp getSegment(String segmentId) {
        ResourceSegmentEntity segment = iResourceSegmentService.getNotNull(segmentId);
        SegmentResp resp = new SegmentResp(segment);
        List<Long> ids = iResourceSegmentService.getSortIds(segment.getDocId());
        resp.setNumber(ids.indexOf(segment.getId()));
        return resp;
    }

    /**
     * 分段列表 1
     */
    public BasePaginResp<SegmentPageResp> segmentPage(SegmentPageReq req) {
        KnowledgeResourceEntity resource = iResourceService.getNotNull(req.getDocId());
        Page<ResourceSegmentEntity> pagedSegments = iResourceSegmentService.page(req.getPage(), req.getPageSize(), req.getDocId(), FileTypeEnum.valueOf(resource.getType()), req.getContent(), req.getStatus());
        List<SegmentPageResp> respList = FileConvert.INSTANCE.toResp(pagedSegments.getRecords());
        List<String> segmentIds = respList.stream().map(SegmentPageResp::getSegmentId).collect(Collectors.toList());
        List<TaskEntity> tasks = iTaskService.listByKeys(segmentIds);
        List<VideoFrameEntity> videoFrameEntities;
        if (FileTypeEnum.VIDEO.getType().equals(resource.getType()) && respList.stream().anyMatch(s -> StringUtils.isBlank(s.getThumbnail()))) {
            videoFrameEntities = iVideoFrameIBaseService.listByDocId(getTenantId(), req.getDocId());
        } else {
            videoFrameEntities = new ArrayList<>();
        }
        respList.forEach(resp -> {
            if (Objects.isNull(resp.getNumber()) && Objects.nonNull(resp.getSort()))
                resp.setNumber(resp.getSort().intValue() + 1);
            Optional<TaskEntity> opTask = tasks.stream().filter(t -> resp.getSegmentId().equals(t.getKey())).findFirst();
            opTask.ifPresent(taskEntity -> resp.setTaskId(taskEntity.getId()));
            if (resp.getStartTimestamp() != null && resp.getEndTimestamp() != null) {
                // 计算设置时长
                resp.setDuration(resp.getEndTimestamp() - resp.getStartTimestamp());
                // 视频文件尝试获取缩略图
                if (FileTypeEnum.VIDEO.getType().equals(resource.getType()) && StringUtils.isBlank(resp.getThumbnail())) {
                    videoFrameEntities.stream().filter(s -> s.getTimePoint() != null && s.getTimePoint() > resp.getStartTimestamp() && s.getTimePoint() < resp.getEndTimestamp()).findFirst().ifPresent(f -> resp.setThumbnail(f.getUrl()));
                    if (StringUtils.isBlank(resp.getThumbnail())) {
                        videoFrameEntities.stream().filter(s -> s.getTimePoint() != null && s.getTimePoint() >= resp.getStartTimestamp() - 1000 && s.getTimePoint() <= resp.getEndTimestamp() + 1000).findFirst().ifPresent(f -> resp.setThumbnail(f.getUrl()));
                    }
                    if (StringUtils.isBlank(resp.getThumbnail())) {
                        videoFrameEntities.stream().findFirst().ifPresent(f -> resp.setThumbnail(f.getUrl()));
                    }
                }
            }
        });
        return new BasePaginResp<>(pagedSegments.getTotal(), respList);
    }

    /**
     * 更新分段
     */
    public void updateSegment(UpdateSegmentReq req) {
        if (StringUtils.isBlank(req.getContent())) {
            throw new ServiceException(RESOURCE_SEGMENT_CONTENT_NOT_EMPTY);
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("updateSegment");
        try {
            ResourceSegmentEntity resourceSegment = iResourceSegmentService.getNotNull(req.getSegmentId());
            stopWatch.stop();
            stopWatch.start("updateSegment--getRes");
            Long id = resourceSegment.getId();
            KnowledgeResourceEntity doc = iResourceService.getNotNull(req.getDocId());
            if (ProcessEnum.ARCHIVE.getValue().equals(doc.getHandleStatus())) {
                throw new ServiceException(RESOURCE_ARCHIVE_LIMIT, "不支持编辑分段");
            }
            stopWatch.stop();
            stopWatch.start("updateSegment--getSort");
            ResourceSegmentEntity segment = new ResourceSegmentEntity();
            segment.setId(id);
            if (Objects.nonNull(req.getNumber())) {
                segment.setSort(iResourceSegmentService.getSort(req.getNumber(), req.getDocId(), req.getSegmentId(), resourceSegment.getSort()));
            }
            stopWatch.stop();
            stopWatch.start("updateSegment--replaceAndDelete");
            segment.setTitle(req.getTitle());
            if (StringUtils.isNotBlank(req.getContent())) {
                segment.setContent(iResourceService.replaceAndDelete(doc.getTenantId(), doc.getDocId(), resourceSegment.getContent(), req.getContent(), StoragePathEnum.Segment));
            }
            if (StringUtils.isNotBlank(req.getPosition())) {
                segment.setPosition(req.getPosition());
            }
            segment.setPage(req.getPage());
            stopWatch.stop();
            stopWatch.start("updateSegment--updateById");
            iResourceSegmentService.updateById(segment);
            stopWatch.stop();
            stopWatch.start("updateSegment--reSort");
            reSort(req.getDocId(), req.getSort(), segment.getSort());
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        }

    }

    /**
     * 重排序
     */
    public void reSort(String docId, Double oldScore, Double newScore) {
        if (Objects.nonNull(oldScore) && oldScore.equals(newScore))
            return;
        Double min;
        Double max;
        if (Objects.nonNull(oldScore) && Objects.nonNull(newScore)) {
            min = Math.min(oldScore, newScore);
            max = Math.max(oldScore, newScore);
        } else {
            min = newScore;
            max = null;
        }
        List<ResourceSegmentEntity> rangeSegList = iResourceSegmentService.listRangeByDocId(docId, min, max);
        if (CollectionUtils.isEmpty(rangeSegList))
            return;
        ResourceSegmentEntity pre = iResourceSegmentService.getPre(docId, min);
        Integer startNum = 0;
        if (Objects.nonNull(pre))
            startNum = pre.getNumber();
        for (ResourceSegmentEntity seg : rangeSegList) {
            startNum++;
            seg.setNumber(startNum);
        }
        iResourceSegmentService.updateBatch(rangeSegList);


    }

    /**
     * 删除分段
     */
    public void deleteSegment(DeleteSegmentReq req) {
        log.info("segment-delete-start");
        if (CollectionUtils.isEmpty(req.getSegmentIds())) {
            return;
        }
        String segmentId = req.getSegmentIds().get(0);
        ResourceSegmentEntity segment = iResourceSegmentService.getNotNull(segmentId);
        log.info("segment-delete-get-end");
        KnowledgeResourceEntity doc = iResourceService.getNotNull(segment.getDocId());
        log.info("segment-delete-res-get-end");
        if (ProcessEnum.ARCHIVE.getValue().equals(doc.getHandleStatus())) {
            throw new ServiceException(RESOURCE_ARCHIVE_LIMIT, "不支持删除分段");
        }
        FileTypeEnum fileTypeEnum = FileTypeEnum.valueOf(doc.getType());
        if (FileTypeEnum.IMAGE.equals(fileTypeEnum)) {
            throw new ServiceException(RESOURCE_IMAGE_SEGMENT_DELETE);
        }
        resourceUtilService.deleteSegment(segment);
        log.info("segment-delete-delete-end");
        reSort(segment.getDocId(), null, segment.getSort());
        log.info("segment-delete-end");
    }

    /**
     * 清理指定文件
     *
     * @param docId
     * @return
     */
    public BaseResp<Void> clear(String docId) {
        KnowledgeResourceEntity resource = iResourceService.getNotNull(docId);
        if (!resource.getGroupId().equals(-1L))
            throw new ServiceException(RESOURCE_CLEAR_TEMP);
        List<String> urlList = new ArrayList<>();
        urlList.add(resource.getUrl());
        if (StringUtils.isNotBlank(resource.getPreviewSrc())) {
            urlList.add(resource.getPreviewSrc());
        }
        iResourceService.deleteByIds(Collections.singletonList(resource.getId()));
        IProvider selectProvider = cosHelper.getSelectProvider();
        for (String url : urlList) {
            try {
                String key = url.replace(selectProvider.getDefaultBucket().getUrlPrefix(), "");
                if (key.startsWith("/"))
                    key = key.substring(1);
                selectProvider.delete(selectProvider.getDefaultBucket().getName(), key);
            } catch (Exception ex) {
                log.error("删除文件失败：{}", ex.getMessage(), ex);
            }
        }
        return new BaseResp<>();
    }

    /**
     * 人脸列表
     */
    public BaseResp<List<FaceListResp>> faceList(FaceListReq req) {
        StopWatch stopWatch = new StopWatch();
        StopwatchUtil.preStart(stopWatch, "iResourceFaceServicelist");
        List<ResourceFaceEntity> faces = iResourceFaceService.list(req.getDocId(), req.getSegmentId());
        StopwatchUtil.stop(stopWatch);
        List<String> personIds = faces.stream().map(ResourceFaceEntity::getSourceId).distinct().collect(Collectors.toList());
        List<PersonInfoDTO> personInfoDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(personIds)) {
            StopwatchUtil.preStart(stopWatch, "faceIdList");
            personInfoDTOList = BatchTaskUtils.batchQuery(personIds, 50, c -> {
                List<PersonInfoDTO> data = personClient.personInfo(new HttpPersonInfoReq().setTenantId(getTenantId()).setIds(c)).getData();
                return data;
            }, null);
            StopwatchUtil.stop(stopWatch);
        }
        log.info("faceList-get-end:{},统计：{}", req.getDocId(), stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        Map<String, PersonInfoDTO> personInfoMap = personInfoDTOList.stream().collect(Collectors.toMap(PersonInfoDTO::getId, person -> person));
        List<FaceListResp> respList = new ArrayList<>();
        faces.stream().collect(Collectors.groupingBy(ResourceFaceEntity::getSourceId)).forEach((sourceId, faceList) -> {
            ResourceFaceEntity faceEntity = faceList.get(0);
            PersonInfoDTO personInfoDTO = personInfoMap.get(faceEntity.getSourceId());
            if (Objects.isNull(personInfoDTO) || ObjectUtil.equal(personInfoDTO.getStatus(), 0)) {
                return;
            }
            if (!Boolean.TRUE.equals(req.getShowUnKnowFace()) && ObjectUtil.equal(personInfoDTO.getIsKnown(), 0)) {
                return;
            }
            FaceListResp resp = new FaceListResp();
            resp.setSegmentId(req.getSegmentId());
            resp.setSourceId(sourceId);
            resp.setScenes(new ArrayList<>());
            resp.setTotalTime(faceList.size());
            faceList.forEach(face -> {
                SceneItem sceneItem = new SceneItem();
                sceneItem.setStartTimestamp(face.getTimePoint());
                sceneItem.setEndTimestamp(face.getTimePoint());
                sceneItem.setPosition(face.getPosition());
                resp.getScenes().add(sceneItem);
            });
            resp.setName(personInfoDTO.getName());
            resp.setHeadUrl(personInfoDTO.getViewImage());
            resp.setDesc(personInfoDTO.getDescription());
            resp.setTypeName(personInfoDTO.getTypeName());
            resp.setSensitiveName(personInfoDTO.getSensitiveName());
            resp.setIsFocus(personInfoDTO.getIsFocus());
            respList.add(resp);
        });
        return new BaseResp<>(respList);
    }

    /**
     * 实体标签列表
     */
    public BaseResp<List<OdTagListResp>> odTagList(BaseSegmentReq req) {
        List<ResourceODTagEntity> odTagEntities = iResourceODTagService.list(req.getDocId(), req.getSegmentId());
        //过滤事件
        odTagEntities = odTagEntities.stream().filter(x -> !OdTagTypeEnum.EVENT.getValue().equals(x.getType())).collect(Collectors.toList());
        List<OdTagListResp> respList = new ArrayList<>();
        List<ODLibResp> tagSourceRespList = new ArrayList<>();
        List<ResourceODTagEntity> targetOds = odTagEntities.stream().filter(x -> OdTagTypeEnum.ENTITY.getValue().equals(x.getType()) && StringUtils.isNotBlank(x.getSampleId())).collect(Collectors.toList());
        List<String> tagSourceIds = targetOds.stream().map(ResourceODTagEntity::getSampleId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tagSourceIds)) {
            ODLibReq odLibReq = new ODLibReq();
            odLibReq.setEnable(1);
            odLibReq.setGuidList(tagSourceIds);
            BaseResp<List<ODLibResp>> listBaseResp = httpPersonClient.listLibByGuidList(odLibReq);
            if (listBaseResp.isSuccess() && CollectionUtils.isNotEmpty(listBaseResp.getData())) {
                tagSourceRespList = listBaseResp.getData();
            }
        }
        List<ODLibResp> finalTagSourceRespList = tagSourceRespList;
        odTagEntities.forEach(odTagEntity -> {
            finalTagSourceRespList.stream().filter(t -> t.getGuid().equals(odTagEntity.getSampleId())).findFirst().ifPresent(tag -> {
                if (StringUtils.isNotBlank(tag.getCatalogName()) && tag.getCatalogName().equals("属性")) {
                    odTagEntity.setType(OdTagTypeEnum.ATTRIBUTE.getValue());
                }
                odTagEntity.setName(tag.getName());
            });
        });


        odTagEntities.stream().collect(Collectors.groupingBy(ResourceODTagEntity::getType)).forEach((type, tags) -> {

            tags.stream().collect(Collectors.groupingBy(ResourceODTagEntity::getName)).forEach((name, odTags) -> {
                OdTagListResp resp = new OdTagListResp();
                resp.setName(name);
                resp.setSegmentId(req.getSegmentId());
                resp.setScenes(new ArrayList<>());
                resp.setType(type);
                resp.setTypeName(OdTagTypeEnum.valueOf(type).getName());
                resp.setTypeNameEn(OdTagTypeEnum.valueOf(type).getEnglishName());
                odTags.forEach(odTag -> {
                    SceneItem sceneItem = new SceneItem();
                    sceneItem.setStartTimestamp(odTag.getTimePoint());
                    sceneItem.setEndTimestamp(odTag.getTimePoint());
                    sceneItem.setPosition(odTag.getPosition());
                    resp.getScenes().add(sceneItem);
                });
                respList.add(resp);
            });
        });

        return new BaseResp<>(respList);
    }

    /***
     * 查询专名标签列表
     */
    public BaseResp<List<TerminologyListResp>> terminologyList(BaseSegmentReq req) {
        List<ResourceTerminologyEntity> terminologyEntities = iResourceTerminologyService.list(req.getDocId(), req.getSegmentId());
        List<TerminologyListResp> respList = new ArrayList<>();
        terminologyEntities.stream().collect(Collectors.groupingBy(ResourceTerminologyEntity::getType)).forEach((type, terminologies) -> {
            TerminologyListResp resp = new TerminologyListResp();
            resp.setType(type);
            resp.setLabels(new ArrayList<>());
            terminologies.stream().collect(Collectors.groupingBy(ResourceTerminologyEntity::getName)).forEach((name, labels) -> {
                        LabelItem labelItem = new LabelItem();
                        labelItem.setName(name);
                        labelItem.setScenes(new ArrayList<>());
                        labels.forEach(odTag -> {
                            SceneItem sceneItem = new SceneItem();
                            sceneItem.setStartTimestamp(odTag.getTimePoint());
                            sceneItem.setEndTimestamp(odTag.getTimePoint());
                            sceneItem.setPosition(odTag.getPosition());
                            labelItem.getScenes().add(sceneItem);
                        });
                        resp.getLabels().add(labelItem);
                    }
            );

            respList.add(resp);
        });
        return new BaseResp<>(respList);
    }

    /**
     * 应用基本信息
     *
     * @return
     */
    public BaseResp<AppInfoResp> appInfo() {
        AppInfoResp resp = AppInfoConvert.INSTANCE.toResp(appInfoConfig);
        resp.setFileCount(iResourceService.getCount(getTenantId()));
        resp.setTempCount(iResourceService.getTempCount(getTenantId()));
        resp.setQaCount(faqService.getCount(getTenantId()));
        resp.setQuestionCount(questionBankService.getTotalCount(getTenantId()));
        return new BaseResp<>(resp);
    }

    /**
     * 临时文件清理
     *
     * @return
     */
    public BaseResp<AppInfoResp> clearTemp() {
        List<KnowledgeResourceEntity> list = iResourceService.listByGroupId(getTenantId(), null, -1);
        resourceUtilService.delete(list);
        return new BaseResp<>();
    }


    public BaseResp<List<String>> eventList() {
        return new BaseResp<>(eventConfig.getItems().stream().map(EventConfig.Event::getName).collect(Collectors.toList()));
    }

    public BaseResp<List<DocInfoEntityResp>> search(FileSearchReq req, List<Long> groupIds) {
        List<KnowledgeResourceEntity> resList = iResourceService.search(getTenantId(), req.getType(), req.getKeyword(), groupIds);
        return new BaseResp<>(DocConvert.INSTANCE.toResp(resList));
    }


    /**
     * 重试
     *
     * @param req
     */
    public TaskEntity segmentRetry(SegmentRetryReq req) {
        iResourceSegmentService.updateStatus(req.getSegmentId(), ProcessEnum.Executing.getValue());
        return iTaskService.send(getTenantId(), getUserCode(), 0L, null, req.getSegmentId(), TaskTypeEnum.SegRetry);
    }

    public List<SearchHit<VideoFrameEntity>> frameList(String fileId) {
        return iVideoFrameIBaseService.list(getTenantId(), Collections.singletonList(fileId));
    }

    /**
     * 获取下载地址
     *
     * @param req 请求
     * @return 返回下载地址
     */
    public String download(DownloadFileReq req) {
        KnowledgeResourceEntity file = iResourceService.getNotNull(req.getDocId());
        String url = operationLogProxyService.download(file);
        if (!StringComUtils.isUrlFileCorrupted(url, 1)) {
            throw new ServerException(NOT_EXIST, "源文件");
        }
        return "/file/download/url?url=" + URLUtil.encodeAll(url) + "&fileName=" + URLUtil.encode(file.getTitle()) + "." + file.getSuffix();
    }


    /**
     * 分享文件
     *
     * @param req 请求
     */
    public void copy(@Valid CopyReq req) {
        //校验文件权限
        List<KnowledgeResourceEntity> resources = iResourceService.listByResIds(req.getFileIds());
        if (CollectionUtils.isEmpty(resources)) {
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "资源");
        }
        KnowledgeGroupEntity target = knowledgeGroupService.getNotNull(req.getTargetId());

        List<KnowledgeGroupEntity> groups = knowledgeGroupService.listByIds(resources.stream().map(KnowledgeResourceEntity::getGroupId).collect(Collectors.toList()));
        for (KnowledgeGroupEntity source : groups) {
            if (source.getVisibleType() > target.getVisibleType()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录可见等级不足,");
            }
            if (source.getImportantLevel() > target.getImportantLevel()) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_ALLOW_MOVE, "目标目录重要等级不足,");
            }
        }
        //校验空间是否足够
        Long size = resources.stream().mapToLong(KnowledgeResourceEntity::getSize).sum();
        IKnowPowerResp iKnowPowerResp = iUserCenterService.getIknowPower(getTenantId());
        Long used = 0L;
        Long limit = 0L;
        if (ResourceVisibleTypeEnum.PUBLIC.getValue().equals(target.getVisibleType())) {
            if (iKnowPowerResp.getOrgSpace() > 0) {
                used = iResourceService.sumSizeByVisibleType(getTenantId(), null, ResourceVisibleTypeEnum.PUBLIC);
                limit = iKnowPowerResp.getOrgSpace();
            }
        } else {
            if (iKnowPowerResp.getMySpace() > 0) {
                used = iResourceService.sumSizeByVisibleType(getTenantId(), getUserCode(), ResourceVisibleTypeEnum.PERSONAL);
                limit = iKnowPowerResp.getMySpace();
            }
        }
        if (limit > 0 && used + size > limit) {
            throw new ServiceException(RESOURCE_SIZE_LIMIT);
        }


        resources.forEach(resource -> {
            KnowledgeResourceEntity exists = iResourceService.getByGroupId_Title_Suffix(req.getTargetId(), resource.getTitle(), resource.getSuffix());
            if (Objects.nonNull(exists)) {
                String title = iResourceService.getNewTitle(req.getTargetId(), resource.getTitle(), resource.getSuffix());
                resource.setTitle(title);
            }
            resource.setDocId(UUID.randomUUID().toString());
            resource.setPreviewSrc("");
            resource.setUrl(iUtilService.copy(resource.getUrl(), StoragePathEnum.File.getValueByUrl(getTenantId(), resource.getDocId(), resource.getUrl())));
            resource.setGroupId(req.getTargetId());
            resource.setHandleStatus(ProcessEnum.Fail.getValue());
            resource.setId(0L);
            resource.setUpdateTime(LocalDateTime.now());
            resource.setCreateTime(LocalDateTime.now());
            JSONObject jsonExtInfo = resource.getJsonExtInfo();
            if (jsonExtInfo.containsKey("system_merge")) {
                jsonExtInfo.remove("system_merge");
                resource.setExtInfo(jsonExtInfo.toJSONString());
            }
        });
        iResourceService.saveBatch(resources);
        resources.forEach(resource -> resourceUtilService.handResource(resource, resource.getTemplateId(), null));
    }

    public void deleteByConversationId(DeleteByConversationIdReq req) {
        List<ResourceExtEntity> resourceExtList = iResourceExtService.list(ResourceExtTypeEnum.ConversationId, req.getConversationIds());
        if (CollectionUtils.isEmpty(resourceExtList)) {
            return;
        }
        List<String> resIds = resourceExtList.stream().map(ResourceExtEntity::getDocId).collect(Collectors.toList());
        iResourceService.delteByResIds(resIds);
        iTaskService.send(getTenantId(), getUserCode(), 0L, resIds, UUID.randomUUID().toString(), TaskTypeEnum.DeleteRes);
    }

    /**
     * 清理过期资源
     */
    public void deleteExpired() {
        List<ResourceExtEntity> resourceExtList = iResourceExtService.listLt(ResourceExtTypeEnum.Expired, StringComUtils.convertStr(LocalDateTime.now()));
        List<String> resIds = resourceExtList.stream().map(ResourceExtEntity::getDocId).collect(Collectors.toList());
        List<KnowledgeResourceEntity> resources = iResourceService.listByResIds(resIds);
        resourceUtilService.delete(resources);
    }

    /**
     * 场景标签重跑
     *
     * @param req 请求
     */
    public void sceneTagRerun(SceneTagRerunReq req) {
        iTaskService.send(getTenantId(), getUserCode(), 0L, new TaskSceneTagRerunReq(req.getTags().stream().map(SceneTagRerunReq.TagItem::getId).collect(Collectors.toList())), UUID.randomUUID().toString(), TaskTypeEnum.SceneTagRerun);
    }

    /**
     * @description: 查询事件列表
     * @author: hejianbao
     * @date: 2025/7/25 09:12
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    public List<ResourceOdTagResp> listResourceOdTag(ResourceOdTagReq resourceOdTagReq) {
        List<KnowledgeResourceEntity> knowledgeResourceEntities = iResourceService.listByGroupIds(resourceOdTagReq.getGroupIdList());
        List<Integer> fileTypes = resourceOdTagReq.getFileTypes();
        if (CollectionUtils.isNotEmpty(fileTypes)) {
            knowledgeResourceEntities = knowledgeResourceEntities.stream().filter(t -> fileTypes.contains(t.getType())
                    && ObjectUtil.equal(t.getHandleStatus(), ProcessEnum.Success.getValue())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(knowledgeResourceEntities)) {
            return Lists.newArrayList();
        }
        List<String> docIdlist = knowledgeResourceEntities.stream().map(KnowledgeResourceEntity::getDocId).distinct().collect(Collectors.toList());
        List<ResourceODTagEntity> resourceODTagEntities = iResourceODTagService.listResourceOdTag(docIdlist, resourceOdTagReq.getName(),
                resourceOdTagReq.getType());
        return resourceODTagEntities.stream().filter(t -> StringUtils.isNotBlank(t.getName())).map(t -> {
            return ResourceOdTagResp.builder()
                    .name(t.getName())
                    .build();

        }).distinct().collect(Collectors.toList());
    }

    /**
     * @description: 查询人脸列表
     * @author: hejianbao
     * @date: 2025/7/25 09:12
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    public List<ResourceFaceResp> listResourceFace(ResourceFaceReq resourceFaceReq) {
        List<KnowledgeResourceEntity> knowledgeResourceEntities = iResourceService.listByGroupIds(resourceFaceReq.getGroupIdList());
        List<Integer> fileTypes = resourceFaceReq.getFileTypes();
        if (CollectionUtils.isNotEmpty(fileTypes)) {
            knowledgeResourceEntities = knowledgeResourceEntities.stream().filter(t -> fileTypes.contains(t.getType())
                    && ObjectUtil.equal(t.getHandleStatus(), ProcessEnum.Success.getValue())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(knowledgeResourceEntities)) {
            return Lists.newArrayList();
        }
        List<String> docIdList = knowledgeResourceEntities.stream().map(KnowledgeResourceEntity::getDocId).distinct().collect(Collectors.toList());
        List<ResourceFaceEntity> resourceFaceEntities = iResourceFaceService.listResourceFace(docIdList, resourceFaceReq.getName());
        if (CollectionUtils.isEmpty(resourceFaceEntities)) {
            return Lists.newArrayList();
        }
        HttpPersonInfoReq req = new HttpPersonInfoReq();
        req.setIds(resourceFaceEntities.stream().map(ResourceFaceEntity::getSourceId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(req.getIds())) {
            return Lists.newArrayList();
        }
        BaseResp<List<PersonInfoDTO>> resp = httpPersonClient.personInfo(req);

        return resp.getData().stream().filter(t -> ObjectUtil.equal(t.getIsKnown(), 1)).map(t -> {
            return ResourceFaceResp.builder()
                    .name(t.getName())
                    .headUrl(CollectionUtils.isEmpty(t.getPhotos()) ? "" : t.getPhotos().get(0).getUrl())
                    .sourceId(t.getId())
                    .build();
        }).distinct().collect(Collectors.toList());
    }

    /**
     * @description: 示例库文件查询
     * @author: hejianbao
     * @date: 2025/7/25 13:57
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    public List<DocInfoEntityResp> listSampleResource() {
        if (ObjectUtil.isEmpty(appInfoConfig.getSampleGroupId())) {
            return Lists.newArrayList();
        }
        List<KnowledgeResourceEntity> knowledgeResourceEntities = iResourceService.listSampleResource(appInfoConfig.getSampleGroupId(),
                FileTypeEnum.VIDEO.getType(), ProcessEnum.Success.getValue());
        if (CollectionUtils.isEmpty(knowledgeResourceEntities)) {
            return Lists.newArrayList();
        }
        return DocConvert.INSTANCE.toResp(knowledgeResourceEntities);
    }

    /**
     * @description: 示例库复制文件到指定知识库
     * @author: hejianbao
     * @date: 2025/7/25 16:00
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    public void copySampleResource(CopySampleReq req) {
        if (log.isDebugEnabled()) {
            log.debug("示例库复制文件到指定知识库,入参：req={}", JSON.toJSONString(req));
        }
        //目标分组id
        Long groupId = req.getTargetId();
        //资源ids
        List<String> docIdList = req.getFileIds();
        KnowledgeGroupEntity knowledgeGroupEntity = knowledgeGroupService.getNotNull(groupId);
        if (ObjectUtil.isEmpty(knowledgeGroupEntity)) {
            throw new ServiceException(NOT_EXIST, "目标分组不存在");
        }
        KnowledgeGroupEntity parentGroup = knowledgeGroupService.get(groupId);
        if (parentGroup.getIsSync()) {
            throw new ServiceException(GROUP_SYNC_LIMIT, "新建");
        }
        List<KnowledgeResourceEntity> knowledgeResourceEntities = iResourceService.listByResIds(docIdList);
        if (CollectionUtils.isEmpty(knowledgeResourceEntities)) {
            throw new ServiceException(NOT_EXIST, "资源不存在");
        }

        Long size = knowledgeResourceEntities.stream().mapToLong(KnowledgeResourceEntity::getSize).sum();
        IKnowPowerResp iKnowPowerResp = iUserCenterService.getIknowPower(getTenantId());
        Long used = 0L;
        Long limit = 0L;
        if (ResourceVisibleTypeEnum.PUBLIC.getValue().equals(knowledgeGroupEntity.getVisibleType())) {
            if (iKnowPowerResp.getOrgSpace() > 0) {
                used = iResourceService.sumSizeByVisibleType(getTenantId(), null, ResourceVisibleTypeEnum.PUBLIC);
                limit = iKnowPowerResp.getOrgSpace();
            }
        } else {
            if (iKnowPowerResp.getMySpace() > 0) {
                used = iResourceService.sumSizeByVisibleType(getTenantId(), getUserCode(), ResourceVisibleTypeEnum.PERSONAL);
                limit = iKnowPowerResp.getMySpace();
            }
        }
        if (limit > 0 && used + size > limit) {
            throw new ServiceException(RESOURCE_SIZE_LIMIT);
        }


        Integer renameType = req.getRenameType();

        //校验
        this.limitCheck(groupId, knowledgeResourceEntities.size());
        for (KnowledgeResourceEntity knowledgeResourceEntity : knowledgeResourceEntities) {
            KnowledgeResourceEntity exist = iResourceService.getByGroupId_Title_Suffix(groupId, knowledgeResourceEntity.getTitle(), knowledgeResourceEntity.getSuffix());
            if (ObjectUtil.equal(renameType, RenameTypeEnum.Skip.getValue()) && Objects.nonNull(exist)) {
                continue;
            }
            String title = knowledgeResourceEntity.getTitle();
            String suffix = knowledgeResourceEntity.getSuffix();
            String url = knowledgeResourceEntity.getUrl();
            String previewSrc = knowledgeResourceEntity.getPreviewSrc();
            String cleanWatermarkUrl = knowledgeResourceEntity.getCleanWatermarkUrl();
            String thumbnail = knowledgeResourceEntity.getThumbnail();
            String docId = knowledgeResourceEntity.getDocId();
            String extInfo = knowledgeResourceEntity.getExtInfo();
            String newDocId = UUID.randomUUID().toString();
            String tenantId = getTenantId();
            String userCode = getUserCode();
            knowledgeResourceEntity.init(tenantId, userCode);
            knowledgeResourceEntity.setId(0L);
            knowledgeResourceEntity.setDocId(newDocId);
            knowledgeResourceEntity.setGroupId(groupId);
            knowledgeResourceEntity.setUrl(iUtilService.copy(url, StoragePathEnum.File.getValueByUrl(tenantId, newDocId, url)));
            if (StringUtils.isBlank(knowledgeResourceEntity.getUrl())) {
                throw new ServiceException(KnowledgeCenterErrorCodeEnum.COPY_ERROR);
            }
            if (ObjectUtil.isNotEmpty(exist)) {
                if (ObjectUtil.equal(renameType, RenameTypeEnum.Cover.getValue())) {
                    resourceUtilService.delete(Collections.singletonList(exist));
                    String oldDocId = exist.getDocId();
                    knowledgeResourceEntity.setId(exist.getId());
                    knowledgeResourceEntity.setDocId(oldDocId);
                    newDocId = oldDocId;
                } else {
                    if (ObjectUtil.equal(renameType, RenameTypeEnum.Rename.getValue())) {
                        knowledgeResourceEntity.setTitle(iResourceService.getNewTitle(groupId, title, suffix));
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(thumbnail)) {
                knowledgeResourceEntity.setThumbnail(iUtilService.copy(thumbnail, StoragePathEnum.File.getValueByUrl(tenantId, newDocId, thumbnail)));
            }
            if (ObjectUtil.isNotEmpty(previewSrc)) {
                knowledgeResourceEntity.setPreviewSrc(iUtilService.copy(previewSrc, StoragePathEnum.File.getValueByUrl(tenantId, newDocId, previewSrc)));
            }
            if (ObjectUtil.isNotEmpty(cleanWatermarkUrl)) {
                knowledgeResourceEntity.setCleanWatermarkUrl(iUtilService.copy(cleanWatermarkUrl,
                        StoragePathEnum.File.getValueByUrl(tenantId, newDocId, cleanWatermarkUrl)));
            }
            if (StringUtils.isNotBlank(extInfo)) {
                JSONObject extObject = JSONObject.parseObject(extInfo);
                this.moveAttach(tenantId, newDocId, extObject);
                knowledgeResourceEntity.setExtInfo(extObject.toJSONString());
            }
            List<ResourceSegmentEntity> originalResourceSegmentList = iResourceSegmentService.listByDocId(docId);
            //保存抽帧数据
            List<VideoFrameEntity> videoFrameEntities = batchSaveSampleFrame(tenantId, userCode, newDocId);
            //保存相关资源信息
            this.saveSegmentResource(originalResourceSegmentList, tenantId, userCode, docId, newDocId, url, knowledgeResourceEntity, videoFrameEntities);
            //语音走ibase,segmentId可以不用匹配
            this.batchSaveSampleResourceAsr(docId, newDocId, tenantId, userCode);
            //资源扩展表
            this.batchSaveSampleResourceExt(docId, newDocId, tenantId, userCode);
            iResourceSegmentService.saveBatch(originalResourceSegmentList);
        }
        for (KnowledgeResourceEntity knowledgeResourceEntity : knowledgeResourceEntities) {
            iResourceService.saveOrUpdate(knowledgeResourceEntity);
        }
        log.info("结束示例库复制文件到指定知识库,req={}", JSON.toJSONString(req));
    }

    /**
     * 复制文件抽帧数据
     *
     * @param tenantId 租户id
     * @param docId    原文件id
     * @param newDocId 新文件id
     * @return 新文件抽帧数据
     */
    private List<VideoFrameEntity> batchSaveSampleFrame(String tenantId, String docId, String newDocId) {
        List<VideoFrameEntity> videoFrameEntities = iVideoFrameIBaseService.listByDocId(tenantId, docId);
        if (CollectionUtils.isEmpty(videoFrameEntities)) {
            return new ArrayList<>();
        }
        videoFrameEntities.forEach(videoFrameEntity -> {
            videoFrameEntity.setDocId(newDocId);
            videoFrameEntity.setTenantId(tenantId);
            videoFrameEntity.setUid("");
            videoFrameEntity.setUrl(StoragePathEnum.Frame.getValueByUrl(tenantId, newDocId, videoFrameEntity.getUrl()));
        });
        iVideoFrameIBaseService.add(tenantId, videoFrameEntities, false);
        return videoFrameEntities;
    }

    /**
     * @description: 保存分段及其相关内容
     * @author: hejianbao
     * @date: 2025/7/29 16:28
     * @param:
     * @return:
     * @return: null
     **/
    private void saveSegmentResource(List<ResourceSegmentEntity> originalResourceSegmentList, String tenantId, String userCode,
                                     String docId, String newDocId, String url, KnowledgeResourceEntity knowledgeResourceEntity, List<VideoFrameEntity> videoFrameEntities) {
        log.info("开始保存分段及其相关内容,docId={},newDocId={}", docId, newDocId);
        for (ResourceSegmentEntity resourceSegmentEntity : originalResourceSegmentList) {
            //根据分段id，进行查询
            String segmentId = resourceSegmentEntity.getSegmentId();
            String thumbnail = resourceSegmentEntity.getThumbnail();
            String originalTenantId = resourceSegmentEntity.getTenantId();
//                String ext = resourceSegmentEntity.getExt();
            String content = resourceSegmentEntity.getContent();
            String newSegmentId = UUID.randomUUID().toString();
            resourceSegmentEntity.init(tenantId, userCode);
            resourceSegmentEntity.setSegmentId(newSegmentId);
            resourceSegmentEntity.setDocId(newDocId);
            resourceSegmentEntity.setExt("");
            resourceSegmentEntity.setId(0L);
            if (ObjectUtil.isNotEmpty(thumbnail)) {
                resourceSegmentEntity.setThumbnail(iUtilService.copy(thumbnail, StoragePathEnum.File.getValueByUrl(tenantId, newDocId, thumbnail)));
            }
            //重新整理分段内容
            if (StringUtils.isNotBlank(content)) {
                List<String> newUrls = StringComUtils.getUrls(content);
                for (String oldUrl : newUrls.stream().distinct().collect(Collectors.toList())) {
                    String copyUrl = iUtilService.copy(oldUrl, StoragePathEnum.Segment.getValueByUrl(tenantId, newDocId, url));
                    if (StringUtils.isNotBlank(copyUrl))
                        content = content.replaceAll(oldUrl, copyUrl);
                }
                resourceSegmentEntity.setContent(content);
            }
            this.batchSaveSampleResourceOdTag(docId, newDocId, segmentId, newSegmentId, tenantId, userCode,videoFrameEntities);
            this.batchSaveSampleResourceFace(docId, newDocId, segmentId, newSegmentId, tenantId, userCode);
            this.batchSaveSampleResourceTerminology(docId, newDocId, segmentId, newSegmentId, tenantId, userCode);
            //语音分块
            this.batchSaveSampleAudioResourceChunk(originalTenantId, segmentId, knowledgeResourceEntity, resourceSegmentEntity);
            //视频分块
            this.batchSaveSampleVideoResourceChunk(originalTenantId, segmentId, knowledgeResourceEntity, resourceSegmentEntity);
            log.info("结束保存分段及其相关内容,docId={},newDocId={}", docId, newDocId);
        }
    }


    /**
     * 移动附件文件
     *
     * @param docId
     * @param extInfo
     */
    public void moveAttach(String teantId, String docId, JSONObject extInfo) {
        Set<String> keys = extInfo.keySet();
        for (String key : keys) {
            Object value = extInfo.get(key);
            if (Objects.isNull(value))
                continue;
            if (value instanceof String) {
                String strValue = value.toString();
                if (strValue.startsWith("[") && strValue.endsWith("]")) {
                    JSONArray arr = JSONArray.parseArray(strValue);
                    for (Object item : arr) {
                        if (!(item instanceof JSONObject)) {
                            continue;
                        }
                        JSONObject obj = (JSONObject) item;
                        if (obj.containsKey("url") && StringUtils.isNotBlank(obj.getString("url"))) {
                            String movedUrl = iUtilService.moveTempToDefault(obj.getString("url"), StoragePathEnum.Attach.getValueByUrl(teantId, docId, obj.getString("url")));
                            if (StringUtils.isNotBlank(movedUrl)) {
                                strValue = strValue.replace(obj.getString("url"), movedUrl);
                            }
                        }
                    }
                    extInfo.put(key, strValue);
                }
                ;

            }
        }
    }

    /**
     * 校验文件限制
     *
     * @param groupId 目录Id
     */
    private void limitCheck(Long groupId, Integer size) {
        if (ObjectUtil.isEmpty(size)) {
            size = 0;
        }
        if (groupId != -1) {
            if (Objects.nonNull(appInfoConfig.getFileLimit())) {
                Long count = iResourceService.getCount(getTenantId());
                if (count + size >= appInfoConfig.getFileLimit()) {
                    throw new ServiceException(RESOURCE_COUNT_LIMIT, String.valueOf(Math.max(0, appInfoConfig.getFileLimit() - count - size)));
                }
            }
        } else {
            if (Objects.nonNull(appInfoConfig.getTempLimit())) {
                Long count = iResourceService.getTempCount(getTenantId());
                if (count + size >= appInfoConfig.getTempLimit()) {
                    throw new ServiceException(RESOURCE_COUNT_LIMIT, String.valueOf(Math.max(0, appInfoConfig.getTempLimit() - count - size)));
                }
            }
        }
    }

    /**
     * @description: 批量保存专名标签
     * @author: hejianbao
     * @date: 2025/7/28 11:30
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    private void batchSaveSampleResourceTerminology(String docId, String newDocId, String segmentId, String newSegmentId, String tenantId, String userCode) {
        if (StringUtils.isEmpty(docId) || StringUtils.isEmpty(newDocId) || StringUtils.isEmpty(segmentId) || StringUtils.isEmpty(newSegmentId)) {
            return;
        }
        List<ResourceTerminologyEntity> originalResourceTerminologyList = iResourceTerminologyService.list(docId, segmentId);
        if (CollectionUtils.isEmpty(originalResourceTerminologyList)) {
            return;
        }
        for (ResourceTerminologyEntity resourceTerminologyEntity : originalResourceTerminologyList) {
            resourceTerminologyEntity.init(tenantId, userCode);
            resourceTerminologyEntity.setDocId(newDocId);
            resourceTerminologyEntity.setSegmentId(newSegmentId);
            resourceTerminologyEntity.setId(0L);
        }
        iResourceTerminologyService.saveBatch(originalResourceTerminologyList);
    }


    /**
     * @description: 批量保存文件扩展信息表
     * @author: hejianbao
     * @date: 2025/7/25 17:12
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    private void batchSaveSampleResourceExt(String docId, String newDocId, String tenantId, String userCode) {
        if (StringUtils.isEmpty(docId) || StringUtils.isEmpty(newDocId)) {
            return;
        }
        List<ResourceExtEntity> originalResourceExtList = iResourceExtService.list(Collections.singletonList(docId), null);
        if (CollectionUtils.isEmpty(originalResourceExtList)) {
            return;
        }
        for (ResourceExtEntity resourceExtEntity : originalResourceExtList) {
            resourceExtEntity.init(tenantId, userCode);
            resourceExtEntity.setDocId(newDocId);
            resourceExtEntity.setId(0L);
        }
        iResourceExtService.saveBatch(originalResourceExtList);
    }

    /**
     * @description: 保存音频分块
     * @author: hejianbao
     * @date: 2025/7/29 16:34
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    private void batchSaveSampleAudioResourceChunk(String originalTenantId, String segmentId, KnowledgeResourceEntity knowledgeResourceEntity,
                                                   ResourceSegmentEntity resourceSegmentEntity) {
        if (StringUtils.isEmpty(originalTenantId) || StringUtils.isEmpty(segmentId) || ObjectUtil.isEmpty(knowledgeResourceEntity)
                || ObjectUtil.isEmpty(resourceSegmentEntity)) {
            return;
        }
        List<AudioChunkEntity> audioSegmentList = iAudioChunkIBaseServiceResource.list(originalTenantId, segmentId, "");
        if (CollectionUtils.isEmpty(audioSegmentList)) {
            return;
        }
        String tenantId = knowledgeResourceEntity.getTenantId();
        String userCode = knowledgeResourceEntity.getCreatorId();
        Long groupId = knowledgeResourceEntity.getGroupId();
        String docId = knowledgeResourceEntity.getDocId();
        String newSegmentId = resourceSegmentEntity.getSegmentId();
        List<AudioChunkEntity> audioChunkEntityList = Lists.newArrayList();
        for (AudioChunkEntity audioChunkEntity : audioSegmentList) {
            String url = audioChunkEntity.getUrl();
            audioChunkEntity.setChunkId(UUID.randomUUID().toString());
            audioChunkEntity.setGroupId(groupId);
            audioChunkEntity.setDocId(docId);
            audioChunkEntity.setSegmentId(newSegmentId);
            audioChunkEntity.setTenantId(tenantId);
            audioChunkEntity.setCreateId(userCode);
            audioChunkEntity.setCreateTime(StringComUtils.convertStr(LocalDateTime.now()));
            if (StringUtils.isNotBlank(url)) {
                String copyUrl = iUtilService.copy(url, StoragePathEnum.Chunk.getValueByUrl(tenantId, docId, url));
                audioChunkEntity.setUrl(copyUrl);
            }
            audioChunkEntityList.add(audioChunkEntity);
        }
        iAudioChunkIBaseServiceResource.add(getTenantId(), audioChunkEntityList, false);
    }

    /**
     * @param originalTenantId
     * @param segmentId
     * @param knowledgeResourceEntity
     * @param resourceSegmentEntity
     * @description: 批量保存分块
     * @author: hejianbao
     * @date: 2025/7/25 16:40
     * @return: null
     */
    private void batchSaveSampleVideoResourceChunk(String originalTenantId, String segmentId,
                                                   KnowledgeResourceEntity knowledgeResourceEntity, ResourceSegmentEntity resourceSegmentEntity) {
        if (StringUtils.isEmpty(originalTenantId) || StringUtils.isEmpty(segmentId)
                || ObjectUtil.isEmpty(knowledgeResourceEntity) || ObjectUtil.isEmpty(resourceSegmentEntity)) {
            return;
        }
        //查询公共知识库的分块
        List<VideoChunkEntity> videoSegmentList = iVideoChunkIBaseServiceResource.list(originalTenantId, segmentId, "");
        if (CollectionUtils.isEmpty(videoSegmentList)) {
            return;
        }
        String extInfo = knowledgeResourceEntity.getExtInfo();
        String tenantId = knowledgeResourceEntity.getTenantId();
        String userCode = knowledgeResourceEntity.getCreatorId();
        Long groupId = knowledgeResourceEntity.getGroupId();
        String docId = knowledgeResourceEntity.getDocId();
        String newSegmentId = resourceSegmentEntity.getSegmentId();
        String content = resourceSegmentEntity.getContent();
        List<VideoChunkEntity> videoChunkEntityList = Lists.newArrayList();
        for (VideoChunkEntity videoChunkEntity : videoSegmentList) {
            String url = videoChunkEntity.getUrl();
            JSONObject meta = videoChunkEntity.getMeta();
            String content1 = videoChunkEntity.getContent();
            videoChunkEntity.setChunkId(UUID.randomUUID().toString());
            videoChunkEntity.setGroupId(groupId);
            videoChunkEntity.setDocId(docId);
            videoChunkEntity.setSegmentId(newSegmentId);
            videoChunkEntity.setCreateTime(StringComUtils.convertStr(LocalDateTime.now()));
            videoChunkEntity.setCreateId(userCode);
            videoChunkEntity.setTenantId(tenantId);
            if (StringUtils.isNotBlank(url)) {
                String copyUrl = iUtilService.copy(url, StoragePathEnum.Chunk.getValueByUrl(tenantId, docId, url));
                videoChunkEntity.setUrl(copyUrl);
            }
            if (ObjectUtil.isNotEmpty(meta) && StringUtils.isNotBlank(extInfo)) {
                videoChunkEntity.setMeta(JSONObject.parseObject(extInfo));
            }
            if (StringUtils.isNotBlank(content1)) {
                videoChunkEntity.setContent(content);
            }
            videoChunkEntityList.add(videoChunkEntity);
        }
        iVideoChunkIBaseServiceResource.add(tenantId, videoChunkEntityList, false);
    }

    /**
     * @param docId
     * @param newDocId
     * @param tenantId
     * @param userCode
     * @description: 批量保存asr
     * @author: hejianbao
     * @date: 2025/7/25 16:40
     * @return:
     */
    private void batchSaveSampleResourceAsr(String docId, String newDocId, String tenantId, String userCode) {
        if (StringUtils.isEmpty(docId) || StringUtils.isEmpty(newDocId)) {
            return;
        }
        List<ResourceASREntity> originalResourceAsrList = iResourceASRService.listByDocId(docId);
        if (CollectionUtils.isEmpty(originalResourceAsrList)) {
            return;
        }
        for (ResourceASREntity resourceASREntity : originalResourceAsrList) {
            resourceASREntity.init(tenantId, userCode);
            resourceASREntity.setDocId(newDocId);
            resourceASREntity.setSegmentId(UUID.randomUUID().toString());
            resourceASREntity.setId(0L);
        }
        iResourceASRService.saveBatch(originalResourceAsrList);
    }

    /**
     * @param docId
     * @param segmentId
     * @param newSegmentId
     * @param tenantId
     * @param userCode
     * @description: 批量保存人脸
     * @author: hejianbao
     * @date: 2025/7/25 16:39
     * @param:
     * @param: null
     * @return:
     */
    private void batchSaveSampleResourceFace(String docId, String newDocId, String segmentId, String newSegmentId, String tenantId, String userCode) {
        if (StringUtils.isEmpty(docId) || StringUtils.isEmpty(newDocId) || StringUtils.isEmpty(segmentId) || StringUtils.isEmpty(newSegmentId)) {
            return;
        }
        List<ResourceFaceEntity> originalResourceFaceList = iResourceFaceService.list(docId, segmentId);
        if (CollectionUtils.isEmpty(originalResourceFaceList)) {
            return;
        }
        for (ResourceFaceEntity resourceFaceEntity : originalResourceFaceList) {
            String headUrl = resourceFaceEntity.getHeadUrl();
            resourceFaceEntity.init(tenantId, userCode);
            resourceFaceEntity.setDocId(newDocId);
            resourceFaceEntity.setSegmentId(newSegmentId);
            resourceFaceEntity.setHeadUrl(iUtilService.copy(headUrl, StoragePathEnum.File.getValueByUrl(tenantId, newDocId, headUrl)));
            resourceFaceEntity.setId(0L);
        }
        iResourceFaceService.saveBatch(originalResourceFaceList);
    }

    /**
     * @param docId
     * @param segmentId
     * @param newSegmentId
     * @param tenantId
     * @param userCode
     * @description: 批量保存实体标签
     * @author: hejianbao
     * @date: 2025/7/25 16:38
     * @param:
     * @return:
     */
    private void batchSaveSampleResourceOdTag(String docId, String newDocId, String segmentId, String newSegmentId, String tenantId, String userCode,List<VideoFrameEntity> videoFrameEntities) {
        if (StringUtils.isEmpty(docId) || StringUtils.isEmpty(newDocId) || StringUtils.isEmpty(segmentId) || StringUtils.isEmpty(newSegmentId)) {
            return;
        }
        List<ResourceODTagEntity> originalResourceODTagList = iResourceODTagService.list(docId, segmentId);
        if (CollectionUtils.isEmpty(originalResourceODTagList)) {
            return;
        }
        for (ResourceODTagEntity resourceODTagEntity : originalResourceODTagList) {
            resourceODTagEntity.init(tenantId, userCode);
            resourceODTagEntity.setDocId(newDocId);
            resourceODTagEntity.setSegmentId(newSegmentId);
            resourceODTagEntity.setId(0L);
            if(StringUtils.isNotBlank(resourceODTagEntity.getExtInfo())){
                TagExtInfoModel extInfo = JSONObject.parseObject(resourceODTagEntity.getExtInfo(),TagExtInfoModel.class);
                videoFrameEntities.stream().filter(x->x.getFrameId().equals(resourceODTagEntity.getFrameId())).findFirst().ifPresent(x->{
                    extInfo.setImage(x.getUrl());
                    resourceODTagEntity.setExtInfo(JSONObject.toJSONString(extInfo));
                });
            }
        }
        iResourceODTagService.saveBatch(originalResourceODTagList);
    }

    /**
     * 批量获取资源信息
     *
     * @param resourceIds 资源id列表
     * @return 资源信息
     */
    public List<KnowledgeResourceEntity> list(List<String> resourceIds) {
        return iResourceService.listByResIds(resourceIds);
    }

    /**
     * 获取单个资源信息
     *
     * @param resourceId 资源id
     * @return 资源信息
     */
    public KnowledgeResourceEntity get(String resourceId) {
        return iResourceService.getNotNull(resourceId);
    }

    /**
     * 获取指定资源的事件标签
     *
     * @param resourceIds 资源id列表
     * @return 标签列表
     */
    public List<ResourceODTagEntity> listEvents(List<String> resourceIds) {
        return iResourceODTagService.listByResIds(resourceIds, OdTagTypeEnum.EVENT.getValue(), null);
    }

    /**
     * 获取扩展信息
     *
     * @param resourceIds 资源id列表
     * @return 扩展
     */
    public List<ResourceExtEntity> listExt(List<String> resourceIds) {
        return iResourceExtService.list(resourceIds, ResourceExtTypeEnum.ImageRecognize);
    }

    /**
     * 任务控制
     *
     * @param resourceIds  资源id 列表
     * @param controlParam 控制 控制参数，1:继续任务,2:暂停任务，3:取消任务
     */
    public void controlTask(List<String> resourceIds, Integer controlParam) {
        // 验证控制参数
        TaskControlEnum taskControl = TaskControlEnum.getByCode(controlParam);
        if (taskControl == null) {
            throw new IllegalArgumentException("无效的任务控制代码: " + controlParam);
        }

        // 获取文件列表
        if (CollectionUtils.isEmpty(resourceIds)) {
            return;
        }

        List<KnowledgeResourceEntity> resources = iResourceService.listByResIds(resourceIds);
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }

        // 循环判断文件当前的状态，根据processenum中的阶段去改变状态
        for (KnowledgeResourceEntity resource : resources) {
            String workflowId = resource.getWorkflowId();
            if (StringUtils.isBlank(workflowId)) {
                continue;
            }
            Integer currentStatus = resource.getHandleStatus();
            ProcessEnum currentProcessEnum = ProcessEnum.valueOf(currentStatus);
            // 如果是暂停操作，用redis记录文件之前的状态
            if (taskControl == TaskControlEnum.PAUSE) {
                if (currentProcessEnum != null && (Objects.equals(currentProcessEnum.getStep(), 3) || Objects.equals(currentProcessEnum.getValue(), ProcessEnum.PAUSED.getValue()))) {
                    continue;
                }
                redisUtil.set(RESOURCE_HANDLE_STATUS_KEY_PREFIX + resource.getDocId(), currentStatus);
                // 更新状态为暂停
                iResourceService.updateStatus(Collections.singletonList(resource.getId()), ProcessEnum.PAUSED);
            }
            // 如果是继续操作，从redis获取之前的状态并恢复
            else if (taskControl == TaskControlEnum.RESUME) {
                if (currentProcessEnum != null && (Objects.equals(currentProcessEnum.getStep(), 3) || !Objects.equals(currentProcessEnum.getValue(), ProcessEnum.PAUSED.getValue()))) {
                    continue;
                }
                Integer previousStatus = (Integer) redisUtil.get(RESOURCE_STUDY_STATUS_KEY_PREFIX + resource.getDocId(), Integer.class);
                if (previousStatus != null) {
                    // 恢复之前的状态
                    iResourceService.updateStatus(Collections.singletonList(resource.getId()), ProcessEnum.valueOf(previousStatus));
                    // 删除redis中的记录
                    redisUtil.del(RESOURCE_HANDLE_STATUS_KEY_PREFIX + resource.getDocId());
                } else {
                    // 如果没有记录，则默认恢复为执行中
                    iResourceService.updateStatus(Collections.singletonList(resource.getId()), ProcessEnum.Executing);
                }
            }
            // 执行工作流控制
            executeWorkflowControl(taskControl, workflowId);
        }
    }

    /**
     * 执行工作流控制操作
     *
     * @param taskControl 控制类型
     * @param workflowId  工作流ID
     */
    private void executeWorkflowControl(TaskControlEnum taskControl, String workflowId) {
        WorkflowAsyncCancelReq request = new WorkflowAsyncCancelReq(workflowId);

        switch (taskControl) {
            case RESUME:
                aasWorkflowClient.resume(request);
                break;
            case PAUSE:
                aasWorkflowClient.pause(request);
                break;
            case CANCEL:
                aasWorkflowClient.cancelAsync(request);
                break;
            default:
                break;
        }
    }

    /**
     * 获取事件列表
     *
     * @param req 请求
     * @return 事件列表
     */
    public BaseResp<List<EventRecognizeResp>> eventRecognizeList(EventRecognizeReq req) {
        KnowledgeResourceEntity resource = iResourceService.getNotNull(req.getDocId());
        List<ResourceODTagEntity> tagList = iResourceODTagService.listByResId(req.getDocId(), OdTagTypeEnum.EVENT.getValue(), null);
        List<EventRecognizeResp> respList = new ArrayList<>();
        tagList.stream().collect(Collectors.groupingBy(ResourceODTagEntity::getName)).forEach((name, subTags) -> {
            List<EventRecognizeResp> sameNameRespList = new ArrayList<>();
            subTags.stream().sorted(Comparator.comparingLong(ResourceODTagEntity::getTimePoint)).forEach(curTag -> {
                TagExtInfoModel tagExtInfo = new TagExtInfoModel();
                if (StringUtils.isNotBlank(curTag.getExtInfo())) {
                    tagExtInfo = JSON.parseObject(curTag.getExtInfo(), TagExtInfoModel.class);
                }
                EventRecognizeResp resp;
                if (sameNameRespList.isEmpty() || curTag.getTimePoint() > (sameNameRespList.get(sameNameRespList.size() - 1).getEndPoint() + 1900)) {
                    resp = new EventRecognizeResp();
                    resp.setName(curTag.getName());
                    resp.setStartPoint(curTag.getTimePoint());
                    resp.setEndPoint(Objects.isNull(tagExtInfo.getEndPoint()) ? (curTag.getTimePoint() + 1000) : tagExtInfo.getEndPoint());
                    resp.setDuration(StringComUtils.convertMillis(resp.getEndPoint() - resp.getStartPoint()));
                    resp.setImages(new ArrayList<>());
                    resp.getImages().add(tagExtInfo.getImage());
                    resp.setDeviceName(tagExtInfo.getDeviceName());
                    if (StringUtils.isBlank(resp.getDeviceName())) {
                        resp.setDeviceName(resource.getTitle());
                    }
                    if (StringUtils.isNotBlank(tagExtInfo.getRecordingTime())) {
                        resp.setStartTime(tagExtInfo.getRecordingTime());
                        resp.setEndTime(StringComUtils.convertStr(StringComUtils.convertDate(tagExtInfo.getRecordingTime()).plus(resp.getEndPoint() - resp.getStartPoint(), ChronoUnit.MILLIS)));
                    } else {
                        resp.setStartTime(StringComUtils.formatSeconds(resp.getStartPoint() / 1000));
                        resp.setEndTime(StringComUtils.formatSeconds(resp.getEndPoint() / 1000));
                    }
                    sameNameRespList.add(resp);
                } else {
                    resp = sameNameRespList.get(sameNameRespList.size() - 1);
                    resp.setEndPoint(Objects.isNull(tagExtInfo.getEndPoint()) ? (curTag.getTimePoint() + 1000) : tagExtInfo.getEndPoint());
                    resp.setDuration(StringComUtils.convertMillis(resp.getEndPoint() - resp.getStartPoint()));
                    resp.getImages().add(tagExtInfo.getImage());
                    if (StringUtils.isNotBlank(tagExtInfo.getRecordingTime())) {
                        resp.setEndTime(StringComUtils.convertStr(StringComUtils.convertDate(tagExtInfo.getRecordingTime()).plus(resp.getEndPoint() - resp.getStartPoint(), ChronoUnit.MILLIS)));
                    } else {
                        resp.setEndTime(StringComUtils.formatSeconds(resp.getEndPoint() / 1000));
                    }
                }

            });
            respList.addAll(sameNameRespList);
        });
        return new BaseResp<>(respList.stream().sorted(Comparator.comparingLong(EventRecognizeResp::getStartPoint).reversed()).collect(Collectors.toList()));
    }

    /**
     * 空间信息
     *
     * @return 空间信息
     */
    public SpaceInfoResp spaceInfo() {
        IKnowPowerResp iKnowPowerResp = iUserCenterService.getIknowPower(getTenantId());
        SpaceInfoResp spaceInfoResp = new SpaceInfoResp();
        spaceInfoResp.setMyResourceLimit(iKnowPowerResp.getMySpace());
        spaceInfoResp.setMyResourceUsed(iResourceService.sumSizeByVisibleType(getTenantId(), getUserCode(), ResourceVisibleTypeEnum.PERSONAL));
        spaceInfoResp.setPublicResourceLimit(iKnowPowerResp.getOrgSpace());
        spaceInfoResp.setPublicResourceUsed(iResourceService.sumSizeByVisibleType(getTenantId(), null, ResourceVisibleTypeEnum.PUBLIC));
        spaceInfoResp.setMaxAnalysisFile(iKnowPowerResp.getMaxAnalysis());
        return spaceInfoResp;


    }

    public BasePaginResp<ResourceFaceResp> pageResourceFace(ResourceFaceReq resourceFaceReq) {
        List<KnowledgeResourceEntity> knowledgeResourceEntities = iResourceService.listByGroupIdsAndFileType(
                resourceFaceReq.getGroupIdList(),
                Lists.newArrayList(FileTypeEnum.VIDEO.getType(), FileTypeEnum.IMAGE.getType()), ProcessEnum.Success.getValue());
        List<Integer> fileTypes = resourceFaceReq.getFileTypes();
        if (CollectionUtils.isNotEmpty(fileTypes)) {
            knowledgeResourceEntities = knowledgeResourceEntities.stream().filter(t -> fileTypes.contains(t.getType())
                    && ObjectUtil.equal(t.getHandleStatus(), ProcessEnum.Success.getValue())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(knowledgeResourceEntities)) {
            return new BasePaginResp<>(0L, Collections.emptyList());
        }
        List<String> docIdList = knowledgeResourceEntities.stream().map(KnowledgeResourceEntity::getDocId).distinct().collect(Collectors.toList());
        Page<ResourceFaceEntity> page = new Page<>(resourceFaceReq.getPage(), resourceFaceReq.getPageSize());
        IPage<ResourceFaceEntity> resourceFaceEntityPage = iResourceFaceService.pageResourceFace(page, docIdList, resourceFaceReq.getName());
        if (CollectionUtils.isEmpty(resourceFaceEntityPage.getRecords())) {
            return new BasePaginResp<>(0L, Collections.emptyList());
        }
        HttpPersonInfoReq req = new HttpPersonInfoReq();
        req.setIds(resourceFaceEntityPage.getRecords().stream().map(ResourceFaceEntity::getSourceId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(req.getIds())) {
            return new BasePaginResp<>(0L, Collections.emptyList());
        }
        BaseResp<List<PersonInfoDTO>> resp = httpPersonClient.personInfo(req);
        Map<String, PersonInfoDTO> personInfoDTOMap = resp.getData().stream().collect(Collectors.toMap(PersonInfoDTO::getId, c -> c));
        return new BasePaginResp<>(resourceFaceEntityPage.getTotal(), resourceFaceEntityPage.getRecords().stream().map(t -> {
            PersonInfoDTO personInfoDTO = personInfoDTOMap.get(t.getSourceId());
            return ResourceFaceResp.builder()
                    .name(Objects.isNull(personInfoDTO) ? t.getName() : personInfoDTO.getName())
                    .headUrl(Objects.isNull(personInfoDTO) || CollectionUtils.isEmpty(personInfoDTO.getPhotos()) ? t.getHeadUrl() : personInfoDTO.getPhotos().get(0).getUrl())
                    .sourceId(t.getSourceId())
                    .build();
        }).collect(Collectors.toList()));
    }

    public BasePaginResp<ResourceOdTagResp> pageTerminology(ResourceTerminologyReq resourceTerminologyReq) {
        // 获取知识资源列表
        List<KnowledgeResourceEntity> knowledgeResourceEntities = iResourceService.listByGroupIdsAndFileType(
                resourceTerminologyReq.getGroupIdList(),
                Lists.newArrayList(FileTypeEnum.VIDEO.getType(), FileTypeEnum.AUDIO.getType()),
                ProcessEnum.Success.getValue());
        if (CollectionUtils.isNotEmpty(resourceTerminologyReq.getFileTypes())) {
            knowledgeResourceEntities = knowledgeResourceEntities.stream().filter(t -> resourceTerminologyReq.getFileTypes().contains(t.getType())).collect(Collectors.toList());
        }
        // 过滤处理成功的资源
//        Integer type = resourceTerminologyReq.getType();
//        if (type != null) {
//            knowledgeResourceEntities = knowledgeResourceEntities.stream()
//                    .filter(t -> type.equals(t.getType()) &&
//                            ObjectUtil.equal(t.getHandleStatus(), ProcessEnum.Success.getValue()))
//                    .collect(Collectors.toList());
//        }

        // 如果没有符合条件的资源，返回空结果
        if (CollectionUtils.isEmpty(knowledgeResourceEntities)) {
            return new BasePaginResp<>(0L, Collections.emptyList());
        }

        // 获取文档ID列表
        List<String> docIdList = knowledgeResourceEntities.stream()
                .map(KnowledgeResourceEntity::getDocId)
                .distinct()
                .collect(Collectors.toList());

        // 分页查询术语标签
        Page<ResourceTerminologyEntity> page = new Page<>(resourceTerminologyReq.getPage(), resourceTerminologyReq.getPageSize());
        // 执行分页查询
        IPage<ResourceTerminologyEntity> terminologyPage = iResourceTerminologyService.page(page, docIdList, resourceTerminologyReq.getName(), resourceTerminologyReq.getType());

        // 转换查询结果为ResourceOdTagResp对象
        List<ResourceOdTagResp> respList = terminologyPage.getRecords().stream()
                .map(entity -> ResourceOdTagResp.builder().name(entity.getName()).build())
                .distinct()
                .collect(Collectors.toList());

        // 返回分页结果
        return new BasePaginResp<>(terminologyPage.getTotal(), respList);
    }

    public BasePaginResp<ResourceOdTagResp> pageResourceTag(ResourceOdTagReq resourceOdTagReq) {
        List<KnowledgeResourceEntity> knowledgeResourceEntities = iResourceService.listByGroupIdsAndFileType(
                resourceOdTagReq.getGroupIdList(),
                Lists.newArrayList(FileTypeEnum.VIDEO.getType(), FileTypeEnum.IMAGE.getType()), ProcessEnum.Success.getValue());
        List<Integer> fileTypes = resourceOdTagReq.getFileTypes();
        if (CollectionUtils.isNotEmpty(fileTypes)) {
            knowledgeResourceEntities = knowledgeResourceEntities.stream().filter(t -> fileTypes.contains(t.getType())
                    && ObjectUtil.equal(t.getHandleStatus(), ProcessEnum.Success.getValue())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(knowledgeResourceEntities)) {
            return new BasePaginResp<>(0L, Collections.emptyList());
        }
        List<String> docIdlist = knowledgeResourceEntities.stream().map(KnowledgeResourceEntity::getDocId).distinct().collect(Collectors.toList());
        IPage<ResourceODTagEntity> resourceODTagEntityIPage = iResourceODTagService.pageResourceOdTag(docIdlist, resourceOdTagReq.getName(),
                resourceOdTagReq.getType(), new Page<>(resourceOdTagReq.getPage(), resourceOdTagReq.getPageSize()));
        return new BasePaginResp<>(resourceODTagEntityIPage.getTotal(), resourceODTagEntityIPage.getRecords().stream().filter(t -> StringUtils.isNotBlank(t.getName())).map(t -> {
            return ResourceOdTagResp.builder()
                    .name(t.getName())
                    .build();

        }).distinct().collect(Collectors.toList()));
    }
}