package com.linker.fusion.knowledgecenter.server.biz;

import com.alibaba.fastjson.JSON;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceASREntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.AudioChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.server.convert.FileConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.file.AsrListReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.audio.AudioChunkAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.audio.AudioChunkDeleteReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.audio.AudioChunkListReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.audio.AudioChunkUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.AsrListResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.AudioChunkResp;
import com.linker.fusion.knowledgecenter.service.domain.EmbeddingService;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IAudioChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.AddChunkDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceASRService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceTerminologyService;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ChunkServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Component
public class AudioBiz extends BaseBiz {
    @Resource
    IAudioChunkIBaseServiceResource iAudioChunkIBaseService;
    @Resource
    IResourceASRService iResourceASRService;

    @Resource
    private EmbeddingService embeddingService;

    @Resource
    private IResourceTerminologyService iResourceTerminologyService;
    @Resource
    private ChunkServiceFactory chunkServiceFactory;
    @Value("${document.image.encode:false}")
    private Boolean imageEncode;


    /**
     * 查询分块列表
     *
     * @param req
     * @return
     */
    public BaseResp<List<AudioChunkResp>> chunkList(AudioChunkListReq req) {
        List<AudioChunkEntity> chunks = iAudioChunkIBaseService.list(getTenantId(), req.getSegmentId(), req.getContent());
        return new BaseResp<>(FileConvert.INSTANCE.toAudioChunkResp(chunks));
    }

    /**
     * asr列表
     *
     * @param req
     * @return
     */
    public BaseResp<List<AsrListResp>> list(AsrListReq req) {
        List<ResourceASREntity> resourceASREntities = iResourceASRService.listByDocId(req.getDocId());
        return new BaseResp<>(FileConvert.INSTANCE.toAsrResp(resourceASREntities));

    }

    public void updateChunk(AudioChunkUpdateReq req) {
        chunkServiceFactory.update(FileTypeEnum.AUDIO, getTenantId(), req.getUid(), req.getContent(), req.getUrl());
    }

    public void addChunk(AudioChunkAddReq req) {
        AddChunkDTO dto = chunkServiceFactory.addPre(req.getDocId(), req.getContent(), req.getUrl(), imageEncode);
        KnowledgeResourceEntity resource = dto.getFile();
        AudioChunkEntity chunk = new AudioChunkEntity();
        chunk.setEnable(Boolean.TRUE.equals(resource.getEnable()) ? 1 : 0);
        chunk.setGroupId(resource.getGroupId());
        chunk.setChunkId(UUID.randomUUID().toString());
        chunk.setSegmentId(req.getSegmentId());
        chunk.setSort(Objects.nonNull(req.getSort()) ? req.getSort() : 0);
        chunk.setTitle(resource.getTitle());
        chunk.setContent(req.getContent());
        chunk.setContentVector(dto.getContentVector());
        chunk.setDocId(req.getDocId());
        chunk.setCreateTime(StringComUtils.convertStr(LocalDateTime.now()));
        chunk.setCreateId(getUserCode());
        chunk.setTenantId(getTenantId());
        chunk.setMeta(JSON.parseObject(resource.getExtInfo()));
        chunk.setSrcType(req.getSrcType());
        chunk.setUrl(dto.getNewUrl());
        chunk.setFileType(resource.getType());
//        chunk.setPersonsLabels(iResourceFaceService.getLabelsBySegmentId(req.getSegmentId()));
//        chunk.setObjectLabels(iResourceODTagService.getLabelsBySegmentId(req.getSegmentId()));
        chunk.setTextLabels(iResourceTerminologyService.getLabelsBySegmentId(req.getSegmentId()));
        iAudioChunkIBaseService.add(getTenantId(), Collections.singletonList(chunk), true);
    }

    public void deleteChunk(AudioChunkDeleteReq req) {
        chunkServiceFactory.delete(FileTypeEnum.AUDIO, getTenantId(), req.getChunkIds());
    }
}
