package com.linker.fusion.knowledgecenter.server.convert;

import com.linker.fusion.knowledgecenter.infrastructure.config.AppInfoConfig;
import com.linker.fusion.knowledgecenter.server.dto.resp.AppInfoResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AppInfoConvert {
    AppInfoConvert INSTANCE = Mappers.getMapper(AppInfoConvert.class);

    AppInfoResp toResp(AppInfoConfig config);
}
