package com.linker.fusion.knowledgecenter.server.controller.api.v2;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.server.biz.SearchBiz;
import com.linker.fusion.knowledgecenter.server.dto.api.v1.ApiAdvancedSearchReq;
import com.linker.fusion.knowledgecenter.server.dto.req.search.AdvancedSearchResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Api(tags = "高级搜索")
@RestController
@RequestMapping("api/knowledge/v1")
public class ApiSearchController {
    @Resource
    private SearchBiz searchBiz;

    @PostMapping("/search/advanced")
    @ApiOperation(value = "高级检索")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<AdvancedSearchResp> advancedSearch(@RequestBody @Valid ApiAdvancedSearchReq req) {
//        return new BaseResp<>(searchBiz.advancedSearch(req));
        return null;
    }


}
