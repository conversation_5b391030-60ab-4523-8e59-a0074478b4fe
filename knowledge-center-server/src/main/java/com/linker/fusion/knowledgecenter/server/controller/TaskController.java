package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.web.annotation.LogFilter;
import com.linker.fusion.knowledgecenter.server.biz.TaskBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.word.ReadTaskReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "任务管理")
@RestController
@RequestMapping("/task")
public class TaskController
{
    @Resource
    private TaskBiz taskBiz;
    @PostMapping("/read")
    @ApiOperation(value = "任务设为已读", tags = "v1.6.2")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    public BaseResp<Void> readTask(@RequestBody ReadTaskReq req) {
        taskBiz.readTask(req);
        return new BaseResp<>();
    }

    @GetMapping("/statusListener")
    @ApiOperation(value = "任务状态监听", tags = {"v1.6.2"})
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @LogFilter
    public SseEmitter statusListener(@RequestParam(value = "ids") List<Long> ids) {
        return taskBiz.statusListener(ids);
    }

}
