package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.alibaba.fastjson.JSON;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TemplateEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.model.AudioStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.DocStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.ImageStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.VideoStrategyModel;
import com.supalle.autotrim.AutoTrim;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@AutoTrim
public class TemplateResp {

    @ApiModelProperty("模版id")
    private Long id;

    @ApiModelProperty("模版名称")
    private String name;

    @ApiModelProperty("模版描述")
    private String description;

    @ApiModelProperty("模版来源 0-内置 1-自定义")
    private Integer source;

    @ApiModelProperty("知识类型 1-文档 2-图片 3-视频 4-音频")
    private Integer type;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("是否生效")
    private Boolean enable;

    @ApiModelProperty("是否默认")
    private Boolean isDefault;

    @ApiModelProperty("文档切分策略")
    private DocStrategyModel strategyDoc;

    @ApiModelProperty("图片切分策略")
    private ImageStrategyModel strategyImage;

    @ApiModelProperty("视频切分策略")
    private VideoStrategyModel strategyVideo;

    @ApiModelProperty("语音切分策略")
    private AudioStrategyModel strategyAudio;

    @ApiModelProperty("示例图片URL")
    private String previewUrl;

    public TemplateResp(TemplateEntity entity) {
        this.id = entity.getId();
        this.name = entity.getName();
        this.description = entity.getDescription();
        this.source = entity.getSource();
        this.type = entity.getType();
        this.sort = entity.getSort();
        this.enable = entity.getEnable();
        this.isDefault = entity.getIsDefault();
        this.previewUrl = entity.getPreviewUrl();

        FileTypeEnum fileTypeEnum = FileTypeEnum.valueOf(type);
        switch (fileTypeEnum) {
            case DOCUMENT:
                strategyDoc = JSON.parseObject(entity.getStrategy(), DocStrategyModel.class);
                break;
            case IMAGE:
                strategyImage = JSON.parseObject(entity.getStrategy(), ImageStrategyModel.class);
                break;
            case VIDEO:
                strategyVideo = JSON.parseObject(entity.getStrategy(), VideoStrategyModel.class);
                break;
            case AUDIO:
                strategyAudio = JSON.parseObject(entity.getStrategy(), AudioStrategyModel.class);
                break;
            default:
                throw new IllegalArgumentException("不支持的文件类型: " + type);
        }
    }
}
