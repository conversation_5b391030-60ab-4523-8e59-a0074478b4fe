package com.linker.fusion.knowledgecenter.server.biz;

import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.infrastructure.entity.CustomEntity;
import com.linker.fusion.knowledgecenter.server.convert.CustomConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.core.BatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.core.EnableReq;
import com.linker.fusion.knowledgecenter.server.dto.req.custom.CustomAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.custom.CustomPageReq;
import com.linker.fusion.knowledgecenter.server.dto.req.custom.CustomUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.CustomFillInResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.CustomInfoEntityResp;
import com.linker.fusion.knowledgecenter.service.domain.custom.CustomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class CustomBiz extends BaseBiz {

    @Resource
    private CustomService customService;

    public void add(CustomAddReq req) {
        customService.create(CustomConvert.INSTANCE.toDto(req, UserContext.getUser()));
    }

    public BasePaginResp<CustomInfoEntityResp> page(CustomPageReq req) {
        BasePaginResp<CustomEntity> page = customService.page(CustomConvert.INSTANCE.toDto(req, UserContext.getUser()));
        return new BasePaginResp<>(page.getTotal(), CustomConvert.INSTANCE.toResp(page.getData()));
    }

    public void update(CustomUpdateReq req) {
        customService.update(CustomConvert.INSTANCE.toDto(req, UserContext.getUser()));
    }

    public void enable(EnableReq req) {
        customService.enable(getTenantId(), getUserCode(), req.getIds(), req.getEnable());
    }

    public void delete(BatchBaseReq req) {
        customService.delete(getTenantId(), getUserCode(), req.getIds());
    }


    public BaseResp<List<CustomFillInResp>> queryFillInfo(Integer manageType) {
        List<CustomEntity> customEntityList = customService.queryFillInfo(getTenantId(), manageType);
        return new BaseResp<>(CustomConvert.INSTANCE.toFillInfo(customEntityList));
    }
}
