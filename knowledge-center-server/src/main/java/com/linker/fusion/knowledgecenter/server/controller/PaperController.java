package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.auth.enums.InterfaceType;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.server.biz.PaperBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.paper.PaperCheckReq;
import com.linker.fusion.knowledgecenter.server.dto.req.paper.PaperGenerateReq;
import com.linker.fusion.knowledgecenter.server.dto.req.paper.PaperSubmitReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.PaperCheckResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.PaperInfoResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.PaperScoreResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.PaperSubmitResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @Author: Yang<PERSON>iyan
 * @Description: 类说明
 * @Date: Created in 2025/4/2
 */
@Api(tags = "试卷")
@RestController
@RequestMapping("/examination/paper")
public class PaperController {

    @Autowired
    private PaperBiz paperBiz;

    @ApiOperation("生成试卷")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/generate")
    public BaseResp<String> generate(@RequestBody PaperGenerateReq req) {
        return paperBiz.generate(req);
    }

    @ApiOperation("查询试卷详情")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @GetMapping("/detail")
    public BaseResp<PaperInfoResp> detail(@RequestParam("paperUniqueId") String paperUniqueId) {
        return paperBiz.detail(paperUniqueId);
    }
    @ApiOperation("答题保存回答")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/save-answer")
    public BaseResp<PaperCheckResp> saveAnswer(@RequestBody PaperCheckReq req) {
        return paperBiz.check(req);
    }

    @ApiOperation("试卷最终提交，返回最终成绩")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @PostMapping("/final-submit")
    public BaseResp<PaperSubmitResp> submit(@RequestBody PaperSubmitReq req) {
        return paperBiz.submit(req);
    }

    @ApiOperation("获取试卷回答最终结果以及回答历史记录")
    @FunctionPoint(interfaceType = InterfaceType.TOC)
    @GetMapping("/result")
    public BaseResp<PaperScoreResp> result(@RequestParam("paperUniqueId") String paperUniqueId) {
        return paperBiz.result(paperUniqueId);
    }

}
