package com.linker.fusion.knowledgecenter.server.dto.model;

import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.QueryParams;
import lombok.Data;

import java.util.List;

@Data
public class AiSearchWorkflowInput {
    private String tenantId;
    private String userId;
    private List<String> departmentCodes;
    private String query;
    private String picUrl;
    private List<Long> groupIds;
    private List<Integer> fileTypes;
    private List<QueryParams.TitleParam> titleParams;
    private List<QueryParams.MetaParam> metaParams;
    private List<QueryParams.VisionParam> visionParams;
    private List<QueryParams.TerminologyParam> terminologyParams;

    private Integer maxResults;
    private Integer topK;
    private Double threshold;
    private Boolean intentRecognitionEnable;
}