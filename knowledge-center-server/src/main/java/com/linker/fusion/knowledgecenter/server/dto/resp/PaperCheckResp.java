package com.linker.fusion.knowledgecenter.server.dto.resp;

import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description: 类说明
 * @Date: Created in 2025/4/2
 */
@Data
public class PaperCheckResp {

//    /**
//     * 正确答案 "A/B/C/D/0/1"
//     */
//    private String correctAnswer;
//
//    /**
//     * 回答结果, 0:错误, 1:正确
//     */
//    private Integer answerResult;
//
//    /**
//     * 答案解析
//     */
//    private String answerAnalysis;



    /**
     * 问题总题数
     */
    private Integer totalQuestionNumber;

    /**
     * 试卷状态, 0:回答中, 1:完成
     */
    private Integer paperStatus;
}
