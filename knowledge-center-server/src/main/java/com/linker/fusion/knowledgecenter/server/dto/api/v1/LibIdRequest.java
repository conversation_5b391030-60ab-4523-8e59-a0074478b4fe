package com.linker.fusion.knowledgecenter.server.dto.api.v1;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * Request
 *
 * <AUTHOR>
 */
@Data
public class LibIdRequest {
    @JsonProperty("lib_id")
    @NotBlank
    @ApiModelProperty(value = "文件库id", required = true)
    private String libId;
}