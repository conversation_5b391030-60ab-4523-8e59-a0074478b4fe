package com.linker.fusion.knowledgecenter.server.controller.rpc;

import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.server.biz.DocBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.rpc.FileDeleteRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.rpc.FileImportRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.req.doc.rpc.VideoCleanRpcReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.VideoMergeReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 文件库统一接口
 */
@Api(tags = "RPC/文件库管理")
@RestController
@RequestMapping("/rpc/file")
@Slf4j
public class RpcFileController {

    @Resource
    private DocBiz docBiz;

    @ApiOperation(value = "扫盘导入")
    @PostMapping("/import")
    public BaseResp<String> importFile(@RequestBody @Valid FileImportRpcReq req) {
        log.info("importFile:{}", req);
        BaseResp<String> resp = new BaseResp<>(docBiz.importFile(req));
        log.info("importFile:end");
        return resp;
    }

    @PostMapping("/delete")
    @ApiOperation(value = "扫盘删除")
    public BaseResp<List<KnowledgeResourceEntity>> delete(@RequestBody @Valid FileDeleteRpcReq req) {
        List<KnowledgeResourceEntity> files = docBiz.delete(req);
        return new BaseResp<>(files);
    }

    @PostMapping("video-merge-execute")
    @ApiOperation(value = "执行视频合并流程")
    public BaseResp<Void> videoMerge(@RequestBody @Valid VideoMergeReq req) {
        log.info("videoMerge:{}", req);
        docBiz.videoMerge(req.getGroupIds());
        log.info("videoMerge:end");
        return new BaseResp<>();
    }

    @PostMapping("video-clean")
    @ApiOperation(value = "预处理流程变更，清除知识库下的视频文件")
    public BaseResp<List<String>> videoClean(@RequestBody @Valid VideoCleanRpcReq req) {
        return new BaseResp<>(docBiz.videoClean(req));
    }
}
