package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class GroupResp {
    private Long id;
    private String name;
    private String description;
    private String logo;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Boolean isSync;

    public GroupResp(KnowledgeGroupEntity group) {
        this.id = group.getId();
        this.name = group.getName();
        this.description = group.getDescription();
        this.logo = group.getLogo();
        this.createTime = group.getCreateTime();
        this.updateTime = group.getUpdateTime();
        this.isSync = group.getIsSync();
    }
}
