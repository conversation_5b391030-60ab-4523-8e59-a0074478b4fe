package com.linker.fusion.knowledgecenter.server.biz;

import com.alibaba.fastjson.JSON;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.ImageChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.server.convert.FileConvert;
import com.linker.fusion.knowledgecenter.server.dto.req.file.image.ImageChunkAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.image.ImageChunkDeleteReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.image.ImageChunkListReq;
import com.linker.fusion.knowledgecenter.server.dto.req.file.image.ImageChunkUpdateReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.ImageChunkResp;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IImageChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.AddChunkDTO;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceFaceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceODTagService;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ChunkServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Component
public class ImageBiz extends BaseBiz {


    @Resource
    private IImageChunkIBaseServiceResource iImageChunkIBaseService;

    @Resource
    private IResourceFaceService iResourceFaceService;

    @Resource
    private IResourceODTagService iResourceODTagService;

    @Resource
    private ChunkServiceFactory chunkServiceFactory;
    @Value("${document.image.encode:false}")
    private Boolean imageEncode;

    /**
     * 查询分块列表
     */
    public List<ImageChunkResp> chunkList(ImageChunkListReq req) {
        List<ImageChunkEntity> chunks = iImageChunkIBaseService.searchSeg(getTenantId(), req.getSegmentId(), req.getContent());
        return FileConvert.INSTANCE.toImageChunkResp(chunks);
    }

    public void updateChunk(ImageChunkUpdateReq req) {
        chunkServiceFactory.update(FileTypeEnum.IMAGE, getTenantId(), req.getUid(), req.getContent(), req.getUrl());
    }

    public void addChunk(ImageChunkAddReq req) {
        AddChunkDTO dto = chunkServiceFactory.addPre(req.getDocId(), req.getContent(), req.getUrl(), imageEncode);
        KnowledgeResourceEntity resource = dto.getFile();
        ImageChunkEntity chunk = new ImageChunkEntity();
        chunk.setEnable(Boolean.TRUE.equals(resource.getEnable()) ? 1 : 0);
        chunk.setGroupId(resource.getGroupId());
        chunk.setChunkId(UUID.randomUUID().toString());
        chunk.setSegmentId(req.getSegmentId());
        chunk.setSort(Objects.nonNull( req.getSort())?req.getSort():0);
        chunk.setTitle(resource.getTitle());
        chunk.setContent(req.getContent());
        chunk.setContentVector(dto.getContentVector());
        chunk.setDocId(req.getDocId());
        chunk.setCreateTime(StringComUtils.convertStr(LocalDateTime.now()));
        chunk.setCreateId(getUserCode());
        chunk.setTenantId(getTenantId());
        chunk.setMeta(JSON.parseObject(resource.getExtInfo()));
        chunk.setSrcType(req.getSrcType());
        chunk.setUrl(dto.getNewUrl());
        chunk.setPersonsLabels(iResourceFaceService.getLabelsBySegmentId(resource.getDocId(), req.getSegmentId()));
        chunk.setObjectLabels(iResourceODTagService.getLabelsBySegmentId(req.getSegmentId()));
//        chunk.setTextLabels(iResourceTerminologyService.getLabelsBySegmentId(req.getSegmentId()));
        iImageChunkIBaseService.add(getTenantId(), Collections.singletonList(chunk), true);
    }

    public void deleteChunk(ImageChunkDeleteReq req) {
        chunkServiceFactory.delete(FileTypeEnum.IMAGE, getTenantId(), req.getChunkIds());
    }
}
