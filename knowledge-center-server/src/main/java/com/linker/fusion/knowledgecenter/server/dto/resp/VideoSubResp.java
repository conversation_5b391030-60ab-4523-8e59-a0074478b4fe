package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.linker.fusion.knowledgecenter.infrastructure.entity.SubVideoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VideoSubResp {

    @ApiModelProperty("关联的docId")
    private String docId;

    @ApiModelProperty("视频分段文件ID")
    private String fileId;

    private String title;

    private String suffix;

    @ApiModelProperty("分段资源地址")
    private String url;

    @ApiModelProperty("文件资源大小（byte）")
    private Long size;

    @ApiModelProperty("时长 毫秒")
    private Long duration;

    @ApiModelProperty("封面图")
    private String thumbnail;

    private Integer operation;

    @ApiModelProperty("开始时间")
    private Long startTimestamp;
    @ApiModelProperty("结束时间")
    private Long endTimestamp;

    public VideoSubResp(SubVideoEntity s) {
        this.docId = s.getDocId();
        this.fileId = s.getFileId();
        this.title = s.getTitle();
        this.suffix = s.getSuffix();
        this.url = s.getUrl();
        this.size = s.getSize();
        this.duration = s.getDuration();
        this.thumbnail = s.getThumbnail();
        this.operation = s.getOperation();
    }
}
