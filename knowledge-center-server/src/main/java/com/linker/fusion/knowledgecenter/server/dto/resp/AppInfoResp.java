package com.linker.fusion.knowledgecenter.server.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AppInfoResp {
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("描述")
    private String desc;
    @ApiModelProperty("文件数量")
    private Long fileCount;
    @ApiModelProperty("文件限制")
    private Integer fileLimit;
    @ApiModelProperty("临时文件数量")
    private Long tempCount;
    @ApiModelProperty("临时文件限制")
    private Integer tempLimit;
    @ApiModelProperty("问答对数量")
    private Long qaCount;
    @ApiModelProperty("问答对限制")
    private Integer qaLimit;
    @ApiModelProperty("过期时间")
    private String expire;
    @ApiModelProperty("题目数量")
    private Long questionCount;
    @ApiModelProperty("题目限制")
    private Integer questionLimit;
}
