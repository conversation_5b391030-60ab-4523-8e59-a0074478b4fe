package com.linker.fusion.knowledgecenter.server.dto.api.v1;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Request
 *
 * <AUTHOR>
 */
@Data
public class ResourceIdsReq {
    @JsonProperty("resource_ids")
    @NotNull
    @ApiModelProperty(value = "文件id列表", required = true)
    private String resourceIds;
}