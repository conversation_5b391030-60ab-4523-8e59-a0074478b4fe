package com.linker.fusion.knowledgecenter.server.dto.resp;

import com.linker.fusion.knowledgecenter.infrastructure.model.AudioStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.DocStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.ImageStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.VideoStrategyModel;
import com.linker.fusion.knowledgecenter.server.dto.resp.core.BaseEntityResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class StrategyDetailResp extends BaseEntityResp {

	@ApiModelProperty("策略id")
	private Long id;

	@ApiModelProperty("文档切分策略")
	private DocStrategyModel strategyDoc;

	@ApiModelProperty("图片切分策略")
	private ImageStrategyModel strategyImage;

	@ApiModelProperty("视频切分策略")
	private VideoStrategyModel strategyVideo;

	@ApiModelProperty("语音切分策略")
	private AudioStrategyModel strategyAudio;

	@ApiModelProperty("类目id")
	private Long groupId;
}
