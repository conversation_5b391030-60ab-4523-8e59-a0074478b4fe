package com.linker.fusion.knowledgecenter.server.controller;

import com.linker.core.auth.annotation.FunctionPoint;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.baseclass.page.BasePaginResp;
import com.linker.fusion.knowledgecenter.server.biz.FaqBiz;
import com.linker.fusion.knowledgecenter.server.biz.FeedbackBiz;
import com.linker.fusion.knowledgecenter.server.dto.req.core.IdBatchBaseReq;
import com.linker.fusion.knowledgecenter.server.dto.req.feedback.FeedbackAcceptReq;
import com.linker.fusion.knowledgecenter.server.dto.req.feedback.FeedbackAddReq;
import com.linker.fusion.knowledgecenter.server.dto.req.feedback.FeedbackImportReq;
import com.linker.fusion.knowledgecenter.server.dto.req.feedback.FeedbackPageReq;
import com.linker.fusion.knowledgecenter.server.dto.resp.FeedbackResp;
import com.linker.fusion.knowledgecenter.server.dto.resp.FeedbackSettingResp;
import com.linker.fusion.knowledgecenter.service.domain.feedback.model.FeedbackDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.linker.core.auth.enums.InterfaceType.TOC;

@Api(tags = "反馈学习")
@RestController
@RequestMapping("feedback")
public class FeedbackController {

    @Resource
    private FeedbackBiz feedbackBiz;

    @Resource
    private FaqBiz faqBiz;

    @GetMapping("setting/get")
    @ApiOperation(value = "获取反馈学习设置")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<FeedbackSettingResp> get(@RequestParam(value = "agentId") Long agentId) {
        return new BaseResp<>(feedbackBiz.getSetting(agentId));
    }

    @PostMapping("setting/update")
    @ApiOperation(value = "更新反馈学习设置")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> update(@RequestBody @Valid FeedbackSettingResp req) {
        feedbackBiz.updateSetting(req);
        return new BaseResp<>();
    }

    @PostMapping("add")
    @ApiOperation(value = "添加反馈")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Long> add(@RequestBody @Valid FeedbackAddReq req) {
        return new BaseResp<>(feedbackBiz.add(req));
    }

    @PostMapping("page")
    @ApiOperation(value = "分页查询")
    @FunctionPoint(interfaceType = TOC)
    public BasePaginResp<FeedbackResp> page(@RequestBody @Valid FeedbackPageReq req) {

        return feedbackBiz.page(req);
    }

    @PostMapping("accept")
    @ApiOperation(value = "采纳")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> accept(@RequestBody @Valid FeedbackAcceptReq req) {
        Long faqId = faqBiz.create(req.getFaq());
        feedbackBiz.accept(faqId, req.getFeedbackId());
        return new BaseResp<>();
    }

    @PostMapping("ignore")
    @ApiOperation(value = "忽略")
    @FunctionPoint(interfaceType = TOC)
    public BaseResp<Void> ignore(@RequestBody @Valid IdBatchBaseReq req) {
        feedbackBiz.ignore(req);
        return new BaseResp<>();
    }

    @GetMapping("/retrieve")
    @ApiOperation(value = "召回测试")
    public BaseResp<FeedbackDTO> search(String tenantId,
                                        Long agentId,
                                        String question,
                                        Double threshold) {
        return new BaseResp<>(feedbackBiz.retrieve(tenantId, agentId, question, threshold));
    }

    @PostMapping("rpc/import")
    @ApiOperation(value = "历史数据导入")
    public BaseResp<Void> rpcImport(@RequestBody @Valid FeedbackImportReq req) {
        feedbackBiz.rpcImport(req);
        return new BaseResp<>();
    }
}
