apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: om
  labels:
    service: knowledge-center-backend
  name: knowledge-center-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      service: knowledge-center-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  template:
    metadata:
      labels:
        service: knowledge-center-backend
        network/soco: "true"
        type: app
    spec:
      containers:
        - args:
          envFrom:
            - configMapRef:
                name: knowledge-center-backend-config
          image: hbt.linker.cc/om/knowledge-center-backend:1.0.0-faq
          imagePullPolicy: "IfNotPresent"
          name: knowledge-center-backend
          ports:
            - containerPort: 8080
          readinessProbe:
            initialDelaySeconds: 40
            timeoutSeconds: 10
            periodSeconds: 15
            failureThreshold: 3
            successThreshold: 1
            httpGet:
              path: /health
              port: 8080
              scheme: HTTP
      imagePullSecrets:
        - name: linker-secret
      restartPolicy: Always
      volumes:
        - configMap:
            name: knowledge-center-backend-config
          name: knowledge-center-backend-config
---
#根据实际需要看是否需要配置service
apiVersion: v1
kind: Service
metadata:
  namespace: om
  labels:
    service: knowledge-center-backend
  name: knowledge-center-backend
spec:
  ports:
    - name: "30308"
      port: 8080
      targetPort: 8080
      nodePort: 30308
      protocol: TCP
  selector:
    service: knowledge-center-backend
  type: NodePort