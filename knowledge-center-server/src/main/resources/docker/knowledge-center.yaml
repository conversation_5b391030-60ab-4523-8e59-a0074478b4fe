apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: om
  labels:
    service: knowledge-center
  name: knowledge-center
spec:
  replicas: 1
  selector:
    matchLabels:
      service: knowledge-center
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  template:
    metadata:
      labels:
        type: app
        service: knowledge-center
    spec:
      containers:
        - args:
          envFrom:
            - configMapRef:
                name: agent-build-config
          image: hbt.linker.cc/om/faq-knowledge-web:v1.0.0-rc1
          imagePullPolicy: "Always"
          name: knowledge-center
          ports:
            - containerPort: 80
          resources: {}
      imagePullSecrets:
        - name: linker-secret
      restartPolicy: Always
      volumes:
        - configMap:
            name: agent-build-config
          name: agent-build-config

---
apiVersion: v1
kind: Service
metadata:
  namespace: om
  labels:
    service: knowledge-center
  name: knowledge-center
spec:
  ports:
    - name: "80"
      port: 80
      targetPort: 80
      nodePort: 30817
      protocol: TCP
  selector:
    service: knowledge-center
  type: NodePort
