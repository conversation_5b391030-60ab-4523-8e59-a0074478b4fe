FROM registry.linker.cc/vos/openjdk:jdk-8u242-withsky
EXPOSE 8080
WORKDIR /usr/local/
ENV JAR_NAME=knowledge-center.jar
ADD knowledge-center.jar knowledge-center.jar
RUN echo 'java -javaagent:/usr/local/${jar_name}="-pwd ${PASSWORD}" -javaagent:/usr/local/skywalking-agent/skywalking-agent.jar -Dskywalking.agent.is_cache_enhanced_class=true -Dskywalking.agent.class_cache_mode=FILE -Dskywalking.agent.keep_tracing=true -Dskywalking.collector.backend_service=\${SKYWALKING_SERVICE} -jar \${JAVA_OPTS} -XX:MaxRAMPercentage=80.0 -XX:+PrintGC -XX:+PrintGCDetails  -Xloggc:/usr/local/logs/gc.log -XX:ErrorFile=/usr/local/logs/hs_err_pid.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/usr/local/logs/jvmlog ${jar_name} -Dspring.config.location=/usr/local/config/*.yaml' > /run.sh && chmod 777 /run.sh
ENTRYPOINT ["/bin/sh","/run.sh"]