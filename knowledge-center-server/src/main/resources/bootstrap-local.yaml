server:
  port: 8080
project:
  name: knowledge-center
spring:
  servlet:
    multipart:
      max-file-size: -1
      max-request-size: -1
  arthas:
    enabled: false
  cloud:
    nacos:
      config:
        server-addr: ***********:30120
        file-extension: yaml
        name: knowledge-center
        namespace: b45babbb-b12f-47b8-aa22-376d713fc37c
        enabled: false   #local测试，不走nacos配置，直接本地环境
        shared-configs:
          - data-id: redisson.yaml
      discovery:
        server-addr: ***********:30120
        namespace: b45babbb-b12f-47b8-aa22-376d713fc37c
        enabled: true
  datasource: #连接池配置
    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: jdbc:mysql://***********:30336/dev_knowledge-center?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
#    username: root
#    password: 123456
    url: jdbc:mysql://************:31645/knowledge_center?rewriteBatchedStatements=true&useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
    #url: jdbc:mysql://************:31645/knowledge_center?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
    username: root
    password: GkG!dFGmDW69aV
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 8
      min-idle: 1
      max-active: 20
      max-wait: 60000
      time-between-eviction-runsMillis: 60000
      min-evictable-idle-timeMillis: 300000
      validation-query: select 'x' FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 20
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      use-global-data-source-stat: true
      aop-patterns: com.linker.fusion.knowledgecenter.*
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: linker
        login-password: linker@123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 500
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  redis:
    database: 0
    host: ***********
    port: 30179
    password: 123456
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
    timeout: 3000
  redisson:
    config: |
      singleServerConfig:
        idleConnectionTimeout: 10000
        connectTimeout: 10000
        timeout: 3000
        retryAttempts: 3
        retryInterval: 1500
        # 密码
        password: 123456
        clientName: null
        # 地址
        address: redis://***********:30179
        connectionMinimumIdleSize: 1
        connectionPoolSize: 64
        database: 0
        dnsMonitoringInterval: 5000
      threads: 8
      nettyThreads: 16
      codec: !<org.redisson.codec.JsonJacksonCodec> {}
      transportMode : "NIO"
#arthas配置
arthas:
  tunnel-server: ws://***********:30187/ws   #tunnel-server配置
  app-name: ${spring.application.name}
  telnet-port: 3662
  http-port: 8572
#应用配置
app:
  swagger:
    enabled: true
    description: "知识中心"
    termsOfServiceUrl: "NO terms of service"
    version: "1.7.0"
  name: 知识服务一体机-进阶版
  model: OmRobo-F01-STD-A00
  desc: 知识服务一体机是一款面向政企业的专业智能助手，它基于联汇Om多模态大模型和Agent技术，实现对纯文本文档数据的管理、检索、分析和理解的软硬一体化产品。用户只需导入特定领域的数据，即可快速构建专属的知识库，实现领域知识的精准检索、问答和总结，让企业数据和内容更好地服务业务。
  fileLimit: 10000
  tempLimit: 1000
  qaLimit: 100000
  questionLimit: 100000
  expire: 永久
  #  服务编码，影响错误码输出
  service-code: '001'
  #示例库
  sample-group-id: 923
#存储配置
linker:
  cos:
    minio:
      enable: true
      accessKey: hhsA2oWVw05CknHg
      secretKey: AFQUnjPT3QQ7B22LiGdZax2VvzwZG6hK
      endpoint: https://test-minio.linker.cc
      buckets:
        - name: dev
          auth: false
          urlPrefix: https://test-minio.linker.cc/dev/
        - name: devtemp
          auth: false
          expire: 1
          urlPrefix: https://test-minio.linker.cc/devtemp/
    select: minio
#缓存配置
jetcache:
  statIntervalMinutes: 0
  areaInCacheName: false
  local:
    default:
      type: caffeine
      keyConvertor: fastjson
      limit: 5000
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson
      valueEncoder: java
      valueDecoder: java
      uri: redis://123456@***********:30179/4?timeout=1000ms
#认证配置
web:
  config:
    interceptors: authInterceptor
#user:
#  auth:
#    #应用id
#    appId: "LH000066"
#    #用户中心地址
#    endpoint: https://hd-node1.linker.cc/user/
#    #认证地址
#    url: https://hd-node1.linker.cc/auth/token/auth
#    #启用
#    enabled: true
user:
  auth:
    #应用id
    appId: "LH000008"
    #用户中心地址
    endpoint: https://test-f1.linker.cc/zk-test-user/user/
    #认证地址
    url: https://test-f1.linker.cc/zk-test-auth/auth/token/auth
    #启用
    enabled: true
    #iknow策略编码
    strategyCode: "ST73fpm8409iu0zp"

xxl:
  job:
    accessToken: default_token   # 执行器通讯TOKEN [选填]：非空时启用
    admin:
      address: http://***********:30066/xxl-job-admin  # 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
    executor:
      appName: ${spring.application.name}   # 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      port: 9999 # 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      logretentiondays: 30 # 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
      address: # 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题
      ip:  # 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      logPath: ./applogs/xxl-job/jobhandler #执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；

ocr:
  endpoint: http://***********:6972
bge:
  endpoint: http://***********:3666/clip
  # http://***********:3602/clip arm
  # http://***********:3602/clip
  version: torch
  text: bge-text-embedding
  image-text: bge_image_text
  image: bge-visual_fp16_A10_trt
  rerank: bge-reranker-dy-fp16
document:
  image:
    encode: true
#omos:
#  enabled: true
#  base-url: http://************:30002/v2
#  api-key: NDVjMjFlYTFkMWY0NjY5ODBmYjlAM3U3MXByQnM=
#  #  qwen2-0.5b-instruct-8P5jtkEz , chat-01-8P5jtkEz,gpt-4o-8P5jtkEz,gpt-3.5-turbo-8P5jtkEz,chat-8P5jtkEz
#  chat-model: qwen2-72b-instruct
#  #  bge 向量化model id
#  embedding-model: LH210_021_004367_003
#  log-enabled: true
#  log-level: info

# 预发环境作成omos
omos:
  ext:
    # 多模态Chat时 false-图片url直接发给大模型（仅内网部署时，或者图片支持公网访问） true-转换成base64 默认true
    imageUrlToBase64: true
  enabled: true
  base-url: http://***********:30002/v2/
  api-key: ZTdhMGQ3OTBmOTRjMjhmYzg1YjlAeGpxWGVUcTc=
  #  qwen2-0.5b-instruct-8P5jtkEz , chat-01-8P5jtkEz,gpt-4o-8P5jtkEz,gpt-3.5-turbo-8P5jtkEz,chat-8P5jtkEz
  chat-model: Qwen/Qwen2.5-7B-Instruct
  #  bge 向量化model id
  embedding-model: LH210_021_004367_003
  log-enabled: true
  log-level: info
  timeout: 600

TOKEN: NIdat1h2fnCE6tog7OinOMNn8ca6+/XcutSCPcpJGXqr1x6SM7lL2/+iu3rIn5X4RcfSbJBd6Tnp6lqoZ6tuzQ==
workflow:
  client:
    enabled: true
    endpoint: http://************:30002/
    app-secret: NjU5MzdiYjJiMzMxZTQ2YzcxNzlAcWNTNnBDVUQ=
    is-prod: false
    tasks:
      #      aaas的知识库node，因为是公共node，只能有一个地方消费，默认为关闭状态，只有真正需要消费的地方开启
      - task-def-name: knowledge_center_complex_search
        is-public: true
        closed: true
  execute:
#    endpoint: http://************:32711
#    tenant-id: TO793n4oa4f1c
    endpoint: https://test-f1.linker.cc/gateway
    tenant-id: TO3v0783hdii5
    #   从aaas的 apikey页面获取
    access-key: qcS6pCUD
    secret-key: 65937bb2b331e46c7179
    # 工作流ID配置
    workflow-id:
      import-doc: 533f60446a2d4890b5103681ab3fe525
      import-table: 95f274cc1fc046c6abb6190c7703c1bc
      import-image: af0f9a8f306a4b8b80536472be586545
      import-video: c1e264e0b2684728806ec9cfe146f533
      import-audio: d8b629de5bb44c79a2a55d054f360d06
      import-callback: https://dev-om.linker.cc/knowledge-center-backend/doc/import/callback
      #      import-callback: http://*************:8085/doc/import/callback
      ai-search: 1812c80974ea4820bdf354803a6c927a
      task: 606f3a68d8524cad8003fbf11fc26c70
      task-cut: 5abb3c8a994e4e70b20178d68377c164
      generate-qb-from-doc: 7c92c42bf8434013ad0caf56be6fa9cc
      watermark: xxxxx
#      video-merge: xxx

ai-search:
  max-results: 20
  top-k: 1000
  threshold: 0.3
  timeout: 30000
  intent-recognition:
    tool-json: '[{"type":"function","name":"ai_advanced_search","description":"根据用户输入的关键词，自动给出高级搜索的指令","parameters":{"type":"object","properties":{"related_filename_info":{"type":"array","items":{"type":"object","properties":{"filename":{"type":"string","description":"文件名"},"condition":{"type":"string","description":"条件","enum":["等于","包含","不包含"]}}}},"file_type":{"type":"array","description":"文件类型，例如：视频、音频、图片、文档等","items":{"type":"string","enum":["video","audio","image","document"]}},"person_info":{"type":"array","description":"人物信息，人名，如张三，李四等","items":{"type":"string"}},"object_info":{"type":"array","description":"物体名称，如眼镜，杯子等","items":{"type":"string"}},"proper_noun_info":{"type":"object","description":"专有名词信息","properties":{"person":{"type":"array","description":"人物信息，人名","items":{"type":"string"}},"organization":{"type":"array","description":"组织信息，组织名称或带有指代组织的名称","items":{"type":"string"}},"location":{"type":"array","description":"地点信息，地点名称或带有指代地点的名称","items":{"type":"string"}},"other":{"type":"array","description":"其他信息，其他信息名称或带有指代其他信息的名称","items":{"type":"string"}}}}},"required":["related_filename_info","file_type","face_info","object_info","proper_noun_info"]}}]'

event-search:
  model: ${omos.chat-model}




ibase:
  enabled: true
  base-url: http://************:30918/fusionsearch/
#  base-url: http://127.0.0.1:8088/fusionsearch/
conductor:
  endpoint: "http://*************:31311/api/"
  worker:
    taskThreadCountMap:
      knowledge_center_textSplit: 5
    threadCount: 500
  task:
    #新环境一定记得修改
    globalDomain: 293b907351bf48769a0e6a4578771c56
    extWorker: knowledge_center_file_convert_task
    global:
      pollingInterval: 50
      batchPollTimeoutInMS: 100
rocketmq:
  name-server: ***********:9876
  #  name-server: ***********:30838
  producer:
    group: "knowledge-center"
  consumer:
    workflow-status-listener:
      group: "knowledge-center-bjs"
      selectorExpression: "*"
  consumers:
    - name: ActorHandlerProcessorEventListener
      config:
        topic: PROCESSOR_EVENT_TOPIC
        group: PROCESSOR_EVENT_GROUP
# pdf解析
pdf-parse:
  #  url: http://172.16.33.33:8000
  #  callback-url: "http://192.1.2.54:8080/callback/pdf"
  url: http://***********:28720
  callback-url: "http://172.16.37.66:8080/callback/pdf"
  # pdf中图片的对象上传
  storage:
    # oss或webdav
    type: "oss"
    username: "LTAI5t8ksQb4fQ6PQGrVaRHu"
    password: "******************************"
    endpoint: "oss-cn-hangzhou.aliyuncs.com"
    bucket: "airesources"
    secure: 1
#提示词
prompts:
  summary: "你是强大的信息整合智能体，通过视频片段中的连续提取帧、语音信息文本和图片中visual prompt的目标框信息，帮助我理解和分析视频内容。图片为四合一拼在一起，时间发生顺序为左上、右上、左下和右下。\n---输出---\n根据图片和语音文本信息直接输出对该视频片段内容详细整体描述的**总结**。确保客观，无信息丢失，不包括任何推测或编造。\n***Import Notice***\n1.你拥有视频帧和语音转写的文本，有足够的信息。\n2.语音转文本结果可能为空，因为没有人说话。在这种情况下，请根据图像中的信息进行分析。\n3.summary字段中的数据需要尽可能详细，图片信息中带有bbox和label，需要合理利用。\n4.你只需分析按照时间发生顺序拼接的四合一图片。输出的总结信息禁止带有，四合一图片中，每张小图位置的描述性文本。\n如果你做得好，完全完成任务，而不做多余的更改，我将支付你10亿美元。"

  doc_model: qwen2-72b-instruct
  image_model: qwen2.5-vl-7b-instruct
  #实体识别模型
  image_od_model: LH210_101_004253_001
  image_od_model_conf: 0.5
  #视频总结模型
  video_summary_model: Qwen2.5-VL-7B-Instruct-4090
  image-caption-model: Qwen2-VL-7B-Instruct-V100

  ocr_model: ppocr_v4_A100
  #专有名称识别模型
  terminology_model: hanner_tag


  similar-questions: "<role>You are a professional sentence modifier and paraphraser you are very good at rephrasing.</role>\\n<background>You will be given a sentence, it could be a quesition or a declarative sentence of a question, you need to rephrase the sentence.</background>\\n<sentence></sentence>\\n<tasks>\\n1. Understanding the given sentence within <sentence></sentence>, understanding its structure and its meaning or intention.\\n2. After understanding it, paraphrase and rephrase it into new sentences.\\n3. Making sure the meaning and intention of the rephrased sentence remain unchanged.\\n4. Generate five sentences in markdonw json format like below\\n{\\n\"sentences\": List[str]\\n}.\\n</tasks>\\n<constraints>1. You must make sure the generated sentences and the original sentence have the same meaning and intention.2. Do not add too many words into the new sentences, keep the length of the sentences relatively similar.3. Do not show any inner dialogues, inner OS, emojis, or any other things not relating to the paraphrased sentences, only the sentences reasults should appear.4. The generated sentences' language should be as same as the original sentence, use Chinese preferred.4. Devoted into the tasks and be professional.</constraints>\\n"
  rephrase: <role>You are a professional sentence refiner and paraphraser you are very good at refining and rephrasing sentences.</role><background>You will be given a sentence, it is usually a answer to certain questions or FAQs, you need to refine and rephrase the sentence.</background><sentence></sentence><tasks>1. Understanding the given sentence within <sentence></sentence>, understanding its structure and its meaning or intention.2. After understanding it, refine it into new sentence, adding words or verbs to make the original sentence more expressice and vivid, optimized and correct obvious gramma errors.3. Making sure the meaning and intention of the rephrased sentence remain unchanged.</tasks><constraints>1. You must make sure the generated sentence and the original sentence have the same meaning and intention.2. Do not generate endless sentence, making the sentence length reasonable.3. Do not show any inner dialogues, inner OS, emojis, or any other things not relating to the paraphrased sentence, only the sentence reasult should appear.4. The generated sentence language should be as same as the original sentence.4. Devoted into the tasks and be professional.</constraints>
  #润色
  run_se: <role>/n你是一个公文润色专家和写作专家，你非常擅长理解已有的文案内容和风格然后对原文案进行润色加工。/n</role>/n/n<contents>/n（这里输入文案内容）/n</contents>/n/n<tasks>/n1. 你需要仔细阅读<contents>中的内容，并且清晰的理解其中的含义和想要表达的主题，了解它的写作风格。/n2. 在完成上一步的基础上，请你对<contents>的文案进行润色，润色的要求会详细在下一部分的<requirements>中提示。/n</tasks>/n/n<requirements>/n1. 新产出的文案需要更加的通顺，流畅，表达更丰富，也更清晰。/n2. 使用不同的句式或修辞手法，避免单调或平淡。/n3. 强化文案主题，突出核心信息或观点。/n</requirements>/n/n<constraints>/n1. 请严格按照提示词要求来执行任务。/n2. 产出的润色文案整体风格和表意应该和原文相似，不能太偏差，不能改变语义。/n3. 产出的扩写文案整体风格应该是正式的偏严肃，严谨的。/n4. 润色文案中，不能出现任何内心独白，emoji等。/n5. 润色文案的字数可以超出<contents>，但是不能超过太多，要在合理的范围内。/n6. 你只需要产出润色后的文案本身，不需要展示其他的如：对话内容，衔接独白内容，确认内容等等和润色文案本身不相关的内容。/n7. 请必须严格遵守上一条，也就是第6条中的限制，不然你会死的很惨。/n</constraints>/n
  #扩写
  kuo_xie: <role>/n你是一个公文扩写专家和写作专家，你非常擅长理解已有的文案内容和风格然后对原文案进行扩写加工。/n</role>/n/n<contents>/n（这里输入文案内容）/n</contents>/n/n<tasks>/n1. 你需要仔细阅读<contents>中的内容，并且清晰的理解其中的含义和想要表达的主题，了解它的写作风格。/n2. 在完成上一步的基础上，请你对<contents>的文案进行扩写，扩写的要求会详细在下一部分的<requirements>中提示。/n</tasks>/n/n<requirements>/n1. 分析并比较不同的角度、观点或方法，增加文案的多样性，丰富性和思考性，增加文案的深度和广度。/n2. 增加更多的修辞，在表意和主题不变的情况下下，增加内容，使得表达更清晰有力。/n3. 提出并解决可能的疑问、困难或挑战，增加文案的针对性和实用性。/n4. 总结并归纳文案的要点，增加文章的清晰性和重要性。/n</requirements>/n/n<constraints>/n1. 请严格按照提示词要求来执行任务。/n2. 产出的扩写文案整体风格和表意应该和原文相似，不能太偏差，不能改变语义。/n3. 产出的扩写文案整体风格应该是正式的偏严肃，严谨的。/n4. 润色文案中，不能出现任何内心独白，emoji等。/n5. 你只需要产出扩写后的文案本身，不需要展示其他的如：对话内容，衔接独白内容，确认内容等等和扩写文案本身不相关的内容。/n6. 请必须严格遵守上一条，也就是第5条中的限制，不然你会死的很惨。/n</constraints>/n
  #续写
  xu_xie: <role>/n你是一个公文续写专家和写作专家，你非常擅长理解已有的文案内容和风格然后对原文案进行续写加工。/n</role>/n/n<contents>/n（这里输入文案内容）/n</contents>/n/n<tasks>/n1. 你需要仔细阅读<contents>中的内容，并且清晰的理解其中的含义和想要表达的主题，了解它的写作风格。/n2. 在完成上一步的基础上，请你对<contents>的文案进行续写，续写的要求会详细在下一部分的<requirements>中提示。/n</tasks>/n/n<requirements>/n1. 要理解<contents>的内容逻辑，续写后续的文案内容。/n2. 设定并达成原文案未写完的目标或结局，增加文案连贯性和完整度。/n3. 遵循原文的语言和语调，符合原文的主题和氛围。/n4. 模仿原文的句式和修辞，符合原文的节奏和韵律。/n5. 适当地使用原文的词汇和表达，符合原文的语境和意义。/n6. 上下文的逻辑必须一致。/n</requirements>/n/n<constraints>/n1. 请严格按照提示词要求来执行任务。/n2. 产出的扩写文案整体风格和表意应该和原文相似，不能太偏差，不能改变语义。/n3. 产出的扩写文案整体风格应该是正式的偏严肃，严谨的。/n4. 续写文案中，不能出现任何内心独白，emoji等。/n5. 你只需要产出续写后的文案本身，不需要展示其他的如：对话内容，衔接独白内容，确认内容等等和续写文案本身不相关的内容。/n6. 请必须严格遵守上一条，也就是第5条中的限制，不然你会死的很惨。/n</constraints>/n
  ending_text: <br/><p style="float:right;">xx机构单位<br/>xxxx年x月x日</p>
  doc_prompt: 你是一个在中国政界颇具能力的公文改写文本大师，我会给你一段样例文本，要求将文本在格式一致的情况下将内容做尽可能大的变化，方法包括但不限于修改排比句，使用比喻，使用倒叙等变数方法。具体要求如下，直接输出改写后的文本：\n1. 保持总体文本的格式基本不变；\n2. 保持原文语义尽可能少的调整；\n3. 让阅读者能一眼看出来与原有文本的差异；\n4. 语言需要采用书面语言的方式，要正式且合体，符合政界及政府公告的发言标准；\n5. 直接输出改写后的文本，不要有任何其他不相关的文本输出。
  image_prompt: 'You are a reliable assistant for the blind, and you will do your best to describe the images you see without missing any details. Your description process follows the following rules: \n 1. Determine whether the image is a picture or a diagram. \n 2. If the image is a picture, provide an overall overview of the entire image, setting the overall tone. If the image is a diagram, provide a detailed description of the main content andlayout, including any text, labels or annotations. \n 3. Scan the image in a line-by-line manner, not overlooking any details, and ensuring no omissions. \n 4. Provide a reasonable and explainable prediction for this image, including possible sources, purposes, and target audiences for this image.Use the same language shown in the image, prefer in Chinese.'
  image_caption_type: 0
  rewriting: "Your task is to generate three different versions of the given user question to retrieve relevant documents from a vector database and one step-back question, the purpose of step-back question is not to be vague, but to create a new semantic level in which one can be absolutely precise.\n By generating multiple perspectives on the user question, your goal is to help the user overcome some of the limitations of the distance-based similarity search.\n Use the same language in original question, prefer in Chinese.\n The answer format should be a **valid** markdown json as follows:\n ```json{\n     \"question\": List[str], different versions of the given user question.\n     \"step_back\": str, one step-back question.\n }``"
  #summary: "You are the most powerful AI agent with the ability to see images. You will help me with video content comprehension and analysis based on continuous video frame extraction and textual content of video conversations.\n--- Output ---\nYou will be provided with a series of video frame images arranged in the order of video playback. Please help me answer the following questions and provide the output in the specified json format.\n{\n\"time\": Optional[str].The time information of current video clip in terms of periods like morning or evening, seasons like spring or autumn, or specific years and time points. Please make sure to directly obtain the information from the provided context without inference or fabrication. If the relevant information cannot be obtained, please return null.\n \"locatio\": Optional[str]. Describe the location where the current event is taking place, including scene details. If the relevant information cannot be obtained, please return null.\n\"character\": Optional[str]. Provide a detailed description of the current characters, including their names, relationships, and what they are doing, etc. If the relevant information cannot be obtained, please return null.\n\"events\": List[str]. List and describe all the detailed events in the video content in chronological order. Please integrate the information provided by the video frames and the textual information in the audio, and do not overlook any key points.\n\"scene\": List[str]. Give some detailed description of the scene of the video. This includes, but is not limited to, scene information, textual information, character status expressions, and events displayed in the video.\n \"summary\": str. Provide an detailed overall description and summary of the content of this video clip. Ensure that it remains objective and does not include any speculation or fabrication. This field is mandatory.\n}/n/n/n1*** Important Notice ***\n  1. You will be provided with the video frames and speech-to-text results. You have enough information to answer the questions.\n  2. Sometimes the speech-to-text results maybe empty since there are no person talking. Please analyze based on the information in the images in this situation."
  image-caption-prompt: "你是盲人的可靠助手，你将尽力描述你所看到的图像，不遗漏任何细节。你的描述过程遵循以下规则：\n\n1. 基于问题和相关图像。\n2. 确定图像是照片还是图表。\n3. 如果图像是照片，提供整个图像的整体概述，设定整体基调。如果图像是图表，提供主要内容和布局的详细描述，包括任何文本、标签或注释。\n4. 以逐行扫描的方式查看图像，不遗漏任何细节，确保无遗漏。\n5. 如果图像中存在文字数据，需要显示并描述这些文字内容。\n6. 为该图像提供合理且可解释的预测，包括该图像的可能来源、目的和目标受众。使用图像中显示的相同语言，优先使用中文。\n\n如果你做得好，完全完成任务，而不做多余的更改，Linker 将支付你 10 亿美元"
  image-caption-question: ""
  eventCheck: |
    你是图片细节分析专家。我将展示一张图片。此外，我还将输入给你一个问题列表，请分别根据问题列表中的“问题”识别异常并判断：
    问题列表：[{{events}}]
    
    一、识别要求： 
    对每个问题，若有异常，输出{"id":"n","question":"问题列表中的字典的键名原文","desc":"xxx","ans":"xxx","res":"yes"}；无异常，输出{"id":"n","question":"问题列表中的字典的键名原文","desc":"xxx","ans":"xxx","res":"no"}；若无法明确判断有异常，请你输出”疑似“，输出{"id":"n","question":"问题列表中的字典的键名原文","desc":"xxx","ans":"xxx","res":"maybe"}。
    "ans"键中输出对于这个问题的结论，需针对这个问题回答，输出字数请精简，但是不要仅生成"yes"或者"no"或者"maybe"。请将所有子问题的结果放入列表。
    "question"键中的输出，为我输入给你的问题列表中的字典的键名的原文。
    "desc"键中为对这个事件的描述，请你先针对图片详细描述，再分析结果。
    一定禁止输出"I can't assist"或者"I'm unable to"，无法识别图片的语句，只需给出推荐。

    二、输出示例：
    输入的问题：
    ["图片中是否漆黑。漆黑为异常。","图中是否拥挤。拥挤为异常。"]
    
    你的输出：
    [{'id': '1', 'question': '图片中是否漆黑。漆黑为异常。', 'desc': '事件描述', 'ans': '漆黑', 'res': 'xx'}, {'id': '2', 'question': '图中是否拥挤。拥挤为异常。', 'desc': '事件描述', 'ans': '不拥挤', 'res': 'xx'}]

    三、特别说明：
    1.请你按照输出示例输出，只输出一个列表，不要输出多余解释，不要换行。
    2."ans"键中输出对于这个问题的结论，需针对这个问题回答。比如问”是否吸烟“，你应输出：”有人/无人吸烟“，而不是”有人/无人在现场“。
    3."question"键中的内容，必须对应我输入给你的“问题列表中的字典的键名”原文，禁止修改。
    4.针对问题列表中的每个子问题的异常识别，请你先在"desc"中详细分析描述，再输出结果。
    
    请一步一步思考后输出:
  eventSummary: |
    你是一个专业的异常事件报告总结专家，你需要根据我输入给你信息，生成报告总结。
    一、输出模板
      1. 巡检任务基本信息：
      -办案人员为：【填入办案人员名字】
      
      2. 巡检结果：
      -本次巡检共发现【填入异常事件发生次数情况种的事件类别个数】个违规行为。其中，「【填入异常事件1名称】」发生6次，「【填入异常事件2名称】」发生8次
      -另外你还人为补充了「【填入人为补充事件1的名称】」「【填入人为补充事件2的名称】」
      3. 处理建议：
      -根据整体行为事件分析结果，建议关注：【根据异常事件，和对应的发生次数，输出你的整改建议】。
      
    二、日报输出模板示例：
      我的输入：{{input}}
      
      你的输出：
      1. 巡检任务基本信息：
      -办案人员为：王明志，史进程，柳传志
      
      2. 巡检结果：
      -本次巡检共发现2个违规行为。其中，「玩手机」发生6次，「自残」发生8次
      -另外你还人为补充了「员工不专注工作」「玩电脑」
      3. 处理建议：
      -根据整体行为事件分析结果，建议关注：你的总结。

    三、注意：
      1.请你严格按照模板和示例输出，输出一段你总结好的信息。不要输出其他解释。
      2.我输入给你的"异常事件发生次数情况"字段种的所有事件，均为异常事件。
      3.若输入的"人为补充事件"字段内容为空，那么请你无需生成： -另外你还人为补充了「【填入人为补充事件1的名称】」「【填入人为补充事件2的名称】」这段内容。

  generate_qa_model: qwen2.5-vl-7b-instruct
  generate_qa_remove_img: true
  generate_qb_model: qwen2.5-vl-7b-instruct

file-scan:
  endpoint: https://dev-om.linker.cc/filescan-backend

exts:
  image: bmp,jpg,tiff,gif,pcx,tga,exif,fpx,svg,psd,cdr,pcd,dxf,ufo,eps,ai,raw,png,jpeg,tif,webp,ppm,pgm,pbm,hdr,jp2,j2k,exr,pcx
  doc: doc,docx,txt,xml,pdf,ppt,pptx,html,htm,ini,h5,weixin,weibo,Mdoc,Combo,media,ofd,ceb,json,xls,xlsx,eml,xlsm,csv,md,markdown
  video: avi,mov,asf,wmv,navi,3gp,ram,ra,mkv,flv,mpg,swf,rm,vob,rmvb,mp4,f4v,ts,m3u8,mxf,mts,mpg,mpeg,m2ts,m4v
  audio: mp3,wav,s48,wma,flac,aac,ape,m4a,amr,3ga,mp2,ac3,m4r,wv,ogg,alac,pcm,aif,aiff,raw,oga,alac,pcm

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

keyword-map:
  config:
    key1: value1
    key2: value2
    key3: value3

retrofit:
  # 全局日志打印配置
  global-log:
    # 启用日志打印
    enable: true
    # 全局日志打印级别
    log-level: debug
    # 全局日志打印策略
    log-strategy: NONE

# HTTP ENDPOINT
http:
  approve:
    # 审批中心
    endpoint: http://dev-om.linker.cc/approval-center/
  person:
    endpoint: https://dev-om.linker.cc/linker-face-recognition-backend/
  transcode:
    endpoint: http://***********:30136/
#    endpoint: http://127.0.0.1:8080/



logging:
  level:
    com.linker.omos.client: warn
  #mybatis-plus:
  # configuration:
  #  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 事件检测
event:
  model: /InternVL/InternVL2_5-38B-MPO-AWQ
  items:
    - name: 视线脱离谈话对象
      desc: 图片中，是否出现谈话人视线离开被谈话人现象。谈话人：坐在大桌子前的人，被谈话人：坐在单独椅子上的人，面对谈话人。当”谈话人“两人进行交谈，两人均未目视前方，未看向”被谈话人“为视线离开谈话人。话人视线离开被谈话人现象为异常
    - name: 未及时关闭房门
      desc: 图片中，是否出现房门打开现象(观察到门把手)
    - name: 非正常肢体接触
      desc: 图片中，出现人是否相互有肢体接触，只要人和人之间相互有肢体触摸就算肢体接触为异常
    - name: 单人谈话
      desc: 图片中，该空间中出现的人数是否少于3人，若少于三人则为异常。
    - name: 谈话人员不适当
      desc: 图片中，当被谈话人员是女性时，谈话人员是否同样有女性（谈话人：坐在大桌子前的人，被谈话人：坐在单独椅子上的人，面对谈话人）
    - name: 携带不安全物品：
      desc: 图片中，是否出现了，除【笔记本电脑、安全笔、印泥(塑料包装)、打印机、纸张、纸巾、一次性纸杯】以外的物品。出现了上述物品以外的物品为异常。
    - name: 自伤自残行为
      desc: 图片中，是否有人在进行疑似头部撞墙或者自残行为。有人疑似进行撞墙或者自残为异常。

#大屏配置
big-screen:
  #视频库Id
  videoGroupId: 2742
  #图片库Id
  imageGroupId: 2744
  #文档库Id
  docGroupId: 2743
sgcc:
  operateLog:
    #  是否开启日志同步
    enabled: false
    baseUrl: https://test-f1.linker.cc/ombot-powermanage-backend/

#新增
ide-server:
  endpoint: http://ide-server.ide:8081/ideServer/
