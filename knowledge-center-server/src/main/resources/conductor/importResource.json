{"createTime": 1727321095779, "updateTime": 1729576286272, "name": "knowledge_center_importResource", "description": "知识中心入库", "version": 8, "tasks": [{"name": "knowledge_center_determine_fileType", "taskReferenceName": "determineFileType", "description": "判断文件类型", "inputParameters": {"fileType": "${workflow.input.resource.type}"}, "type": "SWITCH", "decisionCases": {"table": [{"name": "knowledge_center_tableImport", "taskReferenceName": "tableImport", "inputParameters": {"id": "${workflow.input.resource.id}", "docId": "${workflow.input.resource.docId}", "url": "${workflow.input.resource.url}"}, "type": "SIMPLE", "decisionCases": {}, "defaultCase": [], "forkTasks": [], "startDelay": 0, "joinOn": [], "optional": false, "defaultExclusiveJoinTask": [], "asyncComplete": false, "loopOver": [], "retryCount": 0, "onStateChange": {}, "permissive": false}, {"name": "knowledge_center_saveToIBase", "taskReferenceName": "saveToIBaseTable", "inputParameters": {"type": 3}, "type": "SIMPLE", "decisionCases": {}, "defaultCase": [], "forkTasks": [], "startDelay": 0, "joinOn": [], "optional": false, "defaultExclusiveJoinTask": [], "asyncComplete": false, "loopOver": [], "retryCount": 0, "onStateChange": {}, "permissive": false}], "doc": [{"name": "knowledge_center_fileConvert", "taskReferenceName": "fileConvert", "inputParameters": {"resource": "${workflow.input.resource}", "uri": "http://192.1.1.10:30059/convert/fileConvert", "data": {"data": {"orgCode": "${workflow.input.resource.tenantId}", "resourceId": "${workflow.input.resource.docId}", "fileUrl": "${workflow.input.resource.url}", "officeConvertType": "pdf", "clientId": "A00002"}}, "removeWatermarkUri": "http://192.1.1.10:30059/pdf/removeWater", "removeWatermarkData": {"inputUrl": "${workflow.input.resource.url}", "accuracy": null, "clientId": "A00002"}, "strategy": "${workflow.input.strategy}"}, "type": "SIMPLE", "decisionCases": {}, "defaultCase": [], "forkTasks": [], "startDelay": 0, "joinOn": [], "optional": false, "defaultExclusiveJoinTask": [], "asyncComplete": false, "loopOver": [], "retryCount": 0, "onStateChange": {}, "permissive": false}, {"name": "knowledge_center_textSplit", "taskReferenceName": "docTextSpilt", "inputParameters": {"resource": "${workflow.input.resource}", "strategy": "${workflow.input.strategy}", "log": true, "timeOut": 18000}, "type": "SIMPLE", "decisionCases": {}, "defaultCase": [], "forkTasks": [], "startDelay": 0, "joinOn": [], "optional": false, "defaultExclusiveJoinTask": [], "asyncComplete": false, "loopOver": [], "retryCount": 0, "onStateChange": {}, "permissive": false}, {"name": "knowledge_agent_addToIBaseJoin", "taskReferenceName": "addToIBaseJoin", "inputParameters": {}, "type": "FORK_JOIN", "decisionCases": {}, "defaultCase": [], "forkTasks": [[{"name": "knowledge_center_saveToIBase", "taskReferenceName": "saveToIBaseSeg", "inputParameters": {"type": 1}, "type": "SIMPLE", "decisionCases": {}, "defaultCase": [], "forkTasks": [], "startDelay": 0, "joinOn": [], "optional": false, "defaultExclusiveJoinTask": [], "asyncComplete": false, "loopOver": [], "retryCount": 0, "onStateChange": {}, "permissive": false}], [{"name": "knowledge_center_saveToIBase", "taskReferenceName": "saveToIBaseChunk", "inputParameters": {"type": 2}, "type": "SIMPLE", "decisionCases": {}, "defaultCase": [], "forkTasks": [], "startDelay": 0, "joinOn": [], "optional": false, "defaultExclusiveJoinTask": [], "asyncComplete": false, "loopOver": [], "retryCount": 0, "onStateChange": {}, "permissive": false}]], "startDelay": 0, "joinOn": [], "optional": false, "defaultExclusiveJoinTask": [], "asyncComplete": false, "loopOver": [], "onStateChange": {}, "permissive": false}, {"name": "joinTask", "taskReferenceName": "textImageProcessingJoin", "inputParameters": {}, "type": "JOIN", "decisionCases": {}, "defaultCase": [], "forkTasks": [], "startDelay": 0, "joinOn": ["saveToIBaseSeg", "saveToIBaseChunk"], "optional": false, "defaultExclusiveJoinTask": [], "asyncComplete": false, "loopOver": [], "onStateChange": {}, "permissive": false}]}, "defaultCase": [], "forkTasks": [], "startDelay": 0, "joinOn": [], "optional": false, "defaultExclusiveJoinTask": [], "asyncComplete": false, "loopOver": [], "evaluatorType": "javascript", "expression": "$.fileType ==6 ? 'table' : 'doc'", "onStateChange": {}, "permissive": false}], "inputParameters": [], "outputParameters": {}, "schemaVersion": 2, "restartable": true, "workflowStatusListenerEnabled": true, "ownerEmail": "<EMAIL>", "timeoutPolicy": "ALERT_ONLY", "timeoutSeconds": 120, "variables": {}, "inputTemplate": {}, "enforceSchema": true}