package com.linker.fusion.knowledgecenter.service.domain.question.handler.impl;

import com.alibaba.fastjson.JSONArray;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class FilledQuestionProviderTest {

    private FilledQuestionProvider provider;

    @BeforeEach
    void setUp() {
        provider = new FilledQuestionProvider();
    }

    @Test
    void checkCorrect_WhenInputsAreNotLists_ReturnsZero() {
      List<String> a=   JSONArray.parseArray("[{\"name\":\"吃饭\",\"desc\":\"进食的动作\"},{\"name\":\"喝酒\",\"desc\":\"喝酒水\"}]",String.class);

        assertEquals(0, provider.checkCorrect("not a list", Arrays.asList("answer")));
        assertEquals(0, provider.checkCorrect(Arrays.asList("answer"), "not a list"));
        assertEquals(0, provider.checkCorrect(null, null));

    }

    @Test
    void checkCorrect_WhenEmptyLists_ReturnsZero() {
        assertEquals(0, provider.checkCorrect(Collections.emptyList(), Arrays.asList("answer")));
        assertEquals(0, provider.checkCorrect(Arrays.asList("answer"), Collections.emptyList()));
        assertEquals(0, provider.checkCorrect(Collections.emptyList(), Collections.emptyList()));
    }

    @Test
    void checkCorrect_WhenExactMatch_ReturnsOne() {
        assertEquals(1, provider.checkCorrect(
                Arrays.asList("answer1", "answer2"),
                Arrays.asList("answer1", "answer2")
        ));
    }

    @Test
    void checkCorrect_WhenNumberMatch_ReturnsOne() {
        assertEquals(1, provider.checkCorrect(
                Arrays.asList("42", "3.14"),
                Arrays.asList("42.0", "3.14")
        ));
    }

    @Test
    void checkCorrect_WhenNoMatch_ReturnsZero() {
        assertEquals(0, provider.checkCorrect(
                Arrays.asList("answer1", "answer2"),
                Arrays.asList("wrong1", "wrong2")
        ));
    }

    @Test
    void checkCorrect_WhenPartialMatch_ReturnsZero() {
        assertEquals(0, provider.checkCorrect(
                Arrays.asList("answer1", "answer2"),
                Arrays.asList("answer1", "wrong2")
        ));
    }
}