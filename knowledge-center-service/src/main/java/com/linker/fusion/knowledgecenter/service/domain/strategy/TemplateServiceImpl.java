package com.linker.fusion.knowledgecenter.service.domain.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TemplateEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.ITemplateManager;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.NOT_EXIST;

@Slf4j
@Service
public class TemplateServiceImpl implements TemplateService {

    @Resource
    private ITemplateManager iTemplateManager;

    @Override
    public Long create(TemplateEntity templateEntity) {
        iTemplateManager.save(templateEntity);
        return templateEntity.getId();
    }

    @Override
    public void saveBatch(List<TemplateEntity> systemTemplate) {
        iTemplateManager.saveBatch(systemTemplate);
    }

    @Override
    public Page<TemplateEntity> page(String tenantId, long pageNum, long pageSize, Integer type, String keyword, Boolean enable) {
        Page<TemplateEntity> page = new Page<>(pageNum, pageSize);
        iTemplateManager.page(page,
                new LambdaQueryWrapper<TemplateEntity>()
                        .eq(TemplateEntity::getTenantId, tenantId)
                        .eq(TemplateEntity::getDeleted, false)
                        .eq(type != null, TemplateEntity::getType, type)
                        .like(StringUtils.isNotBlank(keyword), TemplateEntity::getName, StringComUtils.replaceSqlEsc(keyword))
                        .eq(enable != null, TemplateEntity::getEnable, enable)
                        .orderByAsc(TemplateEntity::getSort)
                        .orderByAsc(TemplateEntity::getCreateTime)
        );
        return page;
    }

    @Override
    public List<TemplateEntity> list(String tenantId, Integer type) {
        return iTemplateManager.list(
                new LambdaQueryWrapper<TemplateEntity>()
                        .eq(TemplateEntity::getTenantId, tenantId)
                        .eq(TemplateEntity::getDeleted, false)
                        .eq(TemplateEntity::getEnable, true)
                        .eq(type != null, TemplateEntity::getType, type)
                        .orderByAsc(TemplateEntity::getSort)
                        .orderByAsc(TemplateEntity::getCreateTime)
        );
    }

    @Override
    public TemplateEntity getById(Long id) {
        return iTemplateManager.getById(id);
    }

    @Override
    public TemplateEntity getNotNull(String tenantId, Long id) {
        TemplateEntity templateEntity = iTemplateManager.getById(id);
        if (templateEntity == null || templateEntity.getDeleted() || !StringUtils.equals(templateEntity.getTenantId(), tenantId)) {
            throw new ServiceException(NOT_EXIST, "模版");
        }
        return templateEntity;
    }

    @Override
    public void update(TemplateEntity templateEntity) {
        iTemplateManager.updateById(templateEntity);
    }

    @Override
    public void delete(String tenantId, long id) {
        iTemplateManager.update(
                new LambdaUpdateWrapper<TemplateEntity>()
                        .eq(TemplateEntity::getTenantId, tenantId)
                        .eq(TemplateEntity::getId, id)
                        .set(TemplateEntity::getDeleted, true)
        );
    }

    @Override
    public void enable(String tenantId, long id, boolean enable) {
        iTemplateManager.update(
                new LambdaUpdateWrapper<TemplateEntity>()
                        .eq(TemplateEntity::getTenantId, tenantId)
                        .eq(TemplateEntity::getId, id)
                        .set(TemplateEntity::getEnable, enable)
        );
    }

    @Override
    public TemplateEntity queryByName(String tenantId, String name, Integer type) {
        List<TemplateEntity> list = iTemplateManager.list(
                new LambdaQueryWrapper<TemplateEntity>()
                        .eq(TemplateEntity::getTenantId, tenantId)
                        .eq(TemplateEntity::getType, type)
                        .eq(TemplateEntity::getName, name)
                        .eq(TemplateEntity::getDeleted, false)
        );
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<TemplateEntity> getSystemTemplate(String tenantId) {
        return iTemplateManager.list(
                new LambdaQueryWrapper<TemplateEntity>()
                        .eq(TemplateEntity::getTenantId, tenantId)
                        .eq(TemplateEntity::getSource, 0)
        );
    }

    @Override
    public void cancelDefault(String tenantId, Integer type, long excludeId) {
        iTemplateManager.update(
                new LambdaUpdateWrapper<TemplateEntity>()
                        .eq(TemplateEntity::getTenantId, tenantId)
                        .eq(TemplateEntity::getType, type)
                        .ne(TemplateEntity::getId, excludeId)
                        .set(TemplateEntity::getIsDefault, false)
        );
    }

    @Override
    public TemplateEntity getDefault(String tenantId, Integer type) {
        return iTemplateManager.getOne(
                new LambdaQueryWrapper<TemplateEntity>()
                        .eq(TemplateEntity::getTenantId, tenantId)
                        .eq(TemplateEntity::getType, type)
                        .eq(TemplateEntity::getIsDefault, true)
                        .last("limit 1")
        );
    }
}
