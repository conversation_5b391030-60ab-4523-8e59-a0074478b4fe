package com.linker.fusion.knowledgecenter.service.domain.faq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexTenantMappingManager;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqEmbeddingData;
import com.linker.omagent.core.repository.embedding.EmbeddingRepository;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchRequest;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class FaqIBaseService {

    @Resource
    private EmbeddingRepository embeddingRepository;

    @Autowired
    private IIndexTenantMappingManager iIndexTenantMappingManager;

    private String getIndex(String tenantId) {
        return iIndexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.FAQ);
    }

    private JSONObject getMapping() {
        return JSONObject.parseObject(ResourceUtil.readUtf8Str("mappings/faq.json"));
    }

    public void updateIndex(String tenantId) {
        String index = getIndex(tenantId);
        if (embeddingRepository.indexExist(index)) {
            embeddingRepository.updateIndex(index, getMapping());
        } else {
            embeddingRepository.createIndex(index, getMapping());
        }

    }

    public List<String> add(String tenantId, List<FaqEmbeddingData> docs) {
        String index = getIndex(tenantId);
        embeddingRepository.createIndex(index, getMapping());
        return embeddingRepository.batchAdd(index, docs);
    }

    public void batchUpdate(String tenantId, List<FaqEmbeddingData> docs) {
        embeddingRepository.batchUpdate(getIndex(tenantId), docs);
    }

    public EmbeddingSearchResult<FaqEmbeddingData> search(String tenantId, EmbeddingSearchRequest searchInput) {
        searchInput.setIndexId(getIndex(tenantId));
        return embeddingRepository.search(searchInput, new TypeReference<EmbeddingSearchResult<FaqEmbeddingData>>() {
        });
    }

    public void delete(String tenantId, List<String> uIds) {
        if (uIds == null || uIds.isEmpty()) {
            return;
        }
        String filter = "_uid in ('" + CollUtil.join(uIds, "','") + "')";
        embeddingRepository.delete(getIndex(tenantId), filter, null);
    }

    public void deleteByGroupIds(String tenantId, List<Long> groupId) {
        if (CollectionUtils.isEmpty(groupId)) {
            return;
        }
        String filter = "group_id in (" + groupId.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(",")) + ")";
        embeddingRepository.deleteAsync(getIndex(tenantId), filter, null);
    }

    public void deleteByPath(String tenantId, String path) {
        if (StringUtils.isBlank(path)) {
            return;
        }
        String filter = "group_path like '" + path + "%'";
        embeddingRepository.deleteAsync(getIndex(tenantId), filter, null);
    }
}
