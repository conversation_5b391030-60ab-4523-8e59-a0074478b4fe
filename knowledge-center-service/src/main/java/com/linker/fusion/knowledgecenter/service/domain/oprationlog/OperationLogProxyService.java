package com.linker.fusion.knowledgecenter.service.domain.oprationlog;

import com.linker.fusion.knowledgecenter.infrastructure.constants.LogRecordSubType;
import com.linker.fusion.knowledgecenter.infrastructure.constants.LogRecordType;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.logapi.starter.annotation.LogRecord;
import org.springframework.stereotype.Service;

/**
 * 操作日志代理类，做一个内聚作用，有些要实现空方法可以在这里处理
 */
@Service
public class OperationLogProxyService {
    /**
     * 记录文件删除的操作记录
     * @param file
     */
    @LogRecord(success = "删除{{#file.fileName}}{getKnowledgeGroupTree{#file.groupId}}",
            type = LogRecordType.FILE_MANAGEMENT,
            subType = LogRecordSubType.DELETE_FILE, bizId = "{{#file.docId}}")
    public void deleteFilelogRecord(KnowledgeResourceEntity file) {

    }

    /**
     * 记录创建文件知识库操作
     * @param libId
     * @param name
     * @param visible
     */
    @LogRecord(success = "创建{{#name}}（{{#visible}}）",
            type = LogRecordType.FILE_MANAGEMENT,
            subType = LogRecordSubType.CREATE_KNOWLEDGE_BASE, bizId = "{{#libId}}")
    public void createFileLibRecord(Long libId, String name, String visible) {

    }

    /**
     * 导入完成日志记录
     * @param fileName
     * @param groupId
     * @param resId
     */
    @LogRecord(success = "导入{{#fileName}}{getKnowledgeGroupTree{#groupId}}",
            type = LogRecordType.FILE_MANAGEMENT,
            subType = LogRecordSubType.IMPORT_FILE, bizId = "{{#resId}}")
    public void importDowComplete(String fileName, Long groupId, String resId) {

    }




    /**
     * 记录创建文件知识库操作
     * @param groupId
     * @param name
     * @param visible
     */
    @LogRecord(success = "添加{{#name}}{getParentGroupTree{#groupId}}",
            type = LogRecordType.FILE_MANAGEMENT,
            subType = LogRecordSubType.ADD_DIRECTORY, bizId = "{{#groupId}}")
    public void createFolderLogRecord(Long groupId, String name, String visible) {
    }

    @LogRecord(success = "删除{{#name}}（{{#visible}}）",
            type = LogRecordType.FILE_MANAGEMENT,
            subType = LogRecordSubType.DELETE_KNOWLEDGE_BASE, bizId = "{{#libId}}")
    public void deleteFileLibRecord(Long libId, String name, String visible) {

    }

    @LogRecord(success = "删除{{#name}}{getParentGroupTree{#groupId}}",
            type = LogRecordType.FILE_MANAGEMENT,
            subType = LogRecordSubType.DELETE_DIRECTORY, bizId = "{{#groupId}}")
    public void deleteFileFolderRecord(Long groupId, String name, String visible) {

    }

    /**
     *  移动xx目录（个人/xx知识库/xx目录）到个人/xx知识库/xx目录
     * @param source
     * @param target
     */
    @LogRecord(success = "移动{{#source.name}}{getKnowledgeGroupTree{#source.id}} 到 {getKnowledgeGroupTree{#target.id}}",
            type = LogRecordType.FILE_MANAGEMENT,
            subType = LogRecordSubType.MOVE_DIRECTORY, bizId = "{{#source.id}}")
    public void moveFolderRecord(KnowledgeGroupEntity source, KnowledgeGroupEntity target) {

    }

    @LogRecord(success = "下载{{#resource.fileName}}{getKnowledgeGroupTree{#resource.groupId}}",
            type = LogRecordType.FILE_MANAGEMENT,
            subType = LogRecordSubType.DOWNLOAD_FILE, bizId = "{{#resource.docId}}")
    public String download(KnowledgeResourceEntity resource) {
        return resource.getUrl();
    }
}
