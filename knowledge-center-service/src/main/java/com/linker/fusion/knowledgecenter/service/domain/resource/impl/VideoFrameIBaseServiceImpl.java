package com.linker.fusion.knowledgecenter.service.domain.resource.impl;

import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.linker.fusion.knowledgecenter.infrastructure.common.EsInject;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoFrameEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexTenantMappingManager;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IVideoFrameIBaseService;
import com.linker.omagent.core.repository.embedding.EmbeddingRepository;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchRequest;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchResult;
import com.linker.omagent.core.repository.embedding.SearchHit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.enums.IndexSuffixEnum.FRAME;

@Service
@Slf4j
public class VideoFrameIBaseServiceImpl implements IVideoFrameIBaseService {
    private final ExecutorService fixedThreadPool = new ThreadPoolExecutor(30, Integer.MAX_VALUE, 300L, TimeUnit.SECONDS, new SynchronousQueue<Runnable>());
    @Resource
    private EmbeddingRepository embeddingRepository;
    @Autowired
    private IIndexTenantMappingManager iIndexTenantMappingManager;

    private JSONObject getMapping() {
        return JSONObject.parseObject(ResourceUtil.readUtf8Str("mappings/videoFrame.json"));
    }

    @Override
    public void updateIndex(String tenantId) {
        String index = getIndex(tenantId);
        if (embeddingRepository.indexExist(index)) {
            embeddingRepository.updateIndex(index, getMapping());
        } else {
            embeddingRepository.createIndex(index, getMapping(), EsInject.DisableSettings);
        }
    }

    public String getIndex(String tenantId) {
        return iIndexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.VIDEO) + FRAME.suffix;
    }

    @Override
    public VideoFrameEntity get(String tenantId, String chunkId) {
        JSONArray sort = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        // jsonObject.put("sort", "asc");
        jsonObject.put("create_time", "desc");
        sort.add(jsonObject);
        String filter = "chunk_id='" + chunkId + "'";
        EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                .indexId(getIndex(tenantId))
                .filter(filter)
                .sort(sort)
                .build();
        EmbeddingSearchResult<VideoFrameEntity> search = embeddingRepository.search(request, new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
        });
        if (CollectionUtils.isNotEmpty(search.getHits())) {
            return search.getHits().get(0).getDocument();
        }
        return null;
    }

    @Override
    public void delete(String tenantId, List<String> chunkIds) {
        embeddingRepository.delete(getIndex(tenantId), "chunk_id in ('" + StringUtils.join(chunkIds, "','") + "')", new JSONObject(), true);
    }

    @Override
    public void deleteAsync(String tenantId, List<String> docIds) {
        embeddingRepository.deleteAsync(getIndex(tenantId), "doc_id in ('" + StringUtils.join(docIds, "','") + "')", new JSONObject());
    }

    @Override
    public void clear(String tenantId, String docId) {
        embeddingRepository.delete(getIndex(tenantId), "doc_id='" + docId + "'", new JSONObject());
    }

    @Override
    public void deleteBySegmentId(String tenantId, String segmentId) {
        embeddingRepository.delete(getIndex(tenantId), "segment_id='" + segmentId + "'", new JSONObject());
    }

    @Override
    public List<String> add(String tenantId, List<VideoFrameEntity> chunks, boolean refresh) {
        String index = getIndex(tenantId);
        if (!embeddingRepository.indexExist(index)) {
            embeddingRepository.createIndex(index, getMapping(), EsInject.DisableSettings);
        }
        List<String> ret = new ArrayList<>();
        List<VideoFrameEntity> insertChunks = new ArrayList<>();
        for (VideoFrameEntity chunk : chunks) {
            if (insertChunks.size() >= 100) {
                ret.addAll(embeddingRepository.batchAdd(index, insertChunks, refresh));
                insertChunks = new ArrayList<>();
                continue;
            }
            insertChunks.add(chunk);
        }
        if (CollectionUtils.isNotEmpty(insertChunks)) {
            ret.addAll(embeddingRepository.batchAdd(index, insertChunks, refresh));
        }
        return ret;
    }

    @Override
    public List<VideoFrameEntity> list(String tenantId, String segmentId, String content) {
        JSONArray sort = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("create_time", "desc");
        sort.add(jsonObject);
        String filter = "segment_id='" + segmentId + "'";
        if (StringUtils.isNotBlank(content)) {
            filter += " and ( content like '%" + content + "%' ";
            filter += " or content like '" + content + "') ";
        }
        log.info("filter:" + filter);
        EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                .indexId(getIndex(tenantId))
                .filter(filter)
                .maxResults(9999)
                .exclude(Arrays.asList("content_vector", "image_vector"))
//                .matchQuery(matchQuery)
                .sort(sort)
                .build();
        EmbeddingSearchResult<VideoFrameEntity> search = embeddingRepository.search(request, new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
        });
        List<VideoFrameEntity> collect = search.getHits().stream().map(x -> x.document()).collect(Collectors.toList());
        if (StringUtils.isNotBlank(content)) {
            collect = collect.stream().filter(x -> x.getContent().toLowerCase().contains(content.toLowerCase())).collect(Collectors.toList());
        }
        return collect;
    }

    @Override
    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    @CacheRefresh(refresh = 30, stopRefreshAfterLastAccess = 120)
    @CachePenetrationProtect
    public List<VideoFrameEntity> listByDocId(String tenantId, String docId) {
        long l = System.currentTimeMillis();
        if (StringUtils.isBlank(docId)) {
            return Collections.emptyList();
        }
        JSONArray sort = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time_point", "asc");
        sort.add(jsonObject);
        String filter = "doc_id='" + docId + "'";
        EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                .indexId(getIndex(tenantId))
                .filter(filter)
                .maxResults(9999)
                .exclude(Arrays.asList("content_vector", "image_vector"))
//                .matchQuery(matchQuery)
                .sort(sort)
                .build();
        EmbeddingSearchResult<VideoFrameEntity> search = embeddingRepository.search(request, new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
        });
        List<VideoFrameEntity> collect = search.getHits().stream().map(SearchHit::document).collect(Collectors.toList());
        log.info("listBySegmentIds耗时:{}", System.currentTimeMillis() - l);
        return collect;
    }

    @Override
    public List<SearchHit<VideoFrameEntity>> list(String tenantId, List<String> docIds) {
        JSONArray sort = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("create_time", "desc");
        sort.add(jsonObject);
        String filter = "doc_id in ('" + StringUtils.join(docIds, "','") + "')";
        EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                .indexId(getIndex(tenantId))
                .maxResults(9999)
                .filter(filter)
                .sort(sort)
                .build();
        return embeddingRepository.search(request, new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
        }).getHits();
    }

    @Override
    public List<VideoFrameEntity> listVector(Integer limit, String tenantId, List<String> docIds, String lastId) {
        JSONArray sort = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("create_time", "desc");
        sort.add(jsonObject);
        String filter = "doc_id in ('" + StringUtils.join(docIds, "','") + "')";
        if (StringUtils.isNotBlank(lastId)) {
            filter += " and _uid >'" + lastId + "'";
        }
        EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                .indexId(getIndex(tenantId))
                .maxResults(limit)
                .include(Arrays.asList("doc_id", "frame_id", "time_point", "image_vector", "segment_id"))
                .filter(filter)
                .sort(sort)
                .build();
        return embeddingRepository.search(request, new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
        }).getHits().stream().map(SearchHit::document).collect(Collectors.toList());
    }

    @Override
    public VideoFrameEntity getFirst(String tenantId, String docId) {
        JSONArray sort = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time_point", "asc");
        sort.add(jsonObject);
        String filter = "doc_id = '" + docId + "'";
        log.info("filter:{}", filter);
        EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                .indexId(getIndex(tenantId))
                .filter(filter)
                .maxResults(1)
                .exclude(Arrays.asList("content_vector", "image_vector"))
                .sort(sort)
                .build();
        EmbeddingSearchResult<VideoFrameEntity> search = embeddingRepository.search(request, new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
        });
        Optional<SearchHit<VideoFrameEntity>> optional = search.getHits().stream().findFirst();
        return optional.map(SearchHit::getDocument).orElse(null);
    }

    @Override
    public List<String> getAllUIds(String tenantId, List<String> docIds) {
        List<String> uIds = new ArrayList<>();
        String filter = "doc_id in ('" + StringUtils.join(docIds, "','") + "')";
        EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                .indexId(getIndex(tenantId))
                .filter(filter)
                .include(Collections.singletonList("_uid"))
                .maxResults(9999)
                .build();
        EmbeddingSearchResult<VideoFrameEntity> search = embeddingRepository.search(request, new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
        });
        List<SearchHit<VideoFrameEntity>> hits = search.getHits();
        hits.forEach(x -> uIds.add(x.document().getUid()));
        return uIds;
    }

    @Override
    public EmbeddingSearchResult<VideoFrameEntity> search(String tenantId, EmbeddingSearchRequest searchInput) {
        searchInput.setIndexId(getIndex(tenantId));
        return embeddingRepository.search(searchInput, new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
        });
    }

    @Override
    public void updateEnable(String tenantId, String docId, Boolean enable) {
        List<VideoFrameEntity> chunks = getAllUIds(tenantId, Collections.singletonList(docId))
                .stream().map(uid -> {
                    VideoFrameEntity chunk = new VideoFrameEntity();
                    chunk.setUid(uid);
                    chunk.setEnable(Boolean.TRUE.equals(enable) ? 1 : 0);
                    return chunk;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(chunks)) {
            embeddingRepository.batchUpdate(getIndex(tenantId), chunks);
        }
    }

    @Override
    public void updateGroupId(String tenantId, String docId, Long groupId) {
        List<VideoFrameEntity> chunks = getAllUIds(tenantId, Collections.singletonList(docId))
                .stream().map(uid -> {
                    VideoFrameEntity chunk = new VideoFrameEntity();
                    chunk.setUid(uid);
                    chunk.setGroupId(groupId);
                    return chunk;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(chunks)) {
            embeddingRepository.batchUpdate(getIndex(tenantId), chunks);
        }
    }

    @Override
    public void updateTitle(String tenantId, String docId, String title) {
        List<VideoFrameEntity> chunks = getAllUIds(tenantId, Collections.singletonList(docId))
                .stream().map(uid -> {
                    VideoFrameEntity chunk = new VideoFrameEntity();
                    chunk.setUid(uid);
                    chunk.setTitle(title);
                    return chunk;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(chunks)) {
            embeddingRepository.batchUpdate(getIndex(tenantId), chunks);
        }
    }

    @Override
    public void updateMeta(String tenantId, String docId, JSONObject meta) {
        List<VideoFrameEntity> chunks = getAllUIds(tenantId, Collections.singletonList(docId))
                .stream().map(uid -> {
                    VideoFrameEntity chunk = new VideoFrameEntity();
                    chunk.setUid(uid);
                    chunk.setMeta(meta);
                    return chunk;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(chunks)) {
            embeddingRepository.batchUpdate(getIndex(tenantId), chunks);
        }
    }

    @Override
    public void updateCreateTime(String tenantId, String docId, LocalDateTime createTime) {
        List<VideoFrameEntity> chunks = getAllUIds(tenantId, Collections.singletonList(docId))
                .stream().map(uid -> {
                    VideoFrameEntity chunk = new VideoFrameEntity();
                    chunk.setUid(uid);
                    chunk.setCreateTime(StringComUtils.convertStr(createTime));
                    return chunk;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(chunks)) {
            embeddingRepository.batchUpdate(getIndex(tenantId), chunks);
        }
    }

    @Override
    public List<VideoFrameEntity> listByFrameIds(String tenantId, List<String> frameIdList) {
        if (CollectionUtils.isEmpty(frameIdList))
            return new ArrayList<>();
        JSONArray sort = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("create_time", "desc");
        sort.add(jsonObject);
        String filter = "frame_id in ('" + StringUtils.join(frameIdList, "','") + "')";
        EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                .indexId(getIndex(tenantId))
                .filter(filter)
                .sort(sort)
                .build();
        return embeddingRepository.search(request, new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
        }).getHits().stream().map(h -> h.getDocument()).collect(Collectors.toList());
    }

    @Override
    public void clearByFileIds(String tenantId, List<String> fileIds) {
        if (CollectionUtils.isEmpty(fileIds))
            return;
        embeddingRepository.deleteAsync(getIndex(tenantId), "doc_id in ('" + StringUtils.join(fileIds, "','") + "')", new JSONObject());
    }

    @Override
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    @CacheRefresh(refresh = 13, stopRefreshAfterLastAccess = 300)
    public VideoFrameEntity getOneFrame(String tenantId, String docId, Long startTimestamp, Long endTimestamp) {
        long l = System.currentTimeMillis();
        if (StringUtils.isBlank(docId)) {
            return new VideoFrameEntity();
        }
        JSONArray sort = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time_point", "asc");
        sort.add(jsonObject);
        //增加timePoint between startTimestamp and endTimestamp
        String filter = "doc_id='" + docId + "' AND timePoint between " + startTimestamp + " AND " + endTimestamp;
        EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                .indexId(getIndex(tenantId))
                .filter(filter)
                .maxResults(1)
                .exclude(Arrays.asList("content_vector", "image_vector"))
//                .matchQuery(matchQuery)
                .sort(sort)
                .build();
        EmbeddingSearchResult<VideoFrameEntity> search = embeddingRepository.search(request, new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
        });
        List<VideoFrameEntity> collect = search.getHits().stream().map(SearchHit::document).collect(Collectors.toList());
        log.info("docId:{},getOneFrame耗时:{}，召回数量：{}", docId, System.currentTimeMillis() - l, CollectionUtils.size(collect));
        if (CollectionUtils.isNotEmpty(collect)) {
            return collect.get(0);
        }
        return new VideoFrameEntity();
    }


    @Override
    public List<EmbeddingSearchResult<VideoFrameEntity>> batchSearch(String tenantId, List<EmbeddingSearchRequest> searchInputs) {
        List<CompletableFuture<EmbeddingSearchResult<VideoFrameEntity>>> completableFutures = new ArrayList<>();
        searchInputs.forEach(item -> {
            CompletableFuture<EmbeddingSearchResult<VideoFrameEntity>> future = CompletableFuture.supplyAsync(() -> this.search(tenantId, item), fixedThreadPool);
            completableFutures.add(future);
        });
        // 等待所有异步操作完成
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
        List<EmbeddingSearchResult<VideoFrameEntity>> result = new ArrayList<>();
        completableFutures.forEach(item -> {
            try {
                // 获取单个异步操作的结果
                result.add(item.get());
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        });
        return result;
    }

    @Override
    public void update(String tenantId, VideoFrameEntity chunk) {
        embeddingRepository.batchUpdate(getIndex(tenantId), Arrays.asList(chunk));
    }

    @Override
    public void batchUpdate(String tenantId, List<VideoFrameEntity> docs) {
        embeddingRepository.batchUpdate(getIndex(tenantId), docs);
    }
}
