package com.linker.fusion.knowledgecenter.service.domain.retriever.advanced;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexSuffixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.ChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoFrameEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexTenantMappingManager;
import com.linker.fusion.knowledgecenter.service.domain.EmbeddingService;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.KnowledgeConfig;
import com.linker.fusion.knowledgecenter.service.domain.retriever.MetaFilterUtil;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.Meta;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.QueryParams;
import com.linker.omagent.core.rag.content.retriever.Content;
import com.linker.omagent.core.rag.content.retriever.ContentRetriever;
import com.linker.omagent.core.rag.query.Query;
import com.linker.omagent.core.repository.embedding.*;
import com.linker.omagent.core.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class VideoAdvancedRetriever extends AbstractAdvancedRetriever implements ContentRetriever {

    @Autowired
    private EmbeddingRepository embeddingStore;
    @Autowired
    private IIndexTenantMappingManager indexTenantMappingManager;
    @Autowired
    private EmbeddingService embeddingService;

    private static String buildFilter(QueryParams extra, boolean audio, Set<String> segmentIds) {
        KnowledgeConfig knowledgeConfig = extra.getKnowledgeConfig();
        List<Long> docGroupIds = knowledgeConfig.getDocGroupIds();
        StringBuilder filter = new StringBuilder(String.format("enable = 1 and tenant_id = '%s' ", extra.getTenantId()));
        List<String> docIds = extra.getDocIds();
        //如果docGroupIds和extra.getDocIds()都不为空的情况下，and (group_id in ("xx") or doc_id in ("xx")) 
        MetaFilterUtil.builderKnowledgeFilter(docGroupIds, docIds, filter);
        if (!ObjectUtils.isEmpty(extra.getNotResIds())) {
            String resourceIdsStr = extra.getNotResIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id not in (").append(resourceIdsStr).append(")");
        }
        // 固定FileTypeEnum.VIDEO
        MetaFilterUtil.buildFilter(filter, extra, FileTypeEnum.VIDEO);
        if (audio) {
            filter.append(" and file_type = ").append(FileTypeEnum.VIDEO.getType());
        }
        if (!ObjectUtils.isEmpty(segmentIds)) {
            String segmentIdsStr = segmentIds.stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and segment_id in (").append(segmentIdsStr).append(")");
        }
        return filter.toString();
    }


    @Override
    public List<Content> retrieve(Query query) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        if (!CollectionUtils.containsAny(extra.getFileTypes(), FileTypeEnum.VIDEO.getType())) {
            return Collections.emptyList();
        }
        boolean searchAsr = StringUtils.isNotBlank(query.getInput());
        boolean embeddedRetrieve = StringUtils.isNotBlank(extra.getPicUrl()) || StringUtils.isNotBlank(extra.getEmbeddedKeyword());
        if (searchAsr && !embeddedRetrieve) {
            List<Content> contents = retrieveVideoTitle(query, embeddedRetrieve);
            List<Content> retrieveChunkContents = retrieveAudio(query, embeddedRetrieve);
            List<Content> retrieveFrameContents = retrieveFrameWithQuery(query);
            List<Content> union = ListUtils.union(retrieveChunkContents, contents);
            union.addAll(retrieveFrameContents);
            return union;
        } else if (!searchAsr && embeddedRetrieve) {
            long l = System.currentTimeMillis();
            List<Double> vector = embeddingService.embedImageAndText(extra.getPicUrl(), extra.getEmbeddedKeyword());
            log.info("向量化耗时:{}", System.currentTimeMillis() - l);
            List<Content> retrieveChunkContents = retrieveChunk(query, null, vector);
            List<Content> retrieveFrameContents = retrieveFrame(query, null, vector);
            return ListUtils.union(retrieveChunkContents, retrieveFrameContents);
        } else if (searchAsr && embeddedRetrieve) {
            List<Content> retrieveAudioContents = retrieveAudio(query, embeddedRetrieve);
            Set<String> allSegmentIds = retrieveAudioContents.stream().map(c -> c.getSegment().metadata(Meta.class).getSegmentId()).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(allSegmentIds)) {
                return Collections.emptyList();
            }
            long l = System.currentTimeMillis();
            List<Double> vector = embeddingService.embedImageAndText(extra.getPicUrl(), extra.getEmbeddedKeyword());
            log.info("向量化耗时:{}", System.currentTimeMillis() - l);
            List<Content> retrieveChunkContents = retrieveChunk(query, allSegmentIds, vector);
            List<Content> retrieveFrameContents = retrieveFrame(query, allSegmentIds, vector);
            return ListUtils.union(retrieveChunkContents, retrieveFrameContents);
        }
        return Collections.emptyList();
    }

    private List<Content> retrieveFrameWithQuery(Query query) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        long l = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(extra.getVisionParams())) {
            log.info("未携带视觉参数，retrieveFrameWithQuery跳过检索");
            return new ArrayList<>();
        }
        String filter = buildFilter(extra, false, null);
        log.debug("检索条件:{}", filter);
        String tenantId = extra.getTenantId();
        String index = indexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.VIDEO) + IndexSuffixEnum.FRAME.suffix;

        EmbeddingSearchRequest vectorSearch = EmbeddingSearchRequest
                .builder()
                .indexId(index)
                .include(Lists.newArrayList("file_title", "content", "segment_id", "doc_id", "frame_id", "url", "time_point"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), 100))
                .filter(filter)
                .build();
        // 标题和ASR检索，高亮
        FilterExtendUtil.buildFileTitle(vectorSearch, query.getInput());
        EmbeddingSearchResult<VideoFrameEntity> searchResult = this.embeddingStore.search(vectorSearch,
                new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
                });

        log.info("检索器：{} Frame,检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));
        return searchResult.matches().stream().map(hit -> {
            VideoFrameEntity document = hit.getDocument();
            Meta meta = new Meta();
            meta.setDocId(document.getDocId());
            meta.setSegmentId(document.getSegmentId());
            meta.setFrameId(document.getFrameId());
            meta.setFrameUrl(document.getUrl());
            meta.setTitle(document.getTitle());
            meta.setContent(document.getContent());
            meta.setIndexSuffix(IndexSuffixEnum.FRAME.suffix);
            meta.setScore(hit.getScore());
            meta.setTimepoint(hit.getDocument().getTimePoint());
            meta.setNeedRerank(false);
            meta.setRetriever(description() + " frame");
            return new Content(document.getFrameId(), FileTypeEnum.VIDEO.getValue(), meta);
        }).collect(Collectors.toList());
    }

    public List<Content> retrieveAudio(Query query, boolean embeddedRetrieve) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        long l = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(extra.getVisionParams())) {
            log.info("携带了视觉参数，retrieveAudio检索器跳过检索");
            return new ArrayList<>();
        }
        String filter = buildFilter(extra, true, null);
        log.debug("检索条件:{}", filter);
        String tenantId = extra.getTenantId();
        String index = indexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.AUDIO) + IndexSuffixEnum.AUDIO.suffix;
        EmbeddingSearchRequest vectorSearch = EmbeddingSearchRequest
                .builder()
                .indexId(index)
                .include(Lists.newArrayList("file_title", "content", "segment_id", "doc_id", "chunk_id"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), embeddedRetrieve ? 9999 : 1000))
                .filter(filter)
                .build();
        // 标题和ASR检索，高亮
        FilterExtendUtil.buildFileTitleAndContent(vectorSearch, query.getInput());
        EmbeddingSearchResult<ChunkEntity> searchResult = this.embeddingStore.search(vectorSearch,
                new TypeReference<EmbeddingSearchResult<ChunkEntity>>() {
                });

        log.info("检索器：{} ASR检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));
        return searchResult.matches().stream().map(hit -> {
            ChunkEntity document = hit.getDocument();
            Meta meta = new Meta();
            meta.setDocId(document.getDocId());
            meta.setSegmentId(document.getSegmentId());
            meta.setChunkId(document.getChunkId());
            meta.setTitle(document.getTitle());
            meta.setContent(document.getContent());
            //     高亮
            buildHighlight(hit.getHighlight(), meta);
            meta.setScore(hit.getScore());
            meta.setNeedRerank(extra.isNeedReRank());
            meta.setIndexSuffix(IndexSuffixEnum.AUDIO.suffix);
            meta.setRetriever(description() + " asr");
            return new Content(document.getSegmentId(), FileTypeEnum.VIDEO.getValue(), meta);
        }).collect(Collectors.toList());
    }

    public List<Content> retrieveVideoTitle(Query query, boolean embeddedRetrieve) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        long l = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(extra.getVisionParams())) {
            log.info("携带了视觉参数，retrieveVideoTitle跳过检索");
            return new ArrayList<>();
        }
        String filter = buildFilter(extra, false, null);

        log.debug("检索条件:{}", filter);
        String tenantId = extra.getTenantId();
        String index = indexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.VIDEO) + IndexSuffixEnum.VIDEO.suffix;
        EmbeddingSearchRequest vectorSearch = EmbeddingSearchRequest
                .builder()
                .indexId(index)
                .include(Lists.newArrayList("file_title", "content", "segment_id", "doc_id", "chunk_id"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), embeddedRetrieve ? 9999 : 1000))
                .filter(filter)
                .build();
        // 标题和ASR检索，高亮
        FilterExtendUtil.buildFileTitle(vectorSearch, query.getInput());
        EmbeddingSearchResult<ChunkEntity> searchResult = this.embeddingStore.search(vectorSearch,
                new TypeReference<EmbeddingSearchResult<ChunkEntity>>() {
                });

        log.info("检索器：{} video_title检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));
        return searchResult.matches().stream().map(hit -> {
            ChunkEntity document = hit.getDocument();
            Meta meta = new Meta();
            meta.setDocId(document.getDocId());
            meta.setSegmentId(document.getSegmentId());
            meta.setChunkId(document.getChunkId());
            meta.setTitle(document.getTitle());
            meta.setContent(document.getContent());
            JSONObject highlight = hit.getHighlight();
            //     高亮
            buildHighlight(hit.getHighlight(), meta);
            meta.setScore(hit.getScore());
            meta.setNeedRerank(false);
            meta.setIndexSuffix(IndexSuffixEnum.VIDEO.suffix);
            meta.setRetriever(description() + " video_extquery");
            return new Content(document.getSegmentId(), FileTypeEnum.VIDEO.getValue(), meta);
        }).collect(Collectors.toList());
    }


    public List<Content> retrieveChunk(Query query, Set<String> segmentIds, List<Double> vector) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        long l = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(extra.getVisionParams())) {
            log.info("携带了视觉参数，chunk跳过检索");
            return new ArrayList<>();
        }
        String filter = buildFilter(extra, false, segmentIds);
        log.debug("检索条件:{}", filter);

        String tenantId = extra.getTenantId();
        String index = indexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.VIDEO) + IndexSuffixEnum.VIDEO.suffix;

        MatchQuery<Object> matchQuery = MatchQuery.builder()
                .key("content_vector")
                .value(new EmbeddingValue(vector, extra.getThreshold()))
                .build();
        EmbeddingSearchRequest vectorSearch = EmbeddingSearchRequest
                .builder()
                .indexId(index)
                .include(Lists.newArrayList("file_title", "content", "segment_id", "doc_id", "chunk_id"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), 100))
                .filter(filter)
                .matchQuery(matchQuery)
                .build();

        EmbeddingSearchResult<ChunkEntity> searchResult = this.embeddingStore.search(vectorSearch,
                new TypeReference<EmbeddingSearchResult<ChunkEntity>>() {
                });

        log.info("检索器：{} Chunk,检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));
        return searchResult.matches().stream().map(hit -> {
            ChunkEntity document = hit.getDocument();
            Meta meta = new Meta();
            meta.setDocId(document.getDocId());
            meta.setSegmentId(document.getSegmentId());
            meta.setChunkId(document.getChunkId());
            meta.setTitle(document.getTitle());
            meta.setContent(document.getContent());
            meta.setIndexSuffix(IndexSuffixEnum.VIDEO.suffix);
            meta.setScore(hit.getScore());
            meta.setNeedRerank(extra.isNeedReRank());
            meta.setRetriever(description() + " chunk");
            return new Content(document.getSegmentId(), FileTypeEnum.VIDEO.getValue(), meta);
        }).collect(Collectors.toList());
    }

    public List<Content> retrieveFrame(Query query, Set<String> segmentIds, List<Double> vector) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        long l = System.currentTimeMillis();
        String filter = buildFilter(extra, false, segmentIds);
        log.debug("检索条件:{}", filter);
        String tenantId = extra.getTenantId();
        String index = indexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.VIDEO) + IndexSuffixEnum.FRAME.suffix;
        MatchQuery<Object> matchQuery = MatchQuery.builder().key("image_vector").value(new EmbeddingValue(vector
                , extra.getThreshold())).build();
        EmbeddingSearchRequest vectorSearch = EmbeddingSearchRequest
                .builder()
                .indexId(index)
                .include(Lists.newArrayList("file_title", "content", "segment_id", "doc_id", "frame_id", "url", "time_point"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), 100))
                .filter(filter)
                .matchQuery(matchQuery)
                .build();

        EmbeddingSearchResult<VideoFrameEntity> searchResult = this.embeddingStore.search(vectorSearch,
                new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
                });

        log.info("检索器：{} Frame,检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));
        return searchResult.matches().stream().map(hit -> {
            VideoFrameEntity document = hit.getDocument();
            Meta meta = new Meta();
            meta.setDocId(document.getDocId());
            meta.setSegmentId(document.getSegmentId());
            meta.setFrameId(document.getFrameId());
            meta.setFrameUrl(document.getUrl());
            meta.setTitle(document.getTitle());
            meta.setContent(document.getContent());
            meta.setIndexSuffix(IndexSuffixEnum.FRAME.suffix);
            meta.setScore(hit.getScore());
            meta.setTimepoint(hit.getDocument().getTimePoint());
            meta.setNeedRerank(false);
            meta.setRetriever(description() + " frame");
            return new Content(document.getFrameId(), FileTypeEnum.VIDEO.getValue(), meta);
        }).collect(Collectors.toList());
    }

    private String replaceContent(@NotNull VideoFrameEntity chunkEntity) {
        String sb = chunkEntity.getTitle() + "\n" +
                chunkEntity.getObjectLabels() + "\n" +
                chunkEntity.getPersonsLabels() + "\n" +
                chunkEntity.getTextLabels() + "\n";
        return sb.trim();
    }

    @Override
    public String description() {
        return "视频高级检索器";
    }

    @Override
    public String type() {
        return "advanced";
    }

}