package com.linker.fusion.knowledgecenter.service.domain.oprationlog.function;

import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceVisibleTypeEnum;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.logapi.service.IParseFunction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GetKnowledgeGroupTreeParseFunction implements IParseFunction {

    @Autowired
    private KnowledgeGroupService knowledgeGroupService;

    @Override
    public boolean executeBefore() {
        return true;
    }

    @Override
    public String functionName() {
        return "getKnowledgeGroupTree";
    }

    /**
     * 目录树（个人/xx知识库/xx目录）
     *
     * @param value 函数入参
     * @return
     */
    @Override
    public String apply(Object value) {
        if (value == null) {
            return "";
        }
        if (value instanceof Long) {
            Long groupId = (Long) value;
            if (groupId == -1L) {
                return "个人空间/会话文件";
            }
            KnowledgeGroupEntity group = knowledgeGroupService.getNotNull(groupId);
            List<KnowledgeGroupEntity> parents = knowledgeGroupService.getParents(group);
            String path = group.getName();

            if (CollectionUtils.isNotEmpty(parents)) {
                String parentPath = parents.stream().map(KnowledgeGroupEntity::getName).collect(Collectors.joining("/"));
                path = parentPath + "/" + path;
            }
            Integer visibleType = group.getVisibleType();
            String name = ResourceVisibleTypeEnum.getName(visibleType);
//            todo  查询知识库的父级目录树 "（个人/xx知识库/xx目录）
            return String.format("（%s/%s）", name, path);
        }
        return "";
    }
}