//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.linker.fusion.knowledgecenter.service.domain.retriever;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexSuffixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.ChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexTenantMappingManager;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.KnowledgeConfig;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.Meta;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.QueryParams;
import com.linker.omagent.core.rag.content.retriever.Content;
import com.linker.omagent.core.rag.content.retriever.ContentRetriever;
import com.linker.omagent.core.rag.query.Query;
import com.linker.omagent.core.repository.embedding.EmbeddingRepository;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchRequest;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchResult;
import com.linker.omagent.core.repository.embedding.MatchQuery;
import com.linker.omagent.core.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TextBm25ContentRetriever extends AbstractBm25Retriever implements ContentRetriever {

    @Autowired
    private EmbeddingRepository embeddingStore;
    @Autowired
    IIndexTenantMappingManager indexTenantMappingManager;

    private static String buildFilter(QueryParams extra, String type) {
        KnowledgeConfig knowledgeConfig = extra.getKnowledgeConfig();
        List<Long> docGroupIds = knowledgeConfig.getDocGroupIds();
        StringBuilder filter = new StringBuilder(String.format("enable = 1 and tenant_id = '%s'    ",
                extra.getTenantId()
        ));
        if (CollectionUtils.isNotEmpty(docGroupIds)) {
            String str = docGroupIds.stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            String format = String.format(" and  group_id in (%s)   ",
                    str
            );
            filter.append(format);
        }
        if (!ObjectUtils.isEmpty(extra.getDocIds())) {
            String resourceIdsStr = extra.getDocIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id in (").append(resourceIdsStr).append(")");
        }
        if (!ObjectUtils.isEmpty(extra.getNotResIds())) {
            String resourceIdsStr = extra.getNotResIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id not in (").append(resourceIdsStr).append(")");
        }
        MetaFilterUtil.buildFilter(filter, extra, FileTypeEnum.DOCUMENT);
        return filter.toString();
    }


    @Override
    public List<Content> retrieve(Query query) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        if (!CollectionUtils.containsAny(extra.getFileTypes(), FileTypeEnum.DOCUMENT.getType())) {
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(query.getInput())) {
            return Collections.emptyList();
        }
        long l = System.currentTimeMillis();
        EmbeddingSearchResult<ChunkEntity> searchResult;
        String type = FileTypeEnum.DOCUMENT.getName();
        String filter = buildFilter(extra, type);
        log.debug("检索条件:{}", filter);
        String tenantId = extra.getTenantId();
        String index = indexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.DOCUMENT) + IndexSuffixEnum.DOCUMENT_CHUNK.suffix;
        EmbeddingSearchRequest bm25Search = EmbeddingSearchRequest.builder()
                .indexId(index)
                .matchQuery(MatchQuery.builder().key("content").value(query.input()).build())
                .filter(filter)
                .include(Lists.newArrayList("content", "segment_id", "doc_id", "chunk_id"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), 10))
                .build();
        searchResult = this.embeddingStore.search(bm25Search, new TypeReference<EmbeddingSearchResult<ChunkEntity>>() {
        });
        log.info("检索器：{},检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));
        return searchResult.matches().stream().map(hit -> {
            ChunkEntity document = hit.getDocument();
            Meta meta = new Meta();
            meta.setScore(hit.getScore());
            meta.setChunkId(document.getChunkId());
            meta.setSegmentId(document.getSegmentId());
            meta.setDocId(document.getDocId());
            meta.setRetriever("text_bm25");
            Content content = new Content(document.getContent(), type, meta);
            return content;
        }).collect(Collectors.toList());
    }

    @Override
    public String description() {
        return "存放一些文档数据、参考资料的文档库";
    }


}

