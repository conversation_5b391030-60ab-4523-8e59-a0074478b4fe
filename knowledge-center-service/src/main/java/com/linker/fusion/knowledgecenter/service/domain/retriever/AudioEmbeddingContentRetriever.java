//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.linker.fusion.knowledgecenter.service.domain.retriever;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexSuffixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.AudioChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexTenantMappingManager;
import com.linker.fusion.knowledgecenter.service.domain.EmbeddingService;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.KnowledgeConfig;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.Meta;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.QueryParams;
import com.linker.omagent.core.data.embedding.Embedding;
import com.linker.omagent.core.model.embedding.EmbeddingModel;
import com.linker.omagent.core.rag.content.retriever.Content;
import com.linker.omagent.core.rag.content.retriever.ContentRetriever;
import com.linker.omagent.core.rag.query.Query;
import com.linker.omagent.core.repository.embedding.*;
import com.linker.omagent.core.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 音频向量化
 */
@Slf4j
@Component
public class AudioEmbeddingContentRetriever extends AbstractEmbeddingRetriever implements ContentRetriever {

    @Autowired
    private EmbeddingRepository embeddingStore;
    @Autowired
    private EmbeddingModel embeddingModel;

    @Autowired
    EmbeddingService embeddingService;
    @Autowired
    private IIndexTenantMappingManager indexTenantMappingManager;

    private static String buildFilter(QueryParams extra) {
        KnowledgeConfig knowledgeConfig = extra.getKnowledgeConfig();
        List<Long> docGroupIds = knowledgeConfig.getDocGroupIds();
        StringBuilder filter = new StringBuilder(String.format("enable = 1 and tenant_id = '%s'    ",
                extra.getTenantId()
        ));
        if (CollectionUtils.isNotEmpty(docGroupIds)) {
            String str = docGroupIds.stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            String format = String.format(" and  group_id in (%s)   ",
                    str
            );
            filter.append(format);
        }
        if (!ObjectUtils.isEmpty(extra.getDocIds())) {
            String resourceIdsStr = extra.getDocIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id in (").append(resourceIdsStr).append(")");
        }
        if (!ObjectUtils.isEmpty(extra.getNotResIds())) {
            String resourceIdsStr = extra.getNotResIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id not in (").append(resourceIdsStr).append(")");
        }
        if (!ObjectUtils.isEmpty(extra.getFileTypes())) {
            String filetype = extra.getFileTypes().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append(" and file_type in (").append(filetype).append(")");
        }
        MetaFilterUtil.buildFilter(filter, extra, FileTypeEnum.AUDIO);
        return filter.toString();
    }


    @Override
    public String description() {
        return "音频检索";
    }


    @Override
    public List<Content> retrieve(Query query) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        if (!CollectionUtils.containsAny(extra.getFileTypes(), FileTypeEnum.VIDEO.getType(), FileTypeEnum.AUDIO.getType())) {
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(query.getInput())) {
            return Collections.emptyList();
        }
        long l = System.currentTimeMillis();
        EmbeddingSearchResult<AudioChunkEntity> searchResult;
        String type = FileTypeEnum.AUDIO.getName();
        String filter = buildFilter(extra);
        double threshold = extra.getThreshold();
        String tenantId = extra.getTenantId();
        String index = indexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.AUDIO) + IndexSuffixEnum.AUDIO.suffix;
        Embedding embeddedQuery = embeddingService.embedText(query.getInput()).content();

        List<Double> vector = embeddedQuery.vector();
        MatchQuery<Object> matchQuery = MatchQuery.builder().field("content_vector").value(new EmbeddingValue(vector
                , threshold)).build();
//        todo 获取index
        log.debug("检索条件:{}", filter);
        EmbeddingSearchRequest vectorSearch = EmbeddingSearchRequest
                .builder()
                .indexId(index)
                .matchQuery(matchQuery)
                .include(Lists.newArrayList("content", "segment_id", "doc_id", "chunk_id"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), 10))
                .filter(filter)
                .build();
        searchResult = this.embeddingStore.search(vectorSearch,
                new TypeReference<EmbeddingSearchResult<AudioChunkEntity>>() {
                });

        log.info("检索器：{},检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));
        return searchResult.matches().stream().map(hit -> {
            AudioChunkEntity c = hit.getDocument();
            Meta meta = new Meta();
            meta.setChunkId(c.getChunkId());
            meta.setSegmentId(c.getSegmentId());
            meta.setDocId(c.getDocId());
            meta.setScore(hit.getScore());
            meta.setRetriever("audio_embedding");
            return new Content(c.getContent(), type, meta);
        }).collect(Collectors.toList());
    }
}

