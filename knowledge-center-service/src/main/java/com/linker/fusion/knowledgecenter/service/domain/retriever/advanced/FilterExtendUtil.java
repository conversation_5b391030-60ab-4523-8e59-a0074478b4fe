package com.linker.fusion.knowledgecenter.service.domain.retriever.advanced;


import com.alibaba.fastjson.JSON;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchRequest;

public class FilterExtendUtil {
    public static void main(String[] args) {
        String filterExtend = String.format("[\n" +
                "    {\n" +
                "        \"match\": {\n" +
                "          \"file_title\": {\n" +
                "            \"query\": \"%s\",\n" +
                "            \"operator\": \"and\"\n" +
                "          }\n" +
                "        }\n" +
                "      }\n" +
                "]", 1);
        System.out.println(filterExtend);
    }

    public static void buildFileTitleAndContent(EmbeddingSearchRequest embeddingSearchRequest, String query) {

        String filterExtend = String.format("[\n" +
                "    {\n" +
                "        \"bool\": {\n" +
                "            \"should\": [\n" +
                "                {\n" +
                "                    \"match\": {\n" +
                "                        \"file_title\": {\n" +
                "                            \"query\": \"%s\",\n" +
                "                            \"operator\": \"and\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"wildcard\": {\n" +
                "                        \"file_title.keyword\": \"*%s*\"\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"match\": {\n" +
                "                        \"content\": {\n" +
                "                            \"query\": \"%s\",\n" +
                "                            \"operator\": \"and\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    }\n" +
                "]", query, query, query);
        embeddingSearchRequest.setFilterExtends(JSON.parseArray(filterExtend));
        embeddingSearchRequest.setHighlight(JSON.parseObject(
                "{\n" +
                        "    \"fields\": {\n" +
                        "        \"file_title\": {\n" +
                        "            \"number_of_fragments\": 5,\n" +
                        "            \"pre_tags\": \"<em>\",\n" +
                        "            \"post_tags\": \"</em>\",\n" +
                        "            \"fragment_size\": 200\n" +
                        "        },\n" +
                        "  \"file_title.keyword\": {\n" +
                        "            \"number_of_fragments\": 5,\n" +
                        "            \"pre_tags\": \"<em>\",\n" +
                        "            \"post_tags\": \"</em>\",\n" +
                        "            \"fragment_size\": 200\n" +
                        "        },\n" +
                        "        \"content\": {\n" +
                        "            \"number_of_fragments\": 5,\n" +
                        "            \"pre_tags\": \"<em>\",\n" +
                        "            \"post_tags\": \"</em>\",\n" +
                        "            \"fragment_size\": 300\n" +
                        "        }\n" +
                        "    }\n" +
                        "}"
        ));
    }

    public static void buildFileTitle(EmbeddingSearchRequest embeddingSearchRequest, String query) {

        String filterExtend = String.format("[\n" +
                "    {\n" +
                "        \"bool\": {\n" +
                "            \"should\": [\n" +
                "                {\n" +
                "                    \"match\": {\n" +
                "                        \"file_title\": {\n" +
                "                            \"query\": \"%s\",\n" +
                "                            \"operator\": \"and\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"wildcard\": {\n" +
                "                        \"file_title.keyword\": \"*%s*\"\n" +
                "                    }\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    }\n" +
                "]", query, query);
        embeddingSearchRequest.setFilterExtends(JSON.parseArray(filterExtend));
        embeddingSearchRequest.setHighlight(JSON.parseObject(
                "{\n" +
                        "    \"fields\": {\n" +
                        "        \"file_title\": {\n" +
                        "            \"number_of_fragments\": 5,\n" +
                        "            \"pre_tags\": \"<em>\",\n" +
                        "            \"post_tags\": \"</em>\",\n" +
                        "            \"fragment_size\": 200\n" +
                        "        },\n" +
                        "  \"file_title.keyword\": {\n" +
                        "            \"number_of_fragments\": 5,\n" +
                        "            \"pre_tags\": \"<em>\",\n" +
                        "            \"post_tags\": \"</em>\",\n" +
                        "            \"fragment_size\": 200\n" +
                        "        }\n" +
                        "    }\n" +
                        "}"
        ));
    }
}
