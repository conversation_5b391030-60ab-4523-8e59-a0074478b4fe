package com.linker.fusion.knowledgecenter.service.domain.retriever;

import com.alibaba.fastjson.JSON;
import com.linker.fusion.knowledgecenter.infrastructure.common.Constants;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.TerminologyTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.QueryParams;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class MetaFilterUtil {

    public static void buildFilter(StringBuilder filter, QueryParams extra, FileTypeEnum fileTypeEnum) {
        if (!ObjectUtils.isEmpty(extra.getTitleParams())) {
            for (QueryParams.TitleParam titleParam : extra.getTitleParams()) {
                if ("like".equalsIgnoreCase(titleParam.getOperator())) {
                    filter.append(" and (`file_title.keyword` like '").append(titleParam.getValue()).append("' or `file_title.keyword` like '%").append(titleParam.getValue()).append("%') ");
                }
                if ("=".equals(titleParam.getOperator())) {
                    filter.append(" and `file_title.keyword` = '").append(titleParam.getValue()).append("' ");
                }
            }
        }
        if (!ObjectUtils.isEmpty(extra.getMetaParams())) {
            for (QueryParams.MetaParam metaParam : extra.getMetaParams()) {
//                if (!fileTypeEnum.getType().equals(metaParam.getFileType())) {
//                    continue;
//                }
                String fieldName = metaParam.getField();
                Object value = metaParam.getValue();
                if (Objects.equals(metaParam.getFieldType(), 0) && StringUtils.isNotBlank(value.toString())) {
                    if ("=".equals(metaParam.getOperator())) {
                        filter.append(String.format(" and `meta.%s%s` = '%s' ", Constants.RESOURCE_EXT_INFO_SEARCH, fieldName, value));
                    } else if ("like".equals(metaParam.getOperator())) {
                        filter.append(String.format(" and `meta.%s%s` like '%s' ", Constants.RESOURCE_EXT_INFO_SEARCH, fieldName, value));
                        filter.append(" and (`meta.").append(Constants.RESOURCE_EXT_INFO_SEARCH).append(fieldName).append("` like '").append(value).append("' or `meta.").append(Constants.RESOURCE_EXT_INFO_SEARCH).append(fieldName).append("` like '%").append(value).append("%') ");
                    }
                }
                if ((Objects.equals(metaParam.getFieldType(), 1) || Objects.equals(metaParam.getFieldType(), 4)) && StringUtils.isNotBlank(value.toString())) {
                    filter.append(String.format(" and `meta.%s%s` = '%s' ", Constants.RESOURCE_EXT_INFO_SEARCH, fieldName, value));
                }
                if (Objects.equals(metaParam.getFieldType(), 2)) {
                    if (value instanceof Collection) {
                        List<String> timeList = JSON.parseArray(JSON.toJSONString(value), String.class);
                        if (timeList.size() == 2) {
                            long startTime = StringComUtils.convertToTimestamp(timeList.get(0));
                            long endTime = StringComUtils.convertToTimestamp(timeList.get(1));
                            filter.append(String.format(" and `meta.%s%s` between '%s' and '%s' ", Constants.RESOURCE_EXT_INFO_SEARCH, fieldName, startTime, endTime));
                        }
                    }
                }
                if (Objects.equals(metaParam.getFieldType(), 3) || Objects.equals(metaParam.getFieldType(), 5)) {
                    if (value instanceof Collection) {
                        List<String> valueList = JSON.parseArray(JSON.toJSONString(value), String.class);
                        for (String str : valueList) {
                            if (StringUtils.isBlank(str)) {
                                continue;
                            }
                            filter.append(String.format(" and `meta.%s%s.keyword` = '%s' ", Constants.RESOURCE_EXT_INFO_SEARCH, fieldName, str));
                        }
                    }
                }
            }
        }
        if (!ObjectUtils.isEmpty(extra.getVisionParams())) {
            for (QueryParams.VisionParam visionParam : extra.getVisionParams()) {
                if (StringUtils.isBlank(visionParam.getValue())) {
                    continue;
                }
                if ("1".equals(visionParam.getType())) {
                    if (StringUtils.isNotBlank(visionParam.getId())) {
                        filter.append(" and persons_labels = '").append(visionParam.getId()).append("' ");
                    }else {
                        filter.append(" and persons_labels = '").append(visionParam.getValue()).append("' ");
                    }
                }
                if ("2".equals(visionParam.getType())) {
                    filter.append(" and object_labels = '").append(visionParam.getValue()).append("' ");
                }
            }
        }
        if (!ObjectUtils.isEmpty(extra.getTerminologyParams())) {
            for (QueryParams.TerminologyParam terminologyParam : extra.getTerminologyParams()) {
                if (StringUtils.isBlank(terminologyParam.getValue())) {
                    continue;
                }
                TerminologyTypeEnum terminologyTypeEnum = TerminologyTypeEnum.fromCode(terminologyParam.getType());
                if (terminologyTypeEnum == null) {
                    continue;
                }
                filter.append(" and text_labels = '").append(terminologyTypeEnum.getValue()).append("_").append(terminologyParam.getValue()).append("' ");
            }
        }
        if (StringUtils.isNotBlank(extra.getCreateTimeGt())) {
            filter.append(" and create_time > '").append(extra.getCreateTimeGt()).append("' ");
        }
    }


    public static void builderKnowledgeFilter(List<Long> docGroupIds, List<String> docIds, StringBuilder filter) {
        if (CollectionUtils.isNotEmpty(docGroupIds) && !ObjectUtils.isEmpty(docIds)) {
            String groupIdsStr = docGroupIds.stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            String docIdsStr = docIds.stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append(String.format(" and (group_id in (%s) or doc_id in (%s)) ", groupIdsStr, docIdsStr));
        } else {
            if (CollectionUtils.isNotEmpty(docGroupIds)) {
                String str = docGroupIds.stream().map(t -> String.format("'%s'", t))
                        .collect(Collectors.joining(", "));
                String format = String.format(" and group_id in (%s) ", str);
                filter.append(format);
            }
            if (!ObjectUtils.isEmpty(docIds)) {
                String resourceIdsStr = docIds.stream().map(t -> String.format("'%s'", t))
                        .collect(Collectors.joining(", "));
                filter.append("and doc_id in (").append(resourceIdsStr).append(")");
            }
        }
    }
}
