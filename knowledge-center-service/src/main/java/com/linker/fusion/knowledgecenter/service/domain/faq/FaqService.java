package com.linker.fusion.knowledgecenter.service.domain.faq;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FaqInfoEntity;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqImportDTO;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqPageDTO;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqSaveDTO;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqSearchDTO;
import com.linker.fusion.knowledgecenter.service.domain.group.IGroupBaseService;

import java.util.List;

public interface FaqService extends IGroupBaseService {

    Long create(FaqSaveDTO faqSaveDTO);

    List<String> generateSimilarQuestions(String standardQuestion, List<String> similarQuestions);

    List<FaqInfoEntity> listByIds(String tenantId, List<Long> ids);

    FaqInfoEntity getById(Long id);

    FaqInfoEntity getByUid(String tenantId, String uid);

    Page<FaqInfoEntity> page(FaqPageDTO req);

    /**
     * 通过标准问题ID列表查询相似问题列表
     *
     * @param standardIds 标准问题ID列表
     * @return 有序的相似问题列表
     */
    List<FaqInfoEntity> listByStandardIds(List<Long> standardIds);

    void update(FaqSaveDTO faqSaveDTO);

    void enable(String tenantId, List<Long> ids, Boolean enable);

    void move(String tenantId, List<FaqInfoEntity> faqList, Long groupId);

    void delete(List<FaqInfoEntity> faqList);

    /**
     * 通过导入FAQ
     *
     * @param tenantId          租户ID
     * @param userId            用户ID
     * @param groupId           导入的组ID
     * @param faqInfoEntityList 导入的数据
     * @return 导入结果 成功-null 失败时返回重复的问题列表
     */
    FaqImportDTO importFaq(String tenantId, String userId, Long groupId, List<FaqInfoEntity> faqInfoEntityList);

    void batchImportFaq(String tenantId, String userId, Long groupId, List<FaqInfoEntity> faqInfoEntityList);

    FaqSearchDTO search(String tenantId, String question, List<Long> groupIds, boolean isLike, double threshold);

    List<String> searchByEs(String tenantId, String question, List<Long> groupIds, Integer limit);

    List<String> searchBySql(String tenantId, String question, List<Long> groupIds, Integer limit);

    List<FaqInfoEntity> needMerge();

    void fixFaq();

    Long getCount(String tenantId);

    void saveBatch(List<FaqInfoEntity> faqInfoEntities);

    List<FaqInfoEntity> listByGroupIds(List<Long> groupIds, Integer type);

    List<FaqSearchDTO> searchList(String tenantId, String question, List<Long> groupIds, Integer size, Double threshold);
}
