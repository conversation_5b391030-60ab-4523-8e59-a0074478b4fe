//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rn<PERSON><PERSON>er decompiler)
//

package com.linker.fusion.knowledgecenter.service.domain.retriever;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexSuffixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoFrameEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexTenantMappingManager;
import com.linker.fusion.knowledgecenter.service.domain.EmbeddingService;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.KnowledgeConfig;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.Meta;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.QueryParams;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.ResourceType;
import com.linker.omagent.core.model.embedding.EmbeddingModel;
import com.linker.omagent.core.rag.content.retriever.Content;
import com.linker.omagent.core.rag.content.retriever.ContentRetriever;
import com.linker.omagent.core.rag.query.Query;
import com.linker.omagent.core.repository.embedding.*;
import com.linker.omagent.core.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 帧数据检索器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class VideoFrameEmbeddingContentRetriever implements ContentRetriever {

    @Autowired
    private EmbeddingRepository embeddingStore;
    @Autowired
    private EmbeddingModel embeddingModel;
    @Autowired
    private IIndexTenantMappingManager indexTenantMappingManager;
    @Autowired
    EmbeddingService embeddingService;

    @Override
    public String type() {
        return "search";
    }

    private static String buildFilter(QueryParams extra, String type) {
        KnowledgeConfig knowledgeConfig = extra.getKnowledgeConfig();
        List<Long> docGroupIds = knowledgeConfig.getDocGroupIds();
        StringBuilder filter = new StringBuilder(String.format("enable = 1 and tenant_id = '%s'    ",
                extra.getTenantId()
        ));
        if (CollectionUtils.isNotEmpty(docGroupIds)) {
            String str = docGroupIds.stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            String format = String.format(" and  group_id in (%s)   ",
                    str
            );
            filter.append(format);
        }
        if (!ObjectUtils.isEmpty(extra.getDocIds())) {
            String resourceIdsStr = extra.getDocIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id in  (").append(resourceIdsStr).append(")");
        }
        if (!ObjectUtils.isEmpty(extra.getNotResIds())) {
            String resourceIdsStr = extra.getNotResIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id not in (").append(resourceIdsStr).append(")");
        }
        MetaFilterUtil.buildFilter(filter, extra, FileTypeEnum.VIDEO);
        return filter.toString();
    }


    @Override
    public String description() {
        return "存放的是视频帧数据";
    }


    @Override
    public List<Content> retrieve(Query query) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        if (!CollectionUtils.containsAny(extra.getFileTypes(), FileTypeEnum.VIDEO.getType())) {
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(extra.getPicUrl()) && StringUtils.isBlank(query.getInput())) {
            return Collections.emptyList();
        }
        long l = System.currentTimeMillis();
        EmbeddingSearchResult<VideoFrameEntity> searchResult;
        String type = ResourceType.FRAME.getType();
        String filter = buildFilter(extra, type);
        log.debug("检索条件:{}", filter);
        List<Double> vector = embeddingService.embedImageAndText(extra.getPicUrl(), query.getInput());
        String tenantId = extra.getTenantId();
        String index = indexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.VIDEO) + IndexSuffixEnum.FRAME.suffix;
        MatchQuery<Object> matchQuery = MatchQuery.builder().key("image_vector").value(new EmbeddingValue(vector
                , extra.getThreshold())).build();
        EmbeddingSearchRequest vectorSearch = EmbeddingSearchRequest
                .builder()
                .indexId(index)
                .matchQuery(matchQuery)
                .include(Lists.newArrayList("content", "segment_id", "doc_id", "frame_id", "url"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), 10))
                .filter(filter)
                .build();
        searchResult = this.embeddingStore.search(vectorSearch,
                new TypeReference<EmbeddingSearchResult<VideoFrameEntity>>() {
                });

        log.info("检索器：{},检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));
        return searchResult.matches().stream().map(hit -> {
            VideoFrameEntity c = hit.getDocument();
            Meta meta = new Meta();
            meta.setSegmentId(c.getSegmentId());
            meta.setDocId(c.getDocId());
            meta.setFrameId(c.getFrameId());
            meta.setFrameUrl(c.getUrl());
            meta.setScore(hit.getScore());
            meta.setNeedRerank(false);
            meta.setRetriever("video_frame_embedding");
            return new Content(replaceContent(c), type, meta);
        }).collect(Collectors.toList());
    }

    private String replaceContent(@NotNull VideoFrameEntity chunkEntity) {
        String sb = chunkEntity.getTitle() + "\n" +
//                chunkEntity.getContent() + "\n" +
                chunkEntity.getObjectLabels() + "\n" +
                chunkEntity.getPersonsLabels() + "\n" +
                chunkEntity.getTextLabels() + "\n";
        return sb.trim();
    }
}

