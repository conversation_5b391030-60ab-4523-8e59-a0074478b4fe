package com.linker.fusion.knowledgecenter.service.domain.img;

import com.linker.fusion.knowledgecenter.service.domain.img.model.FaceInfoDTO;
import com.linker.omagent.core.data.detection.OpenVocabularyDetectionOutput;

import java.io.IOException;
import java.util.List;

public interface ImgService {

    /**
     * 标识别后加大模型caption 自带 prompt model
     *
     * @param url
     * @return
     */
    String odMllmCaption(String url);

    /**
     * @param url
     * @param prompt
     * @param model
     * @param question
     * @return
     */
    String mllmCaption(String url, String prompt, String model, String question);

    /**
     * 大模型生成 caption
     *
     * @param url    图片地址
     * @param prompt
     * @param model
     * @return
     */
    String mllmCaption(String url, String prompt, String model);

    /**
     * 目标识别后加大模型caption
     *
     * @param url
     * @param prompt
     * @param model
     * @return
     */
    String odMllmCaption(String url, String prompt, String model);

    String odMllmCaption(String url, String prompt, String model, String question);

    /**
     * 目标识别合成图片
     */
    String odMux(String image);

    /**
     * 通过人脸信息和目标信息给图片画框
     *
     * @param url         图片URL
     * @param faceInfos   人脸信息
     * @param objectInfos 目标信息
     */
    String imageDrawRect(String url, List<FaceInfoDTO> faceInfos, List<OpenVocabularyDetectionOutput.OvdData> objectInfos, int maxSideLength, int maxFileSizeKB) throws Exception;

    /**
     * 合并图片（JPG格式）
     *
     * @param imageUrls 图片URL列表
     * @param imageUrls
     * @return 合并后的图片URL列表
     * @return
     * @throws IOException
     */
    List<String> mergeImages(List<String> imageUrls);
}
