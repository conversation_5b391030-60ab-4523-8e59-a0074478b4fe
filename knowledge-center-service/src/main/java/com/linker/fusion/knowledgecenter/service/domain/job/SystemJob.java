package com.linker.fusion.knowledgecenter.service.domain.job;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.AasWorkflowClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowResultResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowStatus;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.core.BaseClientResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.UserSearchByIdsReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.UserInfoResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IUserCenter;
import com.linker.fusion.knowledgecenter.infrastructure.common.UserMapList;
import com.linker.fusion.knowledgecenter.infrastructure.config.AasWorkflowProperties;
import com.linker.fusion.knowledgecenter.infrastructure.config.AutoLearnConfig;
import com.linker.fusion.knowledgecenter.infrastructure.config.AutoLearnItem;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FeedbackEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FeedbackSettingEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceExtEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FeedbackLearnStatusEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ResourceExtTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IFeedbackManager;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IKnowledgeResourceManager;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.service.domain.feedback.FeedbackService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceExtService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceFaceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ResourceUtilService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.common.Constants.RESOURCE_STUDY_STATUS_KEY_PREFIX;

@Slf4j
@Component
public class SystemJob {

    @Autowired
    private IKnowledgeResourceManager iKnowledgeResourceManager;
    @Autowired
    private IFeedbackManager iFeedbackManager;
    @Autowired
    private IUserCenter userCenter;
    @Resource
    private MigrateGroupService migrateGroupService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private FeedbackService feedbackService;
    @Resource
    private IResourceExtService iResourceExtService;
    @Resource
    private ResourceUtilService resourceUtilService;
    @Resource
    private IResourceService iResourceService;
    @Resource
    RedisTemplate redisTemplate;
    @Resource
    AasWorkflowClient aasWorkflowClient;
    @Resource
    private AasWorkflowClient aaasWorkflowClient;
    @Resource
    private AasWorkflowProperties aasWorkflowProperties;

    @Resource
    private IResourceFaceService iResourceFaceService;

    private final AtomicBoolean hasRun = new AtomicBoolean(false);

//    @Value("${auto-learn-size:5}")
//    private Integer autoLearnSize;
//
//    @Value("${auto-learn-type:3}")
//    private Integer autoLearnType;

    @Resource
    private AutoLearnConfig autoLearnConfig;

    /**
     * 服务启动后应该立刻同步一次数据
     */
    public void syncOnServiceStartup() {
        log.info("服务启动完毕，将执账号到缓存");
        List<String> allUids = iKnowledgeResourceManager.getAllUserId();
        allUids.addAll(iFeedbackManager.getAllUserId());
        BaseClientResp<List<UserInfoResp>> userByCode = userCenter.getUserByCode(new UserSearchByIdsReq(allUids.stream().distinct().collect(Collectors.toList())));
        UserMapList.setList(userByCode.getData());
    }

    @Scheduled(initialDelay = 60, fixedRate = 1, timeUnit = TimeUnit.SECONDS)
    public void sync() {
        if (UserMapList.isTouch) {
            syncOnServiceStartup();
        }
    }

    @Scheduled(initialDelay = 60, fixedDelay = Integer.MAX_VALUE, timeUnit = TimeUnit.SECONDS)
    public void migrateGroup() {
        if (hasRun.compareAndSet(false, true)) {
            RLock lock = redissonClient.getLock("knowledge-center-group-migrate-lock");
            try {
                if (lock.tryLock(0, 30, TimeUnit.SECONDS)) {
                    log.info("获取锁成功");
                    try {
                        migrateGroupService.migrateFaq();
                        migrateGroupService.migrateTable();
                        migrateGroupService.migrateDoc();
                    } finally {
                        lock.unlock();
                    }
                } else {
                    log.info("获取锁失败");
                }
            } catch (InterruptedException e) {
                log.error("获取锁失败", e);
            }
        }

    }

    /**
     * 反馈学习
     */
    @XxlJob("feedbackLearn")
    public void feedbackLearn() {
        RLock lock = redissonClient.getLock("knowledge-center-feedback-lock");
        try {
            if (lock.tryLock(0, 30, TimeUnit.SECONDS)) {
                log.info("反馈学习获取锁成功");
                try {
                    List<FeedbackSettingEntity> allSetting = feedbackService.getAllSetting();
                    Set<Long> agentIds = allSetting.stream().filter(FeedbackSettingEntity::getLearnEnable).map(FeedbackSettingEntity::getAgentId).collect(Collectors.toSet());
                    for (Long agentId : agentIds) {
                        List<FeedbackEntity> feedbackEntityList = feedbackService.listByAgentIdAndLearnStatus(agentId, FeedbackLearnStatusEnum.INIT);
                        if (CollectionUtils.isNotEmpty(feedbackEntityList)) {
                            for (FeedbackEntity feedbackEntity : feedbackEntityList) {
                                feedbackService.learn(feedbackEntity);
                            }
                        }
                    }
                } finally {
                    lock.unlock();
                }
            } else {
                log.info("反馈学习获取锁失败");
            }
        } catch (InterruptedException e) {
            log.error("反馈学习获取锁失败", e);
        }
    }

    /**
     * 清理过期文件
     */
    @Scheduled(initialDelay = 1, fixedRate = 5, timeUnit = TimeUnit.HOURS)
    public void clearExpired() {
        try {
            List<ResourceExtEntity> resourceExtList = iResourceExtService.listLt(ResourceExtTypeEnum.Expired, StringComUtils.convertStr(LocalDateTime.now()));
            List<String> docIds = resourceExtList.stream().map(ResourceExtEntity::getDocId).collect(Collectors.toList());
            List<KnowledgeResourceEntity> resourceList = iResourceService.listByResIds(docIds);
            resourceUtilService.delete(resourceList);
        } catch (Exception e) {
            log.error("过期文件清理异常", e);
        }
    }

    @Scheduled(initialDelay = 30, fixedDelay = 30, timeUnit = TimeUnit.SECONDS)
    public void queryWorkflowStatus() {
        try {
            List<String> workflowIds = redisTemplate.opsForList().range("quickResList", 0, -1);
            for (String workflowId : workflowIds) {
                BaseResp<WorkflowResultResp> resp = aasWorkflowClient.results(workflowId);
                if (Objects.nonNull(resp) && Objects.nonNull(resp.getData())) {
                    if (!WorkflowStatus.RUNNING.equals(resp.getData().getStatus())) {
                        iResourceService.updateByWorkflowId(workflowId, WorkflowStatus.COMPLETED.equals(resp.getData().getStatus()) ? ProcessEnum.Success : ProcessEnum.Fail);
                        redisTemplate.opsForList().remove("quickResList", 0, workflowId);
                    }
                }


            }
        } catch (Exception e) {
            log.error("任务执行异常", e);
        }


    }
    @Scheduled(initialDelay = 30, fixedDelay = 30, timeUnit = TimeUnit.SECONDS)
    public void updateFaceStatus() {
        try {
            List<String> idAndNameList = redisTemplate.opsForList().range("updateFaceList", 0, -1);
            for (String idAndName : idAndNameList) {
                String[] split = idAndName.split("_");
                String id = split[0];
                String name = split[1];
                iResourceFaceService.updateName(id, name);
                redisTemplate.opsForList().remove("updateFaceList", 0, idAndName);
            }
        } catch (Exception e) {
            log.error("任务执行异常", e);
        }


    }









    @XxlJob("autoLearn")
    public void autoLearn() {
        long startTime = System.currentTimeMillis();
        log.info("开始自动学习:{}", JSON.toJSONString(autoLearnConfig));
        for (AutoLearnItem autoLearnItem : autoLearnConfig.getAutoLearnItems()) {
            log.info("处理自动学习项: {}", JSON.toJSONString(autoLearnItem));
            if (autoLearnItem.getEnable()) {
                runTask(autoLearnItem);
            }
        }
        log.info("自动学习完成，耗时：{}ms", System.currentTimeMillis() - startTime);
    }

    private void runTask(AutoLearnItem autoLearnItem) {
        Integer taskSize = autoLearnItem.getSize();
        List<KnowledgeResourceEntity> running = iKnowledgeResourceManager.top("", null, ProcessEnum.Executing, FileTypeEnum.valueOf(autoLearnItem.getType()), taskSize);
        log.info("学习中数量：" + running.size());
        if (running.size() >= taskSize)
            return;
        List<KnowledgeResourceEntity> fails = iKnowledgeResourceManager.top("", null, ProcessEnum.Fail, FileTypeEnum.valueOf(autoLearnItem.getType()), taskSize);
        log.info("失败数量：" + fails.size());
        int size = running.size();
        for (KnowledgeResourceEntity resource : fails) {
            if (Objects.nonNull(resource.getHandleFailReason())
                    && StringUtils.isNotBlank(resource.getHandleFailReason().getMessage())
                    &&
                    (CollectionUtils.isNotEmpty(autoLearnItem.getSkipMsgs())
                            &&
                            autoLearnItem.getSkipMsgs().stream().anyMatch(x -> resource.getHandleFailReason().getMessage().contains(x)))
            ) {
                iKnowledgeResourceManager.logicalDelete(Collections.singletonList(resource.getId()));
                continue;
            }
            handResource(resource, autoLearnItem.getWorkflowId());
            size = size + 1;
            if (size >= taskSize)
                break;
        }
    }

    private void handResource(KnowledgeResourceEntity resource, String workflowId) {
        try {
//            String workflowId = "";
//            switch (FileTypeEnum.valueOf(resource.getType())) {
//                case DOCUMENT:
//                    workflowId = "";
//                    break;
//                case VIDEO:
//                    workflowId = XxlJobHelper.getJobParam();
//                    break;
//                case AUDIO:
//                    workflowId = "";
//                    break;
//                case IMAGE:
//                    workflowId = "";
//                    break;
//                default:
//                    break;
//            }
            Map<String, Object> inputs = new HashedMap<>();
            inputs.put("fileId", resource.getDocId());
            WorkflowAsyncRunResp workflowAsyncRunResp = aaasWorkflowClient.runAsync(
                    new WorkflowAsyncRunReq()
                            .setId(workflowId)
                            .setInput(inputs)
                            .setCallback(aasWorkflowProperties.getWorkflowId().getImportCallback() + "/" + resource.getDocId())
            );
            if (workflowAsyncRunResp == null || !Objects.equals("0", workflowAsyncRunResp.getCode())) {
                log.error("下发工作流任务失败{}", JSON.toJSONString(workflowAsyncRunResp));
                throw new RuntimeException("下发工作流任务失败");
            }
            String workflowInstanceId = workflowAsyncRunResp.getData();
            log.info("文件导入下发工作流任务成功：docId:{}, workflowInstanceId:{}", resource.getDocId(), workflowInstanceId);
            resource.setWorkflowId(workflowInstanceId);
            resource.setHandleStatus(ProcessEnum.Executing.getValue());
        } catch (Exception ex) {
            log.error("下发工作流任务失败", ex);
            resource.setHandleStatus(ProcessEnum.Fail.getValue());
        }
        iKnowledgeResourceManager.updateById(resource);
        redissonClient.getBucket(RESOURCE_STUDY_STATUS_KEY_PREFIX + resource.getDocId()).delete();
    }

    /**
     * 查询三天前还在执行中的resource，将状态改成失败
     */
    @Scheduled(initialDelay = 1, fixedDelay = 12, timeUnit = TimeUnit.HOURS)
    public void handleExpireTask() {
        try {
            LocalDateTime endTime = LocalDateTime.now().minusDays(3);
            LocalDateTime startTime = endTime.minusDays(3);

            //通过状态
            ArrayList<Integer> statusList = Lists.newArrayList(ProcessEnum.QUEUED.getValue()
                    , ProcessEnum.PREPROCESSING.getValue(), ProcessEnum.Executing.getValue());
            List<KnowledgeResourceEntity> knowledgeResourceEntities =
                    iResourceService.getFileFromTimeRange(statusList, startTime, endTime);
            if (CollectionUtils.isNotEmpty(knowledgeResourceEntities)) {
                log.info("开始清理数据：{},docid:{}", knowledgeResourceEntities.size(), JSON.toJSONString(knowledgeResourceEntities
                        .stream().map(c -> c.getDocId()).collect(Collectors.toList())));
                knowledgeResourceEntities.forEach(
                        resource -> {
                            resource.setHandleStatus(ProcessEnum.Fail.getValue());
                            resource.setUpdateTime(LocalDateTime.now());
                            resource.setHandleFailReason(new KnowledgeResourceEntity.HandleFailReason(0, "文件处理超时"));
                            iKnowledgeResourceManager.updateById(resource);
                        }
                );
            }
        } catch (Exception e) {
            log.error("任务执行异常", e);
        }


    }



}
