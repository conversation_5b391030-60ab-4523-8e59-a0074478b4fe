package com.linker.fusion.knowledgecenter.service.domain.resource.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.AasWorkflowClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncCancelReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunResp;
import com.linker.fusion.knowledgecenter.infrastructure.config.AasWorkflowProperties;
import com.linker.fusion.knowledgecenter.infrastructure.config.EventConfig;
import com.linker.fusion.knowledgecenter.infrastructure.config.PromptMappingConfig;
import com.linker.fusion.knowledgecenter.infrastructure.dto.KnowledgeResourceDTO;
import com.linker.fusion.knowledgecenter.infrastructure.dto.data.PromptInfoDTO;
import com.linker.fusion.knowledgecenter.infrastructure.dto.data.VariableDTO;
import com.linker.fusion.knowledgecenter.infrastructure.entity.*;
import com.linker.fusion.knowledgecenter.infrastructure.enums.*;
import com.linker.fusion.knowledgecenter.infrastructure.es.ChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoFrameEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.*;
import com.linker.fusion.knowledgecenter.infrastructure.model.StrategyModelBasic;
import com.linker.fusion.knowledgecenter.infrastructure.model.TabDisplayUseCustomer;
import com.linker.fusion.knowledgecenter.service.domain.PromptService;
import com.linker.fusion.knowledgecenter.service.domain.common.IUtilService;
import com.linker.fusion.knowledgecenter.service.domain.common.ImageContentBuilder;
import com.linker.fusion.knowledgecenter.service.domain.custom.CustomService;
import com.linker.fusion.knowledgecenter.service.domain.group.IGroupBaseService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.oprationlog.OperationLogProxyService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IAudioChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IDocChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceExtService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceFaceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceSegmentService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IVideoFrameIBaseService;
import com.linker.fusion.knowledgecenter.service.domain.strategy.TemplateService;
import com.linker.omagent.core.data.input.Prompt;
import com.linker.omagent.core.data.input.PromptTemplate;
import com.linker.omagent.core.data.message.*;
import com.linker.omagent.core.data.output.Response;
import com.linker.omagent.core.repository.embedding.SearchHit;
import com.linker.omagent.omos.model.OmChatLanguageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class ResourceUtilService implements IGroupBaseService {
    @Resource
    private KnowledgeGroupService knowledgeGroupService;
    @Resource
    private IResourceService iResourceService;
    @Resource
    private IResourceExtService iResourceExtService;
    @Resource
    private IResourceFaceService iResourceFaceService;
    @Resource
    private TemplateService templateService;
    @Resource
    private CustomService customService;
    @Resource
    private IResourceSegmentService iResourceSegmentService;
    @Resource
    private AasWorkflowProperties aasWorkflowProperties;
    @Resource
    private IDocChunkIBaseServiceResource iChunkIBaseService;
    @Autowired
    private PromptMappingConfig promptMappingConfig;


    @Resource
    private OmChatLanguageModel llmModel;

    @Resource
    private EventConfig eventConfig;
    @Resource
    private AasWorkflowClient aaasWorkflowClient;
    @Resource
    private ChunkServiceFactory chunkServiceFactory;
    @Resource
    private IUtilService iUtilService;

    @Resource
    private IResourceTerminologyManager iResourceTerminologyManager;
    @Resource
    private IResourceFaceManager iResourceFaceManager;
    @Resource
    private IResourceODTagManager iResourceODTagManager;
    @Resource
    private IResourceASRManager iResourceASRManager;

    @Resource
    private IVideoFrameIBaseService iVideoFrameIBaseService;

    @Autowired
    OperationLogProxyService operationLogProxyService;
    @Autowired
    private IAudioChunkIBaseServiceResource iAudioChunkIBaseServiceResource;

    @Resource
    private ISubVideoEntityManager iSubVideoEntityManager;

    @Resource
    private ImageContentBuilder imageContentBuilder;

    @Resource
    private PromptService promptService;


    public void move(String tenantId, List<String> docIds, Long groupId) {
        String groupPath = knowledgeGroupService.getGroupPath(tenantId, groupId);
        List<SearchHit<ChunkEntity>> chunks = iChunkIBaseService.list(tenantId, docIds.stream().map(String::valueOf).collect(Collectors.toList()));
        List<String> uidList = chunks.stream().map(SearchHit::getDocument).map(ChunkEntity::getUid).collect(Collectors.toList());
        List<ChunkEntity> updateList = new ArrayList<>();
        for (String uid : uidList) {
            ChunkEntity updateEntity = new ChunkEntity();
            updateEntity.setUid(uid);
            updateEntity.setPath(groupPath);
            updateList.add(updateEntity);
        }
        iChunkIBaseService.batchUpdate(tenantId, updateList);
        iResourceService.updateGroupIdByDocIds(docIds, groupId);
    }

    public List<String> eventCheck(String image, List<String> events) {
        if (CollectionUtils.isEmpty(events)) {
            return Collections.emptyList();
        }
        // 启用的事件
        List<EventConfig.Event> list = eventConfig.getItems().stream().filter(t -> events.contains(t.getName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> matchEvents = new ArrayList<>();

        List<ChatMessage> messageList = new ArrayList<>();
        List<Content> contents = new ArrayList<>();
        contents.add(new TextContent(promptMappingConfig.getEventCheck().replaceAll("\\{\\{events}}", list.stream().map(t -> String.format("'%s'", t.getDesc())).collect(Collectors.joining(", ")))));
//        contents.add(new ImageContent(ImageUtils.imageBase64(image), MimeTypeUtils.IMAGE_JPEG_VALUE, ImageContent.DetailLevel.AUTO));
        contents.add(imageContentBuilder.build(image));
        messageList.add(UserMessage.from(contents));
        ChatModelConfig config = new ChatModelConfig();
        config.setTemperature(0.1);
        config.setModelName(eventConfig.getModel());
        Response<AiMessage> response = llmModel.generate(config, messageList);

        String text = response.content().text();
        text = text.replaceAll("```", StringUtils.EMPTY).replaceAll("json", StringUtils.EMPTY);
        log.info("事件结果：{}", text);
        try {
            JSONArray jsonArray = JSON.parseArray(text);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if (jsonObject.containsKey("res") && jsonObject.getString("res").equals("yes")) {
                    if (i < list.size()) {
                        matchEvents.add(list.get(i).getName());
                    }
                }
            }
        } catch (Exception ex) {
            log.error("json格式出错：{}", ex.getMessage(), ex);
        }
        return matchEvents;

    }


    public static void main(String[] args) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("num_frame", 1 + "");
        variables.put("desc", String.join("/n", Lists.newArrayList("1", "2")));
        variables.put("question", "$ssd");
        Prompt prompt = PromptTemplate.from("你是视频巡检专家，视频总共有{{num_frame}}帧，以下是特定帧中检测到的对象信息:{{desc}}，根据所给信息,回答问题。问题：{{question}}。\n").apply(variables);
        System.out.println(prompt.text());
    }

    /**
     * 新的事件总结
     *
     * @param allEventRet 分段列表
     */
    public void eventSummary(KnowledgeResourceEntity resource, List<String> allEventRet, String question, com.alibaba.fastjson.JSONObject jsonLog) {
        if (StringUtils.isEmpty(question)) {
            return;
        }
        String summaryRet = "当前视频片段中无异常行为或特定事件发生。";
        if (!allEventRet.isEmpty()) {
            PromptInfoDTO promptInfoDTO = new PromptInfoDTO();
            promptInfoDTO.setPromptKey("event_summary");
            promptInfoDTO.setReqs(Arrays.asList(
                    new VariableDTO("num_frame", allEventRet.size() + ""),
                    new VariableDTO("desc", String.join("/n", allEventRet)),
                    new VariableDTO("question", question)
            ));
            try {
                String ret = promptService.runNewPrompt(resource.getDocId(), promptInfoDTO, eventConfig.getModel());
                jsonLog.put("summary", ret);
                if (StringUtils.isNotBlank(ret))
                    summaryRet = ret;
            } catch (Exception ex) {
                jsonLog.put("summary_error", ex.getMessage());
                log.error("事件总结失败：{}", ex.getMessage(), ex);
            }
        }
        iResourceExtService.save(resource, ResourceExtTypeEnum.K2Result, summaryRet);
    }


    /**
     * 处理文件资源
     *
     * @param resource
     * @param templateId
     * @param strategy
     */

    public void handResource(KnowledgeResourceEntity resource, Long templateId, String strategy) {
        if (Objects.isNull(resource) || Objects.isNull(resource.getId())) {
            return;
        }
        TemplateEntity templateEntity = null;
        // 如果使用了模版ID，优先使用模版配置的策略
        if (Objects.nonNull(templateId)) {
            templateEntity = templateService.getById(templateId);
        }
        if ((StringUtils.isBlank(strategy) || "{}".equals(strategy)) && (Objects.isNull(templateEntity) || templateEntity.getDeleted() || !templateEntity.getEnable())) {
            templateEntity = templateService.getDefault(resource.getTenantId(), resource.getType());
        }
        if (Objects.nonNull(templateEntity) && Objects.equals(resource.getTenantId(), templateEntity.getTenantId())) {
            strategy = templateEntity.getStrategy();
        }
        resource.setTemplateId(templateId);
        resource.setStrategy(strategy);
        StrategyModelBasic strategyModel = iResourceService.formatStrategy(resource.getType(), strategy);
        Integer nextStatus;
        Integer failedStatus = ProcessEnum.Fail.getValue();
        try {
            String workflowId = "";
            switch (FileTypeEnum.valueOf(resource.getType())) {
                case DOCUMENT:
                    workflowId = aasWorkflowProperties.getWorkflowId().getImportDoc();
                    break;
                case VIDEO:
                    workflowId = aasWorkflowProperties.getWorkflowId().getImportVideo();
                    break;
                case AUDIO:
                    workflowId = aasWorkflowProperties.getWorkflowId().getImportAudio();
                    break;
                case IMAGE:
                    workflowId = aasWorkflowProperties.getWorkflowId().getImportImage();
                    break;
                default:
                    break;
            }

            WorkflowAsyncRunResp workflowAsyncRunResp = null;
            Map<String, Object> inputs = new HashedMap<>();
            inputs.put("docId", resource.getDocId());
            inputs.put("strategy", strategyModel);
            inputs.put("resource", KnowledgeResourceDTO.convertFromKnowledgeResourceEntity(resource));

            if (ProcessEnum.MERGE_FAILED.getValue().equals(resource.getHandleStatus()) || (resource.isMerge() && StringUtils.isBlank(resource.getUrl()))) {
                nextStatus = ProcessEnum.MERGING.getValue();
                failedStatus = ProcessEnum.MERGE_FAILED.getValue();
                List<String> subVideoUrlList = iSubVideoEntityManager.listByDocId(resource.getDocId()).stream().map(SubVideoEntity::getUrl).collect(Collectors.toList());
                inputs.put("subVideoUrlList", subVideoUrlList);
                workflowAsyncRunResp = aaasWorkflowClient.runAsync(
                        new WorkflowAsyncRunReq()
                                .setId(aasWorkflowProperties.getWorkflowId().getVideoMerge())
                                .setInput(inputs)
                                .setCallback(aasWorkflowProperties.getWorkflowId().getImportCallback() + "/" + resource.getDocId())
                );
            } else {
                nextStatus = ProcessEnum.QUEUED.getValue();
                // 按需添加
                inputs.put("tenantId", resource.getTenantId());
                inputs.put("type", resource.getType());
                inputs.put("groupId", resource.getGroupId());
                inputs.put("groupPath", resource.getGroupPath());
                inputs.put("title", resource.getTitle());
                inputs.put("subheading", resource.getSubheading());
                inputs.put("suffix", resource.getSuffix().toLowerCase());
                inputs.put("description", resource.getDescription());
                inputs.put("url", resource.getUrl());
                inputs.put("cleanWatermarkFlag", resource.getCleanWatermarkFlag());
                inputs.put("size", resource.getSize());
                inputs.put("count", resource.getCount());
                inputs.put("extInfo", resource.getExtInfo());
                inputs.put("startTimestamp", System.currentTimeMillis());
                iResourceService.updateById(resource);
                workflowAsyncRunResp = aaasWorkflowClient.runAsync(
                        new WorkflowAsyncRunReq()
                                .setId(workflowId)
                                .setInput(inputs)
                                .setCallback(aasWorkflowProperties.getWorkflowId().getImportCallback() + "/" + resource.getDocId())
                );
            }
            if (workflowAsyncRunResp == null || !Objects.equals("0", workflowAsyncRunResp.getCode())) {
                log.error("下发工作流任务失败{}", com.alibaba.fastjson.JSON.toJSONString(workflowAsyncRunResp));
                throw new RuntimeException("下发工作流任务失败");
            }
            String workflowInstanceId = workflowAsyncRunResp.getData();
            log.info("文件导入下发工作流任务成功：docId:{}, workflowInstanceId:{}", resource.getDocId(), workflowInstanceId);
            resource.setWorkflowId(workflowInstanceId);
            resource.setHandleStatus(nextStatus);
        } catch (Exception ex) {
            log.error("下发工作流任务失败", ex);
            resource.setHandleStatus(failedStatus);
        }
        iResourceService.updateById(resource);
    }

    /**
     * 查询并转换为显示实体
     *
     * @param resIds
     * @return
     */
    public List<KnowledgeResourceEntity> listDTOByResIds(List<String> resIds) {
        List<KnowledgeResourceEntity> result = iResourceService.listByResIds(resIds);
        return updateTabDisplayUseCustom(result);
    }

    /**
     * 设置显示的自定义属性
     *
     * @param list
     * @return
     */
    public List<KnowledgeResourceEntity> updateTabDisplayUseCustom(List<KnowledgeResourceEntity> list) {
        for (KnowledgeResourceEntity res : list) {
            List<CustomEntity> customEntities = customService.tabDisplayUseList(res.getTenantId(), res.getType());
            List<TabDisplayUseCustomer> tabDisplayUseCustomers = new ArrayList<>();
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(res.getExtInfo());
            customEntities.forEach(x -> {
                String value = jsonObject.getString("customer_" + x.getField());
                TabDisplayUseCustomer tabDisplayUseCustomer = new TabDisplayUseCustomer();
                tabDisplayUseCustomer.fillField(x, value);

                tabDisplayUseCustomers.add(tabDisplayUseCustomer);
            });
            res.setTabDisplayUseCustomers(tabDisplayUseCustomers);
        }
        return list;
    }

    /**
     * 删除资源
     *
     * @param fileList
     */
    public void delete(List<KnowledgeResourceEntity> fileList) {
        delete(fileList, true);
    }

    /**
     * 删除资源
     *
     * @param fileList
     */
    public void delete(List<KnowledgeResourceEntity> fileList, boolean deleteSubVideo) {
        if (CollectionUtils.isEmpty(fileList))
            return;
        List<String> docIds = fileList.stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList());
        iResourceService.deleteByIds(fileList.stream().map(BaseEntity::getId).collect(Collectors.toList()));
        iResourceExtService.deleteByResIds(docIds);
        fileList.forEach(file -> {
            try {
                //记录删除的操作日志
                operationLogProxyService.deleteFilelogRecord(file);
                //取消学习
                cancelLearning(file);
                iUtilService.deleteFile(file.getUrl());
                iUtilService.deleteFile(file.getThumbnail());
                if (!StringUtils.equals(file.getUrl(), file.getPreviewSrc())) {
                    iUtilService.deleteFile(file.getPreviewSrc());
                }
                iResourceService.getAttachUrls(file).forEach(url -> iUtilService.deleteFile(url));

                if (deleteSubVideo) {
                    iSubVideoEntityManager.listByDocId(file.getDocId()).forEach(subVideoEntity -> {
                        iUtilService.deleteFile(subVideoEntity.getUrl());
                        iUtilService.deleteFile(subVideoEntity.getThumbnail());
                    });
                    iSubVideoEntityManager.deleteByDocIds(Collections.singletonList(file.getDocId()));
                }
            } catch (Exception ex) {
                log.error("文件关联信息删除失败：{}\n{}", file.getDocId(), ex.getMessage(), ex);
            }
        });
        List<ResourceSegmentEntity> segmentList = iResourceSegmentService.listByDocIds(docIds);
        deleteSegment(segmentList);
        fileList.stream().collect(Collectors.groupingBy(KnowledgeResourceEntity::getType))
                .forEach((fileType, subFileList) -> {
                    List<String> fileIds = subFileList.stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList());
                    switch (FileTypeEnum.valueOf(fileType)) {
                        case DOCUMENT:
                            break;
                        case VIDEO:
                            iResourceODTagManager.clearByDocIds(fileIds);
                            iResourceFaceManager.clearByDocIds(fileIds);
                            List<SearchHit<VideoFrameEntity>> frames = iVideoFrameIBaseService.list(subFileList.get(0).getTenantId(), fileIds);
                            for (SearchHit<VideoFrameEntity> frame : frames) {
                                iUtilService.deleteFile(frame.getDocument().getUrl());
                            }

                            iVideoFrameIBaseService.clearByFileIds(subFileList.get(0).getTenantId(), fileIds);
                            iResourceASRManager.clearByFileIds(fileIds);
                            iAudioChunkIBaseServiceResource.deleteByResIds(subFileList.get(0).getTenantId(), fileIds);
                            iResourceTerminologyManager.clearByFileIds(fileIds);

                            break;
                        case AUDIO:
                            iResourceTerminologyManager.clearByFileIds(fileIds);
                            iResourceASRManager.clearByFileIds(fileIds);
                            iAudioChunkIBaseServiceResource.deleteByResIds(subFileList.get(0).getTenantId(), fileIds);
                            break;
                        case IMAGE:
                            iResourceFaceManager.clearByDocIds(fileIds);
                            iResourceODTagManager.clearByDocIds(fileIds);
                            break;
                    }
                });

    }

    @Override
    public KnowledgeTypeEnum getType() {
        return KnowledgeTypeEnum.FILE;
    }

    @Override
    public void deleteByGroupIds(List<Long> groupIds) {
        List<KnowledgeResourceEntity> fileList = iResourceService.listByGroupIds(groupIds);
        delete(fileList);
    }

    @Override
    public List<Long> listGroupIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return iResourceService.listByIds(ids).stream().map(KnowledgeResourceEntity::getGroupId).collect(Collectors.toList());
    }

    /**
     * 取消学习
     *
     * @param file
     */
    public void cancelLearning(KnowledgeResourceEntity file) {
        if (!ProcessEnum.Executing.getValue().equals(file.getHandleStatus()) && !ProcessEnum.MERGING.getValue().equals(file.getHandleStatus()))
            return;
        try {
            BaseResp<Object> baseResp = aaasWorkflowClient.cancelAsync(new WorkflowAsyncCancelReq(file.getWorkflowId()));
            log.info("取消工作流结果 {}", com.alibaba.fastjson.JSON.toJSONString(baseResp));
        } catch (Exception ex) {
            log.warn("工作流未能取消 {}", ex.getMessage(), ex);
        }
    }

    /**
     * 删除分段
     *
     * @param segment
     */
    public void deleteSegment(ResourceSegmentEntity segment) {
        log.info("segment-delete-start");
        iResourceService.replaceAndDelete(segment.getTenantId(), segment.getDocId(), segment.getContent(), "", StoragePathEnum.Segment);
        log.info("segment-delete-img-end");
        iResourceSegmentService.deleteBySegmentId(segment.getSegmentId());
        log.info("segment-delete-delete-end");
        chunkServiceFactory.deleteBySegment(segment);
        log.info("segment-delete-end");
    }

    /**
     * 删除分段
     *
     * @param segments
     */
    public void deleteSegment(List<ResourceSegmentEntity> segments) {
        if (CollectionUtils.isEmpty(segments))
            return;
        for (ResourceSegmentEntity segment : segments)
            iResourceService.replaceAndDelete(segment.getTenantId(), segment.getDocId(), segment.getContent(), "", StoragePathEnum.Segment);
        iResourceSegmentService.deleteBySegmentIds(segments.stream().map(s -> s.getSegmentId()).collect(Collectors.toList()));
        chunkServiceFactory.deleteBySegments(segments);
    }
}
