package com.linker.fusion.knowledgecenter.service.domain.common;



import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.LockSupport;

/**
 * <AUTHOR>
 * @date 2024年10月31日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
public abstract class ResourceIndexingThread implements Runnable{

    /**
     * 阻塞等待
     */
    protected void waitTask() {
        LockSupport.park();
    }

    /**
     * 等待释放
     */
    protected void signalTask() {
        LockSupport.unpark(this.thread);
    }


    public ResourceIndexingThread() {
    }

    /**
     * 等待时间
     */
    private static final long JOIN_TIME = 90000L;

    /**
     * 线程
     */
    protected Thread thread;

    protected final CountDownLatch2 waitPoint = new CountDownLatch2(1);

    /**
     * 是否notify
     */
    protected volatile AtomicBoolean hasNotified = new AtomicBoolean(false);

    /**
     * 线程是否停止
     */
    protected volatile boolean stopped = false;

    /**
     * 是否后台线程
     */
    protected boolean isDaemon = false;

    /**
     * 是否开始
     */
    private final AtomicBoolean started = new AtomicBoolean(false);


    public abstract String getServiceName();

    /**
     * 开启
     */
    public void start() {
        log.info("Try to start service thread:{} started:{} lastThread:{}", this.getServiceName(), this.started.get(), this.thread);
        if (this.started.compareAndSet(false, true)) {
            this.stopped = false;
            this.thread = new Thread(this, this.getServiceName());
            this.thread.setDaemon(this.isDaemon);
            this.thread.start();
        }
    }

    /**
     * 关闭
     */
    public void shutdown() {
        this.shutdown(false);
    }

    /**
     * 关闭
     *
     * @param interrupt
     */
    public void shutdown(boolean interrupt) {
        log.info("Try to shutdown service thread:{} started:{} lastThread:{}", this.getServiceName(), this.started.get(), this.thread);
        if (this.started.compareAndSet(true, false)) {
            this.stopped = true;
            log.info("shutdown thread " + this.getServiceName() + " interrupt " + interrupt);
            if (this.hasNotified.compareAndSet(false, true)) {
                this.waitPoint.countDown();
            }

            try {
                if (interrupt) {
                    this.thread.interrupt();
                }
                long beginTime = System.currentTimeMillis();
                if (!this.thread.isDaemon()) {
                    this.thread.join(this.getJointime());
                }
                long elapsedTime = System.currentTimeMillis() - beginTime;
                log.info("join thread " + this.getServiceName() + " elapsed time(ms) " + elapsedTime + " " + this.getJointime());
            } catch (InterruptedException var6) {
                log.error("Interrupted", var6);
            }

        }
    }

    public long getJointime() {
        return 90000L;
    }


    public void makeStop() {
        if (this.started.get()) {
            this.stopped = true;
            log.info("makestop thread " + this.getServiceName());
        }
    }

    public void wakeup() {
        if (this.hasNotified.compareAndSet(false, true)) {
            this.waitPoint.countDown();
        }

    }

    protected void waitForRunning(long interval) {
        if (this.hasNotified.compareAndSet(true, false)) {
            this.onWaitEnd();
        } else {
            this.waitPoint.reset();
            try {
                this.waitPoint.await(interval, TimeUnit.MILLISECONDS);
            } catch (InterruptedException var7) {
                log.error("Interrupted", var7);
            } finally {
                this.hasNotified.set(false);
                this.onWaitEnd();
            }

        }
    }

    protected void onWaitEnd() {
    }

    public boolean isStopped() {
        return this.stopped;
    }

    public boolean isDaemon() {
        return this.isDaemon;
    }

    public void setDaemon(boolean daemon) {
        this.isDaemon = daemon;
    }
}
