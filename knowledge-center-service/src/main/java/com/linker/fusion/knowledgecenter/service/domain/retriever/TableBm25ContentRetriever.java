package com.linker.fusion.knowledgecenter.service.domain.retriever;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexTenantMappingManager;
import com.linker.fusion.knowledgecenter.service.domain.EmbeddingService;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.KnowledgeConfig;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TableEmbeddingData;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.Meta;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.QueryParams;
import com.linker.omagent.core.rag.content.retriever.Content;
import com.linker.omagent.core.rag.content.retriever.ContentRetriever;
import com.linker.omagent.core.rag.query.Query;
import com.linker.omagent.core.repository.embedding.EmbeddingRepository;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchRequest;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchResult;
import com.linker.omagent.core.repository.embedding.MatchQuery;
import com.linker.omagent.core.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TableBm25ContentRetriever extends AbstractBm25Retriever implements ContentRetriever {

    @Autowired
    IIndexTenantMappingManager indexTenantMappingManager;
    @Autowired
    EmbeddingService embeddingService;
    @Autowired
    private EmbeddingRepository embeddingStore;

    private static String buildFilter(QueryParams extra) {
        KnowledgeConfig knowledgeConfig = extra.getKnowledgeConfig();
        List<Long> docGroupIds = knowledgeConfig.getTableGroupIds();
        StringBuilder filter = new StringBuilder(String.format("enable = 1 and tenant_id = '%s'    ",
                extra.getTenantId()
        ));
        if (CollectionUtils.isNotEmpty(docGroupIds)) {
            String str = docGroupIds.stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            String format = String.format(" and  group_id in (%s)   ",
                    str
            );
            filter.append(format);
        }
        if (!ObjectUtils.isEmpty(extra.getDocIds())) {
            String resourceIdsStr = extra.getDocIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id in (").append(resourceIdsStr).append(")");
        }
        if (!ObjectUtils.isEmpty(extra.getNotResIds())) {
            String resourceIdsStr = extra.getNotResIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id not in (").append(resourceIdsStr).append(")");
        }
        return filter.toString();
    }

    @Override
    public List<Content> retrieve(Query query) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        if (!CollectionUtils.containsAny(extra.getFileTypes(), FileTypeEnum.TABLE.getType())) {
            return Collections.emptyList();
        }

        long l = System.currentTimeMillis();
        EmbeddingSearchResult<TableEmbeddingData> searchResult;
        String filter = buildFilter(extra);
        log.debug("检索条件:{}", filter);
        String index = indexTenantMappingManager.getIndex(extra.getTenantId(), IndexPrefixEnum.TABLE);
        List<Double> vector = embeddingService.embedText(query.getInput()).content().vector();
        log.info("向量化耗时:{}", System.currentTimeMillis() - l);
        EmbeddingSearchRequest vectorSearch = EmbeddingSearchRequest
                .builder()
                .indexId(index)
                .matchQuery(MatchQuery.builder().field("content").value(query.input()).build())
                .exclude(Lists.newArrayList("text_vector"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), 10))
                .filter(filter)
                .build();
        searchResult = this.embeddingStore.search(vectorSearch,
                new TypeReference<EmbeddingSearchResult<TableEmbeddingData>>() {
                });

        log.info("检索器：{},检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));

//        return searchResult.matches().stream().map(SearchHit::document).map((c) -> {
//            Meta meta = new Meta();
//            meta.setChunkId(c.getChunkId());
//            meta.setSegmentId(c.getSegmentId());
//            meta.setDocId(c.getDocId());
//            Content content = new Content(c.getContent(), ResourceType.TABLE.getType(), meta);
//            return content;
//        }).collect(Collectors.toList());
        return searchResult.matches().stream().map(hit -> {
            TableEmbeddingData c = hit.getDocument();
            Meta meta = new Meta();
            meta.setChunkId(c.getUid());
            meta.setNumber(c.getSort().intValue());
            meta.setSegmentId(c.getUid());
            meta.setDocId(c.getDocId());
            meta.setScore(hit.getScore());
            return new Content(c.getContent(), FileTypeEnum.TABLE.getName(), meta);
        }).collect(Collectors.toList());
    }

    @Override
    public String description() {
        return "表格向量检索";
    }
}
