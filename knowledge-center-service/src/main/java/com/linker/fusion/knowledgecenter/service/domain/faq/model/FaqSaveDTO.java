package com.linker.fusion.knowledgecenter.service.domain.faq.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class FaqSaveDTO {

    private Long id;

    private Long groupId;

    private String standardQuestion;

    private List<String> similarQuestions = new ArrayList<>();

    private Boolean isLlmEnhanced;

    private Integer answerType;

    private List<String> answers;

    private Boolean enable;

    private String tenantId;

    private String userId;
    private String segmentInfo;
    private String resourceId;

}
