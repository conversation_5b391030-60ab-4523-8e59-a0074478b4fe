package com.linker.fusion.knowledgecenter.service.domain.group.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.common.executor.ExecutorFactory;
import com.alibaba.nacos.common.executor.NameThreadFactory;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.AasWorkflowClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunResp;
import com.linker.fusion.knowledgecenter.infrastructure.config.ResourceIndexConfig;
import com.linker.fusion.knowledgecenter.infrastructure.dto.KnowledgeIndexTaskDTO;
import com.linker.fusion.knowledgecenter.infrastructure.entity.IndexAuthEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexAuthManager;
import com.linker.fusion.knowledgecenter.service.domain.common.ResourceIndexingThread;
import com.linker.fusion.knowledgecenter.service.domain.common.TimeRangeValidator;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025年09月18日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
public class ResourceIndexingWorker extends ResourceIndexingThread {

    /**
     * 租户id
     */
    private final String tenantId;

    /**
     * 最后调用时间
     */
    protected volatile LocalDateTime lastCallTime;


    /**
     * 快队列缓存消息数据
     */
    private volatile LinkedBlockingDeque<String> tasks = new LinkedBlockingDeque<>(1024);


    /**
     * aaas工作流客户端
     */
    private final AasWorkflowClient aaasWorkflowClient;

    /**
     * 资源服务
     */
    private final IResourceService iResourceService;

    /**
     * 资源队列管理器
     */
    private final ResourceQueueManager resourceQueueManager;

    /**
     * 索引定时任务配置管理器
     */
    private final IIndexAuthManager indexAuthManager;

    /**
     * 请求算法线程
     */
    protected ThreadPoolExecutor executorService;

    /**
     * 定时线程
     */
    ScheduledExecutorService scheduledexecutorService;

    /**
     * 索引配置
     */
    private final ResourceIndexConfig resourceIndexConfig;

    /**
     * 构造函数
     *
     * @param tenantId
     * @param aaasWorkflowClient
     * @param iResourceService
     * @param resourceQueueManager
     */
    public ResourceIndexingWorker(String tenantId, AasWorkflowClient aaasWorkflowClient,
                                  IIndexAuthManager indexAuthManager, ResourceIndexConfig resourceIndexConfig,
                                  IResourceService iResourceService, ResourceQueueManager resourceQueueManager) {
        this.tenantId = tenantId;
        this.aaasWorkflowClient = aaasWorkflowClient;
        this.iResourceService = iResourceService;
        this.resourceQueueManager = resourceQueueManager;
        this.indexAuthManager = indexAuthManager;
        this.lastCallTime = LocalDateTime.now();
        this.resourceIndexConfig = resourceIndexConfig;
        //请求线程池
        this.executorService = ExecutorBuilder.create()
                .setCorePoolSize(32)
                .setMaxPoolSize(64)
                .useSynchronousQueue()
                .build();
    }

    /**
     * 检查索引时间是否在配置的时间范围内
     *
     * @return
     */
    private Boolean checkIndexTimeOk() {
        IndexAuthEntity indexAuthEntity = indexAuthManager.getIndexTimeConfig(tenantId);
        if (indexAuthEntity != null && indexAuthEntity.getIndexTimeScheduleOpen()) {
            String indexTimeConfig = indexAuthEntity.getIndexTimeConfig();
            if (StrUtil.isNotBlank(indexTimeConfig)) {
                List<String> split = StrUtil.split(indexTimeConfig, StrUtil.DASHED);
                if (CollectionUtil.isNotEmpty(split) && split.size() == 2) {
                    String indexTimeBegin = CollectionUtil.getFirst(split);
                    String indexTimeEnd = CollectionUtil.getLast(split);
                    return TimeRangeValidator.isCurrentTimeInRange(indexTimeBegin, indexTimeEnd);
                }
            }
        }
        return true;
    }


    @Override
    public String getServiceName() {
        return "ResourceIndexingWorker_" + this.tenantId;
    }


    /**
     * 关闭线程池
     * @param shutdownNow
     */
    public void shutdown(boolean shutdownNow) {
        log.info("准备关闭资源索引worker ：{}", tenantId);
        if (this.scheduledexecutorService != null) {
            this.scheduledexecutorService.shutdown();
        }
        if (this.executorService != null) {
            this.executorService.shutdown();
        }
        super.shutdown(shutdownNow);
    }


    /**
     * 初始化定时任务去拉取索引任务
     */
    private void init() {
        //定时拉取消息
        this.scheduledexecutorService = ExecutorFactory.newScheduledExecutorService(1, new NameThreadFactory("IndexWorker-" + tenantId + "-"));
        //定时获取并发控制
        this.scheduledexecutorService.scheduleWithFixedDelay(() -> {
            try {
                //检查索引时间是否在配置的时间范围内
                if (checkIndexTimeOk()) {
                    Integer indexPullBatchSize = this.resourceIndexConfig.getIndexPullBatchSize();
                    Integer concurrentSize = resourceQueueManager.getResourceIndexingConcurrentSize(tenantId);
                    //并发配置数大于正在运行的任务数
                    if (concurrentSize > 0) {
                        Integer runningSize = resourceQueueManager.getResourceIndexingRunningSize(tenantId);
                        if (concurrentSize > runningSize) {
                            int pullSize = NumberUtil.sub(concurrentSize, runningSize).intValue();
                            pollIndexTask(pullSize);
                        }
                    } else if (concurrentSize < 0) {
                        //无限制，默认拉取批量数量
                        pollIndexTask(indexPullBatchSize);
                    }
                }
            } catch (Exception e) {
                log.warn("「{}」，资源索引worker获取资源发生异常", tenantId, e);
            }
        }, 0L, resourceIndexConfig.getIndexPullInterval(), TimeUnit.MILLISECONDS);
    }


    /**
     * 拉取索引任务
     *
     * @param size
     */
    private void pollIndexTask(int size) {
        //从队列中拉取消息
        List<String> indexJob = resourceQueueManager.getIndexJob(tenantId, size);
        if (CollectionUtil.isNotEmpty(indexJob)) {
            //加入到快队列
            tasks.addAll(indexJob);
        }
    }

    @Override
    public void run() {
        log.info("{},启动资源索引worker,currentThread:{}", tenantId, Thread.currentThread().getId());
        init();
        while (!this.stopped) {
            try {
                //尝试调用
                String poll = tasks.take();
                if (StrUtil.isNotBlank(poll)) {
                    executorService.execute(RunnableWrapper.of(() -> {
                        KnowledgeIndexTaskDTO knowledgeIndexTask = JSON.parseObject(poll, KnowledgeIndexTaskDTO.class);
                        Long knowledgeResourceId = knowledgeIndexTask.getKnowledgeResourceId();
                        log.info("{}, 资源索引worker 处理任务:{}", tenantId, knowledgeResourceId);
                        handleIndexTask(knowledgeIndexTask);
                    }));
                }
            } catch (Exception e) {
                if (!this.stopped) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        //任务停止运行
        log.info("资源索引worker ：{}, 停止工作", tenantId);
    }


    /**
     * 处理索引任务
     * 判断状态&下发工作流&变更资源状态
     *
     * @param knowledgeIndexTask
     */
    private void handleIndexTask(KnowledgeIndexTaskDTO knowledgeIndexTask) {
        Long knowledgeResourceId = knowledgeIndexTask.getKnowledgeResourceId();
        WorkflowAsyncRunReq workflowAsyncRunReq = knowledgeIndexTask.getWorkflowAsyncRunReq();
        Integer nextStatus = knowledgeIndexTask.getNextStatus();
        if (ObjectUtil.isNotNull(knowledgeResourceId) && ObjectUtil.isNotNull(workflowAsyncRunReq) && ObjectUtil.isNotNull(nextStatus)) {
            KnowledgeResourceEntity knowledgeResourceEntity = iResourceService.get(knowledgeResourceId);
            if (ObjectUtil.isNotNull(knowledgeResourceEntity)) {
                Integer failedStatus = ProcessEnum.Fail.getValue();
                //判断状态是否正常
                if (ProcessEnum.MERGING.getValue().equals(nextStatus)) {
                    if (!ProcessEnum.MERGING_TASK.getValue().equals(knowledgeResourceEntity.getHandleStatus())) {
                        log.warn("{}, 资源索引状态异常，当前状态:{}，期望状态:{}", knowledgeResourceId, knowledgeResourceEntity.getHandleStatus(), ProcessEnum.MERGING_TASK.getValue());
                        return;
                    }
                    failedStatus = ProcessEnum.MERGE_FAILED.getValue();
                } else if (ProcessEnum.QUEUED.getValue().equals(nextStatus)) {
                    if (!ProcessEnum.QUEUED_TASK.getValue().equals(knowledgeResourceEntity.getHandleStatus())) {
                        log.warn("{}, 资源索引状态异常，当前状态为:{}，期望状态:{}", knowledgeResourceId, knowledgeResourceEntity.getHandleStatus(), ProcessEnum.QUEUED_TASK.getValue());
                        return;
                    }
                } else {
                    return;
                }
                WorkflowAsyncRunResp workflowAsyncRunResp = aaasWorkflowClient.runAsync(workflowAsyncRunReq);
                if (workflowAsyncRunResp == null || !Objects.equals("0", workflowAsyncRunResp.getCode())) {
                    log.error("{},下发工作流任务失败{}", knowledgeResourceId, JSON.toJSONString(workflowAsyncRunResp));
                    knowledgeResourceEntity.setHandleStatus(failedStatus);
                } else {
                    log.info("{}, 文件导入下发工作流任务成功：docId:{}, workflowInstanceId:{}", knowledgeResourceId, knowledgeResourceEntity.getDocId(), workflowAsyncRunResp.getWorkflowInstanceId());
                    knowledgeResourceEntity.setWorkflowId(workflowAsyncRunResp.getData());
                    knowledgeResourceEntity.setHandleStatus(nextStatus);
                }
                iResourceService.updateById(knowledgeResourceEntity);
            }
        }
    }
}
