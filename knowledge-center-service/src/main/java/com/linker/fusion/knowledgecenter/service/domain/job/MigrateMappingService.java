package com.linker.fusion.knowledgecenter.service.domain.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linker.fusion.knowledgecenter.infrastructure.entity.*;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.MigrateTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.ChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.SegmentEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IMigrationTaskManager;
import com.linker.fusion.knowledgecenter.infrastructure.mapper.KnowledgeGroupMapper;
import com.linker.fusion.knowledgecenter.service.domain.custom.CustomService;
import com.linker.fusion.knowledgecenter.service.domain.faq.FaqIBaseService;
import com.linker.fusion.knowledgecenter.service.domain.faq.FaqService;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.FaqEmbeddingData;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.TableService;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IDocChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.dto.TableEmbeddingData;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceSegmentService;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.TableIBaseOldService;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.TableIBaseService;
import com.linker.omagent.core.repository.embedding.SearchHit;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MigrateMappingService {

    @Resource
    private IMigrationTaskManager migrationTaskManager;

    @Resource
    private IResourceService iResourceService;



    @Resource
    private IResourceSegmentService iResourceSegmentService;

    @Resource
    private IDocChunkIBaseServiceResource iChunkIBaseService;

    @Resource
    private FaqIBaseService faqIBaseService;

    @Resource
    private FaqService faqService;

    @Resource
    private TableIBaseOldService tableIBaseOldService;

    @Resource
    private TableIBaseService tableIBaseService;

    @Resource
    private TableService tableService;

    @Resource
    private KnowledgeGroupService knowledgeGroupService;

    @Resource
    private KnowledgeGroupMapper knowledgeGroupMapper;

    @Resource
    private CustomService customService;

    @Value("${migrate.urlPrefixOld:https://minio-ombot.linker.cc/fusion}")
    private String urlPrefixOld;

    @Value("${migrate.urlPrefixNew:https://minio.om-agent.cn/fusion}")
    private String urlPrefixNew;

    private List<MigrationTaskEntity> selectTask(String type, int limit) {
        return migrationTaskManager.list(
                new LambdaQueryWrapper<MigrationTaskEntity>()
                        .eq(MigrationTaskEntity::getType, type)
                        .eq(MigrationTaskEntity::getSuccess, false)
                        .orderByAsc(MigrationTaskEntity::getId)
                        .last("limit " + limit)
        );
    }

    @SneakyThrows
    public void docAddGroupId() {
        Set<String> indexUpdatedSet = new HashSet<>();

        while (true) {
            List<MigrationTaskEntity> migrationTasks = selectTask(MigrateTypeEnum.DOC_SET_GROUP_ID.toString().toLowerCase(), 10);
            if (CollectionUtils.isEmpty(migrationTasks)) {
                break;
            }
            for (MigrationTaskEntity migrationTask : migrationTasks) {
                try {
                    KnowledgeResourceEntity docEntity = iResourceService.get(Long.valueOf(migrationTask.getContentId()));
                    if (!indexUpdatedSet.contains(docEntity.getTenantId())) {
                        iChunkIBaseService.updateIndex(docEntity.getTenantId());
                        indexUpdatedSet.add(docEntity.getTenantId());
                    }
                    iResourceService.updateDocChunk(docEntity.getTenantId(), Collections.singletonList(docEntity.getDocId()), docEntity.getGroupId(), docEntity.getEnable());
                    migrationTask.setSuccess(true);
                    migrationTask.setMessage("success");
                } catch (Exception e) {
                    log.error("tableAddGroupId error", e);
                    migrationTask.setSuccess(false);
                    migrationTask.setMessage(e.getMessage());
                }
                migrationTask.setUpdateTime(LocalDateTime.now());
                migrationTaskManager.updateById(migrationTask);
            }
            Thread.sleep(1000);
        }
    }

    public void faqAddGroupId() {
        Set<String> indexUpdatedSet = new HashSet<>();

        while (true) {

            List<MigrationTaskEntity> migrationTasks = selectTask(MigrateTypeEnum.FAQ_SET_GROUP_ID.toString().toLowerCase(), 100);
            if (CollectionUtils.isEmpty(migrationTasks)) {
                break;
            }
            for (MigrationTaskEntity migrationTask : migrationTasks) {
                try {
                    FaqInfoEntity faqInfo = faqService.getById(Long.valueOf(migrationTask.getContentId()));
                    if (!indexUpdatedSet.contains(faqInfo.getTenantId())) {
                        faqIBaseService.updateIndex(faqInfo.getTenantId());
                        indexUpdatedSet.add(faqInfo.getTenantId());
                    }
                    FaqEmbeddingData faqEmbeddingData = new FaqEmbeddingData().setUid(faqInfo.getUid()).setGroupId(faqInfo.getGroupId());
                    faqIBaseService.batchUpdate(faqInfo.getTenantId(), Collections.singletonList(faqEmbeddingData));
                    migrationTask.setSuccess(true);
                    migrationTask.setMessage("success");
                } catch (Exception e) {
                    log.error("migrateFaq error", e);
                    migrationTask.setSuccess(false);
                    migrationTask.setMessage(e.getMessage());
                }
                migrationTask.setUpdateTime(LocalDateTime.now());
                migrationTaskManager.updateById(migrationTask);
            }
        }
    }

    public void tableAddGroupId() {
        Set<String> indexUpdatedSet = new HashSet<>();

        while (true) {
            List<MigrationTaskEntity> migrationTasks = selectTask(MigrateTypeEnum.TABLE_SET_GROUP_ID.toString().toLowerCase(), 100);
            if (CollectionUtils.isEmpty(migrationTasks)) {
                break;
            }
            for (MigrationTaskEntity migrationTask : migrationTasks) {
                try {
                    KnowledgeResourceEntity tableEntity = tableService.getEntityById(Long.valueOf(migrationTask.getContentId()));
                    if (!indexUpdatedSet.contains(tableEntity.getTenantId())) {
                        tableIBaseService.updateIndex(tableEntity.getTenantId());
                        indexUpdatedSet.add(tableEntity.getTenantId());
                    }
                    List<String> allUIds = tableIBaseService.getAllUIds(tableEntity.getTenantId(), Collections.singletonList(tableEntity.getDocId()));
                    List<TableEmbeddingData> docList = new ArrayList<>();
                    for (String allUId : allUIds) {
                        docList.add(
                                new TableEmbeddingData()
                                        .setUid(allUId)
                                        .setGroupId(tableEntity.getGroupId())
                                        .setEnable(Boolean.TRUE.equals(tableEntity.getEnable()) ? 1 : 0)
                        );
                    }
                    tableIBaseService.batchUpdate(tableEntity.getTenantId(), docList);
                    migrationTask.setSuccess(true);
                    migrationTask.setMessage("success");
                } catch (Exception e) {
                    log.error("tableAddGroupId error", e);
                    migrationTask.setSuccess(false);
                    migrationTask.setMessage(e.getMessage());
                }
                migrationTask.setUpdateTime(LocalDateTime.now());
                migrationTaskManager.updateById(migrationTask);
            }
        }
    }

    public void tableReplaceRow() {
        while (true) {
            List<MigrationTaskEntity> migrationTasks = selectTask(MigrateTypeEnum.TABLE_REPLACE_ROW.toString().toLowerCase(), 10);
            if (CollectionUtils.isEmpty(migrationTasks)) {
                break;
            }
            for (MigrationTaskEntity migrationTask : migrationTasks) {
                try {
                    KnowledgeResourceEntity tableEntity = tableService.getEntityById(Long.valueOf(migrationTask.getContentId()));
                    // 先从老的ES索引中查出数据
                    List<TableEmbeddingData> allDocList = tableIBaseOldService.getByDocId(tableEntity.getTenantId(), tableEntity.getDocId());
                    if (CollectionUtils.isNotEmpty(allDocList)) {
                        for (TableEmbeddingData tableEmbeddingData : allDocList) {
                            List<Double> textVector = tableIBaseOldService.getTextVector(tableEntity.getTenantId(), tableEmbeddingData.getUid());
                            if (CollectionUtils.isEmpty(textVector)) {
                                log.warn("tableReplaceRow textVector为空,tenantId:{},uid:{}", tableEntity.getTenantId(), tableEmbeddingData.getUid());
                            }
                            tableEmbeddingData.setUid(null);
                            tableEmbeddingData.setTextVector(textVector);
                            // 直接入到新索引
                            tableIBaseService.add(tableEntity.getTenantId(), Collections.singletonList(tableEmbeddingData));
                        }
                    }
                    migrationTask.setSuccess(true);
                    migrationTask.setMessage("success");
                } catch (Exception e) {
                    log.error("tableReplaceRow error", e);
                    migrationTask.setSuccess(false);
                    migrationTask.setMessage(e.getMessage());
                }
                migrationTask.setUpdateTime(LocalDateTime.now());
                migrationTaskManager.updateById(migrationTask);
            }
        }
    }

    public void fixSyncGroup() {
        List<KnowledgeGroupEntity> libList = knowledgeGroupMapper.selectList(
                new LambdaQueryWrapper<KnowledgeGroupEntity>()
                        .eq(KnowledgeGroupEntity::getDeleted, false)
                        .eq(KnowledgeGroupEntity::getIsLibrary, true)
                        .eq(KnowledgeGroupEntity::getIsSync, false)
                        .select(KnowledgeGroupEntity::getId)
        );
        if (CollectionUtils.isEmpty(libList)) {
            return;
        }
        List<KnowledgeGroupEntity> foldList = knowledgeGroupMapper.selectList(
                new LambdaQueryWrapper<KnowledgeGroupEntity>()
                        .eq(KnowledgeGroupEntity::getDeleted, false)
                        .eq(KnowledgeGroupEntity::getIsLibrary, false)
                        .eq(KnowledgeGroupEntity::getIsSync, true)
                        .in(KnowledgeGroupEntity::getParentId, libList.stream().map(KnowledgeGroupEntity::getId).collect(Collectors.toList()))
                        .select(KnowledgeGroupEntity::getParentId)
        );
        log.info("fixSyncGroup size:{}", foldList.size());
        for (Long id : foldList.stream().map(KnowledgeGroupEntity::getParentId).distinct().collect(Collectors.toList())) {
            log.info("fixSyncGroup id:{}", id);
            knowledgeGroupService.update(id, null, null, false);
        }
    }

    @SneakyThrows
    public void docSegmentToDb() {
        Set<String> indexUpdatedSet = new HashSet<>();

        while (true) {
            List<MigrationTaskEntity> migrationTasks = selectTask(MigrateTypeEnum.DOC_SEGMENT_TO_DB.toString(), 10);
            if (CollectionUtils.isEmpty(migrationTasks)) {
                break;
            }
            for (MigrationTaskEntity migrationTask : migrationTasks) {
                try {
                    KnowledgeResourceEntity resource = iResourceService.get(Long.valueOf(migrationTask.getContentId()));
                    JSONObject extInfo = JSON.parseObject(resource.getExtInfo());
                    if (extInfo == null) {
                        extInfo = new JSONObject();
                    }
                    JSONObject newExtInfo = new JSONObject();
                    newExtInfo.putAll(extInfo);
                    extInfo.forEach((k, v) -> {
                        if (k.startsWith("customer_")) {
                            String field = StringUtils.replace(k, "customer_", "");
                            CustomEntity custom = customService.getByField(resource.getTenantId(), field);
                            if (custom != null) {
                                customService.fillSearchExtInfo(custom.getFieldType(), field, newExtInfo, v != null ? v.toString() : null);
                            }
                        }
                    });
                    iResourceService.updateExtInfo(resource.getDocId(), newExtInfo.toString());

                    if (!indexUpdatedSet.contains(resource.getTenantId())) {
                        iChunkIBaseService.updateIndex(resource.getTenantId());
                        indexUpdatedSet.add(resource.getTenantId());
                    }
                    List<SegmentEntity> segmentEntityList =new ArrayList<>();

                    if (CollectionUtils.isNotEmpty(segmentEntityList)) {
                        int number = 1;
                        List<ResourceSegmentEntity> resourceSegmentEntities = new ArrayList<>();
                        for (SegmentEntity s : segmentEntityList) {
                            ResourceSegmentEntity segmentEntity = new ResourceSegmentEntity()
                                    .setSegmentId(s.getSegmentId())
                                    .setFileType(FileTypeEnum.DOCUMENT.getType())
                                    .setDocId(s.getDocId())
                                    .setTitle(s.getTitle())
                                    .setContent(StringUtils.replace(s.getContent(), urlPrefixOld, urlPrefixNew))
                                    .setSort(s.getSort())
                                    .setNumber(number++)
                                    .setPage(s.getPage())
                                    .setPosition(s.getPosition())
                                    .setStatus(ProcessEnum.Success.getValue());
                            segmentEntity.setCreatorId(s.getCreateId() != null ? s.getCreateId() : resource.getCreatorId());
                            segmentEntity.setTenantId(resource.getTenantId());
                            segmentEntity.setCreateTime(LocalDateTimeUtil.parse(s.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                            resourceSegmentEntities.add(segmentEntity);
                        }
                        iResourceSegmentService.saveBatch(resourceSegmentEntities);
                    }
                    List<SearchHit<ChunkEntity>> chunks = iChunkIBaseService.list(resource.getTenantId(), Collections.singletonList(resource.getDocId()));
                    List<ChunkEntity> chunkEntities = chunks.stream().map(SearchHit::getDocument).map(chunk -> {
                        ChunkEntity newChunk = new ChunkEntity();
                        newChunk.setUid(chunk.getUid());
                        newChunk.setTitle(resource.getTitle());
                        newChunk.setMeta(newExtInfo);
                        newChunk.setUrl(StringUtils.replace(chunk.getUrl(), urlPrefixOld, urlPrefixNew));
                        return newChunk;
                    }).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(chunkEntities)) {
                        iChunkIBaseService.batchUpdate(resource.getTenantId(), chunkEntities);
                    }
//                    List<String> allUIds = iChunkIBaseService.getAllUIds(resource.getTenantId(), Collections.singletonList(resource.getDocId()));
//                    if (CollectionUtils.isNotEmpty(allUIds)) {
//                        List<ChunkEntity> chunkEntities = allUIds.stream().map(uid -> new ChunkEntity()
//                                .setUid(uid)
//                                .setTitle(resource.getTitle())
//                                .setMeta(newExtInfo)
//                        ).collect(Collectors.toList());
//                        iChunkIBaseService.batchUpdate(resource.getTenantId(), chunkEntities);
//                    }

                    migrationTask.setSuccess(true);
                    migrationTask.setMessage("success");
                } catch (Exception e) {
                    log.error("DOC_SEGMENT_TO_DB error", e);
                    migrationTask.setSuccess(false);
                    migrationTask.setMessage(e.getMessage());
                }
                migrationTask.setUpdateTime(LocalDateTime.now());
                migrationTaskManager.updateById(migrationTask);
            }
            Thread.sleep(100);
        }

    }
}
