package com.linker.fusion.knowledgecenter.service.domain.group.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.linker.core.auth.utils.UserContext;
import com.linker.core.redis.RedissonUtil;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.AasWorkflowClient;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexAuthManager;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.omagent.starter.event.RocketMQEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RKeys;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025年09月18日
 * @version: 1.0
 * @description: TODO
 */
@Service
@Slf4j
public class ResourceQueueManager {

    @Resource
    RedissonClient redissonClient;

    @Resource
    private AasWorkflowClient aaasWorkflowClient;

    @Resource
    private IResourceService iResourceService;

    @Resource
    IIndexAuthManager indexAuthManager;

    /**
     * redisson lua脚本 执行器
     */
    RScript redissonScript;

    /**
     * 任务前缀
     */
    public static final String JOB_PREFIX = "know_index:";

    /**
     * 任务队列前缀
     */
    public static final String JOB_QUEUE_PREFIX = JOB_PREFIX + "queue:";

    /**
     * 下发任务lua脚本
     */
    public static final String PUSH_JOB_SCRIPT = "local set = redis.call(\"hsetnx\", KEYS[1], ARGV[1], ARGV[2])\n" +
            "if set == 1 then\n" +
            "  redis.call(\"rpush\", KEYS[2], ARGV[1])\n" +
            "end\n" +
            "return set";

    /**
     * 获取任务lua脚本
     */
    public static final String GET_JOB_SCRIPT = "local job_ids = redis.call(\"lrange\", KEYS[1], 0, ARGV[1] - 1)\n" +
            "local count = #job_ids\n" +
            "local results = {}\n" +
            "if count > 0 then\n" +
            "  redis.call(\"ltrim\", KEYS[1], count, -1)\n" +
            "  results = redis.call(\"hmget\", KEYS[2], unpack(job_ids))\n" +
            "end\n" +
            "return results";



    /**
     * 脚本map，key为脚本名称，value为脚本sha值
     */
    public static HashMap<String, String> JOB_SCRIPT_MAP = new HashMap<>();


    /**
     * 统计数据获取
     *
     * @param
     * @return
     */
    private Set<String> scanTenantQueueKeys() {
        // 获取 RKeys 对象
        RKeys rKeys = redissonClient.getKeys();
        Set<String> keysAll = new HashSet<>();
        Iterable<String> keys = rKeys.getKeysByPattern(JOB_QUEUE_PREFIX + "*", 200);
        for (String key : keys) {
            keysAll.add(key);
        }
        return keysAll;
    }

    /**
     * 获取队列workers
     *
     * @return
     */
    public List<String> getTenantQueueWorkers() {
        List<String> workers = new ArrayList<>();
        Set<String> keys = scanTenantQueueKeys();
        if (CollectionUtil.isNotEmpty(keys)) {
            for (String key : keys) {
                workers.add(key.replace(JOB_QUEUE_PREFIX, ""));
            }
        }
        return workers;
    }


    @PostConstruct
    public void init() {
        this.redissonScript = redissonClient.getScript(StringCodec.INSTANCE);
        String pushJobRes = redissonScript.scriptLoad(PUSH_JOB_SCRIPT);
        JOB_SCRIPT_MAP.put("push_job", pushJobRes);
        log.info("push_job.lua脚本加载结果:{}", pushJobRes);
        String getJobRes = redissonScript.scriptLoad(GET_JOB_SCRIPT);
        JOB_SCRIPT_MAP.put("get_job", getJobRes);
        log.info("get_job.lua脚本加载结果:{}", getJobRes);
    }


    /**
     * 下发任务
     *
     * @param jobId
     * @param jobData
     */
    public Boolean pushIndexJob(String jobId, String jobData) {
        String tenantId = UserContext.getUser().getTenantInfoDTO().getTenantId();
        String jobQueueKey = JOB_QUEUE_PREFIX + tenantId;
        String jobKey = JOB_PREFIX + "data";
        log.info("{},下发index任务:{}", tenantId, jobId);
        Object pushJob = redissonScript.evalSha(RScript.Mode.READ_WRITE,
                JOB_SCRIPT_MAP.get("push_job"), RScript.ReturnType.INTEGER,
                Arrays.asList(jobKey, jobQueueKey),
                Arrays.asList(jobId, jobData));
        return pushJob != null && (Integer) pushJob == 1;
    }


    /**
     * 获取任务
     *
     * @param tenantId
     * @param size
     * @return
     */
    public List<String> getIndexJob(String tenantId, int size) {
        ArrayList<String> jobList = new ArrayList<>();
        String jobQueueKey = JOB_QUEUE_PREFIX + tenantId;
        String jobKey = JOB_PREFIX + "data";
        List<String> jobIds = redissonScript.evalSha(RScript.Mode.READ_WRITE, JOB_SCRIPT_MAP.get("get_job"),
                RScript.ReturnType.MULTI, Arrays.asList(jobQueueKey, jobKey), Collections.singletonList(String.valueOf(size)));
        if (CollectionUtils.isEmpty(jobIds)) {
            return jobList;
        }
        jobList.addAll(jobIds);
        return jobList;
    }


    /**
     * 获取指定租户正在资源索引的任务数量
     * todo @keke
     * @param tenantId
     * @return
     */
    public Integer getResourceIndexingRunningSize(String tenantId) {

        return 0;
    }


    /**
     * 获取指定租户的资源索引并发数
     * todo @keke
     * @param tenantId
     * @return -1则表示不限制并发
     */
    public Integer getResourceIndexingConcurrentSize(String tenantId) {

        return -1;
    }

}
