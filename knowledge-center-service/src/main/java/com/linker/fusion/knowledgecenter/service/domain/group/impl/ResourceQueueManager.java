package com.linker.fusion.knowledgecenter.service.domain.group.impl;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2025年09月18日
 * @version: 1.0
 * @description: TODO
 */
@Service
@Slf4j
public class ResourceQueueManager {

    @Resource
    RedissonClient redissonClient;


    @PostConstruct
    public void init() {
        RScript script = redissonClient.getScript(StringCodec.INSTANCE);

     //加载resource/lua/push_job.lua文件
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("lua/push_job.lua")) {
            redissonClient.getScript().config().addScript(inputStream);

        } catch (IOException e) {
            log.error("加载lua脚本失败", e);
        }

    }

}
