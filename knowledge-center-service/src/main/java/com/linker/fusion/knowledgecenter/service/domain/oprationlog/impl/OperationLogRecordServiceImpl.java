package com.linker.fusion.knowledgecenter.service.domain.oprationlog.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linker.core.auth.utils.UserContext;
import com.linker.fusion.knowledgecenter.infrastructure.client.sgcc.ThirdAuditLogAddReq;
import com.linker.fusion.knowledgecenter.infrastructure.dto.query.LogRecordPageQuery;
import com.linker.fusion.knowledgecenter.infrastructure.entity.LogRecordEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.ILogRecordManager;
import com.linker.fusion.knowledgecenter.service.domain.oprationlog.OperationLogRecordService;
import com.linker.fusion.knowledgecenter.service.domain.sgcc.SgccService;
import com.linker.logapi.beans.LogRecord;
import com.linker.logapi.service.ILogRecordService;
import com.linker.user.api.dto.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create on 2020/4/29 4:34 下午
 */
@Slf4j
@Service
public class OperationLogRecordServiceImpl implements ILogRecordService, OperationLogRecordService {

    @Autowired
    private ILogRecordManager logRecordManager;

    @Autowired
    private SgccService sgccService;
    @Override
    public void record(LogRecord logRecord) {
//        todo 优化成缓冲池的形式，根据时间和条数进行批量插入
        LogRecordEntity logRecordEntity = LogRecordEntity.build(logRecord);
        if (UserContext.getUser() != null) {
            String userCode = UserContext.userCode();
            User user = UserContext.getUser().getUser();
            String nickName = user.getNickName();
            String userName = user.getUserName();
            logRecordEntity.setCreatorId(userCode);
            logRecordEntity.setTenantId(UserContext.tenantId());
            logRecordEntity.setUsername(userName);
            logRecordEntity.setIp(UserContext.getIp());
            logRecordEntity.setOperator(nickName);
        }
        log.info("【logRecord】log={}", logRecord);
        logRecordManager.save(logRecordEntity);
//      同步日志给第三方
        sgccService.syncLog(ThirdAuditLogAddReq.build(logRecordEntity));
    }

    @Override
    public List<LogRecord> queryLog(String bizNo, String type) {
        return new ArrayList<>();
    }

    @Override
    public List<LogRecord> queryLogByBizId(String bizNo, String type, String subType) {
        return new ArrayList<>();
    }


    @Override
    public IPage<LogRecordEntity> page(LogRecordPageQuery query) {
        IPage<LogRecordEntity> page = logRecordManager.getPageList(query);
        return page;
    }
}
