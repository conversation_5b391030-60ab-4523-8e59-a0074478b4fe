package com.linker.fusion.knowledgecenter.service.domain.npc;

import com.linker.fusion.knowledgecenter.infrastructure.entity.NpcSearchLogEntity;

import java.time.LocalDateTime;
import java.util.List;

public interface INpcSearchLogService {

    void save(NpcSearchLogEntity npcSearchLogEntity);

    List<NpcSearchLogEntity> listByUser(String tenantId, String userCode, Integer fileType, Integer searchType);

    List<NpcSearchLogEntity> listByTenant(String tenantId, Integer fileType, Integer searchType, LocalDateTime startTime);

    void deleteByUser(String tenantId, String userCode, Integer fileType, Integer searchType);
}
