package com.linker.fusion.knowledgecenter.service.domain.group;

import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunReq;

/**
 * <AUTHOR>
 * @date 2025年09月18日
 * @version: 1.0
 * @description: TODO
 */
public interface IResourceWorkflowHandleQueueService {

    /**
     * 为资源添加索引任务,异步执行
     *
     * @param knowledgeResourceId 资源id
     * @param workflowAsyncRunReq 工作流异步运行请求
     * @return
     */
    Boolean addIndexTaskForResource(Long knowledgeResourceId, Integer nextStatus, WorkflowAsyncRunReq workflowAsyncRunReq);
}
