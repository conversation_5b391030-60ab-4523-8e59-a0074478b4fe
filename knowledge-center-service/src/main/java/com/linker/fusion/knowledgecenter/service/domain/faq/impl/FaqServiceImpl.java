package com.linker.fusion.knowledgecenter.service.domain.faq.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.core.base.exception.BaseException;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.config.AppInfoConfig;
import com.linker.fusion.knowledgecenter.infrastructure.config.PromptMappingConfig;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FaqInfoEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FaqQuestionTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.StoragePathEnum;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IFaqInfoManager;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.service.domain.EmbeddingService;
import com.linker.fusion.knowledgecenter.service.domain.common.IUtilService;
import com.linker.fusion.knowledgecenter.service.domain.faq.FaqIBaseService;
import com.linker.fusion.knowledgecenter.service.domain.faq.FaqService;
import com.linker.fusion.knowledgecenter.service.domain.faq.model.*;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.omagent.core.data.message.*;
import com.linker.omagent.core.data.output.Response;
import com.linker.omagent.core.model.chat.ChatLanguageModel;
import com.linker.omagent.core.repository.embedding.*;
import com.linker.omagent.starter.config.OmOsProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;
import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FaqServiceImpl implements FaqService {

    // EmbeddingModel暂不可用，使用BGEClient封装，后续直接替换引用类
    @Resource
    private EmbeddingService embeddingService;

    @Resource
    private ChatLanguageModel chatLanguageModel;

    @Resource
    private FaqIBaseService faqIBaseService;

    @Resource
    private IFaqInfoManager faqInfoManager;

    @Resource
    @Lazy
    private KnowledgeGroupService knowledgeGroupService;
    @Resource
    private IUtilService iUtilService;
    @Resource
    private AppInfoConfig appInfoConfig;

    @Override
    public KnowledgeTypeEnum getType() {
        return KnowledgeTypeEnum.FAQ;
    }

    @Autowired
    OmOsProperties omOsProperties;
//    @Value("${prompts.similar-questions}")
//    private String similarQuestionsPrompt;
//
//    @Value("${prompts.rephrase}")
//    private String rephrasePrompt;


    @Autowired
    private PromptMappingConfig promptMappingConfig;

    @Override
    public Long create(FaqSaveDTO faqSaveDTO) {
        LocalDateTime now = LocalDateTime.now();
        String standardQuestion = faqSaveDTO.getStandardQuestion();
        List<String> similarQuestions = faqSaveDTO.getSimilarQuestions();
        // 判断问题重复
        if (similarQuestions.contains(standardQuestion)) {
            throw new ServiceException(FAQ_QUESTION_DUPLICATE);
        }
        if (new HashSet<>(similarQuestions).size() < similarQuestions.size()) {
            throw new ServiceException(FAQ_QUESTION_DUPLICATE);
        }
        checkLimit(faqSaveDTO.getTenantId());

        //问题DUP校验
        List<String> questions = new ArrayList<>();
        questions.add(standardQuestion);
        questions.addAll(similarQuestions);
        checkQuestionDuplicate(faqSaveDTO.getTenantId(), questions);
        // 问题向量化
        Map<String, List<Double>> embedQuestionsMap = embedQuestions(questions);
        // 存ES
        List<FaqEmbeddingData> faqEmbeddingDataList = new ArrayList<>();
        faqEmbeddingDataList.add(
                new FaqEmbeddingData()
                        .setTenantId(faqSaveDTO.getTenantId())
                        .setGroupId(faqSaveDTO.getGroupId())
                        .setGroupPath("")
                        .setType(FaqQuestionTypeEnum.STANDARD.getValue())
                        .setEnable(faqSaveDTO.getEnable() ? 1 : 0)
                        .setQuestion(standardQuestion)
                        .setQuestionVector(embedQuestionsMap.get(standardQuestion))
                        .setCreateTime(DateUtil.format(now, NORM_DATETIME_PATTERN))
        );
        for (String similarQuestion : similarQuestions) {
            faqEmbeddingDataList.add(
                    new FaqEmbeddingData()
                            .setTenantId(faqSaveDTO.getTenantId())
                            .setGroupId(faqSaveDTO.getGroupId())
                            .setGroupPath("")
                            .setType(FaqQuestionTypeEnum.SIMILAR.getValue())
                            .setEnable(faqSaveDTO.getEnable() ? 1 : 0)
                            .setQuestion(similarQuestion)
                            .setQuestionVector(embedQuestionsMap.get(similarQuestion))
                            .setCreateTime(DateUtil.format(now, NORM_DATETIME_PATTERN))
            );
        }
        Map<String, String> uidMap = ibaseSave(faqSaveDTO.getTenantId(), faqEmbeddingDataList);
        // 存DB
        FaqInfoEntity faqInfoEntityStandard = new FaqInfoEntity();
        faqInfoEntityStandard.setUid(uidMap.get(standardQuestion));
        faqInfoEntityStandard.setTenantId(faqSaveDTO.getTenantId());
        faqInfoEntityStandard.setGroupId(faqSaveDTO.getGroupId());
        faqInfoEntityStandard.setGroupPath("");
        faqInfoEntityStandard.setType(FaqQuestionTypeEnum.STANDARD.getValue());
        faqInfoEntityStandard.setQuestion(standardQuestion);
        faqInfoEntityStandard.setIsLlmEnhanced(faqSaveDTO.getIsLlmEnhanced());
        faqInfoEntityStandard.setAnswerType(faqSaveDTO.getAnswerType());
        faqInfoEntityStandard.setAnswers(faqSaveDTO.getAnswers());
        faqInfoEntityStandard.setEnable(faqSaveDTO.getEnable());
        faqInfoEntityStandard.setDeleted(false);
        faqInfoEntityStandard.setCreateTime(now);
        faqInfoEntityStandard.setCreatorId(faqSaveDTO.getUserId());
        faqInfoEntityStandard.setUpdateTime(now);
        faqInfoEntityStandard.setUpdateId(faqSaveDTO.getUserId());
        faqInfoEntityStandard.setSegmentInfo(faqSaveDTO.getSegmentInfo());
        faqInfoEntityStandard.setResourceId(faqSaveDTO.getResourceId());
        List<String> uIds = new ArrayList<>();
        uidMap.forEach((key, value) -> {
            uIds.add(value);
        });
        try {
            faqInfoManager.save(faqInfoEntityStandard);
        } catch (Exception ex) {
            log.error("faq保存异常:{}", ex.getMessage(), ex);
            faqIBaseService.delete(faqInfoEntityStandard.getTenantId(), uIds);
            throw new ServiceException(KnowledgeCenterErrorCodeEnum.SQL_ERROR);
        }
        try {
            faqInfoEntityStandard.setAnswers(iUtilService.moveContentUrlToDefault(faqInfoEntityStandard.getTenantId(), faqInfoEntityStandard.getId().toString(), faqInfoEntityStandard.getAnswers(), StoragePathEnum.QA));
            faqInfoManager.updateById(faqInfoEntityStandard);
        } catch (Exception ex) {
            faqInfoManager.removeById(faqInfoEntityStandard.getId());
            faqIBaseService.delete(faqInfoEntityStandard.getTenantId(), uIds);
            throw ex;
        }
        for (String similarQuestion : similarQuestions) {
            FaqInfoEntity faqInfoEntitySimilar = new FaqInfoEntity();
            faqInfoEntitySimilar.setUid(uidMap.get(similarQuestion));
            faqInfoEntitySimilar.setTenantId(faqSaveDTO.getTenantId());
            faqInfoEntitySimilar.setGroupId(faqSaveDTO.getGroupId());
            faqInfoEntitySimilar.setGroupPath("");
            faqInfoEntitySimilar.setType(FaqQuestionTypeEnum.SIMILAR.getValue());
            faqInfoEntitySimilar.setStandardId(faqInfoEntityStandard.getId());
            faqInfoEntitySimilar.setQuestion(similarQuestion);
            faqInfoEntitySimilar.setEnable(true);
            faqInfoEntitySimilar.setDeleted(false);
            faqInfoEntitySimilar.setCreateTime(now);
            faqInfoEntitySimilar.setCreatorId(faqSaveDTO.getUserId());
            faqInfoEntitySimilar.setUpdateTime(now);
            faqInfoEntitySimilar.setUpdateId(faqSaveDTO.getUserId());
            faqInfoManager.save(faqInfoEntitySimilar);
        }
        return faqInfoEntityStandard.getId();
    }

    /**
     * 校验文件限制
     *
     * @param tenantId
     */
    private void checkLimit(String tenantId) {
        //todo 看情况是否需要加上redis锁 多并发的情况
        if (Objects.nonNull(appInfoConfig.getQaLimit())) {
            Long count = getCount(tenantId);
            if (count >= appInfoConfig.getQaLimit()) {
                throw new ServiceException(FAQ_COUNT_LIMIT, String.valueOf(Math.max(0, appInfoConfig.getQaLimit() - count)));
            }
        }
    }


    private void checkQuestionDuplicate(String tenantId, List<String> questions) {
        if (faqInfoManager.countByQuestion(tenantId, questions) > 0) {
            throw new BaseException(FAQ_QUESTION_DUPLICATE);
        }
    }

    private Map<String, List<Double>> embedQuestions(List<String> questions) {
        Map<String, List<Double>> questionEmbeddingsMap = new HashMap<>();
        for (String question : questions) {
            List<Double> vector = embeddingService.embedText(question).content().vector();
            questionEmbeddingsMap.put(question, vector);
        }
        return questionEmbeddingsMap;
    }

    private Map<String, String> ibaseSave(String tenantId, List<FaqEmbeddingData> faqEmbeddingDataList) {
        Map<String, String> uidMap = new HashMap<>();
        if (CollectionUtils.isEmpty(faqEmbeddingDataList))
            return uidMap;
        List<String> uIds = faqIBaseService.add(tenantId, faqEmbeddingDataList);

        for (int i = 0; i < faqEmbeddingDataList.size(); i++) {
            uidMap.put(faqEmbeddingDataList.get(i).getQuestion(), uIds.get(i));
        }
        return uidMap;
    }

    @Override
    public List<String> generateSimilarQuestions(String standardQuestion, List<String> similarQuestions) {

        List<ChatMessage> messageList = new ArrayList<>();
        SystemMessage systemMessage = SystemMessage.from(promptMappingConfig.getSimilarQuestions());
        messageList.add(systemMessage);

        UserMessage userMessage = UserMessage.from("<sentence>" + standardQuestion + "</sentence>");
        ChatModelConfig config = new ChatModelConfig();
        config.setModelName(omOsProperties.getChatModel());
        config.setResponseFormat("json_object");
        messageList.add(userMessage);
        Response<AiMessage> response = chatLanguageModel.generate(config, messageList);

        AiMessage aiMessage = response.content();
        log.info("aiMessage: {}", aiMessage);
        List<String> generateSimilarQuestions = formatAiMessage(aiMessage.text());
        if (!CollectionUtils.isEmpty(similarQuestions)) {
            generateSimilarQuestions.removeAll(similarQuestions);
        }
        int count = CollectionUtils.isEmpty(similarQuestions) ? 5 : 5 - similarQuestions.size();
        return ListUtil.sub(generateSimilarQuestions, 0, count);
    }

//    private List<String> formatAiMessage(String text) {
//        List<String> sentenceList = new ArrayList<>();
//        String[] split = text.split("\n");
//        for (String s : split) {
//            sentenceList.add(s.replaceAll("\\d+\\.\\s*", ""));
//        }
//        return sentenceList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
//    }

    private List<String> formatAiMessage(String text) {
        try {
            GenerateSimilarQuestionResp generateSimilarQuestionResp = JSON.parseObject(text, GenerateSimilarQuestionResp.class);
            return generateSimilarQuestionResp.getSentences().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("格式化AiMessage失败", e);
        }
        return Collections.emptyList();
    }

    @Data
    static class GenerateSimilarQuestionResp {

        private List<String> sentences;
    }

    @Override
    public List<FaqInfoEntity> listByIds(String tenantId, List<Long> ids) {
        return faqInfoManager.getByIds(tenantId, ids);
    }

    @Override
    public FaqInfoEntity getById(Long id) {
        return faqInfoManager.getById(id);
    }

    @Override
    public FaqInfoEntity getByUid(String tenantId, String uid) {
        FaqInfoEntity faqInfo = faqInfoManager.getByUid(tenantId, uid);

        // 如果是相似问题，找到标准问题记录
        if (FaqQuestionTypeEnum.SIMILAR.getValue().equals(faqInfo.getType())) {
            FaqInfoEntity faqStandard = faqInfoManager.getById(faqInfo.getStandardId());
            if (faqStandard != null) {
                faqInfo.setAnswers(faqStandard.getAnswers());
                faqInfo.setAnswerType(faqStandard.getAnswerType());
            }
        }
        return faqInfo;
    }

    @Override
    public Page<FaqInfoEntity> page(FaqPageDTO req) {
        Page<FaqInfoEntity> page = new Page<>(req.getPage(), req.getPageSize());
        if (CollectionUtils.isEmpty(req.getGroupIds()))
            return page;
        faqInfoManager.page(page,
                new LambdaQueryWrapper<FaqInfoEntity>()
                        .eq(FaqInfoEntity::getTenantId, req.getTenantId())
                        .eq(FaqInfoEntity::getType, FaqQuestionTypeEnum.STANDARD.getValue())
                        .eq(FaqInfoEntity::getDeleted, false)
                        .in(FaqInfoEntity::getGroupId, req.getGroupIds())
                        .eq(req.getEnable() != null, FaqInfoEntity::getEnable, req.getEnable())
                        .like(StringUtils.isNotBlank(req.getKeyword()), FaqInfoEntity::getQuestion, StringComUtils.replaceSqlEsc(req.getKeyword()))
                        .orderByDesc(FaqInfoEntity::getCreateTime)
                        .orderByDesc(FaqInfoEntity::getId)
        );

        return page;
    }

    @Override
    public List<FaqInfoEntity> listByStandardIds(List<Long> standardIds) {
        return faqInfoManager.listSimilarByStandardIds(standardIds);
    }

    @Override
    public void update(FaqSaveDTO faqSaveDTO) {
        Long id = faqSaveDTO.getId();
        LocalDateTime now = LocalDateTime.now();
        String tenantId = faqSaveDTO.getTenantId();
        String userId = faqSaveDTO.getUserId();
        // 校验要更新的FAQ
        FaqInfoEntity faqStandard = faqInfoManager.getById(id);
        if (faqStandard == null || faqStandard.getDeleted() || !Objects.equals(faqStandard.getTenantId(), tenantId)) {
            throw new ServiceException(FAQ_NOT_FOUND);
        }

        String standardQuestion = faqSaveDTO.getStandardQuestion();
        List<String> similarQuestions = faqSaveDTO.getSimilarQuestions();
        // 判断问题重复
        if (similarQuestions.contains(standardQuestion)) {
            throw new ServiceException(FAQ_QUESTION_DUPLICATE);
        }
        if (new HashSet<>(similarQuestions).size() < similarQuestions.size()) {
            throw new ServiceException(FAQ_QUESTION_DUPLICATE);
        }

        faqSaveDTO.setAnswers(iUtilService.moveContentUrlToDefault(faqSaveDTO.getTenantId(), faqSaveDTO.getId().toString(), faqSaveDTO.getAnswers(), StoragePathEnum.QA));
        iUtilService.deleteOldUrls(faqStandard.getAnswers(), faqSaveDTO.getAnswers());
        // 问题DUP校验
        List<String> questions = new ArrayList<>();
        questions.add(standardQuestion);
        questions.addAll(similarQuestions);
        checkQuestionDuplicate(tenantId, questions, id);

        // 统一删除现有的相似问题
        List<FaqInfoEntity> existingSimilarQuestions = faqInfoManager.listSimilarByStandardIds(Collections.singletonList(id));
        faqInfoManager.update(
                new LambdaUpdateWrapper<FaqInfoEntity>()
                        .eq(FaqInfoEntity::getStandardId, id)
                        .set(FaqInfoEntity::getDeleted, true)
        );
        List<String> deleteIds = existingSimilarQuestions.stream().map(FaqInfoEntity::getUid).collect(Collectors.toList());
        faqIBaseService.delete(tenantId, deleteIds);

        // 标准问题修改
        ibaseUpdate(tenantId, faqStandard, standardQuestion, faqSaveDTO.getEnable());
        faqStandard.setIsLlmEnhanced(faqSaveDTO.getIsLlmEnhanced());
        faqStandard.setAnswerType(faqSaveDTO.getAnswerType());
        faqStandard.setAnswers(faqSaveDTO.getAnswers());
        faqStandard.setEnable(faqSaveDTO.getEnable());
        faqStandard.setUpdateTime(now);
        faqStandard.setUpdateId(userId);
        faqInfoManager.updateById(faqStandard);

        // 重新新增相似问题
        List<FaqEmbeddingData> faqEmbeddingDataList = new ArrayList<>();
        Map<String, List<Double>> embedQuestionsMap = embedQuestions(similarQuestions);

        for (String similarQuestion : similarQuestions) {
            faqEmbeddingDataList.add(new FaqEmbeddingData()
                    .setTenantId(tenantId)
                    .setGroupId(faqStandard.getGroupId())
                    .setGroupPath(faqStandard.getGroupPath())
                    .setType(FaqQuestionTypeEnum.SIMILAR.getValue())
                    .setEnable(faqStandard.getEnable() ? 1 : 0)
                    .setQuestion(similarQuestion)
                    .setQuestionVector(embedQuestionsMap.get(similarQuestion))
                    .setCreateTime(DateUtil.format(now, NORM_DATETIME_PATTERN)));
        }

        Map<String, String> uidMap = ibaseSave(tenantId, faqEmbeddingDataList);
        for (String similarQuestion : similarQuestions) {
            FaqInfoEntity faqInfoEntitySimilar = new FaqInfoEntity();
            faqInfoEntitySimilar.setUid(uidMap.get(similarQuestion));
            faqInfoEntitySimilar.setTenantId(tenantId);
            faqInfoEntitySimilar.setGroupId(faqStandard.getGroupId());
            faqInfoEntitySimilar.setGroupPath(faqStandard.getGroupPath());
            faqInfoEntitySimilar.setType(FaqQuestionTypeEnum.SIMILAR.getValue());
            faqInfoEntitySimilar.setStandardId(faqStandard.getId());
            faqInfoEntitySimilar.setQuestion(similarQuestion);
            faqInfoEntitySimilar.setEnable(faqStandard.getEnable());
            faqInfoEntitySimilar.setDeleted(false);
            faqInfoEntitySimilar.setCreateTime(now);
            faqInfoEntitySimilar.setCreatorId(userId);
            faqInfoEntitySimilar.setUpdateTime(now);
            faqInfoEntitySimilar.setUpdateId(userId);
            faqInfoManager.save(faqInfoEntitySimilar);
        }
    }

    private void ibaseUpdate(String tenantId, FaqInfoEntity faqInfoEntity, String updateQuestion, Boolean enable) {
        if (!Objects.equals(faqInfoEntity.getQuestion(), updateQuestion)) {
            List<Double> vector = embeddingService.embedText(updateQuestion).content().vector();
            faqIBaseService.batchUpdate(tenantId, Collections.singletonList(
                    new FaqEmbeddingData()
                            .setUid(faqInfoEntity.getUid())
                            .setQuestion(updateQuestion)
                            .setQuestionVector(vector)
                            .setEnable(enable == null ? null : enable ? 1 : 0)
            ));
            faqInfoEntity.setQuestion(updateQuestion);
        }
    }

    private void checkQuestionDuplicate(String tenantId, List<String> questions, Long standardId) {
        List<FaqInfoEntity> faqList = faqInfoManager.listByQuestion(tenantId, questions);
        for (FaqInfoEntity faq : faqList) {
            if (FaqQuestionTypeEnum.STANDARD.getValue().equals(faq.getType()) && Objects.equals(faq.getId(), standardId)) {
                continue;
            }
            if (FaqQuestionTypeEnum.SIMILAR.getValue().equals(faq.getType()) && Objects.equals(faq.getStandardId(), standardId)) {
                continue;
            }
            throw new BaseException(FAQ_QUESTION_DUPLICATE);
        }
    }

    private void check(String tenantId, Collection<Long> ids) {
        int countStandardByIds = (int) faqInfoManager.countStandardByIds(tenantId, ids);
        if (countStandardByIds != ids.size()) {
            throw new ServiceException(INVALID_ARGUMENT, "选择问答对有误");
        }
    }

    private void checkAllisStandard(Collection<FaqInfoEntity> faqList) {
        if (faqList.stream().anyMatch(faqInfoEntity -> FaqQuestionTypeEnum.SIMILAR.getValue().equals(faqInfoEntity.getType()))) {
            throw new ServiceException(INVALID_ARGUMENT, " 选择问答对有误");
        }
    }

    @Override
    public void enable(String tenantId, List<Long> ids, Boolean enable) {
        List<FaqInfoEntity> faqListStandard = faqInfoManager.listByIds(ids);
        checkAllisStandard(faqListStandard);
        for (FaqInfoEntity faqInfo : faqListStandard) {
            if (CollectionUtils.isEmpty(faqInfo.getAnswers())) {
                throw new BaseException(FAQ_ANSWER_EMPTY);
            }
        }

        List<FaqInfoEntity> faqListSimilar = faqInfoManager.listSimilarByStandardIds(faqListStandard.stream().map(FaqInfoEntity::getId).collect(Collectors.toList()));

        List<Long> mergedIds = Stream.concat(faqListStandard.stream(), faqListSimilar.stream())
                .map(FaqInfoEntity::getId)
                .distinct()
                .collect(Collectors.toList());

        List<String> mergedUIds = Stream.concat(faqListStandard.stream(), faqListSimilar.stream())
                .map(FaqInfoEntity::getUid)
                .distinct()
                .collect(Collectors.toList());

        // 更新IBase
        List<FaqEmbeddingData> docs = new ArrayList<>();
        mergedUIds.forEach(uId -> docs.add(new FaqEmbeddingData().setUid(uId).setEnable(enable ? 1 : 0)));
        faqIBaseService.batchUpdate(tenantId, docs);
        // 更新DB
        faqInfoManager.update(
                new LambdaUpdateWrapper<FaqInfoEntity>()
                        .in(FaqInfoEntity::getId, mergedIds)
                        .set(FaqInfoEntity::getEnable, enable)
                        .set(FaqInfoEntity::getUpdateTime, new Date())
        );
    }

    @Override
    public void move(String tenantId, List<FaqInfoEntity> faqList, Long groupId) {
        checkAllisStandard(faqList);
        List<FaqInfoEntity> faqListSimilar = faqInfoManager.listSimilarByStandardIds(faqList.stream().map(FaqInfoEntity::getId).collect(Collectors.toList()));
        // 更新IBase
        List<String> mergedUIds = Stream.concat(faqList.stream(), faqListSimilar.stream())
                .map(FaqInfoEntity::getUid)
                .distinct()
                .collect(Collectors.toList());
        List<FaqEmbeddingData> docs = new ArrayList<>();
        mergedUIds.forEach(uId -> docs.add(new FaqEmbeddingData().setUid(uId).setGroupId(groupId)));
        faqIBaseService.batchUpdate(tenantId, docs);

        // 更新DB
        List<Long> mergedIds = Stream.concat(faqList.stream(), faqListSimilar.stream())
                .map(FaqInfoEntity::getId)
                .distinct()
                .collect(Collectors.toList());
        faqInfoManager.update(
                new LambdaUpdateWrapper<FaqInfoEntity>()
                        .in(FaqInfoEntity::getId, mergedIds)
                        .set(FaqInfoEntity::getGroupId, groupId)
                        .set(FaqInfoEntity::getGroupPath, "")
                        .set(FaqInfoEntity::getUpdateTime, new Date())
        );
    }

    @Override
    public void delete(List<FaqInfoEntity> faqList) {

        if (CollectionUtils.isEmpty(faqList))
            return;
        String tenantId = faqList.get(0).getTenantId();
        List<FaqInfoEntity> faqListSimilar = faqInfoManager.listSimilarByStandardIds(faqList.stream().map(FaqInfoEntity::getId).collect(Collectors.toList()));

        // 删除Ibase
        List<String> mergedUIds = Stream.concat(faqList.stream(), faqListSimilar.stream())
                .map(FaqInfoEntity::getUid)
                .distinct()
                .collect(Collectors.toList());

        faqIBaseService.delete(tenantId, mergedUIds);

        // 删除DB，软删
        List<Long> mergedIds = Stream.concat(faqList.stream(), faqListSimilar.stream())
                .map(FaqInfoEntity::getId)
                .distinct()
                .collect(Collectors.toList());
        faqInfoManager.update(
                new LambdaUpdateWrapper<FaqInfoEntity>()
                        .in(FaqInfoEntity::getId, mergedIds)
                        .set(FaqInfoEntity::getDeleted, true)
                        .set(FaqInfoEntity::getUpdateTime, new Date())
        );

        faqList.forEach(faq -> {
            iUtilService.deleteOldUrls(faq.getAnswers(), new ArrayList<>());
        });
    }

    @Override
    public FaqImportDTO importFaq(String tenantId, String userId, Long groupId, List<FaqInfoEntity> faqInfoEntityList) {
        List<String> importStandardQuestionList = faqInfoEntityList.stream().map(FaqInfoEntity::getQuestion).collect(Collectors.toList());
        List<String> duplicates = filterDuplicates(importStandardQuestionList);
        if (!duplicates.isEmpty()) {
            log.warn("重复的问题：{}", duplicates);
            return new FaqImportDTO(IMPORT_QUESTION_DUPLICATE, duplicates);
        }
        // 用LinkedHashMap保持顺序
        LinkedHashMap<String, FaqInfoEntity> entityMap = faqInfoEntityList.stream()
                .collect(Collectors.toMap(
                        FaqInfoEntity::getQuestion,
                        Function.identity(),
                        (v1, v2) -> v1,
                        LinkedHashMap::new
                ));
        List<FaqInfoEntity> existSimilarFaqs = faqInfoManager.selectOneBySimilarQuestion(tenantId, importStandardQuestionList);
        if (!CollectionUtils.isEmpty(existSimilarFaqs)) {
            return new FaqImportDTO(IMPORT_QUESTION_DUPLICATE_EXIST_SIMILAR, existSimilarFaqs.stream().map(FaqInfoEntity::getQuestion).collect(Collectors.toList()));
        }
        // 更新的
        List<FaqInfoEntity> faqList = faqInfoManager.listByQuestion(tenantId, entityMap.keySet());
        for (FaqInfoEntity faq : faqList) {
            FaqInfoEntity faqUpdateDTo = entityMap.get(faq.getQuestion());
            this.update(
                    new FaqSaveDTO()
                            .setId(faq.getId())
                            .setStandardQuestion(faqUpdateDTo.getQuestion())
                            .setIsLlmEnhanced(false)
                            .setAnswerType(faqUpdateDTo.getAnswerType())
                            .setAnswers(faqUpdateDTo.getAnswers())
                            .setTenantId(tenantId)
                            .setUserId(userId)
            );
            entityMap.remove(faq.getQuestion());
        }
        if (!CollectionUtils.isEmpty(faqList)) {
            this.move(tenantId, faqList, groupId);
        }
        // 新增的
        Collection<FaqInfoEntity> values = entityMap.values();
        for (FaqInfoEntity newFaq : values) {
            this.create(
                    new FaqSaveDTO()
                            .setGroupId(groupId)
                            .setStandardQuestion(newFaq.getQuestion())
                            .setIsLlmEnhanced(false)
                            .setAnswerType(newFaq.getAnswerType())
                            .setAnswers(newFaq.getAnswers())
                            .setEnable(true)
                            .setTenantId(tenantId)
                            .setUserId(userId)
            );
        }
        return null;
    }

    @Override
    public void batchImportFaq(String tenantId, String userId, Long groupId, List<FaqInfoEntity> faqInfoEntityList) {
        List<String> importStandardQuestionList = faqInfoEntityList.stream().map(FaqInfoEntity::getQuestion).collect(Collectors.toList());
        List<String> duplicates = filterDuplicates(importStandardQuestionList);
        //过滤重复的问题
        faqInfoEntityList.removeIf(faqInfoEntity -> duplicates.contains(faqInfoEntity.getQuestion()));
        //查询数据库中是否已经存在相似问题
        List<FaqInfoEntity> existSimilarFaqs = faqInfoManager.selectOneBySimilarQuestion(tenantId, importStandardQuestionList);
        if (!CollectionUtils.isEmpty(existSimilarFaqs)) {
            List<String> existQuestion = existSimilarFaqs.stream().map(FaqInfoEntity::getQuestion).collect(Collectors.toList());
            //过滤重复相似的问题
            faqInfoEntityList.removeIf(faqInfoEntity -> existQuestion.contains(faqInfoEntity.getQuestion()));
        }
        // 如果过滤后没有数据，直接返回
        if (faqInfoEntityList.isEmpty()) {
            return;
        }
        // 重新构建entityMap，用LinkedHashMap保持顺序
        LinkedHashMap<String, FaqInfoEntity> entityMap = faqInfoEntityList.stream()
                .collect(Collectors.toMap(
                        FaqInfoEntity::getQuestion,
                        Function.identity(),
                        (v1, v2) -> v1,
                        LinkedHashMap::new
                ));

        // 查询已存在的标准问题（用于更新）
        List<FaqInfoEntity> existingFaqs = faqInfoManager.listByQuestion(tenantId, entityMap.keySet());
        // 分离需要更新和新增的数据
        List<FaqInfoEntity> toUpdateList = new ArrayList<>();
        List<FaqInfoEntity> toCreateList = new ArrayList<>();
        for (FaqInfoEntity faq : faqInfoEntityList) {
            boolean exists = existingFaqs.stream()
                    .anyMatch(existing -> existing.getQuestion().equals(faq.getQuestion()));
            if (exists) {
                toUpdateList.add(faq);
            } else {
                toCreateList.add(faq);
            }
        }

        // 更新已存在的FAQ
        if (!toUpdateList.isEmpty()) {
            List<FaqSaveDTO> updateDTOs = toUpdateList.stream().map(faq ->
                    new FaqSaveDTO()
                            .setId(existingFaqs.stream()
                                    .filter(existing -> existing.getQuestion().equals(faq.getQuestion()))
                                    .findFirst().get().getId())
                            .setStandardQuestion(faq.getQuestion())
                            .setIsLlmEnhanced(false)
                            .setAnswerType(faq.getAnswerType())
                            .setAnswers(faq.getAnswers())
                            .setTenantId(tenantId)
                            .setUserId(userId)
            ).collect(Collectors.toList());
            // 批量更新
            updateDTOs.forEach(this::update);
            // 移动到指定分组
            this.move(tenantId, existingFaqs, groupId);
        }

        // 批量创建新的FAQ
        if (!toCreateList.isEmpty()) {
            List<FaqInfoEntity> newFaqList = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            for (FaqInfoEntity newFaq : toCreateList) {
                FaqInfoEntity faqEntity = new FaqInfoEntity();
                faqEntity.setTenantId(tenantId);
                faqEntity.setGroupId(groupId);
                faqEntity.setUid(userId);
                faqEntity.setType(FaqQuestionTypeEnum.STANDARD.getValue());
                faqEntity.setQuestion(newFaq.getQuestion());
                faqEntity.setIsLlmEnhanced(false);
                faqEntity.setAnswerType(newFaq.getAnswerType());
                faqEntity.setAnswers(newFaq.getAnswers());
                faqEntity.setGroupPath("");
                faqEntity.setEnable(true);
                faqEntity.setDeleted(false);
//                faqEntity.setResourceId(newFaq.getResourceId());
//                faqEntity.setSegmentInfo(newFaq.getSegmentInfo());
//                faqEntity.setStandardId(newFaq.getStandardId());
                faqEntity.setCreateTime(now);
                faqEntity.setCreatorId(userId);
                faqEntity.setUpdateTime(now);
                faqEntity.setUpdateId(userId);
                newFaqList.add(faqEntity);
            }
            try {
                // 使用MyBatis-Plus批量插入
                faqInfoManager.saveBatch(newFaqList, 1000);
            } catch (Exception e) {
                log.warn("批量导入FAQ失败, 将进行单条插入", e);
                for (FaqInfoEntity faqInfoEntity : newFaqList) {
                    try {
                        faqInfoManager.save(faqInfoEntity);
                    } catch (Exception ex) {
                        log.warn("单条导入FAQ失败, question: {}", faqInfoEntity.getQuestion(), ex);
                    }
                }
            }
        }
    }

    private List<String> filterDuplicates(List<String> input) {
        return input.stream()
                .collect(Collectors.groupingBy(s -> s, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    @Override
    public FaqSearchDTO search(String tenantId, String question, List<Long> groupIds, boolean isLike, double threshold) {

        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(question) || CollectionUtils.isEmpty(groupIds)) {
            return null;
        }
        List<Double> vector = embeddingService.embedText(question).content().vector();

//        List<String> groupPathList = new ArrayList<>();
//        for (Long groupId : groupIds) {
//            try {
//                groupPathList.add(knowledgeGroupService.getGroupPath(tenantId, groupId));
//            } catch (Exception ignore) {
//                // 因为组可能被删除，这里直接过滤掉
//            }
//        }
//        if (groupPathList.isEmpty()) {
//            return null;
//        }
//        groupPathList.replaceAll(s -> "group_path " + (isLike ? "like" : "=") + " '" + s + (isLike ? "%'" : "'"));
//        String filterStr = "enable = 1 and (" + String.join(" or ", groupPathList) + ")";

        String filterStr = "enable = 1 and group_id in (" + groupIds.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(",")) + ")";
        log.info("FAQ过滤：{}", filterStr);
        EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                .maxResults(1)
                .filter(filterStr)
                .exclude(Collections.singletonList("question_vector"))
                .matchQuery(MatchQuery.builder().field("question_vector")
                        .value(new EmbeddingValue(vector, threshold)).build())
                .build();
        EmbeddingSearchResult<FaqEmbeddingData> searchResult = faqIBaseService.search(tenantId, searchRequest);
        List<SearchHit<FaqEmbeddingData>> matches = searchResult.matches();
        if (CollectionUtils.isEmpty(matches)) {
            return null;
        }
        SearchHit<FaqEmbeddingData> searchHit = matches.get(0);
        FaqSearchDTO faqSearchDTO = new FaqSearchDTO();
        faqSearchDTO.setUId(searchHit.getDocument().getUid());
        faqSearchDTO.setScore(searchHit.getScore());
        faqSearchDTO.setQuestion(searchHit.getDocument().getQuestion());
        // 从DB找到命中的原始FAQ记录
        FaqInfoEntity faqInfo = faqInfoManager.getByUid(tenantId, searchHit.getDocument().getUid());
        if (faqInfo == null) {
            return null;
        }
        // 如果是相似问题，找到标准问题记录
        if (FaqQuestionTypeEnum.SIMILAR.getValue().equals(faqInfo.getType())) {
            faqInfo = faqInfoManager.getById(faqInfo.getStandardId());
        }
        faqSearchDTO.setAnswerType(faqInfo.getAnswerType());
        faqSearchDTO.setIsLlmEnhanced(faqInfo.getIsLlmEnhanced());
        // 从原始FAQ记录中随机获取一个答案
        List<String> answers = faqInfo.getAnswers();
        String answer = answers.get(new Random().nextInt(answers.size()));
        // 如果回答类型是纯文本，并开启大模型润色
        if (faqInfo.getAnswerType() == 0 && faqInfo.getIsLlmEnhanced()) {
            answer = answerRephrase(answer);
        }
        faqSearchDTO.setAnswer(answer);
        return faqSearchDTO;
    }

    private String answerRephrase(String answer) {
        try {
            List<ChatMessage> messageList = new ArrayList<>();
            SystemMessage systemMessage = SystemMessage.from(promptMappingConfig.getRephrase());
            messageList.add(systemMessage);

            UserMessage userMessage = UserMessage.from(answer);
            messageList.add(userMessage);
            Response<AiMessage> response = chatLanguageModel.generate(messageList);

            AiMessage aiMessage = response.content();
            return aiMessage.text();
        } catch (Exception e) {
            log.error("answerRephrase error", e);
        }
        return answer;
    }

    @Override
    public List<String> searchByEs(String tenantId, String question, List<Long> groupIds, Integer limit) {

//        List<String> groupPathList = new ArrayList<>();
//        for (Long groupId : groupIds) {
//            try {
//                groupPathList.add(knowledgeGroupService.getGroupPath(tenantId, groupId));
//            } catch (Exception ignore) {
//                // 因为组可能被删除，这里直接过滤掉
//            }
//        }
//        if (groupPathList.isEmpty()) {
//            return Collections.emptyList();
//        }
//        groupPathList.replaceAll(s -> "group_path like '" + s + "%'");
//        String filterStr = "enable = 1 and (" + String.join(" or ", groupPathList) + ")";
        String filterStr = "enable = 1 and group_id in (" + groupIds.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(",")) + ")";
        log.info("FAQ过滤：{}", filterStr);
        EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                .maxResults(limit * 2)
                .filter(filterStr)
                .include(Collections.singletonList("question"))
                .matchQuery(new MatchQuery("question", question.trim()))
                .build();
        EmbeddingSearchResult<FaqEmbeddingData> searchResult = faqIBaseService.search(tenantId, searchRequest);
        List<SearchHit<FaqEmbeddingData>> matches = searchResult.matches();
        if (CollectionUtils.isEmpty(matches)) {
            return Collections.emptyList();
        }
        return matches.stream().map(SearchHit::getDocument).map(FaqEmbeddingData::getQuestion).filter(s -> s.contains(question)).limit(limit).collect(Collectors.toList());
    }

    @Override
    public List<String> searchBySql(String tenantId, String question, List<Long> groupIds, Integer limit) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        List<FaqInfoEntity> faqInfoEntityList = faqInfoManager.selectByQuestionAndGroupIds(tenantId, question, groupIds, limit);
        return faqInfoEntityList.stream().map(FaqInfoEntity::getQuestion).collect(Collectors.toList());
    }

    @Override
    public List<FaqInfoEntity> needMerge() {
        return faqInfoManager.list(
                new LambdaQueryWrapper<FaqInfoEntity>()
                        .eq(FaqInfoEntity::getGroupId, 0)
                        .eq(FaqInfoEntity::getDeleted, false)
        );
    }

    @Override
    public void fixFaq() {
        List<FaqInfoEntity> faqInfoEntityList = faqInfoManager.list(
                new LambdaQueryWrapper<FaqInfoEntity>()
                        .eq(FaqInfoEntity::getType, 0)
        );
        for (FaqInfoEntity faqInfoEntity : faqInfoEntityList) {
            List<FaqInfoEntity> similarList = faqInfoManager.list(
                    new LambdaQueryWrapper<FaqInfoEntity>()
                            .eq(FaqInfoEntity::getStandardId, faqInfoEntity.getId())
            );
            String uid = faqInfoEntity.getUid();
            List<String> uIds = similarList.stream().map(FaqInfoEntity::getUid).collect(Collectors.toList());
            uIds.add(uid);
            if (faqInfoEntity.getDeleted()) {
                faqIBaseService.delete(faqInfoEntity.getTenantId(), uIds);
            } else {
                List<FaqEmbeddingData> collect = uIds.stream().map(s -> new FaqEmbeddingData().setUid(s).setEnable(faqInfoEntity.getEnable() ? 1 : 0)).collect(Collectors.toList());
                faqIBaseService.batchUpdate(faqInfoEntity.getTenantId(), collect);
            }
        }
    }

    @Override
    public Long getCount(String tenantId) {

        return faqInfoManager.getCount(tenantId);
    }

    @Override
    public void saveBatch(List<FaqInfoEntity> faqInfoEntities) {
        if (CollectionUtils.isEmpty(faqInfoEntities))
            return;
        faqInfoManager.saveBatch(faqInfoEntities);
    }

    @Override
    public List<FaqInfoEntity> listByGroupIds(List<Long> groupIds, Integer type) {
        return faqInfoManager.listByGroupIds(groupIds, type);
    }

    @Override
    public List<FaqSearchDTO> searchList(String tenantId, String question, List<Long> groupIds, Integer size, Double threshold) {

        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(question) || CollectionUtils.isEmpty(groupIds)) {
            return null;
        }
        List<KnowledgeGroupEntity> childrenList = knowledgeGroupService.getChildren(tenantId, KnowledgeTypeEnum.FAQ.getType(), groupIds);
        groupIds.addAll(childrenList.stream().map(KnowledgeGroupEntity::getId).collect(Collectors.toList()));
        List<Double> vector = embeddingService.embedText(question).content().vector();

//        List<String> groupPathList = new ArrayList<>();
//        for (Long groupId : groupIds) {
//            try {
//                groupPathList.add(knowledgeGroupService.getGroupPath(tenantId, groupId));
//            } catch (Exception ignore) {
//                // 因为组可能被删除，这里直接过滤掉
//            }
//        }
//        if (groupPathList.isEmpty()) {
//            return null;
//        }
//        groupPathList.replaceAll(s -> "group_path " + (isLike ? "like" : "=") + " '" + s + (isLike ? "%'" : "'"));
//        String filterStr = "enable = 1 and (" + String.join(" or ", groupPathList) + ")";

        String filterStr = "enable = 1 and group_id in (" + groupIds.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(",")) + ")";
        log.info("FAQ过滤：{}", filterStr);
        EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                .maxResults(size)
                .filter(filterStr)
                .exclude(Collections.singletonList("question_vector"))
                .matchQuery(MatchQuery.builder().field("question_vector")
                        .value(new EmbeddingValue(vector, threshold)).build())
                .build();
        EmbeddingSearchResult<FaqEmbeddingData> searchResult = faqIBaseService.search(tenantId, searchRequest);
        List<SearchHit<FaqEmbeddingData>> matches = searchResult.matches();
        if (CollectionUtils.isEmpty(matches)) {
            return new ArrayList<>();
        }
        // First batch fetch all needed FAQ info to reduce DB calls
        Set<String> uIds = matches.stream()
                .map(searchHit -> searchHit.getDocument().getUid())
                .collect(Collectors.toSet());

// Batch fetch all FAQ info in one query
        Map<String, FaqInfoEntity> faqInfoMap = faqInfoManager.getByUids(tenantId, uIds)
                .stream()
                .collect(Collectors.toMap(FaqInfoEntity::getUid, Function.identity()));

// Prepare standard question IDs for batch fetching
        List<Long> standardIds = faqInfoMap.values().stream()
                .filter(faq -> FaqQuestionTypeEnum.SIMILAR.getValue().equals(faq.getType()))
                .map(FaqInfoEntity::getStandardId)
                .collect(Collectors.toList());

// Batch fetch standard questions
        Map<Long, FaqInfoEntity> standardFaqMap = faqInfoManager.getByIds(tenantId,standardIds)
                .stream()
                .collect(Collectors.toMap(FaqInfoEntity::getId, Function.identity()));

// Initialize random once instead of per item
        Random random = new Random();

        List<FaqSearchDTO> collect = matches.stream()
                .map(searchHit -> {
                    String uid = searchHit.getDocument().getUid();
                    FaqInfoEntity faqInfo = faqInfoMap.get(uid);

                    if (faqInfo == null) {
                        return null;
                    }

                    // Handle similar questions
                    if (FaqQuestionTypeEnum.SIMILAR.getValue().equals(faqInfo.getType())) {
                        faqInfo = standardFaqMap.get(faqInfo.getStandardId());
                        if (faqInfo == null) {
                            return null;
                        }
                    }

                    FaqSearchDTO faqSearchDTO = new FaqSearchDTO();
                    faqSearchDTO.setUId(uid);
                    faqSearchDTO.setScore(searchHit.getScore());
                    faqSearchDTO.setQuestion(searchHit.getDocument().getQuestion());
                    faqSearchDTO.setAnswerType(faqInfo.getAnswerType());
                    faqSearchDTO.setIsLlmEnhanced(faqInfo.getIsLlmEnhanced());

                    List<String> answers = faqInfo.getAnswers();
                    String answer = answers.get(random.nextInt(answers.size()));

                    if (faqInfo.getAnswerType() == 0 && faqInfo.getIsLlmEnhanced()) {
                        answer = answerRephrase(answer);
                    }

                    faqSearchDTO.setAnswer(answer);
                    return faqSearchDTO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return collect;

    }

    @Override
    public void deleteByGroupIds(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return;
        }
        List<FaqInfoEntity> faqList = faqInfoManager.listByGroupIds(groupIds, 0);
        delete(faqList);
    }

    @Override
    public List<Long> listGroupIds(List<Long> ids) {
        return faqInfoManager.listByIds(ids).stream().map(FaqInfoEntity::getGroupId).collect(Collectors.toList());
    }

}
