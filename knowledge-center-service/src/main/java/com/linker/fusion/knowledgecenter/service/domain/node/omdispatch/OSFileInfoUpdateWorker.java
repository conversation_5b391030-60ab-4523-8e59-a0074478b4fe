package com.linker.fusion.knowledgecenter.service.domain.node.omdispatch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.FileInfoUpdateInput;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.FileInfoUpdateOutput;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.omos.client.config.WorkflowTaskContext;
import com.linker.omos.client.config.WorkflowTaskListener;
import com.linker.omos.client.config.WorkflowTaskMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@Slf4j
@WorkflowTaskMessageListener(taskDefName = "knowledge_center_file_info_update")
public class OSFileInfoUpdateWorker implements WorkflowTaskListener<FileInfoUpdateInput, FileInfoUpdateOutput> {

    @Resource
    IResourceService iResourceService;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public FileInfoUpdateOutput onTask(FileInfoUpdateInput input, WorkflowTaskContext workflowTaskContext) {
        RLock lock = redissonClient.getLock("knowledge_center:file_info_update:" + input.getDocId());
        try {
            lock.lock();
            if (Objects.nonNull(input.getSize()) ||
                    Objects.nonNull(input.getTime()) ||
                    StringUtils.isNotBlank(input.getFormat()) ||
                    Objects.nonNull(input.getWidth()) ||
                    Objects.nonNull(input.getHeight()) ||
                    StringUtils.isNotBlank(input.getAspectRatio()) ||
                    Objects.nonNull(input.getRate()) ||
                    Objects.nonNull(input.getFps())
            ) {
                KnowledgeResourceEntity res = iResourceService.getNotNull(input.getDocId());
                JSONObject extInfo = JSONObject.parseObject(res.getExtInfo());
                if (Objects.nonNull(input.getSize()))
                    extInfo.put("sys_size", input.getSize());
                if (Objects.nonNull(input.getTime()))
                    extInfo.put("sys_time", input.getTime());
                if (StringUtils.isNotBlank(input.getFormat()))
                    extInfo.put("sys_format", input.getFormat());
                if (Objects.nonNull(input.getWidth()))
                    extInfo.put("sys_width", input.getWidth());
                if (Objects.nonNull(input.getHeight()))
                    extInfo.put("sys_height", input.getHeight());
                if (StringUtils.isNotBlank(input.getAspectRatio()))
                    extInfo.put("sys_aspectRatio", input.getAspectRatio());
                if (Objects.nonNull(input.getRate()))
                    extInfo.put("sys_rate", input.getRate());
                if (Objects.nonNull(input.getFps()))
                    extInfo.put("sys_fps", input.getFps());
                iResourceService.updateExtInfo(input.getDocId(), JSON.toJSONString(extInfo));
            }
            if (Objects.nonNull(input.getSize()))
                iResourceService.updateSize(input.getDocId(), input.getSize());
            if (Objects.nonNull(input.getStrategy()))
                iResourceService.updateStrategy(input.getDocId(), JSON.toJSONString(input.getStrategy()));
            if (StringUtils.isNotBlank(input.getPreviewUrl()))
                iResourceService.updatePreviewUrl(input.getDocId(), input.getPreviewUrl());
            if (StringUtils.isNotBlank(input.getThumbnail()))
                iResourceService.updateThumbnail(input.getDocId(), input.getThumbnail());
            if (StringUtils.isNotBlank(input.getPreviewUrl())) {
                iResourceService.updatePreviewUrl(input.getDocId(), input.getPreviewUrl());
            }
            return new FileInfoUpdateOutput();
        } finally {
            lock.unlock();
        }
    }
}
