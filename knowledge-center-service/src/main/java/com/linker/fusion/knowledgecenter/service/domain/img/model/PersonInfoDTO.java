package com.linker.fusion.knowledgecenter.service.domain.img.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class PersonInfoDTO {

    @ApiModelProperty("人物名称")
    private String name;

    @ApiModelProperty("1、常规人物；2、敏感人物")
    private Integer type = 1;

    @ApiModelProperty("敏感分类ID")
    private Long sensitiveId = 0L;

    @ApiModelProperty(value = "敏感分类名称")
    private String sensitiveName;

    @ApiModelProperty("是否重点人物")
    private Boolean isFocus = false;

    @ApiModelProperty("描述信息")
    private String description;

    /**
     * 人脸库维护数组
     */
    private List<PersonInfoDTO.PhotoMapResp> photos = new ArrayList<>();


    /**
     * 人脸信息详情
     */
    @Data
    public static class PhotoMapResp {
        /**
         * 地址
         */
        private String url;

        /**
         * Checked
         */
        private Boolean checked = true;
    }
}
