package com.linker.fusion.knowledgecenter.service.domain;

import com.google.common.collect.Lists;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.IdeServerClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.person.PromptResp;
import com.linker.fusion.knowledgecenter.infrastructure.common.SplitImageUrlUtil;
import com.linker.fusion.knowledgecenter.infrastructure.dto.data.PromptInfoDTO;
import com.linker.fusion.knowledgecenter.infrastructure.dto.data.VariableDTO;
import com.linker.fusion.knowledgecenter.service.domain.common.ImageContentBuilder;
import com.linker.omagent.core.data.message.*;
import com.linker.omagent.core.data.output.Response;
import com.linker.omagent.omos.model.OmChatLanguageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PromptService {

    @Resource
    private OmChatLanguageModel llmModel;
    @Resource
    private ImageContentBuilder imageContentBuilder;
    @Resource
    IdeServerClient ideServerClient;

    /**
     * 运行并获取最新提示词
     *
     * @param id         资源id
     * @param promptInfo 提示词列表
     * @param model      模型
     * @return 运行结果
     */
    public String runNewPrompt(String id, PromptInfoDTO promptInfo, String model) {
        return runPrompt(id, readNewPrompt(promptInfo), model);
    }

    /**
     * 运行提示词
     *
     * @param id         资源id
     * @param promptInfo 提示词列表
     * @param model      模型
     * @return 运行结果
     */
    public String runPrompt(String id, PromptInfoDTO promptInfo, String model) {
        log.info("runPrompt: id={}, prompt={}", id, promptInfo);
        // 解析提示词列表并校验
        List<PromptResp.PromptDto> promptDtoList = com.alibaba.fastjson.JSON.parseArray(promptInfo.getPrompts(), PromptResp.PromptDto.class);
        if (CollectionUtils.isEmpty(promptDtoList)) {
            return null;
        }
        // 构建变量映射表
        Map<String, Object> variableMap = buildVariableMap(promptInfo);
        // 组装按角色分类的提示词内容
        LinkedList<PromptResp.PromptContentDto> promptContents = buildPromptContents(promptDtoList, variableMap);
        // 构建聊天消息列表
        List<ChatMessage> chatMessages = buildChatMessages(promptContents);
        // 调用LLM生成响应
        Response<AiMessage> response = generateAiResponse(chatMessages, model);
        // 处理响应结果
        return processResponse(response, id);
    }

    /**
     * 构建变量映射表（变量名 -> 变量值）
     */
    private Map<String, Object> buildVariableMap(PromptInfoDTO promptInfo) {
        if (CollectionUtils.isEmpty(promptInfo.getReqs())) {
            return Collections.emptyMap();
        }
        Map<String, Object> variableMap = new LinkedHashMap<>();
        for (VariableDTO variable : promptInfo.getReqs()) {
            String variableKey = "{{" + variable.getName() + "}}";
            // 直接赋值类型变量
            variableMap.put(variableKey, variable.getValue());
        }
        return variableMap;
    }


    /**
     * 组装按角色（SYSTEM/USER/ASSISTANT）分类的提示词内容
     */
    private LinkedList<PromptResp.PromptContentDto> buildPromptContents(List<PromptResp.PromptDto> promptDtoList, Map<String, Object> variableMap) {
        LinkedList<PromptResp.PromptContentDto> promptContents = new LinkedList<>();
        // 初始化SYSTEM角色容器（始终放在首位）
        promptContents.add(createPromptContent("SYSTEM", new LinkedList<>()));

        for (PromptResp.PromptDto prompt : promptDtoList) {
            String role = prompt.getRole();
            String content = prompt.getContent();

            // 跳过空角色或空内容的提示词
            if (StringUtils.isBlank(role) || StringUtils.isBlank(content)) {
                continue;
            }

            // 替换内容中的变量
            content = replaceVariables(content, variableMap);

            if ("SYSTEM".equals(role)) {
                // SYSTEM角色内容追加到首个容器
                promptContents.getFirst().getContents().add(content);
            } else {
                // 其他角色内容追加到最后一个同角色容器或新建容器
                addContentToLastRoleContainer(promptContents, role, content);
            }
        }
        return promptContents;
    }

    /**
     * 替换内容中的变量占位符
     */
    private String replaceVariables(String content, Map<String, Object> variableMap) {
        if (MapUtils.isEmpty(variableMap)) {
            return content;
        }
        String processedContent = content;
        for (Map.Entry<String, Object> entry : variableMap.entrySet()) {
            // 风险点：entry.getValue()可能为null，调用toString()会抛出NullPointerException
            processedContent = processedContent.replaceAll(Pattern.quote(entry.getKey()),
                    entry.getValue() != null ? entry.getValue().toString() : "");
        }
        return processedContent;
    }

    /**
     * 将内容添加到最后一个同角色的容器中，无则新建
     */
    private void addContentToLastRoleContainer(LinkedList<PromptResp.PromptContentDto> promptContents, String role, String content) {
        // 查找最后一个同角色容器
        Optional<PromptResp.PromptContentDto> lastRoleContainer = promptContents.stream()
                .filter(container -> role.equals(container.getRole()))
                .reduce((first, second) -> second);

        if (lastRoleContainer.isPresent()) {
            lastRoleContainer.get().getContents().add(content);
        } else {
            // 新建角色容器
            promptContents.add(createPromptContent(role, Lists.newLinkedList(Collections.singleton(content))));
        }
    }

    /**
     * 创建提示词内容容器
     */
    private PromptResp.PromptContentDto createPromptContent(String role, LinkedList<String> contents) {
        PromptResp.PromptContentDto contentDto = new PromptResp.PromptContentDto();
        contentDto.setRole(role);
        contentDto.setContents(contents);
        return contentDto;
    }

    /**
     * 构建聊天消息列表
     */
    private List<ChatMessage> buildChatMessages(LinkedList<PromptResp.PromptContentDto> promptContents) {
        List<ChatMessage> messageList = new ArrayList<>();

        for (PromptResp.PromptContentDto contentDto : promptContents) {
            String role = contentDto.getRole();
            LinkedList<String> contents = contentDto.getContents();

            if (CollectionUtils.isEmpty(contents)) {
                continue;
            }

            switch (role) {
                case "SYSTEM":
                    messageList.add(new SystemMessage(String.join(";", contents)));
                    break;
                case "USER":
                    messageList.add(UserMessage.from(buildUserContents(contents)));
                    break;
                case "ASSISTANT":
                    messageList.add(new AiMessage(String.join(";", contents)));
                    break;
            }
        }
        return messageList;
    }

    /**
     * 构建用户消息内容（处理文本和图片）
     */
    private List<Content> buildUserContents(LinkedList<String> contents) {
        List<Content> userContents = new LinkedList<>();

        for (String content : contents) {
            List<Map<String, String>> urlMaps = SplitImageUrlUtil.splitToMapToList(content);
            for (Map<String, String> urlMap : urlMaps) {
                if (MapUtils.isEmpty(urlMap)) {
                    continue;
                }
                urlMap.forEach((key, value) -> {
                    if (StringUtils.isNotBlank(key)) {
                        if ("text".equals(value)) {
                            userContents.add(new TextContent(key));
                        } else if ("image".equals(value)) {
                            userContents.add(imageContentBuilder.build(key));
                        }
                    }
                });
            }
        }
        return userContents;
    }

    /**
     * 调用LLM模型生成响应
     */
    private Response<AiMessage> generateAiResponse(List<ChatMessage> messageList, String model) {
        if (StringUtils.isBlank(model)) {
            log.error("模型名称不能为空");
            return null;
        }

        ChatModelConfig config = new ChatModelConfig();
        config.setTemperature(0.1);
        config.setModelName(model);

        try {
            // 添加超时控制和异常捕获
            return llmModel.generate(config, messageList);
        } catch (Exception e) {
            log.error("LLM模型调用失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理LLM响应结果（提取JSON、清理格式、记录日志）
     */
    private String processResponse(Response<AiMessage> response, String id) {
        if (response == null || response.content() == null) {
            log.warn("runPrompt: id={} 未获取到响应结果", id);
            return null;
        }

        long startTime = System.currentTimeMillis();
        String text = response.content().text();
        // 避免空指针：先检查text是否为null
        if (text != null) {
            text = text.replaceAll("```", StringUtils.EMPTY)
                    .replaceAll("json", StringUtils.EMPTY)
                    .replaceAll("\n", StringUtils.EMPTY);

            // 优化正则表达式，支持嵌套JSON结构
            if (StringUtils.isNotBlank(text)) {
                Matcher matcher = Pattern.compile("(?s)(\\{.*}|\\[.*])").matcher(text);
                if (matcher.find()) {
                    text = matcher.group(1);
                }
            }
        } else {
            text = StringUtils.EMPTY;
        }

        log.info("runPrompt: id={}, text={}, costTime={}ms",
                id, text, System.currentTimeMillis() - startTime);
        return text;
    }


    /**
     * 获取最新的提示词
     *
     * @param olds 旧提示词
     * @return 返回新的提示词
     */
    public List<PromptInfoDTO> readNewPrompt(List<PromptInfoDTO> olds) {
        if (CollectionUtils.isEmpty(olds)) {
            return Collections.emptyList();
        }
        //查询提示词
        List<PromptInfoDTO> newPrompts = new ArrayList<>();
        List<String> promptKeys = olds.stream().map(PromptInfoDTO::getPromptKey).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(promptKeys)) {
            log.info("开始获取提示词信息 promptKeys={}", com.alibaba.fastjson.JSON.toJSONString(promptKeys));
            BaseResp<List<PromptResp>> baseResp = ideServerClient.promptGetList(1, promptKeys);
            List<PromptResp> dataList = baseResp.getData();
            if (CollectionUtils.isNotEmpty(dataList)) {
                log.info("获取提示词信息结果 dataLis={}", com.alibaba.fastjson.JSON.toJSONString(dataList));
                olds.forEach(promptInfo -> {
                    Optional<PromptResp> opPromptResp = dataList.stream().filter(d -> d.getPromptKey().equals(promptInfo.getPromptKey())).findFirst();
                    if (!opPromptResp.isPresent()) {
                        return;
                    }
                    if (CollectionUtils.isEmpty(promptInfo.getReqs())) {
                        promptInfo.setReqs(new ArrayList<>());
                    }
                    PromptResp promptResp = opPromptResp.get();
                    promptInfo.setPrompts(promptResp.getPrompts());
                    if (StringUtils.isNotBlank(promptResp.getVariables())) {
                        List<VariableDTO> variableDtos = com.alibaba.fastjson.JSON.parseArray(promptResp.getVariables(), VariableDTO.class);
                        for (VariableDTO variableDto : variableDtos) {
                            if (promptInfo.getReqs().stream().anyMatch(r -> r.getName().equals(variableDto.getName()))) {
                                continue;
                            }
                            promptInfo.getReqs().add(variableDto);
                        }
                    }
                    newPrompts.add(promptInfo);

                });
            }
        }
        return newPrompts;
    }

    /**
     * 获取最新的提示词
     *
     * @param old 旧提示词
     * @return 返回新的提示词
     */
    public PromptInfoDTO readNewPrompt(PromptInfoDTO old) {
        List<PromptInfoDTO> newPrompts = readNewPrompt(Collections.singletonList(old));
        if (CollectionUtils.isNotEmpty(newPrompts)) {
            return newPrompts.get(0);
        }
        return null;
    }
}
