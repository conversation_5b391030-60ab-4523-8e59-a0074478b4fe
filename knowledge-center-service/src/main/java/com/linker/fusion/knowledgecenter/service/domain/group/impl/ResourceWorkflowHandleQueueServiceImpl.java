package com.linker.fusion.knowledgecenter.service.domain.group.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunReq;
import com.linker.fusion.knowledgecenter.infrastructure.dto.KnowledgeIndexTaskDTO;
import com.linker.fusion.knowledgecenter.service.domain.group.IResourceWorkflowHandleQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025年09月18日
 * @version: 1.0
 * @description: TODO
 */
@Service
@Slf4j
public class ResourceWorkflowHandleQueueServiceImpl implements IResourceWorkflowHandleQueueService {

    @Resource
    ResourceQueueManager resourceQueueManager;


    @Override
    public Boolean addIndexTaskForResource(Long knowledgeResourceId, Integer nextStatus, WorkflowAsyncRunReq workflowAsyncRunReq) {
        if (ObjectUtil.isNull(workflowAsyncRunReq) || ObjectUtil.isNull(knowledgeResourceId)) {
            return false;
        }
        KnowledgeIndexTaskDTO knowledgeIndexTaskDTO = new KnowledgeIndexTaskDTO(knowledgeResourceId, nextStatus, workflowAsyncRunReq);

        // 资源id作为key，索引任务作为value
        return resourceQueueManager.pushIndexJob(knowledgeResourceId.toString(), JSON.toJSONString(knowledgeIndexTaskDTO));

    }
}
