package com.linker.fusion.knowledgecenter.service.domain.retriever.advanced;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.Meta;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public abstract class AbstractAdvancedRetriever {

    public void buildHighlight(JSONObject highlight, Meta meta) {
        if (highlight == null) {
            return;
        }

        // 取 title 高亮（两个字段合并去重）
        List<String> titleHighlights = Stream.of(
                        Optional.ofNullable(highlight.getJSONArray("file_title.keyword"))
                                .orElse(new JSONArray()),
                        Optional.ofNullable(highlight.getJSONArray("file_title"))
                                .orElse(new JSONArray()))
                .flatMap(Collection::stream)
                .distinct()
                .map(Object::toString)
                .collect(Collectors.toList());

        // 取 content 高亮
        List<String> contentHighlights = Optional.ofNullable(highlight.getJSONArray("content"))
                .map(arr -> arr.stream().map(Object::toString).collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        meta.setHighlightTitle(titleHighlights.isEmpty() ? null : titleHighlights);
        meta.setHighlightContent(contentHighlights.isEmpty() ? null : contentHighlights);
    }
}
