package com.linker.fusion.knowledgecenter.service.domain.resource.extendData;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceFaceEntity;

import java.util.List;
import java.util.Set;

public interface IResourceFaceService {

    void save(ResourceFaceEntity entity);

    void saveBatch(List<ResourceFaceEntity> entityList);

    Set<String> getDocIdsByFaceIds(List<String> faceIds);

    List<ResourceFaceEntity> list(String docId, String segmentId);

    List<ResourceFaceEntity> listByDocIdAndFaceId(List<String> docIds, String faceId);

    void deleteByDocId(String docId);

    void clearByDocId(String docId);

    List<String> getLabelsBySegmentId(String docId, String segmentId);

    /**
     * @description: 查询人脸列表
     * @author: heji<PERSON><PERSON>
     * @date: 2025/7/25 09:12
     * @param:
     * @param: null
     * @return:
     * @return: null
     **/
    List<ResourceFaceEntity> listResourceFace(List<String> docIdList, String name);

    /**
     * 分页查询人脸列表
     *
     * @param page      分页参数
     * @param docIdList 文档ID列表
     * @param name      人脸名称
     * @return 分页结果
     */
    IPage<ResourceFaceEntity> pageResourceFace(Page<ResourceFaceEntity> page, List<String> docIdList, String name);
    /**
     * 更新人脸名称
     *
     * @param id   人脸ID
     * @param name 人脸名称
     */
    void updateName(String id, String name);
}