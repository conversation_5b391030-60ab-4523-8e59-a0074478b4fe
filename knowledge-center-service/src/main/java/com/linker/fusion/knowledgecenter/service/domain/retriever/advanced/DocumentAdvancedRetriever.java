package com.linker.fusion.knowledgecenter.service.domain.retriever.advanced;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexSuffixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.ChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexTenantMappingManager;
import com.linker.fusion.knowledgecenter.service.domain.EmbeddingService;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.KnowledgeConfig;
import com.linker.fusion.knowledgecenter.service.domain.retriever.MetaFilterUtil;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.Meta;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.QueryParams;
import com.linker.omagent.core.rag.content.retriever.Content;
import com.linker.omagent.core.rag.content.retriever.ContentRetriever;
import com.linker.omagent.core.rag.query.Query;
import com.linker.omagent.core.repository.embedding.*;
import com.linker.omagent.core.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DocumentAdvancedRetriever extends AbstractAdvancedRetriever implements ContentRetriever {

    @Autowired
    private EmbeddingRepository embeddingStore;
    @Autowired
    private IIndexTenantMappingManager indexTenantMappingManager;
    @Autowired
    private EmbeddingService embeddingService;

    private static String buildFilter(QueryParams extra, boolean isEmbedding) {
        KnowledgeConfig knowledgeConfig = extra.getKnowledgeConfig();
        List<Long> docGroupIds = knowledgeConfig.getDocGroupIds();
        StringBuilder filter = new StringBuilder(String.format("enable = 1 and tenant_id = '%s' ",
                extra.getTenantId()
        ));
        if (!isEmbedding) {
            filter.append("and `src_type` = 'text' ");
        }

        List<String> docIds = extra.getDocIds();
        //如果docGroupIds和extra.getDocIds()都不为空的情况下，and (group_id in ("xx") or doc_id in ("xx"))
        MetaFilterUtil.builderKnowledgeFilter(docGroupIds, docIds, filter);
        if (!ObjectUtils.isEmpty(extra.getNotResIds())) {
            String resourceIdsStr = extra.getNotResIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id not in (").append(resourceIdsStr).append(")");
        }
        MetaFilterUtil.buildFilter(filter, extra, FileTypeEnum.DOCUMENT);
        return filter.toString();
    }

    @Override
    public List<Content> retrieve(Query query) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        if (!CollectionUtils.containsAny(extra.getFileTypes(), FileTypeEnum.DOCUMENT.getType())) {
            return Collections.emptyList();
        }
        long l = System.currentTimeMillis();
        boolean embeddedRetrieve = StringUtils.isNotBlank(extra.getPicUrl()) || StringUtils.isNotBlank(extra.getEmbeddedKeyword());
        EmbeddingSearchResult<ChunkEntity> searchResult;
        String filter = buildFilter(extra, embeddedRetrieve);
        log.debug("检索条件:{}", filter);
        String tenantId = extra.getTenantId();
        String index = indexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.DOCUMENT) + IndexSuffixEnum.DOCUMENT_CHUNK.suffix;

        EmbeddingSearchRequest vectorSearch = EmbeddingSearchRequest
                .builder()
                .indexId(index)
                .include(Lists.newArrayList("file_title", "content", "segment_id", "doc_id", "chunk_id"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), embeddedRetrieve ? 100 : 1000))
                .filter(filter)
                .build();

        if (embeddedRetrieve) {
            List<Double> vector = embeddingService.embedImageAndText(extra.getPicUrl(), extra.getEmbeddedKeyword());
            log.info("向量化耗时:{}", System.currentTimeMillis() - l);
            MatchQuery<Object> matchQuery = MatchQuery.builder().key("content_vector").value(new EmbeddingValue(vector, extra.getThreshold())).build();
            vectorSearch.setMatchQuery(matchQuery);
        }
        if (StringUtils.isNotBlank(query.getInput())) {
            FilterExtendUtil.buildFileTitleAndContent(vectorSearch, query.getInput());
        }
        searchResult = this.embeddingStore.search(vectorSearch,
                new TypeReference<EmbeddingSearchResult<ChunkEntity>>() {
                });

        log.info("检索器：{},检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));
        return searchResult.matches().stream().map(hit -> {
            ChunkEntity document = hit.getDocument();
            Meta meta = new Meta();
            meta.setDocId(document.getDocId());
            meta.setSegmentId(document.getSegmentId());
            meta.setChunkId(document.getChunkId());
            meta.setTitle(document.getTitle());
            meta.setContent(document.getContent());
            //     高亮
            buildHighlight(hit.getHighlight(), meta);
            meta.setScore(hit.getScore());
            meta.setNeedRerank(!embeddedRetrieve);
            meta.setRetriever(description());
            return new Content(document.getSegmentId(), FileTypeEnum.DOCUMENT.getValue(), meta);
        }).collect(Collectors.toList());
    }

    @Override
    public String description() {
        return "文档高级检索器";
    }

    @Override
    public String type() {
        return "advanced";
    }
}
