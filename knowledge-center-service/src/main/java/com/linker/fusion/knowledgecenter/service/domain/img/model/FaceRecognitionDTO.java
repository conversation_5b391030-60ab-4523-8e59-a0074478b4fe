package com.linker.fusion.knowledgecenter.service.domain.img.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class FaceRecognitionDTO {

    @ApiModelProperty("输入的URL")
    private String url;

    @ApiModelProperty("人脸信息")
    private List<FaceInfoDTO> faceInfos;

}
