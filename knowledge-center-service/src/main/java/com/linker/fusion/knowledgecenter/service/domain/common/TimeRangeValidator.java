package com.linker.fusion.knowledgecenter.service.domain.common;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 时间段校验工具类
 * 支持时间格式校验、时间先后顺序校验、当前时间是否在指定时间段内判断
 */
public class TimeRangeValidator {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    /**
     * 校验时间格式是否正确
     *
     * @param timeStr 时间字符串，格式：HH:mm
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidTimeFormat(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return false;
        }
        try {
            LocalTime.parse(timeStr.trim(), TIME_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 校验时间段参数（格式和先后顺序）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 校验结果
     */
    public static ValidationResult validateTimeRange(String startTime, String endTime) {
        // 1. 校验格式
        if (!isValidTimeFormat(startTime)) {
            return ValidationResult.error("开始时间格式不正确，请使用HH:mm格式（如：09:03）");
        }
        if (!isValidTimeFormat(endTime)) {
            return ValidationResult.error("结束时间格式不正确，请使用HH:mm格式（如：20:56）");
        }

        // 2. 解析时间
        LocalTime start = LocalTime.parse(startTime.trim(), TIME_FORMATTER);
        LocalTime end = LocalTime.parse(endTime.trim(), TIME_FORMATTER);

        // 3. 校验先后顺序（注意：这里允许跨天的情况，如22:00-06:00）
        // 如果开始时间等于结束时间，认为是无效的
        if (start.equals(end)) {
            return ValidationResult.error("开始时间和结束时间不能相同");
        }
        if (start.isAfter(end)) {
            return ValidationResult.error("开始时间不能晚于结束时间");
        }

        return ValidationResult.success("时间段参数校验通过");
    }

    /**
     * 判断当前时间是否在指定时间段内
     *
     * @param startTime 开始时间（HH:mm格式）
     * @param endTime   结束时间（HH:mm格式）
     * @return true-在时间段内，false-不在时间段内
     */
    public static boolean isCurrentTimeInRange(String startTime, String endTime) {
        // 先校验参数
        ValidationResult validation = validateTimeRange(startTime, endTime);
        if (!validation.isValid()) {
            throw new IllegalArgumentException(validation.getMessage());
        }
        LocalTime start = LocalTime.parse(startTime.trim(), TIME_FORMATTER);
        LocalTime end = LocalTime.parse(endTime.trim(), TIME_FORMATTER);
        LocalTime now = LocalTime.now();
        return !now.isBefore(start) && !now.isAfter(end);
    }


    /**
     * 校验结果类
     */
    @AllArgsConstructor
    @Data
    public static class ValidationResult {
        private final boolean valid;
        private final String message;

        public static ValidationResult success(String message) {
            return new ValidationResult(true, message);
        }

        public static ValidationResult error(String message) {
            return new ValidationResult(false, message);
        }

    }

}
