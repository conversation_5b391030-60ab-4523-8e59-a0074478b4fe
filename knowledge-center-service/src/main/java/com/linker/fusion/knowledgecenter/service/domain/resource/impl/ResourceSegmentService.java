package com.linker.fusion.knowledgecenter.service.domain.resource.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.core.base.exception.BusinessException;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.config.PromptMappingConfig;
import com.linker.fusion.knowledgecenter.infrastructure.dto.data.VideoSummaryDO;
import com.linker.fusion.knowledgecenter.infrastructure.entity.*;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.StoragePathEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.TerminologyTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.AudioChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.ChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.VideoChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IResourceSegmentManager;
import com.linker.fusion.knowledgecenter.infrastructure.model.DocStrategyModel;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.service.domain.EmbeddingService;
import com.linker.fusion.knowledgecenter.service.domain.common.IUtilService;
import com.linker.fusion.knowledgecenter.service.domain.common.ImageContentBuilder;
import com.linker.fusion.knowledgecenter.service.domain.img.ImgService;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.EventAIResp;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.SegmentAssItem;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.SummaryItem;
import com.linker.fusion.knowledgecenter.service.domain.node.omdispatch.model.VideoFrameAssItem;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IAudioChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.chunk.IVideoChunkIBaseServiceResource;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceSegmentService;
import com.linker.omagent.core.data.document.splitter.DocumentSplitter;
import com.linker.omagent.core.data.document.splitter.DocumentSplitters;
import com.linker.omagent.core.data.message.*;
import com.linker.omagent.core.data.output.Response;
import com.linker.omagent.core.model.Tokenizer;
import com.linker.omagent.core.rag.query.Segment;
import com.linker.omagent.omos.model.OmChatLanguageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum.NOT_EXIST;

@Service
@Slf4j
public class ResourceSegmentService implements IResourceSegmentService {

    @Resource
    IResourceSegmentManager resourceSegmentManager;
    @Resource
    ImgService imgService;
    @Resource
    OmChatLanguageModel omChatLanguageModel;
    @Resource
    PromptMappingConfig promptMappingConfig;
    @Resource
    IVideoChunkIBaseServiceResource iVideoChunkIBaseService;
    @Resource
    EmbeddingService embeddingService;
    @Resource
    IAudioChunkIBaseServiceResource iAudioChunkIBaseService;
    @Resource
    IUtilService iUtilService;
    @Value("${document.image.encode:false}")
    private Boolean imageEncode;

    @Resource
    private ImageContentBuilder imageContentBuilder;

    @Override
    public void save(ResourceSegmentEntity entity) {
        resourceSegmentManager.save(entity);
    }

    @Override
    public void saveBatch(List<ResourceSegmentEntity> entities) {
        resourceSegmentManager.saveBatch(entities);
    }

    @Override
    public void updateBatch(List<ResourceSegmentEntity> entities) {
        resourceSegmentManager.updateBatchById(entities);
    }

    @Override
    public Page<ResourceSegmentEntity> page(long page, long pageSize, String docId, FileTypeEnum fileTypeEnum, String content, List<Integer> status) {
        return resourceSegmentManager.page(page, pageSize, docId, fileTypeEnum, content, status);
    }

    @Override
    public ResourceSegmentEntity getNotNull(String segmentId) {
        ResourceSegmentEntity resourceSegment = resourceSegmentManager.get(segmentId);
        if (Objects.isNull(resourceSegment))
            throw new ServiceException(NOT_EXIST, "分段");
        return resourceSegment;

    }

    @Override
    public ResourceSegmentEntity get(String segmentId) {
        return resourceSegmentManager.get(segmentId);
    }

    @Override
    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    @CacheRefresh(refresh = 53, stopRefreshAfterLastAccess = 3600)
    public List<ResourceSegmentEntity> getSementList(List<String> segmentIds) {
        return resourceSegmentManager.listBySegmentIds(segmentIds);
    }

    @Override
    @Cached(expire = 120, cacheType = CacheType.LOCAL)
    @CacheRefresh(refresh = 57, stopRefreshAfterLastAccess = 7 * 72 * 3600)
    public ResourceSegmentEntity getCacheSegment(String segmentId) {
        return resourceSegmentManager.get(segmentId);
    }

    @Override
    public void updateById(ResourceSegmentEntity entity) {
        resourceSegmentManager.updateById(entity);
    }


    @Override
    public void deleteByDocId(String docId) {
        resourceSegmentManager.deleteByDocId(docId);
    }

    @Override
    public void deleteBySegmentId(String segmentId) {
        resourceSegmentManager.deleteBySegmentId(segmentId);
    }

    @Override
    public void deleteBySegmentIds(List<String> segmentIds) {
        if (CollectionUtils.isEmpty(segmentIds))
            return;
        resourceSegmentManager.deleteBySegmentIds(segmentIds);
    }

    @Override
    public List<Long> getSortIds(String docId) {
        return resourceSegmentManager.list(
                new LambdaQueryWrapper<ResourceSegmentEntity>()
                        .select(ResourceSegmentEntity::getId)
                        .eq(ResourceSegmentEntity::getDocId, docId)
                        .orderByAsc(ResourceSegmentEntity::getSort)
        ).stream().map(ResourceSegmentEntity::getId).collect(Collectors.toList());
    }

    @Override
    public Double getSort(Integer number, String docId, String segmentId, Double curSort) {
        Integer take = 3;
        Integer skip = number - 2;
        if (number.equals(1)) {
            skip = 0;
        }
        List<ResourceSegmentEntity> preSegList = resourceSegmentManager.listByDocId(docId, skip, take);
        if (CollectionUtils.isEmpty(preSegList)) {
            //数据库没有数据
            if (skip.equals(0L))
                return 1D;
            else {
                Double maxSort = resourceSegmentManager.maxSort(docId);
                if (Objects.isNull(maxSort))
                    return 1D;
                //设置的值原大于数据条数
                if (curSort.equals(maxSort))
                    return curSort;
                else
                    return maxSort + 1;
            }
        } else if (preSegList.size() == 1) {
            Double sort = preSegList.get(0).getSort();
            //数据本身时 不做修改
            if (preSegList.get(0).getSegmentId().equals(segmentId))
                return preSegList.get(0).getSort();
            //数据库只有1条数据
            if (skip.equals(0)) {
                //新数据插再前面
                if (number.equals(1)) {
                    return sort / 2;
                }
                //新数据插在后面
                else {
                    return sort + 1;
                }
            } else
                return sort + 1;
        } else if (preSegList.size() == 2) {
            if (skip.equals(0)) {
                if (number == 1) {
                    if (preSegList.get(0).getSegmentId().equals(segmentId))
                        return preSegList.get(0).getSort();
                    else
                        return preSegList.get(0).getSort() / 2;
                } else {
                    if (preSegList.get(1).getSegmentId().equals(segmentId))
                        return preSegList.get(1).getSort();
                    else if (number.equals(2)) {
                        return (preSegList.get(1).getSort() + preSegList.get(0).getSort()) / 2;
                    } else {
                        return preSegList.get(1).getSort() + 1;
                    }
                }
            } else {
                if (preSegList.get(1).getSegmentId().equals(segmentId))
                    return preSegList.get(1).getSort();
                else
                    return preSegList.get(1).getSort() + 1;
            }
        } else {
            if (number.equals(1)) {
                if (preSegList.get(0).getSegmentId().equals(segmentId)) {
                    return curSort;
                } else {
                    return preSegList.get(0).getSort() / 2;
                }
            }
            if (preSegList.get(1).getSegmentId().equals(segmentId))
                return preSegList.get(1).getSort();
            Double middleSort = preSegList.get(1).getSort();
            //后移
            if (curSort < middleSort) {
                return (preSegList.get(1).getSort() + preSegList.get(2).getSort()) / 2;
            } else {
                return (preSegList.get(0).getSort() + preSegList.get(1).getSort()) / 2;
            }


        }

    }

    @Override
    public void clearByDocId(String docId) {
        resourceSegmentManager.clearByDocId(docId);
    }

    @Override
    public List<ResourceSegmentEntity> listByDocId(String docId) {
        return resourceSegmentManager.listByDocId(docId);
    }

    @Override
    public List<ResourceSegmentEntity> listByDocIds(List<String> docIds) {
        return resourceSegmentManager.listByDocIds(docIds);
    }

    @Override
    public ResourceSegmentEntity getPre(String docId, Double min) {
        return resourceSegmentManager.getPre(docId, min);
    }

    @Override
    public List<ResourceSegmentEntity> listRangeByDocId(String docId, Double min, Double max) {
        return resourceSegmentManager.listRangeByDocId(docId, min, max);
    }

    @Override
    public List<ChunkEntity> reChunk(KnowledgeResourceEntity resource, DocStrategyModel strategyModel, ResourceSegmentEntity segment) {
        List<ChunkEntity> chunks = new ArrayList<>();

        if (StringUtils.isNotBlank(segment.getTitle())) {
            ChunkEntity titleChunk = ChunkEntity.create(resource, segment, "");
            titleChunk.setPage(segment.getPage());
            titleChunk.setContent(segment.getTitle());
            chunks.add(titleChunk);
        }
        //不需要切分 图片分块不需要重新切分
        if (strategyModel.getChunkFlag().equals(0)) {
            ChunkEntity chunk = ChunkEntity.create(resource, segment);
            chunk.setPage(segment.getPage());
            chunk.setContent(segment.getContent());
            chunks.add(chunk);
        } else {
            List<String> imgList = new ArrayList<>();
            List<String> tableList = new ArrayList<>();
            //提取所有图片 单独一块
            String content = StringComUtils.getImgUrlsAndReplace(segment.getContent(), imgList);
            //提取表格 表格单独一块
            content = StringComUtils.getTables(content, tableList);
            //去除标签后 判断是否存在文字内容
            String removedTagContent = StringComUtils.removeTags(content, Arrays.asList("p", "br"));
            if (StringUtils.isNotBlank(removedTagContent)) {
                String splitRegex = "(?<=[" + StringUtils.join(strategyModel.getSplitTextList(), "") + "])\\s*";
                DocumentSplitter recursive = DocumentSplitters.recursive(strategyModel.getMaxLength(), 0, splitRegex, (Tokenizer) null);
                List<Segment> segmentItems = recursive.split(new com.linker.omagent.core.data.document.Document(content));
                if (strategyModel.getMergeShort().equals(1)) {
                    segmentItems = DocumentSplitters.merge(segmentItems, strategyModel.getMaxLength() * strategyModel.getRatio() / 100);
                }
                for (Segment segmentItem : segmentItems) {
                    if (StringUtils.isBlank(segmentItem.getOutput())) {
                        continue;
                    }
                    ChunkEntity chunk = ChunkEntity.create(resource, segment, "");
                    chunk.setPage(segment.getPage());
                    chunk.setContent(segmentItem.getOutput());
                    chunks.add(chunk);
                }
            }
            for (String table : tableList) {
                ChunkEntity chunk = ChunkEntity.create(resource, segment, table);
                chunk.setPage(segment.getPage());
                chunk.setUrl("");
                chunk.setContent(table);
                chunks.add(chunk);
            }
            for (String img : imgList) {
                String newUrl = iUtilService.copy(img, StoragePathEnum.Chunk.getValueByUrl(resource.getTenantId(), resource.getDocId(), img));
                if (StringUtils.isBlank(newUrl)) {
                    newUrl = img;
                }
                ChunkEntity chunk = ChunkEntity.create(resource, segment, newUrl);
                chunk.setPage(segment.getPage());
                setChunkImageCaption(chunk, strategyModel.getImageEnhance());
                chunks.add(chunk);
            }


        }
        for (int i = 0; i < chunks.size(); i++) {
            ChunkEntity chunk = chunks.get(i);
            chunk.setSort((double) i + 1);
//            chunk.setCreateTime(StringComUtils.convertStr(LocalDateTime.now().plusSeconds(chunks.size() - i)));
        }
        chunks.forEach(chunkEntity -> {
            chunkEntity.setContentVector(embeddingService.embedImageAndText(imageEncode ? chunkEntity.getUrl() : null, chunkEntity.getContent()));
        });
        return chunks;
    }

    /**
     * 设置分块图片的caption
     *
     * @param chunk
     * @param imageEnhance
     */
    public void setChunkImageCaption(ChunkEntity chunk, Integer imageEnhance) {
        if (imageEnhance != null && imageEnhance.equals(1)) {
            if (chunk != null && chunk.getSrcType() != null && "image".equals(chunk.getSrcType())) {
                String imageCaption = imgService.odMllmCaption(chunk.getUrl());
                if (StringUtils.isNotBlank(chunk.getContent())) {
                    imageCaption = chunk.getContent() + "\n" + imageCaption;
                }
                chunk.setContent(imageCaption);
            }
        }
    }


    /**
     * 总结
     *
     * @param segmentAssItem
     */
    @Override
    public void summary(SegmentAssItem segmentAssItem,JSONObject jsonLog) {
        List<ChatMessage> messageList = new ArrayList<>();
        SystemMessage systemMessage = SystemMessage.from(promptMappingConfig.getSummary());
        messageList.add(systemMessage);
        List<Content> contents = new ArrayList<>();
        StringBuilder userPrompt = new StringBuilder();
        userPrompt.append("视频中的语音文本信息: ");
        SummaryItem summaryItem = new SummaryItem();
        summaryItem.setText(segmentAssItem.getAsrText());
        summaryItem.setLanguage("zh");
        summaryItem.setDuration(((double) (segmentAssItem.getEndTimestamp() - segmentAssItem.getStartTimestamp())) / 1000);
        userPrompt.append(JSON.toJSONString(summaryItem));
        userPrompt.append("\n视频帧信息:");
        contents.add(new TextContent(userPrompt.toString()));
        List<VideoFrameAssItem> frameAssItems = segmentAssItem.getFrameAssItems();
        // 只取selected=true的图片
        List<String> imageUrls = frameAssItems.stream().filter(VideoFrameAssItem::isSelected).map(VideoFrameAssItem::getPaintedUrl).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        imageUrls = imgService.mergeImages(imageUrls);
        jsonLog.put("segmentId_"+segmentAssItem.getSegmentId()+"_img_merge", imageUrls);
        imageUrls.forEach(item -> {
            try {
//                contents.add(new ImageContent(ImageUtils.imageBase64(item), MimeTypeUtils.IMAGE_JPEG_VALUE, ImageContent.DetailLevel.AUTO));
                contents.add(imageContentBuilder.build(item));
            } catch (Exception e) {
                log.warn(String.format("图片%s获取base64失败", item));
            }
        });
        contents.add(new TextContent("\n总结:"));
        UserMessage userMessage = UserMessage.from(contents);
        messageList.add(userMessage);
        ChatModelConfig config = new ChatModelConfig();
//        config.setResponseFormat("json_object");
        config.setTemperature(0.0);
        config.setPresencePenalty(0d);
        config.setFrequencyPenalty(0d);
        config.setModelName(segmentAssItem.getSummaryModel());
        long startTime = System.currentTimeMillis();
        try {
            Response<AiMessage> response = omChatLanguageModel.generate(config, messageList);
            String text = response.content().text();
            //格式化输出
            text = formatText(text, segmentAssItem);
            segmentAssItem.setContent(text);
            jsonLog.put("segmentId_"+segmentAssItem.getSegmentId()+"_summary_ret", text);
            if (StringUtils.isNotBlank(text)) {
                segmentAssItem.setStatus(ProcessEnum.Success);
            } else {
                segmentAssItem.setStatus(ProcessEnum.Fail);
            }
        } catch (Exception ex) {
            jsonLog.put("segmentId_"+segmentAssItem.getSegmentId()+"_summary_error", ex.getMessage());
            if (ex.getMessage().contains("timeout")) {
                segmentAssItem.setContent("模型开小差了");
            } else if (ex.getMessage().contains("data_inspection_failed")) {
                segmentAssItem.setContent("内容涉及敏感信息");
            }
            segmentAssItem.setStatus(ProcessEnum.Fail);
            log.error("docId:{},segId:{},summaryError:{},duration:{},token:%s", segmentAssItem.getResource().getDocId(), segmentAssItem.getSegmentId(), ex.getMessage(), (System.currentTimeMillis() - startTime) / 1000, ex);
            throw new BusinessException("总结失败");
        }
    }

    private static final String LINE_FORMAT = "%s：%s\n";

    @Override
    public String formatText(String text, SegmentAssItem segmentAssItem) {
        try {
            VideoSummaryDO videoSummaryDO = new VideoSummaryDO();
            videoSummaryDO.setSummary(text);

            // 处理人物信息
            List<String> faces = segmentAssItem.getFaces().stream()
                    .filter(c -> StringUtils.isNotBlank(c.getName()) &&
                            !StringUtils.contains(c.getName(), "未知人物"))
                    .map(ResourceFaceEntity::getName)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(faces)) {
                videoSummaryDO.setCharacter(String.join(",", faces));
            }

            // 处理时间信息
            List<ResourceTerminologyEntity> timeLabel = segmentAssItem.getTerminologies().stream()
                    .filter(c -> c.getType().equals(TerminologyTypeEnum.TIME.getValue()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeLabel)) {
                videoSummaryDO.setTime(timeLabel.stream()
                        .map(ResourceTerminologyEntity::getName)
                        .distinct()
                        .collect(Collectors.joining(",")));
            }

            // 处理地点信息
            List<ResourceTerminologyEntity> locationLabel = segmentAssItem.getTerminologies().stream()
                    .filter(c -> c.getType().equals(TerminologyTypeEnum.GPE.getValue()) ||
                            c.getType().equals(TerminologyTypeEnum.ORG.getValue()))
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(locationLabel)) {
                videoSummaryDO.setLocation(locationLabel.stream()
                        .map(ResourceTerminologyEntity::getName)
                        .collect(Collectors.joining(",")));
            }

            // 构建结果字符串
            StringBuilder result = new StringBuilder();

            if (StringUtils.isNotBlank(videoSummaryDO.getTime())) {
                result.append(String.format(LINE_FORMAT, "时间", videoSummaryDO.getTime()));
            }
            if (StringUtils.isNotBlank(videoSummaryDO.getLocation())) {
                result.append(String.format(LINE_FORMAT, "地点", videoSummaryDO.getLocation()));
            }
            if (StringUtils.isNotBlank(videoSummaryDO.getCharacter())) {
                result.append(String.format(LINE_FORMAT, "人物", videoSummaryDO.getCharacter()));
            }
            if (StringUtils.isNotBlank(videoSummaryDO.getSummary())) {
                result.append(String.format(LINE_FORMAT, "总结", videoSummaryDO.getSummary()));
            }

            return result.toString();
        } catch (Exception e) {
            log.error("video summary format error", e);
            return text;
        }
    }

    @Override
    public void importAndSaveSegment(SegmentAssItem segmentAssItem) {

        if (segmentAssItem.getStatus().equals(ProcessEnum.Success)) {
            //保存视频分块
            saveVideoChunk(segmentAssItem);
            //保存语音分库
            saveAudioChunk(segmentAssItem);
        }
        if (CollectionUtils.isNotEmpty(segmentAssItem.getEventAIRespList())) {
            segmentAssItem.setContent(segmentAssItem.getContent() + "\n" + segmentAssItem.getEventAIRespList().stream().map(EventAIResp::getDesc).filter(StringUtils::isNotBlank).collect(Collectors.joining("\n")));
        }
        //保存分段
        saveSegment(segmentAssItem);
    }

    @Override
    public void removeById(Long id) {
        resourceSegmentManager.removeById(id);
    }

    @Override
    public ResourceSegmentEntity getLatestSegmentByDocId(String docId) {
        return resourceSegmentManager.getOne(
                new LambdaQueryWrapper<ResourceSegmentEntity>()
                        .eq(ResourceSegmentEntity::getDeleted, false)
                        .eq(ResourceSegmentEntity::getDocId, docId)
                        .orderByDesc(ResourceSegmentEntity::getPage)
                        .last("limit 1")
        );
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        resourceSegmentManager.updateStatus(id, status);
    }

    @Override
    public void updateStatus(String segId, Integer status) {
        resourceSegmentManager.updateStatus(segId, status);
    }


    /**
     * 保存分段
     *
     * @param segmentAssItem
     */
    private void saveSegment(SegmentAssItem segmentAssItem) {
        try {
            ResourceSegmentEntity segment = new ResourceSegmentEntity();
            segment.setSort(segmentAssItem.getSort());
            segment.setNumber(segmentAssItem.getSort().intValue());
            segment.setTitle(segmentAssItem.getResource().getTitle());
            segment.setContent(segmentAssItem.getContent());
            segment.setDocId(segmentAssItem.getResource().getDocId());
            segment.setSegmentId(segmentAssItem.getSegmentId());
            segment.setPage(0);
            segment.setPosition("{}");
            segment.setExt(JSON.toJSONString(segmentAssItem));
            segment.setThumbnail(segmentAssItem.getThumbnail());
            segment.setStatus(segmentAssItem.getStatus().getValue());
            segment.setFileType(segmentAssItem.getResource().getType());
            segment.setStartTimestamp(segmentAssItem.getStartTimestamp());
            segment.setEndTimestamp(segmentAssItem.getEndTimestamp());
            segment.setTenantId(segmentAssItem.getResource().getTenantId());
            segment.setCreateTime(LocalDateTime.now());
            segment.setUpdateTime(segmentAssItem.getResource().getUpdateTime());
            segment.setUpdateId(segmentAssItem.getResource().getUpdateId());
            segment.setCreatorId(segmentAssItem.getResource().getCreatorId());
            save(segment);
        } catch (Exception ex) {
            log.error("segId:{},saveSegmentError:{}", segmentAssItem.getSegmentId(), ex.getMessage(), ex);
        }
    }

    /**
     * 保存视频分块
     *
     * @param segmentAssItem
     */
    private void saveVideoChunk(SegmentAssItem segmentAssItem) {
        try {
            List<VideoChunkEntity> chunks = new ArrayList<>();
            VideoChunkEntity summaryChunk = createChunkForSegAss(segmentAssItem);
            summaryChunk.setContent(segmentAssItem.getContent());
            if (StringUtils.isNotBlank(summaryChunk.getContent()))
                summaryChunk.setContentVector(embeddingService.embedText(summaryChunk.getContent()).content().vector());
            chunks.add(summaryChunk);
            segmentAssItem.getEventAIRespList().forEach(eventAIResp -> {
                VideoChunkEntity eventChunk = createChunkForSegAss(segmentAssItem);
                eventChunk.setContent(eventAIResp.getDesc());
                if (StringUtils.isNotBlank(eventChunk.getContent()))
                    eventChunk.setContentVector(embeddingService.embedText(eventChunk.getContent()).content().vector());
                eventChunk.setSort((double)chunks.size());
                chunks.add(eventChunk);
            });
            iVideoChunkIBaseService.add(segmentAssItem.getResource().getTenantId(), chunks, false);
        } catch (Exception ex) {
            segmentAssItem.setStatus(ProcessEnum.Fail);
            log.error("segId:{},videoChunkError:{}", segmentAssItem.getSegmentId(), ex.getMessage(), ex);
        }

    }

    private VideoChunkEntity createChunkForSegAss(SegmentAssItem segmentAssItem) {
        VideoChunkEntity videoChunk = new VideoChunkEntity();
        videoChunk.setGroupId(segmentAssItem.getResource().getGroupId());
        videoChunk.setTenantId(segmentAssItem.getResource().getTenantId());
        videoChunk.setCreateId(segmentAssItem.getResource().getCreatorId());
        videoChunk.setCreateTime(StringComUtils.convertStr(segmentAssItem.getResource().getCreateTime()));
        videoChunk.setChunkId(UUID.randomUUID().toString());
        videoChunk.setContent(segmentAssItem.getContent());
        videoChunk.setEnable(1);
        videoChunk.setTitle(segmentAssItem.getResource().getTitle());
        videoChunk.setSort(segmentAssItem.getSort());
        videoChunk.setMeta(JSONObject.parseObject(segmentAssItem.getResource().getExtInfo()));
        videoChunk.setDocId(segmentAssItem.getResource().getDocId());
        videoChunk.setSegmentId(segmentAssItem.getSegmentId());
        videoChunk.setStartTimestamp(segmentAssItem.getStartTimestamp());
        videoChunk.setEndTimestamp(segmentAssItem.getEndTimestamp());
        videoChunk.setObjectLabels(segmentAssItem.getOdTags().stream().map(ResourceODTagEntity::getName).distinct().collect(Collectors.toList()));
        videoChunk.setPersonsLabels(ListUtils.union(
                segmentAssItem.getFaces().stream().map(ResourceFaceEntity::getName).distinct().collect(Collectors.toList()),
                segmentAssItem.getFaces().stream().map(ResourceFaceEntity::getSourceId).distinct().collect(Collectors.toList()))
        );
        videoChunk.setTextLabels(segmentAssItem.getTerminologyNames());
        return videoChunk;
    }


    /**
     * 保存语音分块
     *
     * @param segmentAssItem
     */
    private void saveAudioChunk(SegmentAssItem segmentAssItem) {
        try {
            if (StringUtils.isBlank(segmentAssItem.getAsrText()))
                return;
            AudioChunkEntity audioChunk = new AudioChunkEntity();
            audioChunk.setChunkId(UUID.randomUUID().toString());
            audioChunk.setContent(segmentAssItem.getAsrText());
            audioChunk.setEnable(1);
            audioChunk.setTitle(segmentAssItem.getResource().getTitle());
            audioChunk.setSort(segmentAssItem.getSort());
            audioChunk.setMeta(JSONObject.parseObject(segmentAssItem.getResource().getExtInfo()));
            audioChunk.setDocId(segmentAssItem.getResource().getDocId());
            audioChunk.setSegmentId(segmentAssItem.getSegmentId());
            audioChunk.setStartTimestamp(segmentAssItem.getStartTimestamp());
            audioChunk.setEndTimestamp(segmentAssItem.getEndTimestamp());
            audioChunk.setObjectLabels(segmentAssItem.getOdTags().stream().map(x -> x.getName()).distinct().collect(Collectors.toList()));
            audioChunk.setPersonsLabels(segmentAssItem.getFaces().stream().map(x -> x.getName()).distinct().collect(Collectors.toList()));
            audioChunk.setTextLabels(segmentAssItem.getTerminologyNames());
            audioChunk.setFileType(segmentAssItem.getResource().getType());
            audioChunk.setContentVector(embeddingService.embedText(audioChunk.getContent()).content().vector());
            audioChunk.setTenantId(segmentAssItem.getResource().getTenantId());
            audioChunk.setGroupId(segmentAssItem.getResource().getGroupId());
            audioChunk.setCreateId(segmentAssItem.getResource().getCreatorId());
            audioChunk.setCreateTime(StringComUtils.convertStr(segmentAssItem.getResource().getCreateTime()));
            iAudioChunkIBaseService.add(segmentAssItem.getResource().getTenantId(), Arrays.asList(audioChunk), false);
        } catch (Exception ex) {
            segmentAssItem.setStatus(ProcessEnum.Fail);
            log.error("segId:{},audioChunkError:{}", segmentAssItem.getSegmentId(), ex.getMessage(), ex);
        }
    }

    public void updateThumbnail(String segmentId, String url) {
        resourceSegmentManager.updateThumbnail(segmentId, url);
    }
}
