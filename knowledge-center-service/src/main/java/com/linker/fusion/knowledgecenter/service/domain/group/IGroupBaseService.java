package com.linker.fusion.knowledgecenter.service.domain.group;

import com.linker.fusion.knowledgecenter.infrastructure.enums.KnowledgeTypeEnum;

import java.util.List;

public interface IGroupBaseService {

    KnowledgeTypeEnum getType();
    void deleteByGroupIds(List<Long> groupIds);

    /**
     * 获取分组Id列表
     *
     * @param ids 资源id
     * @return
     */
    List<Long> listGroupIds(List<Long> ids);
}
