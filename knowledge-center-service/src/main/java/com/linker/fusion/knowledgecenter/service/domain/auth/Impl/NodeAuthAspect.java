package com.linker.fusion.knowledgecenter.service.domain.auth.Impl;

import cn.hutool.core.date.StopWatch;
import com.linker.core.auth.utils.UserContext;
import com.linker.core.base.exception.ServiceException;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.ResourceSegmentEntity;
import com.linker.fusion.knowledgecenter.infrastructure.es.BaseChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.exception.KnowledgeCenterErrorCodeEnum;
import com.linker.fusion.knowledgecenter.service.domain.auth.GroupNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.auth.MenuAndResNodeAuth;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.group.impl.KnowledgeGroupFactory;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.extendData.IResourceSegmentService;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ChunkServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Aspect
@Component
@Slf4j
public class NodeAuthAspect {
    private final ExpressionParser parser = new SpelExpressionParser();
    private final StandardEvaluationContext context = new StandardEvaluationContext();
    @Resource
    private KnowledgeGroupFactory knowledgeGroupFactory;
    @Resource
    private KnowledgeGroupService knowledgeGroupService;
    @Resource
    private IResourceSegmentService resourceSegmentService;
    @Resource
    private ChunkServiceFactory chunkServiceFactory;
    @Resource
    private IResourceService iResourceService;

    @Around("@annotation(menuAndResNodeAuth)")
    public Object checkAuth(ProceedingJoinPoint joinPoint, MenuAndResNodeAuth menuAndResNodeAuth) throws Throwable {
        StopWatch stopWatch = new StopWatch();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String methodName = String.format("%s-%s-", signature.getDeclaringType().getSimpleName(), signature.getMethod().getName());
        stopWatch.start(methodName + "checkAuth");
        try {
            // 1. 获取方法参数信息
            String[] paramNames = signature.getParameterNames();
            Object[] paramValues = joinPoint.getArgs();
            // 2. 设置SpEL上下文
            for (int i = 0; i < paramNames.length; i++) {
                if (paramValues[i] instanceof HttpServletResponse) {
                    continue;
                }
                context.setVariable(paramNames[i], paramValues[i]);
            }
            // 3. 解析资源ID（支持SpEL表达式）
            List<Long> resourceIds;
            if (StringUtils.isNotBlank(menuAndResNodeAuth.ids()))
                resourceIds = parseLongIds(menuAndResNodeAuth.ids());
            else {
                List<String> docIds = new ArrayList<>();
                if (StringUtils.isNotBlank(menuAndResNodeAuth.segmentIds())) {
                    List<String> segmentIds = parseStringIds(menuAndResNodeAuth.segmentIds());
                    List<ResourceSegmentEntity> segments = resourceSegmentService.getSementList(segmentIds);
                    docIds = segments.stream().map(ResourceSegmentEntity::getDocId).collect(Collectors.toList());
                } else if (StringUtils.isNotBlank(menuAndResNodeAuth.chunkIds())) {
                    List<BaseChunkEntity> chunks = (List<BaseChunkEntity>) chunkServiceFactory.getInstance(menuAndResNodeAuth.type().getType()).listByChunkIds(UserContext.getUser().getTenantInfoDTO().getTenantId(), parseStringIds(menuAndResNodeAuth.chunkIds()));
                    docIds = chunks.stream().map(BaseChunkEntity::getDocId).collect(Collectors.toList());
                } else if (StringUtils.isNotBlank(menuAndResNodeAuth.resIds())) {
                    docIds = parseStringIds(menuAndResNodeAuth.resIds());
                }
                List<KnowledgeResourceEntity> resList = iResourceService.listByResIds(docIds);
                resourceIds = resList.stream().map(KnowledgeResourceEntity::getId).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(resourceIds)) {
                knowledgeGroupFactory.checkMenuAndResNodeAuth(resourceIds, menuAndResNodeAuth.type(), menuAndResNodeAuth.operatorType(), menuAndResNodeAuth.node());
            }
            if (StringUtils.isNotBlank(menuAndResNodeAuth.groupIds())) {
                List<Long> groupIds = parseLongIds(menuAndResNodeAuth.groupIds());
                List<KnowledgeGroupEntity> groups = knowledgeGroupService.listByIds(groupIds);
                groups.forEach(group -> knowledgeGroupFactory.checkMenuAndNodeAuth(group, menuAndResNodeAuth.operatorType(), menuAndResNodeAuth.node()));
            }
        } finally {
            stopWatch.stop();
        }
        stopWatch.start(methodName + "proceed");
        try {
            return joinPoint.proceed();
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Around("@annotation(groupNodeAuth)")
    public Object checkAuth(ProceedingJoinPoint joinPoint, GroupNodeAuth groupNodeAuth) throws Throwable {
        StopWatch stopWatch = new StopWatch();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String methodName = String.format("%s-%s-", signature.getDeclaringType().getSimpleName(), signature.getMethod().getName());
        stopWatch.start(methodName + "checkAuth");
        try {
            // 1. 获取方法参数信息
            String[] paramNames = signature.getParameterNames();
            Object[] paramValues = joinPoint.getArgs();
            // 2. 设置SpEL上下文
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], paramValues[i]);
            }
            if (StringUtils.isNotBlank(groupNodeAuth.groupIds())) {
                List<Long> groupIds = parseLongIds(groupNodeAuth.groupIds());
                List<KnowledgeGroupEntity> groups = knowledgeGroupService.listByIds(groupIds);
                if (CollectionUtils.isEmpty(groups) || groups.size() != groupIds.size()) {
                    throw new ServiceException(KnowledgeCenterErrorCodeEnum.NOT_EXIST, "目录");
                }
                groups.forEach(group -> knowledgeGroupFactory.checkMenuAndNodeAuth(group, groupNodeAuth.operatorType(), groupNodeAuth.node()));
            }
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        }
        // 5. 执行原方法
        stopWatch.start(methodName + "proceed");
        try {
            return joinPoint.proceed();
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    /**
     * 获取Long类型的数据
     *
     * @param idsExpression 表达式
     * @return 列表
     */
    private List<Long> parseLongIds(String idsExpression) {
        if (idsExpression == null || idsExpression.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            Object value = parser.parseExpression(idsExpression).getValue(context);
            if (value instanceof List) {
                List<Long> ids = new ArrayList<>();
                ((List<?>) value).forEach(obj -> {
                    if (obj instanceof List) {
                        ((List<?>) obj).forEach(obj1 -> {
                            if (obj1 instanceof Long) {
                                ids.add((Long) obj1);
                            } else if (obj1 instanceof String) {
                                ids.add(Long.valueOf((String) obj1));
                            }
                        });
                    } else if (obj instanceof Long) {
                        ids.add((Long) obj);
                    } else if (obj instanceof String) {
                        ids.add(Long.valueOf((String) obj));
                    }
                });
                return ids;

            } else if (value instanceof Long) {
                return Collections.singletonList((Long) value);
            }
            return Collections.emptyList();
        } catch (Exception e) {
            throw new IllegalArgumentException("资源ID表达式解析失败: " + idsExpression, e);
        }
    }

    /**
     * 获取String类型的数据
     *
     * @param idsExpression 表达式
     * @return 列表
     */
    private List<String> parseStringIds(String idsExpression) {
        if (idsExpression == null || idsExpression.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            Object value = parser.parseExpression(idsExpression).getValue(context);
            if (value instanceof List) {
                List<String> ids = new ArrayList<>();
                ((List<?>) value).forEach(obj -> {
                    if (obj instanceof List) {
                        ((List<?>) obj).forEach(obj1 -> {
                            if (obj1 instanceof String) {
                                ids.add((String) obj1);
                            }
                        });
                    }
                    if (obj instanceof String) {
                        ids.add((String) obj);
                    }
                });
                return ids;

            } else if (value instanceof String) {
                return Collections.singletonList((String) value);
            }
            return Collections.emptyList();
        } catch (Exception e) {
            throw new IllegalArgumentException("资源ID表达式解析失败: " + idsExpression, e);
        }
    }
}

