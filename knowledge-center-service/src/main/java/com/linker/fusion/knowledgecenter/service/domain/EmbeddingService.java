package com.linker.fusion.knowledgecenter.service.domain;

import com.alibaba.fastjson.JSON;
import com.linker.fusion.knowledgecenter.infrastructure.client.BGEClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.ImageEncodeReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.ImageTextEncodeReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.TextEncodeReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.ImageEncodeResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.TextEncodeResp;
import com.linker.fusion.knowledgecenter.infrastructure.config.BGEPropConfig;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.omagent.core.data.embedding.Embedding;
import com.linker.omagent.core.data.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class EmbeddingService {

    @Resource
    private BGEClient bgeClient;

    public Response<Embedding> embedText(String text) {
        TextEncodeResp textEncodeResp = bgeClient.textEncode(new TextEncodeReq(Collections.singletonList(text)));
        return Response.from(new Embedding(textEncodeResp.getFeatures().get(0), null));
    }

    public List<List<Double>> embedText(List<String> texts) {
        return embedText(texts, BGEPropConfig.TextBatch);
    }

    public List<List<Double>> embedText(List<String> texts, Integer batch) {
        List<List<Double>> res = new ArrayList<>();
        List<List<String>> lists = StringComUtils.partitionList(texts, batch);
        for (List<String> list : lists) {
            List<List<Double>> temp = Collections.nCopies(list.size(), new ArrayList<>());
            try {
                temp = bgeClient.textEncode(new TextEncodeReq(list)).getFeatures();
            } catch (Exception ex) {
                log.error(JSON.toJSONString(texts));
                log.error("文本向量化失败:", ex);
            } finally {
                res.addAll(temp);
            }
        }
        return res;
    }

    public Response<Embedding> embedImage(String image) {
        try {
            ImageEncodeResp resp = bgeClient.imageEncode(new ImageEncodeReq(Collections.singletonList(image), "url"));
            return Response.from(new Embedding(resp.getFeatures().get(0), null));
        } catch (Exception ex) {
            log.error("图片向量化失败：{}",image, ex);
        }
        return Response.from(new Embedding(null, null));
    }

    public List<List<Double>> embedImage(List<String> images) {
        List<List<Double>> temp = Collections.nCopies(images.size(), new ArrayList<>());
        try {
            temp = bgeClient.imageEncode(new ImageEncodeReq(images, "url")).getFeatures();
        } catch (Exception ex) {
            log.error(JSON.toJSONString(images));
            log.error("图片向量化失败", ex);
        }
        return temp;
    }

    public List<Double> embedImageAndText(String image, String text) {
        try {
            if (StringUtils.isNotBlank(image) && StringUtils.isNotBlank(text)) {
                ImageEncodeResp resp = bgeClient.imageTextEncode(
                        new ImageTextEncodeReq(
                                Collections.singletonList(new ImageTextEncodeReq.ImageTextPair(image, text)),
                                "url")
                );
                return resp.getFeatures().get(0);
            }
            if (StringUtils.isNotBlank(image)) {
                return embedImage(image).content().vector();
            }
            if (StringUtils.isNotBlank(text)) {
                return embedText(text).content().vector();
            }
        } catch (Exception ex) {
            log.warn("text：{} | image：{}", text, image);
            log.error("图片文本向量化失败", ex);
        }
        return null;
    }

}
