package com.linker.fusion.knowledgecenter.service.domain.faq.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class FaqEmbeddingData {

    /**
     * 文档uid
     */
    @JsonProperty("_uid")
    private String uid;

    @JsonProperty("tenant_id")
    private String tenantId;

    /**
     * 类目ID
     */
    @JsonProperty("group_id")
    private Long groupId;

    /**
     * 类目全路径
     */
    @Deprecated
    @JsonProperty("group_path")
    private String groupPath;

    /**
     * 0-标准问题 1-类似问题
     */
    private Integer type;

    /**
     * 1-已启用 0-已禁用
     */
    private Integer enable;

    /**
     * 问题内容
     */
    private String question;

    /**
     * 问题内容向量
     */
    @JsonProperty("question_vector")
    public List<Double> questionVector;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    public String createTime;

}
