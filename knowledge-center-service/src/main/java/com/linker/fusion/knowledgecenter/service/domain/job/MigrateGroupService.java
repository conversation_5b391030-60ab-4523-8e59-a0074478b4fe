package com.linker.fusion.knowledgecenter.service.domain.job;

import com.linker.fusion.knowledgecenter.infrastructure.client.model.req.UserBaseInfoReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.model.resp.UserBaseInfoResp;
import com.linker.fusion.knowledgecenter.infrastructure.client.usercenter.IUserCenter;
import com.linker.fusion.knowledgecenter.infrastructure.entity.AuthEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.FaqInfoEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeGroupEntity;
import com.linker.fusion.knowledgecenter.infrastructure.entity.KnowledgeResourceEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.*;
import com.linker.fusion.knowledgecenter.service.domain.auth.IAuthService;
import com.linker.fusion.knowledgecenter.service.domain.faq.FaqService;
import com.linker.fusion.knowledgecenter.service.domain.group.KnowledgeGroupService;
import com.linker.fusion.knowledgecenter.service.domain.resource.IResourceService;
import com.linker.fusion.knowledgecenter.service.domain.resource.TableService;
import com.linker.fusion.knowledgecenter.service.domain.resource.impl.ResourceUtilService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MigrateGroupService {

    @Resource
    private KnowledgeGroupService knowledgeGroupService;

    @Resource
    private IResourceService iResourceService;

    @Resource
    private TableService tableService;

    @Resource
    private FaqService faqService;

    @Resource
    private IUserCenter iUserCenter;

    @Resource
    private IAuthService iAuthService;
    @Resource
    private ResourceUtilService resourceUtilService;

    public void migrateFaq() {

        List<FaqInfoEntity> faqInfoEntityList = faqService.needMerge();
        if (CollectionUtils.isEmpty(faqInfoEntityList)) {
            log.info("migrateFaq 没有需要迁移的FAQ");
            return;
        }
        Map<String, List<FaqInfoEntity>> map = faqInfoEntityList.stream().collect(Collectors.groupingBy(FaqInfoEntity::getTenantId));

        for (Map.Entry<String, List<FaqInfoEntity>> entry : map.entrySet()) {
            String tenantId = entry.getKey();
            String userCode = getTenantManageUserCode(tenantId);
            if (StringUtils.isBlank(userCode)) {
                log.info("migrateFaq 管理员信息为空tenantId:{}", tenantId);
                continue;
            }
            KnowledgeGroupEntity group = addTempLib(tenantId, userCode, KnowledgeTypeEnum.FAQ);

            List<FaqInfoEntity> faqList = entry.getValue();
            faqService.move(tenantId, faqList.stream().filter(s -> s.getType() == 0).collect(Collectors.toList()), group.getId());

            // 给租户下所有人只读权限
            auth(group.getId(), tenantId, userCode);
        }
    }

    private KnowledgeGroupEntity addTempLib(String tenantId, String userCode, KnowledgeTypeEnum type) {
        // 新建临时库
        KnowledgeGroupEntity group = new KnowledgeGroupEntity();
        // 标记为库
        group.setIsLibrary(true);
        group.setParentId(KnowledgeGroupEntity.ROOT_ID);
        group.setSort(0d);
        // 元数据
        group.setType(type.getType());
        group.setName("临时库");
        group.setDescription(null);
        group.setLogo(null);
        group.setIsSync(false);
        // 通用
        group.setTenantId(tenantId);
        group.setUpdateId(userCode);
        group.setUpdateTime(LocalDateTime.now());
        group.setCreatorId(userCode);
        group.setCreateTime(LocalDateTime.now());
        group.setDeleted(false);
        knowledgeGroupService.saveOrUpdate(group);
        return group;
    }

    private String getTenantManageUserCode(String tenantId) {
        UserBaseInfoResp userBaseInfoResp = iUserCenter.getUserInfoByTenantId(
                new UserBaseInfoReq()
                        .setTenantId(tenantId)).getData();
        if (userBaseInfoResp == null) {
            return null;
        }
        return userBaseInfoResp.getUserCode();
    }

    private void auth(Long groupId, String tenantId, String userCode) {

        AuthEntity auth = new AuthEntity();
        auth.setParentId(0L);
        auth.setAuthType(AuthTypeEnum.Tenant.getValue());
        auth.setAuthLevel(AuthLevelEnum.View.getValue());
        auth.setAuthId(tenantId);
        auth.setSourceId(groupId.toString());
        auth.setSourceType(SourceTypeEnum.Group.getValue());
        auth.setDeleted(false);
        auth.setCreateTime(LocalDateTime.now());
        auth.setCreatorId(userCode);
        auth.setCreateTime(LocalDateTime.now());
        auth.setUpdateId(userCode);
        auth.setUpdateTime(LocalDateTime.now());
        auth.setTenantId(tenantId);
        iAuthService.saveOrUpdate(auth);
    }

    public void migrateDoc() {
        List<KnowledgeResourceEntity> knowledgeResourceEntityList = iResourceService.list(FileTypeEnum.DOCUMENT,0L);
        if (CollectionUtils.isEmpty(knowledgeResourceEntityList)) {
            log.info("migrateDoc 没有需要迁移的文档");
            return;
        }
        Map<String, List<KnowledgeResourceEntity>> map = knowledgeResourceEntityList.stream().collect(Collectors.groupingBy(KnowledgeResourceEntity::getTenantId));
        for (Map.Entry<String, List<KnowledgeResourceEntity>> entry : map.entrySet()) {
            String tenantId = entry.getKey();
            String userCode = getTenantManageUserCode(tenantId);
            if (StringUtils.isBlank(userCode)) {
                log.info("migrateDoc 管理员信息为空tenantId:{}", tenantId);
                continue;
            }
            KnowledgeGroupEntity group = addTempLib(tenantId, userCode, KnowledgeTypeEnum.FILE);

            List<KnowledgeResourceEntity> docList = entry.getValue();
            List<String> docIds = docList.stream().map(KnowledgeResourceEntity::getDocId).collect(Collectors.toList());
            resourceUtilService.move(tenantId, docIds, group.getId());
            // 给租户下所有人只读权限
            auth(group.getId(), tenantId, userCode);
        }
    }

    public void migrateTable() {
        List<KnowledgeResourceEntity> knowledgeResourceEntityList = tableService.needMerge();
        if (CollectionUtils.isEmpty(knowledgeResourceEntityList)) {
            log.info("migrateTable 没有需要迁移的表格");
            return;
        }
        Map<String, List<KnowledgeResourceEntity>> map = knowledgeResourceEntityList.stream().collect(Collectors.groupingBy(KnowledgeResourceEntity::getTenantId));
        for (Map.Entry<String, List<KnowledgeResourceEntity>> entry : map.entrySet()) {
            String tenantId = entry.getKey();

            String userCode = getTenantManageUserCode(tenantId);
            if (StringUtils.isBlank(userCode)) {
                log.warn("migrateTable 管理员信息为空tenantId:{}", tenantId);
                continue;
            }
            KnowledgeGroupEntity group = addTempLib(tenantId, userCode, KnowledgeTypeEnum.TABLE);

            List<KnowledgeResourceEntity> entityList = entry.getValue();

            auth(group.getId(), tenantId, userCode);
        }
    }

}
