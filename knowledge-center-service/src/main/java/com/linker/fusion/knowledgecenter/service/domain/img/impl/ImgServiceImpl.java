package com.linker.fusion.knowledgecenter.service.domain.img.impl;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSON;
import com.linker.fusion.knowledgecenter.infrastructure.config.PromptMappingConfig;
import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.service.domain.common.ImageContentBuilder;
import com.linker.fusion.knowledgecenter.service.domain.common.ImageConverter;
import com.linker.fusion.knowledgecenter.service.domain.common.ImageUtils;
import com.linker.fusion.knowledgecenter.service.domain.common.MinioStorage;
import com.linker.fusion.knowledgecenter.service.domain.img.ImgService;
import com.linker.fusion.knowledgecenter.service.domain.img.model.DrawBoxInfo;
import com.linker.fusion.knowledgecenter.service.domain.img.model.FaceInfoDTO;
import com.linker.omagent.core.data.detection.ObjectDetectionInput;
import com.linker.omagent.core.data.detection.ObjectDetectionOutput;
import com.linker.omagent.core.data.detection.OpenVocabularyDetectionOutput;
import com.linker.omagent.core.data.message.*;
import com.linker.omagent.core.data.output.Response;
import com.linker.omagent.omos.model.OmChatLanguageModel;
import com.linker.omagent.omos.model.OmObjectDetectionModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class ImgServiceImpl implements ImgService {

    @Autowired
    private OmObjectDetectionModel omObjectDetectionModel;

    @Autowired
    private MinioStorage minioStorage;

    @Resource
    private OmChatLanguageModel omChatLanguageModel;

    @Autowired
    private PromptMappingConfig promptMappingConfig;

    @Resource
    private ImageContentBuilder imageContentBuilder;

    @Override
    public String odMllmCaption(String url, String prompt, String model, String question) {
        return mllmCaption(odMux(url), prompt, model, question);
    }


    @Override
    public String odMllmCaption(String url, String prompt, String model) {
        return mllmCaption(odMux(url), prompt, model);
    }

    @Override
    public String odMllmCaption(String url) {
        return mllmCaption(odMux(url), promptMappingConfig.getImagePrompt(), promptMappingConfig.getImageModel());
    }

    @Override
    public String mllmCaption(String url, String prompt, String model) {
        return mllmCaption(url, prompt, model, "");
    }

    @Override
    public String mllmCaption(String url, String prompt, String model, String question) {
        String res = "";
        // 至少100
        if (StringUtils.isNotBlank(url) && StringComUtils.isUrlFileCorruptedLeast(url) && promptMappingConfig.getImageCaptionType() > -1) {
            try {
                log.debug("图片caption：" + url);
                List<ChatMessage> messageList = new ArrayList<>();
                SystemMessage systemMessage = SystemMessage.from(prompt);
                messageList.add(systemMessage);
                List<Content> contents = new ArrayList<>();
                if (StringUtils.isNotBlank(question)) {
                    contents.add(new TextContent("question:" + question));
                }
//                String type = MimeTypeUtils.IMAGE_JPEG_VALUE;
//                String extName = FileUtil.extName(url);
//                if ("png".equalsIgnoreCase(extName)) {
//                    type = MimeTypeUtils.IMAGE_PNG_VALUE;
//                }
//                contents.add(new ImageContent(StringComUtils.imageBase64(url), type, ImageContent.DetailLevel.AUTO));
                contents.add(imageContentBuilder.build(url));
                UserMessage userMessage = UserMessage.from(contents);
                messageList.add(userMessage);
                ChatModelConfig config = new ChatModelConfig();
                config.setModelName(model);
                config.setTemperature(0d);
                Response<AiMessage> response = omChatLanguageModel.generate(config, messageList);
                res = response.content().text();
                log.debug("图片caption：" + url + " ->> 结果：" + res);
            } catch (Exception ex) {
                log.error(url + " ->>url caption 失败" + ex.getMessage());
            }
        }
        return res;
    }

    @Override
    public String odMux(String url) {
        if (StringUtils.isNotBlank(url) && StringComUtils.isUrlFileCorruptedLeast(url) && promptMappingConfig.getImageCaptionType().equals(0)) {
            ObjectDetectionInput input = new ObjectDetectionInput();
            input.setInput(url);
            input.setInputType("image_url");

            List<ObjectDetectionInput.SubList> subLists = new ArrayList<>();
            ObjectDetectionInput.SubList subList = new ObjectDetectionInput.SubList();
            subList.setModel(promptMappingConfig.getImageOdModel());
            subList.setConfigCode("");
            subLists.add(subList);

            input.setSubList(subLists);
            ObjectDetectionOutput detection = omObjectDetectionModel.detection(input);
            if (detection.getCode() == 200 && CollectionUtils.isNotEmpty(detection.getData())) {
                List<DrawBoxInfo> drawBoxInfos = new ArrayList<>();
                for (ObjectDetectionOutput.DetectionData datum : detection.getData()) {
                    for (ObjectDetectionOutput.Bbox bbox : datum.getBboxList()) {
                        drawBoxInfos.add(new DrawBoxInfo(bbox.getBbox(), String.join(",", bbox.getLabel())));
                    }
                }
                if (CollectionUtils.isNotEmpty(drawBoxInfos)) {
                    log.info(url + " -> OD开始达标打标：" + JSON.toJSON(drawBoxInfos));
                    BufferedImage bufferedImage = ImageUtils.imageDrawBox(url, drawBoxInfos);
                    String odUrl = minioStorage.storageTempImage(bufferedImage, FileUtil.extName(url));
                    log.info(url + " -> OD打标结果：" + odUrl);
                    return odUrl;
                }
            }
        }
        return url;
    }


    @Override
    public String imageDrawRect(String url, List<FaceInfoDTO> faceInfos, List<OpenVocabularyDetectionOutput.OvdData> objectDetectionResult, int maxSideLength, int maxFileSizeKB) throws Exception {

        List<DrawBoxInfo> drawBoxInfos = new ArrayList<>();
        // 人脸信息
        if (CollectionUtils.isNotEmpty(faceInfos)) {
            for (FaceInfoDTO faceInfoDTO : faceInfos) {
                if (faceInfoDTO.getPersonInfo() != null) {
                    drawBoxInfos.add(new DrawBoxInfo(faceInfoDTO.getBbox(), faceInfoDTO.getPersonInfo().getName()));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(objectDetectionResult)) {
            for (OpenVocabularyDetectionOutput.OvdData ovdData : objectDetectionResult) {
                String label = ovdData.getLabel();
                OpenVocabularyDetectionOutput.OvdResult ovdResult = ovdData.getOvdResult();
                if (CollectionUtils.isNotEmpty(ovdResult.getBboxList())) {
                    for (OpenVocabularyDetectionOutput.Bbox bbox : ovdResult.getBboxList()) {
                        if (bbox != null && CollectionUtils.isNotEmpty(bbox.getBbox())) {
                            drawBoxInfos.add(new DrawBoxInfo(bbox.getBbox(), label));
                        }
                    }
                }
            }
        }

        BufferedImage bufferedImage = ImageUtils.imageDrawBox(url, drawBoxInfos);

        bufferedImage = ImageConverter.compressImage(bufferedImage, maxSideLength, maxFileSizeKB);
        return minioStorage.storageTempImage(bufferedImage, "jpg");
    }

    @Override
    public List<String> mergeImages(List<String> imageUrls) {
        if (CollectionUtils.isEmpty(imageUrls) || imageUrls.size() == 1) {
            return imageUrls;
        }
        List<BufferedImage> bufferedImages = null;
        try {
            bufferedImages = ImageUtils.mergeImages(imageUrls.toArray(new String[0]), false);
            List<String> res = new ArrayList<>();
            for (BufferedImage bufferedImage : bufferedImages) {
                res.add(minioStorage.storageTempImage(bufferedImage, "jpg"));
            }
            return res;
        } catch (IOException e) {
            return imageUrls;
        }

    }

}
