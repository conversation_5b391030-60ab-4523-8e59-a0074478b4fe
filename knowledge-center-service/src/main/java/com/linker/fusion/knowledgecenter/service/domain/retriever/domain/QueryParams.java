package com.linker.fusion.knowledgecenter.service.domain.retriever.domain;

import com.linker.fusion.knowledgecenter.infrastructure.utils.StringComUtils;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.KnowledgeConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryParams {
    /**
     * 分组id
     */
    private List<String> groupIds;
    /**
     * 资源id
     */
    private List<String> docIds;
    /**
     * 需要重排序
     */
    private boolean needReRank = true;
    /**
     * 排除资源id
     */
    private List<String> notResIds;
    /**
     * 文件类型集合
     */
    private List<Integer> fileTypes;
    /**
     * 最大数
     */
    private Integer maxResult;
    /**
     * 图片
     */
    private String picUrl;
    private String embeddedKeyword;
    private String biz;
    private String tenantId;
    private double threshold;
//    private ChatReq.TextSummary summary;

    private KnowledgeConfig knowledgeConfig;


    @ApiModelProperty("标题搜索参数列表")
    private List<TitleParam> titleParams;

    @ApiModelProperty("结构化字段搜索参数列表")
    private List<MetaParam> metaParams;

    @ApiModelProperty("画面信息参数列表")
    private List<VisionParam> visionParams;

    @ApiModelProperty("专名标签搜索参数列表")
    private List<TerminologyParam> terminologyParams;

    /**
     * 创建时间大于
     */
    private String createTimeGt;

    @ApiModelProperty("纯元数据检索，以支持意图识别分析出的高级筛选条件")
    private boolean pureMeta = false;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TitleParam implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("搜索参数类型 \n" +
                "=等于 \n" +
                "like包含")
        private String operator;

        @ApiModelProperty("标题搜索文本")
        private String value;

        public String getValue() {
            if (value == null) {
                return null;
            }
            return StringComUtils.replaceIbaseEscapeWords(value);
        }
    }

    @Data
    public static class MetaParam {

        /**
         * 结构化字段对应的知识类型 1-文档 2-图片 3-视频 4-音频
         */
        private Integer fileType;

        /**
         * 结构化字段id
         */
        private String field;

        /**
         * 结构化字段类型 0-字符串 1-数字 2-时间 3-标签 4-单选 5-多选
         */
        private Integer fieldType;

        /**
         * 搜索参数类型
         * <p>
         * 0-字符串 支持 = like
         * <p>
         * 1-数字 4-单选 固定 =
         * <p>
         * 2-时间 固定 between
         * <p>
         * 3-标签 5-多选 固定 多个=
         */
        private String operator;

        /**
         * 搜索参数值
         */
        private Object value;

        public Object getValue() {
            if (value == null) {
                return null;
            }
            if (value instanceof String) {
                return StringComUtils.replaceIbaseEscapeWords((String) value);
            }
            return value;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VisionParam implements Serializable{

        private static final long serialVersionUID = 1L;

        @ApiModelProperty("1-人脸信息 2-实体信息")
        private String type;
        @ApiModelProperty("id")
        private String id;
        @ApiModelProperty("搜索文本")
        private String value;

        public String getValue() {
            if (value == null) {
                return null;
            }
            return StringComUtils.replaceIbaseEscapeWords(value);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TerminologyParam implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 1-人物、2-地点、3-机构、4-其他
         */
        private Integer type;

        @ApiModelProperty("搜索文本")
        private String value;

        public String getValue() {
            if (value == null) {
                return null;
            }
            return StringComUtils.replaceIbaseEscapeWords(value);
        }
    }

}
