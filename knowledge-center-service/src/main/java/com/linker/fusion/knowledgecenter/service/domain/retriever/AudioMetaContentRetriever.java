package com.linker.fusion.knowledgecenter.service.domain.retriever;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.linker.fusion.knowledgecenter.infrastructure.enums.FileTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexPrefixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.IndexSuffixEnum;
import com.linker.fusion.knowledgecenter.infrastructure.es.AudioChunkEntity;
import com.linker.fusion.knowledgecenter.infrastructure.manager.IIndexTenantMappingManager;
import com.linker.fusion.knowledgecenter.service.domain.node.conductor.model.input.KnowledgeConfig;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.Meta;
import com.linker.fusion.knowledgecenter.service.domain.retriever.domain.QueryParams;
import com.linker.omagent.core.rag.content.retriever.Content;
import com.linker.omagent.core.rag.content.retriever.ContentRetriever;
import com.linker.omagent.core.rag.query.Query;
import com.linker.omagent.core.repository.embedding.EmbeddingRepository;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchRequest;
import com.linker.omagent.core.repository.embedding.EmbeddingSearchResult;
import com.linker.omagent.core.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AudioMetaContentRetriever implements ContentRetriever {

    @Autowired
    private EmbeddingRepository embeddingStore;

    @Resource
    private IIndexTenantMappingManager indexTenantMappingManager;

    private static String buildFilter(String input, QueryParams extra) {
        // `meta.sys_fps` is null 标记只搜音频文件 
        StringBuilder filter = new StringBuilder(String.format("`meta.sys_fps` is null and enable = 1 and tenant_id = '%s' ", extra.getTenantId()));

        if (StringUtils.isNotBlank(input)) {
            filter.append(" and (")
                    .append("`file_title.keyword` like '").append(input).append("' ")
                    .append(" or `file_title.keyword` like '%").append(input).append("%' ")
//                .append(" or content like '").append(input).append("' ")
                    .append(" or text_labels like '").append(input).append("' ")
//                .append(" or persons_labels = '").append(input).append("' ")
//                .append(" or object_labels = '%").append(input).append("%' ")
                    .append(")");
        }
        KnowledgeConfig knowledgeConfig = extra.getKnowledgeConfig();
        List<Long> docGroupIds = knowledgeConfig.getDocGroupIds();
        if (CollectionUtils.isNotEmpty(docGroupIds)) {
            String str = docGroupIds.stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            String format = String.format(" and group_id in (%s) ", str);
            filter.append(format);
        }
        if (!ObjectUtils.isEmpty(extra.getNotResIds())) {
            String resourceIdsStr = extra.getNotResIds().stream().map(t -> String.format("'%s'", t))
                    .collect(Collectors.joining(", "));
            filter.append("and doc_id not in (").append(resourceIdsStr).append(")");
        }
        MetaFilterUtil.buildFilter(filter, extra, FileTypeEnum.AUDIO);
        return filter.toString();
    }

    @Override
    public List<Content> retrieve(Query query) {
        QueryParams extra = query.getMetadata().extra(QueryParams.class);
        if (!CollectionUtils.containsAny(extra.getFileTypes(), FileTypeEnum.AUDIO.getType())) {
            return Collections.emptyList();
        }
        if (!extra.isPureMeta() && StringUtils.isBlank(query.getInput())) {
            return Collections.emptyList();
        }

        long l = System.currentTimeMillis();
        EmbeddingSearchResult<AudioChunkEntity> searchResult;
        String type = FileTypeEnum.AUDIO.getName();
        String filter = buildFilter(query.getInput(), extra);
        String tenantId = extra.getTenantId();
        String index = indexTenantMappingManager.getIndex(tenantId, IndexPrefixEnum.AUDIO) + IndexSuffixEnum.AUDIO.suffix;

//        todo 获取index
        log.debug("检索条件:{}", filter);
        JSONArray sort = new JSONArray();
        Map<String, String> sortMap = new LinkedHashMap<>();
        sortMap.put("sort", "asc");
        sortMap.put("create_time", "desc");
        sort.add(sortMap);
        EmbeddingSearchRequest vectorSearch = EmbeddingSearchRequest
                .builder()
                .indexId(index)
                .exclude(Lists.newArrayList("content_vector"))
                .maxResults(Utils.getOrDefault(extra.getMaxResult(), 10))
                .sort(sort)
                .filter(filter)
                .build();
        searchResult = this.embeddingStore.search(vectorSearch,
                new TypeReference<EmbeddingSearchResult<AudioChunkEntity>>() {
                });

        log.info("检索器：{},检索结束耗时:{},召回数量：{}", description(), System.currentTimeMillis() - l, CollectionUtils.size(searchResult.matches()));
        return searchResult.matches().stream().map(hit -> {
            AudioChunkEntity c = hit.getDocument();
            Meta meta = new Meta();
            meta.setChunkId(c.getChunkId());
            meta.setSegmentId(c.getSegmentId());
            meta.setDocId(c.getDocId());
            meta.setScore(hit.getScore());
            meta.setRetriever("audio_meta");
            return new Content(replaceContent(c), type, meta);
        }).collect(Collectors.toList());
    }

    private String replaceContent(@NotNull AudioChunkEntity chunkEntity) {
        String sb = chunkEntity.getTitle() + "\n" +
//                chunkEntity.getContent() + "\n" +
                chunkEntity.getTextLabels() + "\n";
        return sb.trim();
    }

    @Override
    public String description() {
        return "音频元数据检索";
    }

    @Override
    public String type() {
        return "search";
    }
}
