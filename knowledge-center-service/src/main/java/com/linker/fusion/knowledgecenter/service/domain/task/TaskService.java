package com.linker.fusion.knowledgecenter.service.domain.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.linker.core.base.exception.BusinessException;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.AasWorkflowClient;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncCancelReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunReq;
import com.linker.fusion.knowledgecenter.infrastructure.client.aas.model.WorkflowAsyncRunResp;
import com.linker.fusion.knowledgecenter.infrastructure.config.AasWorkflowProperties;
import com.linker.fusion.knowledgecenter.infrastructure.entity.TaskEntity;
import com.linker.fusion.knowledgecenter.infrastructure.enums.ProcessEnum;
import com.linker.fusion.knowledgecenter.infrastructure.enums.TaskTypeEnum;
import com.linker.fusion.knowledgecenter.infrastructure.manager.ITaskManager;
import com.linker.fusion.knowledgecenter.infrastructure.model.AuthActionModel;
import com.linker.fusion.knowledgecenter.infrastructure.model.DocAction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class TaskService implements ITaskService {

    @Resource
    private ITaskManager taskManager;
    @Resource
    private AasWorkflowClient aaasWorkflowClient;
    @Resource
    private AasWorkflowProperties aasWorkflowProperties;

    @Override
    public void save(TaskEntity taskEntity) {
        taskManager.save(taskEntity);
    }

    @Override
    public void update(TaskEntity taskEntity) {
        taskManager.updateById(taskEntity);
    }

    @Override
    public TaskEntity get(Long id) {
        return taskManager.getById(id);
    }

    @Override
    public TaskEntity getNotNull(Long id) {
        TaskEntity task = get(id);
        if (Objects.isNull(task))
            throw new BusinessException("任务不存在");
        return task;
    }

    @Override
    public TaskEntity getByKey(String key) {
        return taskManager.getByKey(key);
    }

    @Override
    public void updateReadStatus(Long groupId, String tenantId, String creatorId) {
        taskManager.updateReadStatus(groupId, tenantId, creatorId);
    }

    @Override
    public void updateStatusToFail(String tenantId, String creatorId, String key) {
        taskManager.updateStatusToFail(tenantId, creatorId, key);
    }

    @Override
    public List<TaskEntity> listUnread(String tenantId, String userCode) {
        return taskManager.listUnread(tenantId, userCode);
    }

    @Override
    public void checkHasRun(String tenantId, String userCode, Long groupId) {
        TaskEntity task = taskManager.getLastRun(tenantId, userCode, groupId);
        if (Objects.nonNull(task)) {
            throw new BusinessException("当前存在正在执行的任务");
        }
    }

    @Override
    public TaskEntity send(String tenantId, String userCode, Long groupId, Object content, String key, TaskTypeEnum type) {
        return send(tenantId, userCode, groupId, content, key, type, new HashedMap<>());
    }

    public TaskEntity send(String tenantId, String userCode, Long groupId, Object content, String key, TaskTypeEnum type, Map<String, Object> inputs) {

        if (groupId > 0) {
            //设置当前用户当前目录的其他任务为已读
            updateReadStatus(groupId, tenantId, userCode);
        } else if (StringUtils.isNotBlank(key)) {
            //设置当前key值的其他任务为失败.
            updateStatusToFail(tenantId, userCode, key);
        }
        TaskEntity taskEntity = new TaskEntity();
        taskEntity.init(tenantId, userCode);
        taskEntity.setGroupId(groupId);
        taskEntity.setType(type.getValue());
        taskEntity.setReadStatus(0);
        taskEntity.setKey(key);
        taskEntity.setStatus(ProcessEnum.Executing.getValue());
        taskEntity.setContent(Objects.isNull(content) ? "{}" : JSON.toJSONString(content));
        save(taskEntity);
        // Add taskId to provided inputs map
        inputs.put("taskId", taskEntity.getId());

        // 根据任务类型选择不同的工作流ID
        String workflowId;
        if (TaskTypeEnum.DocumentToQB.equals(type)) { // 3 表示从文档生成题目
            workflowId = aasWorkflowProperties.getWorkflowId().getGenerateQbFromDoc();
            if (workflowId == null || workflowId.isEmpty()) {
                log.error("未配置从文档生成题目的工作流ID (workflow.execute.workflow-id.generate-qb-from-doc)");
                throw new RuntimeException("未配置从文档生成题目的工作流ID");
            }
        } else if (TaskTypeEnum.FileCut.equals(type)) {
            workflowId = aasWorkflowProperties.getWorkflowId().getTaskCut();
            if (workflowId == null || workflowId.isEmpty()) {
                log.error("未配置通用的任务工作流ID (workflow.execute.workflow-id.task-cut)");
                throw new RuntimeException("未配置通用的任务工作流ID");
            }
        } else {
            workflowId = aasWorkflowProperties.getWorkflowId().getTask();
            if (workflowId == null || workflowId.isEmpty()) {
                log.error("未配置通用的任务工作流ID (workflow.execute.workflow-id.task)");
                throw new RuntimeException("未配置通用的任务工作流ID");
            }
        }

        WorkflowAsyncRunResp workflowAsyncRunResp = aaasWorkflowClient.runAsync(
                new WorkflowAsyncRunReq()
                        .setId(workflowId) // 使用动态选择的工作流ID
                        .setInput(inputs)
        );
        if (Objects.isNull(workflowAsyncRunResp) || !Objects.equals("0", workflowAsyncRunResp.getCode())) {
            log.error("下发工作流任务失败{}", JSON.toJSONString(workflowAsyncRunResp));
            throw new RuntimeException("下发工作流任务失败");
        }
        String workflowInstanceId = workflowAsyncRunResp.getData();
        log.info("ai生成问答对下发工作流任务成功：taskId:{}, workflowInstanceId:{}", taskEntity.getId(), workflowInstanceId);
        taskEntity.setResult("{\"workflowId\":\"" + workflowInstanceId + "\"}");
        update(taskEntity);

        return taskEntity;
    }

    @Override
    public void shutdown(List<Long> ids) {
        for (TaskEntity taskEntity : ListByIds(ids)) {
            try {
                JSONObject ret = JSONObject.parseObject(taskEntity.getResult());
                aaasWorkflowClient.cancelAsync(new WorkflowAsyncCancelReq(ret.getString("workflowId")));
            } catch (Exception e) {
                log.error("取消任务失败", e);
            }
        }
    }

    @Override
    public List<TaskEntity> ListByIds(List<Long> ids) {
        return taskManager.listByIds(ids);
    }

    @Override
    public List<TaskEntity> listByKeys(List<String> keys) {
        if (CollectionUtils.isEmpty(keys))
            return new ArrayList<>();
        return taskManager.listBykeys(keys);
    }

    @Override
    public List<TaskEntity> listByGroupIdAndType(Long groupId, Integer type) {
        return taskManager.list(
                new LambdaQueryWrapper<TaskEntity>()
                        .eq(TaskEntity::getGroupId, groupId)
                        .eq(TaskEntity::getType, type)
                        .eq(TaskEntity::getDeleted, false)
        );
    }

    @Override
    @Cached(expire = 60, cacheType = CacheType.LOCAL)
//    @CacheRefresh(refresh = 32, stopRefreshAfterLastAccess = 3600)
    public List<AuthActionModel> testCache(List<String> keys) {
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        AuthActionModel authActionModel = new AuthActionModel();
        authActionModel.setDocAction(new DocAction());
        log.info("testCache");
        return Lists.newArrayList(authActionModel);
    }
}
