package com.linker.fusion.knowledgecenter.service.domain.img.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
@Accessors(chain = true)
public class FaceInfoDTO {

    @ApiModelProperty("是否匹配")
    private boolean matched;

    @ApiModelProperty("人物ID")
    private String personId;

    @ApiModelProperty("人脸坐标")
    private List<Integer> bbox;

    @ApiModelProperty("人物信息")
    private PersonInfoDTO personInfo;

    @ApiModelProperty("人脸剪切图")
    private String image;

    public String getViewImage() {
        if (personId != null && personId.length() > 5 && personInfo != null) {
            return CollectionUtils.isNotEmpty(personInfo.getPhotos()) ? personInfo.getPhotos().get(0).getUrl() : getImage();
        }
        return getImage();
    }
}