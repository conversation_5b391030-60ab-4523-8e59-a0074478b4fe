<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.linker.fusion</groupId>
        <artifactId>knowledge-center</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>knowledge-center-service</artifactId>


    <dependencies>
        <dependency>
            <groupId>com.linker.fusion</groupId>
            <artifactId>knowledge-center-infrastructure</artifactId>

        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.huaban</groupId>
            <artifactId>jieba-analysis</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>xxljob-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.linker.omos</groupId>
            <artifactId>omos-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>
