<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>linker-parent</artifactId>
        <groupId>com.linker.parent</groupId>
        <version>2.7.18-SNAPSHOT</version>
    </parent>


    <modelVersion>4.0.0</modelVersion>
    <artifactId>knowledge-center</artifactId>
    <groupId>com.linker.fusion</groupId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>

    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.linker</groupId>
                <artifactId>linker-core</artifactId>
                <type>pom</type>
                <scope>import</scope>
                <version>2.0.2-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk8</artifactId>
                <version>1.8.21</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.linker.omagent</groupId>
                <artifactId>omagent-spring-boot-starter</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.linker.fusion</groupId>
                <artifactId>knowledge-center-service</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.linker.fusion</groupId>
                <artifactId>knowledge-center-infrastructure</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <!--conductor-client-->
            <!--            <dependency>-->
            <!--                <groupId>com.linker.conductor</groupId>-->
            <!--                <artifactId>conductor-client</artifactId>-->
            <!--                <version>1.0.2-SNAPSHOT</version>-->
            <!--            </dependency>-->
            <!-- jetcache-redis-->
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis-lettuce</artifactId>
                <version>2.7.3</version>
            </dependency>

        </dependencies>

    </dependencyManagement>
    <build>
        <!-- mvn versions:set -DnewVersion=1.1.9.7 versions:commit -->
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>knowledge-center-server</module>
        <module>knowledge-center-infrastructure</module>
        <module>knowledge-center-service</module>

    </modules>
</project>
