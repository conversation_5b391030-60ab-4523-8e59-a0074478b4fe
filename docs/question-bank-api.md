# 题库管理接口文档

## 数据库设计

### t_question_bank 题库表

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | bigint | 是 | 主键ID |
| tenant_id | varchar(50) | 是 | 租户ID |
| uid | varchar(64) | 是 | uid |
| group_id | bigint | 是 | 所属分组ID |
| question | varchar(200) | 是 | 问题内容 |
| question_image | varchar(1024) | 否 | 问题图片URL |
| answer | text | 是 | 答案内容 |
| analysis | varchar(3000) | 否 | 解析说明 |
| options | json | 否 | 选项JSON |
| question_type | varchar(50) | 是 | 题目类型:CHOICE-单选题,MULTIPLE_CHOICE-多选题,TRUE_FALSE-判断题,FILL_IN_BLANK-填空题 |
| enable | tinyint(1) | 是 | 是否启用 0-禁用 1-启用 |
| deleted | tinyint(1) | 是 | 是否删除 0-未删除 1-已删除 |
| creator_id | varchar(50) | 是 | 创建人ID |
| create_time | datetime | 是 | 创建时间 |
| update_id | varchar(50) | 是 | 更新人ID |
| update_time | datetime | 是 | 更新时间 |

索引:
- PRIMARY KEY (`id`)
- KEY `idx_tenant_group` (`tenant_id`, `group_id`)

```sql
CREATE TABLE `t_question_bank` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
  `uid` varchar(64) NOT NULL COMMENT 'uid',
  `group_id` bigint NOT NULL COMMENT '所属分组ID',
  `question` varchar(200) NOT NULL COMMENT '问题内容',
  `question_image` varchar(1024) DEFAULT NULL COMMENT '问题图片URL',
  `answer` text NOT NULL COMMENT '答案内容',
  `analysis` varchar(3000) DEFAULT NULL COMMENT '解析说明',
  `options` json DEFAULT NULL COMMENT '选项JSON',
  `question_type` varchar(50) NOT NULL COMMENT '题目类型:CHOICE-单选题,MULTIPLE_CHOICE-多选题,TRUE_FALSE-判断题,FILL_IN_BLANK-填空题',
  `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用 1-启用',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  `creator_id` varchar(50) NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_id` varchar(50) NOT NULL COMMENT '更新人ID',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_group` (`tenant_id`, `group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='题库表';
```

### 接口说明

#### 基础信息
- 基础路径: `/question-bank`
- 数据格式: `application/json`
- 字符编码: `UTF-8`

#### API接口

##### 1. 分页查询题目列表

###### 接口信息
- **接口**: GET `/page`
- **描述**: 分页查询题目列表，支持按分组、关键词搜索

###### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | int | 是 | 页码，从1开始 |
| pageSize | int | 是 | 每页条数 |
| groupId | long | 是 | 分组ID |
| keyword | string | 否 | 搜索关键词 |
| enable | boolean | 否 | 是否启用 |
| questionType | string | 否 | 题目类型:CHOICE-单选题,MULTIPLE_CHOICE-多选题,TRUE_FALSE-判断题,FILL_IN_BLANK-填空题 |
| includeSubDir | int | 否 | 是否包含子目录 0-否 1-是 |

###### 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 响应码 0-成功 |
| msg | string | 响应信息 |
| data.total | long | 总记录数 |
| data.records | array | 记录列表 |
| data.records[].id | long | 题目ID |
| data.records[].groupId | long | 分组ID |
| data.records[].question | string | 问题内容 |
| data.records[].questionImage | string | 问题图片URL |
| data.records[].answer | string | 答案内容 |
| data.records[].analysis | string | 解析说明 |
| data.records[].options | array | 选项列表 |
| data.records[].options[].key | string | 选项序号 |
| data.records[].options[].value | string | 选项内容 |
| data.records[].options[].image | string | 选项图片URL |
| data.records[].questionType | string | 题目类型 |
| data.records[].enable | boolean | 是否启用 |
| data.records[].createTime | string | 创建时间 |
| data.records[].updateTime | string | 更新时间 |
| data.records[].authActions | object | 权限信息 |
| data.records[].authActions.canView | boolean | 是否可查看 |
| data.records[].authActions.canEdit | boolean | 是否可编辑 |
| data.records[].authActions.canDelete | boolean | 是否可删除 |
| data.records[].groupParentResp | object | 分组信息 |
| data.records[].groupParentResp.id | long | 分组ID |
| data.records[].groupParentResp.type | string | 分组类型 |
| data.records[].groupParentResp.parents | array | 父级分组列表 |

##### 2. 创建题目

###### 接口信息
- **接口**: POST `/create`
- **描述**: 创建新的题目

###### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| groupId | long | 是 | 分组ID |
| question | string | 否 | 问题内容，与问题图片至少填写一项 |
| questionImage | string | 否 | 问题图片URL，与问题内容至少填写一项 |
| answer | string | 是 | 答案内容 |
| analysis | string | 否 | 解析说明 |
| options | array | 否 | 选项列表 |
| options[].key | string | 是 | 选项序号 |
| options[].value | string | 否 | 选项内容，与选项图片至少填写一项 |
| options[].image | string | 否 | 选项图片URL，与选项内容至少填写一项 |
| questionType | string | 是 | 题目类型 |

###### 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 响应码 0-成功 |
| msg | string | 响应信息 |
| data | object | 创建的题目信息 |

##### 3. 更新题目

###### 接口信息
- **接口**: POST `/update`
- **描述**: 更新题目信息

###### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | long | 是 | 题目ID |
| question | string | 否 | 问题内容，与问题图片至少填写一项 |
| questionImage | string | 否 | 问题图片URL，与问题内容至少填写一项 |
| answer | string | 是 | 答案内容 |
| analysis | string | 否 | 解析说明 |
| options | array | 否 | 选项列表 |
| options[].key | string | 是 | 选项序号 |
| options[].value | string | 否 | 选项内容，与选项图片至少填写一项 |
| options[].image | string | 否 | 选项图片URL，与选项内容至少填写一项 |

###### 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 响应码 0-成功 |
| msg | string | 响应信息 |

##### 4. 删除题目

###### 接口信息
- **接口**: POST `/delete`
- **描述**: 批量删除题目

###### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| ids | array | 是 | 题目ID列表 |

###### 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 响应码 0-成功 |
| msg | string | 响应信息 |

##### 5. 启用/禁用题目

###### 接口信息
- **接口**: POST `/enable`
- **描述**: 启用或禁用单个题目

###### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | long | 是 | 题目ID |
| enable | boolean | 是 | 是否启用 |

###### 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 响应码 0-成功 |
| msg | string | 响应信息 |

##### 6. 批量启用/禁用题目

###### 接口信息
- **接口**: POST `/batch-enable`
- **描述**: 批量启用或禁用题目

###### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| ids | array | 是 | 题目ID列表 |
| enable | boolean | 是 | 是否启用 |

###### 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 响应码 0-成功 |
| msg | string | 响应信息 |

##### 7. 移动题目

###### 接口信息
- **接口**: POST `/move`
- **描述**: 将题目移动到其他分组

###### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| ids | array | 是 | 题目ID列表 |
| targetGroupId | long | 是 | 目标分组ID |

###### 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 响应码 0-成功 |
| msg | string | 响应信息 |

##### 8. 文档生成题目

###### 接口信息
- **接口**: POST `/ai/doc/generate`
- **描述**: 通过上传文档生成题库题目
- **权限**: 仅开放给"苏州电力"租户

###### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| groupId | long | 是 | 目标分组ID |
| file | file | 是 | 上传的文档文件(支持pdf、doc、docx) |
| questionTypes | array | 否 | 期望生成的题目类型列表，默认为["CHOICE"] |

###### 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 响应码 0-成功 |
| msg | string | 响应信息 |
| data | object | 任务信息 |
| data.id | long | 任务ID |
| data.type | int | 任务类型 |
| data.status | int | 任务状态 |
| data.readStatus | int | 已读状态 |
| data.groupId | long | 目录ID |
| data.content | string | 任务内容 |
| data.result | string | 任务结果 |

###### 响应示例

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 123456,
        "type": 2,
        "status": 1,
        "readStatus": 0,
        "groupId": 1001,
        "content": "文档题目生成任务",
        "result": "文档题目生成成功"
    }
}
```

### 题目格式说明

#### 题目类型

| 类型值 | 描述 |
|--------|------|
| CHOICE | 单选题 |
| MULTIPLE_CHOICE | 多选题 |
| TRUE_FALSE | 判断题 |
| FILL_IN_BLANK | 填空题 |

#### 题目格式详情

##### 单选题 (CHOICE)

| 字段名 | 格式说明 | 示例 |
|--------|----------|------|
| question | 问题内容，最大长度200字，与问题图片至少填写一项 | `"什么是Spring Framework?"` |
| question_image | 问题图片URL，最大100M，格式同用户中台数据权限 | `"https://example.com/image.jpg"` |
| answer | 单选题填写选项序号(A/B/C/D) | `"A"` |
| analysis | 题目解析，最大长度3000字 | `"选项A正确，因为..."` |
| options | JSON数组，每个选项包含序号(key)、内容(value)和图片(image)，每个选项内容最大长度200字。<br>格式: `[{"key": "A", "value": "选项内容", "image": "图片URL"}]` | ```[{"key": "A", "value": "选项1", "image": "https://example.com/1.jpg"}, {"key": "B", "value": "选项2", "image": null}]``` |

##### 多选题 (MULTIPLE_CHOICE)

| 字段名 | 格式说明 | 示例 |
|--------|----------|------|
| question | 问题内容，最大长度200字，与问题图片至少填写一项 | `"以下哪些是正确的?"` |
| question_image | 问题图片URL，最大100M，格式同用户中台数据权限 | `"https://example.com/image.jpg"` |
| answer | 多选题填写选项序号，用逗号分隔(A,B,C)，至少选择2个选项 | `"A,B,C"` |
| analysis | 题目解析，最大长度3000字 | `"选项A、B、C正确，因为..."` |
| options | JSON数组，最多8个选项，每个选项包含序号(key)、内容(value)和图片(image)。<br>格式: `[{"key": "A", "value": "选项内容", "image": "图片URL"}]` | ```[{"key": "A", "value": "选项1", "image": "https://example.com/1.jpg"}, {"key": "B", "value": "选项2", "image": null}]``` |

##### 判断题 (TRUE_FALSE)

| 字段名 | 格式说明 | 示例 |
|--------|----------|------|
| question | 问题内容，最大长度200字，与问题图片至少填写一项 | `"Java是编译型语言吗?"` |
| question_image | 问题图片URL，最大100M，格式同用户中台数据权限 | `"https://example.com/image.jpg"` |
| answer | 填写 "1"(正确) 或 "0"(错误) | `"1"` 或 `"0"` |
| analysis | 题目解析，最大长度3000字 | `"本题正确，因为..."` |
| options | 固定格式的JSON数组。<br>格式: `[{"key": "1", "value": "正确"}, {"key": "0", "value": "错误"}]` | ```[{"key": "1", "value": "正确"}, {"key": "0", "value": "错误"}]``` |

##### 填空题 (FILL_IN_BLANK)

| 字段名 | 格式说明 | 示例 |
|--------|----------|------|
| question | 问题内容，最大长度200字，与问题图片至少填写一项 | `"请填写Spring框架的核心容器组件"` |
| question_image | 问题图片URL，最大100M，格式同用户中台数据权限 | `"https://example.com/image.jpg"` |
| answer | JSON数组格式，支持多个答案。<br>格式: `[{"key": "1", "value": "答案1"}, {"key": "2", "value": "答案2"}]` | ```[{"key": "1", "value": "BeanFactory"}, {"key": "2", "value": "ApplicationContext"}]``` |
| analysis | 题目解析，最大长度3000字 | `"正确答案解释..."` |
| options | 固定格式的JSON数组。<br>格式: `[{"key": "1"}, {"key": "2"}]` | ```[{"key": "1"}, {"key": "2"}]``` |

#### 字段通用说明

1. **question字段**:
   - 与question_image至少填写一项
   - 最大长度: 200字符
   - 不支持HTML标签

2. **question_image字段**:
   - 选填字段
   - 图片大小限制: 100M
   - 格式要求: 同用户中台数据权限
   - 超限提示: "图片上传限制最大100M"

3. **answer字段**:
   - 必填字段
   - 根据题目类型有不同的格式要求
   - 多选题答案需按字母顺序排序且至少选择2个选项
   - 填空题答案最大长度3000字

4. **options字段**:
   - 必填字段（填空题除外）
   - 采用统一的JSON数组格式
   - key: 选项标识，必须唯一
   - value: 选项内容，最大长度200字符
   - image: 选项图片URL，最大100M
   - 选项数量限制：
     * 单选题: 最多4个选项，至少2个选项
     * 多选题: 最多8个选项，至少2个选项
     * 判断题: 固定2个选项

### 其他说明

#### 错误码说明

| 错误码 | 描述 | 处理建议 |
|--------|------|----------|
| 400 | 参数错误 | 检查请求参数是否符合要求 |
| 401 | 未授权 | 检查用户是否登录 |
| 403 | 权限不足 | 检查用户是否有对应的操作权限 |
| 404 | 资源不存在 | 检查请求的资源是否存在 |
| 500 | 服务器内部错误 | 请联系管理员 |

#### 文档生成题目流程

```mermaid
flowchart TD
    Start[用户上传文档] --> Auth{权限检查}
    Auth -- 未授权 --> Error1[权限错误]
    Auth -- 已授权 --> ValidateFile{文件验证}
    
    ValidateFile -- 格式/大小不符 --> Error2[文件验证失败]
    ValidateFile -- 验证通过 --> Process[解析文档]
    
    Process --> ExtractContent[提取文档内容]
    ExtractContent --> GenerateQuestions[AI生成题目和选项]
    GenerateQuestions --> FormatConversion[数据格式转换和处理]
    
    FormatConversion --> FilterDuplicates[查重过滤]
    FilterDuplicates --> CheckFiltered{过滤后是否有题目}
    CheckFiltered -- 全部被过滤 --> Error3[生成失败:全部题目重复]
    CheckFiltered -- 有可用题目 --> CheckCapacity{容量检查}
    
    CheckCapacity -- 剩余容量为0 --> Error4[容量不足错误]
    CheckCapacity -- 超出容量 --> InsertPartial[准备剩余容量数量题目]
    CheckCapacity -- 容量充足 --> InsertAll[准备全部题目]
    
    InsertPartial --> GenerateAnalysis[AI生成答案解析]
    InsertAll --> GenerateAnalysis
    
    GenerateAnalysis --> SaveToDB[保存题目到数据库]
    SaveToDB --> Complete[生成完成]
    Complete --> End[结束]

    Error1 --> End
    Error2 --> End
    Error3 --> End
    Error4 --> End
```

#### 文档生成题目说明

1. **权限控制**
   - 仅对"苏州电力"租户开放
   - 需要目标分组的写入权限

2. **文件限制**
   - 支持格式：pdf、doc、docx
   - 大小限制：遵循系统通用文件上传限制

3. **生成规则**
   - 默认生成单选题
   - 支持的题型：单选题、多选题、填空题
   - 必须包含字段：题目类型、问题、答案
   - 答案解析为可选字段

4. **查重规则**
   - 范围：本租户内+当前目录内
   - 对比字段：问题标题
   - 重复题目将被过滤不新增
   - 全部题目重复时返回生成失败

5. **容量控制**
   - 生成数量不得超过剩余容量
   - 超出时仅插入剩余容量数量的题目
   - 剩余容量为0时直接报错

6. **状态控制**
   - 新生成的题目默认为未生效状态
   - 支持后续手动修改状态

7. **并发控制**
   - 同一账号可对同一目录重复操作
   - 每次操作独立生成新的任务ID


